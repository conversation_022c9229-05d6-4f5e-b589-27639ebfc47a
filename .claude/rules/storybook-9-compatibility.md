# Storybook 9.0 Compatibility Rules

## Overview
This project uses Storybook 9.0.18. This version introduces breaking changes in package structure that require specific import patterns and configurations.

## Critical Import Rules

### ✅ CORRECT - Use New Consolidated Imports

```typescript
// Meta and StoryObj types
import type { <PERSON>a, StoryObj } from '@storybook/react';

// Actions
import { action } from 'storybook/actions';

// Testing utilities  
import { fn, expect, userEvent, within } from 'storybook/test';

// Other consolidated packages
import { ... } from 'storybook/manager-api';
import { ... } from 'storybook/preview-api';
import { ... } from 'storybook/theming';
import { ... } from 'storybook/viewport';
import { ... } from 'storybook/highlight';
```

### ❌ FORBIDDEN - Old Package Imports

```typescript
// These imports will cause build failures
import type { Meta, StoryObj } from '@storybook/react-webpack5';
import { action } from '@storybook/addon-actions';
import { expect, userEvent, within } from '@storybook/testing-library';
import { ... } from '@storybook/addon-backgrounds';
import { ... } from '@storybook/addon-controls';
import { ... } from '@storybook/addon-interactions';
import { ... } from '@storybook/addon-measure';
import { ... } from '@storybook/addon-outline';
import { ... } from '@storybook/addon-toolbars';
import { ... } from '@storybook/addon-viewport';
import { ... } from '@storybook/manager-api';
import { ... } from '@storybook/preview-api';
import { ... } from '@storybook/test';
import { ... } from '@storybook/theming';
```

## Configuration Requirements

### Webpack Configuration
The `.storybook/main.ts` file must include:

```typescript
webpackFinal: async (config) => {
  // SCSS loader support
  config.module?.rules?.push({
    test: /\.scss$/,
    use: ['style-loader', 'css-loader', 'sass-loader'],
  });
  
  // Fix chunk loading issues
  if (config.optimization) {
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          priority: -10,
          chunks: 'all',
        },
      },
    };
  }
  
  return config;
},
```

### Required Dependencies
```json
{
  "devDependencies": {
    "style-loader": "^3.x.x",
    "css-loader": "^6.x.x", 
    "sass-loader": "^13.x.x",
    "sass": "^1.x.x"
  }
}
```

## Package Consolidation Map

| Old Package | New Import Path | Status |
|-------------|----------------|---------|
| `@storybook/addon-actions` | `storybook/actions` | ✅ Consolidated |
| `@storybook/addon-backgrounds` | N/A | ❌ Removed |
| `@storybook/addon-controls` | N/A | ❌ Removed |  
| `@storybook/addon-highlight` | `storybook/highlight` | ✅ Consolidated |
| `@storybook/addon-interactions` | N/A | ❌ Removed |
| `@storybook/addon-measure` | N/A | ❌ Removed |
| `@storybook/addon-outline` | N/A | ❌ Removed |
| `@storybook/addon-toolbars` | N/A | ❌ Removed |
| `@storybook/addon-viewport` | `storybook/viewport` | ✅ Consolidated |
| `@storybook/manager-api` | `storybook/manager-api` | ✅ Consolidated |
| `@storybook/preview-api` | `storybook/preview-api` | ✅ Consolidated |
| `@storybook/test` | `storybook/test` | ✅ Consolidated |
| `@storybook/theming` | `storybook/theming` | ✅ Consolidated |

## Common Migration Tasks

### 1. Update Story Files
When creating new `.stories.tsx` files:

```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { action } from 'storybook/actions';
import ComponentName from './ComponentName';

const meta = {
  title: 'Components/ComponentName',
  component: ComponentName,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onClick: { action: 'clicked' },
  },
} satisfies Meta<typeof ComponentName>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onClick: action('clicked'),
  },
};
```

### 2. Fix Existing Files
Run this search to find outdated imports:

```bash
# Find files with old imports
grep -r "@storybook/react-webpack5\|@storybook/addon-" src/

# Replace in all files
find src/ -name "*.stories.tsx" -exec sed -i '' 's/@storybook\/react-webpack5/@storybook\/react/g' {} \;
find src/ -name "*.stories.tsx" -exec sed -i '' 's/@storybook\/addon-actions/storybook\/actions/g' {} \;
```

### 3. Remove Deprecated Dependencies
```bash
npm uninstall @storybook/addon-actions @storybook/addon-backgrounds @storybook/addon-controls @storybook/addon-interactions @storybook/addon-measure @storybook/addon-outline @storybook/addon-toolbars
```

## Troubleshooting

### Common Errors

1. **"Loading chunk vendors-node_modules_react_jsx-dev-runtime_js failed"**
   - Clear cache: `rm -rf node_modules/.cache .storybook-cache`
   - Restart Storybook: `npm run storybook`

2. **"Module not found: Error: Can't resolve '@storybook/addon-actions'"**
   - Update import to: `import { action } from 'storybook/actions';`
   - Remove old dependency: `npm uninstall @storybook/addon-actions`

3. **"Module parse failed: Unexpected token" for SCSS files**
   - Ensure SCSS loaders are configured in `.storybook/main.ts`
   - Install required dependencies: `npm install --save-dev style-loader css-loader sass-loader sass`

## Verification Commands

```bash
# Check for outdated imports
grep -r "@storybook/addon-\|@storybook/react-webpack5" src/ || echo "✅ No outdated imports found"

# Start Storybook
npm run storybook

# Build Storybook
npm run build-storybook
```

## References

- [Storybook 9.0 Migration Guide](https://storybook.js.org/docs/9/migration-guide)
- [Package Structure Changes](https://storybook.js.org/docs/9/migration-guide#package-structure-changes)
- [Storybook 9.0 Changelog](https://github.com/storybookjs/storybook/blob/main/CHANGELOG.md)

---

**Last Updated**: 2025-01-08  
**Storybook Version**: 9.0.18  
**Status**: ✅ Active Rule