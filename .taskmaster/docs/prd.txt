<context>
# Overview  
Xây dựng hệ thống map data động từ API backend vào các components trong blog-ui-v5. <PERSON>ệ thống này gi<PERSON>i quyết vấn đề hiển thị data static trong components, gi<PERSON>p tạo ra blog website với nội dung động, cập nhật real-time từ API backend.

# Core Features  
- **Dynamic Data Services**: Services layer để fetch data từ API (latest posts, trending, popular, categories, tags)
- **Custom React Hooks**: Hooks quản lý data fetching, caching, infinite scroll
- **Component Integration**: Cập nhật existing components để support dynamic data
- **Server-Side Rendering**: SSR support cho SEO và performance
- **Real-time Updates**: WebSocket integration cho live content updates
- **Performance Optimization**: Caching, lazy loading, error handling

# User Experience  
- **Content Readers**: Xem nội dung mới nhất, trending được cập nhật real-time
- **Blog Administrators**: Quản lý content với immediate preview
- **Mobile Users**: Fast loading với progressive enhancement
</context>

<PRD>
# Technical Architecture

### 1. Data Services Layer
- Tạo BlogService để quản lý việc fetch data bài viết từ API backend (port 9077)
- Implement các methods: getLatestPosts, getTrendingPosts, getPopularPosts, getPostsByCategory, getRelatedPosts
- Tạo TagService để fetch data tags phổ biến
- Tạo CategoryService để fetch data categories
- Implement error handling và retry logic cho tất cả API calls

### 2. Custom Hooks cho Data Fetching
- Tạo useBlogPosts hook cho việc fetch posts theo type (latest, trending, popular)
- Implement useInfiniteScroll hook cho pagination và lazy loading
- Tạo useTags hook cho việc fetch popular tags
- Implement caching strategy với React Query hoặc SWR

### 3. Components với Dynamic Data
- Refactor BlogGrid component để accept data từ API
- Tạo BlogGridWithData component wrapper có thể tự fetch data
- Refactor BlogCarousel component để hiển thị trending posts
- Tạo PopularTagsWithData component tự động fetch tags phổ biến
- Implement PostCard component với real-time view count

### 4. Page-level Integration
- Integrate data fetching vào trang chủ với Server-Side Rendering
- Implement category pages với dynamic data loading
- Tạo search results page với real-time search
- Implement individual blog post page với related posts

### 5. Performance Optimization
- Implement data caching strategy
- Add loading skeletons cho tất cả components
- Implement error boundaries và fallback UI
- Add infinite scroll cho blog listing pages
- Optimize images và implement lazy loading

### 6. Real-time Features
- Setup WebSocket connection cho live updates
- Implement real-time trending posts updates
- Add live view count updates cho blog posts
- Implement real-time notifications cho new posts

## Yêu cầu kỹ thuật

### API Integration
- Sử dụng API backend tại localhost:9077
- Implement proper authentication headers (X-API-Key, X-Tenant-ID, X-Website-ID)
- Handle API response format theo chuẩn đã định nghĩa
- Implement proper error handling cho network failures

### Type Safety
- Định nghĩa TypeScript interfaces cho tất cả API responses
- Tạo type definitions cho component props
- Implement proper type guards cho API data validation

### Testing Requirements
- Unit tests cho tất cả services
- Integration tests cho components với data
- E2E tests cho user workflows
- Performance tests cho data loading

### Code Organization
- Tổ chức code theo clean architecture pattern
- Separation of concerns giữa data layer và presentation layer
- Reusable components không phụ thuộc vào specific data sources
- Consistent naming conventions

## Acceptance Criteria

### Phase 1: Core Data Layer
- [ ] BlogService implement đầy đủ các methods cần thiết
- [ ] TagService và CategoryService hoạt động chính xác
- [ ] Error handling và retry logic work properly
- [ ] Type definitions đầy đủ và accurate

### Phase 2: Custom Hooks
- [ ] useBlogPosts hook fetch data correctly cho all types
- [ ] useInfiniteScroll implement pagination properly
- [ ] Caching strategy hoạt động và improve performance
- [ ] Loading states được handle properly

### Phase 3: Component Integration
- [ ] BlogGrid hiển thị data từ API correctly
- [ ] BlogCarousel show trending posts với auto-refresh
- [ ] PopularTags component update real-time
- [ ] All components handle loading và error states

### Phase 4: Page-level Features
- [ ] Homepage load data với SSR
- [ ] Category pages work với dynamic routing
- [ ] Search functionality return accurate results
- [ ] Individual post pages show related content

### Phase 5: Performance & UX
- [ ] Loading times under 2 seconds cho initial load
- [ ] Smooth infinite scroll experience
- [ ] Proper error messages và recovery options
- [ ] Mobile-responsive cho tất cả features

### Phase 6: Real-time Features
- [ ] WebSocket connection stable và reliable
- [ ] Live updates không interrupt user experience
- [ ] Real-time data sync properly với backend
- [ ] Performance không bị impact bởi real-time features

## Constraints và Limitations

### Technical Constraints
- Phải tương thích với existing codebase architecture
- Không break existing functionality
- Follow existing coding standards và conventions
- Support cả development và production environments

### Performance Constraints  
- Initial page load time không vượt quá 3 seconds
- API calls được optimize để minimize requests
- Memory usage phải reasonable cho mobile devices
- Smooth 60fps scrolling experience

### Browser Support
- Support modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Graceful degradation cho older browsers

## Success Metrics
- Giảm initial load time ít nhất 30%
- Tăng user engagement với dynamic content
- Zero data loading errors trong normal conditions
- Smooth user experience across tất cả devices
- Maintainable và scalable code architecture