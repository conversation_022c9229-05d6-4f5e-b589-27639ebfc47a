---
id: task-high.2
title: Auto-apply templates on tenant creation
status: To Do
assignee: []
created_date: '2025-08-07'
labels:
  - rbac
  - tenant
  - backend
dependencies:
  - task-high.1
parent_task_id: task-high
---

## Description

Hook into TenantService.CreateTenant to automatically apply default role templates when a new tenant is created

## Acceptance Criteria

- [ ] New tenants get default roles automatically
- [ ] Owner role assigned to tenant creator
- [ ] Works with auto_assign_on_tenant_creation flag
- [ ] No manual intervention needed
