---
id: task-high.5
title: Template-to-Role converter implementation
status: To Do
assignee: []
created_date: '2025-08-07'
labels:
  - rbac
  - core
  - backend
dependencies: []
parent_task_id: task-high
---

## Description

Implement the conversion logic from RoleTemplate to Role with proper permission assignment

## Acceptance Criteria

- [ ] Converts template properties to role
- [ ] Maps default_permissions correctly
- [ ] Handles different scopes (tenant/website/global)
- [ ] Validates before creation
