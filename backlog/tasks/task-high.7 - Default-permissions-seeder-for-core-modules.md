---
id: task-high.7
title: Default permissions seeder for core modules
status: To Do
assignee: []
created_date: '2025-08-07'
labels:
  - rbac
  - seeder
  - backend
dependencies: []
parent_task_id: task-high
---

## Description

Create seeder to populate default permissions for all core modules (blog, user, media, etc)

## Acceptance Criteria

- [ ] Seeds all core module permissions
- [ ] Idempotent (safe to run multiple times)
- [ ] Links to role templates
- [ ] Documented permission matrix
