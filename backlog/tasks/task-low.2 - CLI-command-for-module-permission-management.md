---
id: task-low.2
title: CLI command for module permission management
status: To Do
assignee: []
created_date: '2025-08-07'
labels:
  - cli
  - rbac
  - devops
dependencies:
  - task-high.4
parent_task_id: task-low
---

## Description

Create CLI commands for applying module permissions: rbac-cli module:apply, module:rollback, module:status

## Acceptance Criteria

- [ ] CLI commands work correctly
- [ ] Supports dry-run mode
- [ ] Shows progress for large operations
- [ ] Handles errors gracefully
