---
id: task-low.6
title: Admin UI for template management
status: To Do
assignee: []
created_date: '2025-08-07'
labels:
  - frontend
  - rbac
  - admin
dependencies: []
parent_task_id: task-low
---

## Description

Create admin interface for managing role templates (view, edit, apply to tenants)

## Acceptance Criteria

- [ ] UI shows all templates
- [ ] Can edit template properties
- [ ] Can apply to multiple tenants
- [ ] Shows application status
