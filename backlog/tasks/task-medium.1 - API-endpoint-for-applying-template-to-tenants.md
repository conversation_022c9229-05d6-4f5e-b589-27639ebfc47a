---
id: task-medium.1
title: API endpoint for applying template to tenants
status: To Do
assignee: []
created_date: '2025-08-07'
labels:
  - rbac
  - api
  - backend
dependencies:
  - task-high.1
parent_task_id: task-medium
---

## Description

Create REST API endpoint POST /api/admin/rbac/role-templates/{id}/apply to apply template to selected tenants

## Acceptance Criteria

- [ ] Endpoint accepts tenant_ids array
- [ ] Validates permissions
- [ ] Returns success/failure per tenant
- [ ] Proper error handling
