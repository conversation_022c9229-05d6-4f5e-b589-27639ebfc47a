# Blog API v5 Project Rules

## Code Style and Conventions

### General
- Use English for all code, comments, and documentation
- Follow Go idioms and best practices
- Keep functions small and focused (single responsibility)
- Prefer composition over inheritance

### Naming
- Use meaningful, descriptive names
- Avoid abbreviations unless widely understood
- Repository methods: `GetByID`, `Create`, `Update`, `Delete`
- Service methods: Business-focused names
- DTOs: `{Entity}{Action}Request/Response`

### Error Handling
- Always return errors, don't panic
- Wrap errors with context: `fmt.<PERSON><PERSON><PERSON>("failed to X: %w", err)`
- Use custom error types for business logic errors
- Log errors at the handler level, not in services/repositories

## Architecture Rules

### Multi-Tenant & Multi-Website
- ALL queries must include both `tenantID` and `websiteID`
- Use `scopes.TenantWebsiteScope(ctx, tenantID, websiteID)`
- Never allow cross-tenant or cross-website data access
- Repository methods must have `tenantID, websiteID` as first parameters after `ctx`

### Database
- MySQL 8.0.16+ only, no PostgreSQL
- Use `INT UNSIGNED` for IDs, not BIGINT
- Status-based soft deletes (e.g., `status = 'deleted'`)
- One table per migration file
- Edit original migration files for schema changes, don't create ALTER migrations

### API Design
- All APIs follow standard response format:
```json
{
  "status": { "code": 200, "message": "Success", ... },
  "data": { ... },
  "meta": { ... }
}
```
- Use cursor-based pagination, NOT offset-based
- All endpoints must have clear request/response DTOs
- Field validation using struct tags

### Module Structure
```
internal/modules/{module}/
├── models/      # Domain models
├── repositories/# Data access layer  
├── services/    # Business logic
├── handlers/    # HTTP handlers
└── routes.go    # Route registration
```

## Field Naming Standards

### Text Blocks
- Backend expects `content` field for Text blocks
- Frontend should send `content`, not `text`
- Validation requires `content` field to be present

### Common Fields
- `tenant_id`, `website_id` - Multi-tenant scope
- `created_by`, `updated_by` - Audit fields
- `created_at`, `updated_at` - Timestamps
- `status` - For soft deletes and state management

## Testing Requirements
- Write tests for critical business logic
- Mock external dependencies
- Use table-driven tests for multiple scenarios
- Test both success and error cases

## Security
- Always validate tenant/website access
- Sanitize user inputs
- Use prepared statements (GORM handles this)
- Never expose internal IDs in error messages
- JWT tokens are user-based, not tenant-based

## Performance
- Use indexes for frequent queries
- Batch operations when possible
- Implement caching for read-heavy endpoints
- Use context for request cancellation

## Git Workflow
- Never commit directly to main
- Use descriptive commit messages
- Reference issue numbers in commits
- Keep commits atomic and focused

## Documentation
- Document all public APIs with Swagger annotations
- Keep README updated with setup instructions
- Document complex business logic inline
- Update CLAUDE.md for AI assistant context