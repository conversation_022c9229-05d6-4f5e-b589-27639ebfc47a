#!/bin/bash

# Pre-commit hook for N8N database backup
# This hook runs before each commit to backup the N8N database

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
log_info() {
    echo -e "${GREEN}[PRE-COMMIT]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[PRE-COMMIT]${NC} $1"
}

log_error() {
    echo -e "${RED}[PRE-COMMIT]${NC} $1"
}

# Get the repository root directory
REPO_ROOT=$(git rev-parse --show-toplevel)
BACKUP_SCRIPT="${REPO_ROOT}/scripts/backup-n8n.sh"
CLEAN_BIN_SCRIPT="${REPO_ROOT}/scripts/clean-bin.sh"

log_info "Running pre-commit checks..."

# 1. Clean binary files first
log_info "Cleaning binary files..."
if [ -f "${CLEAN_BIN_SCRIPT}" ]; then
    if [ ! -x "${CLEAN_BIN_SCRIPT}" ]; then
        chmod +x "${CLEAN_BIN_SCRIPT}"
    fi
    "${CLEAN_BIN_SCRIPT}"
else
    log_warn "Binary cleanup script not found at: ${CLEAN_BIN_SCRIPT}"
    log_warn "Skipping binary file cleanup"
fi

# 2. Check for any remaining binary files in bin directory that might be accidentally staged
BIN_DIR="${REPO_ROOT}/bin"
if [ -d "${BIN_DIR}" ]; then
    STAGED_BINS=$(git diff --cached --name-only | grep "^bin/" | grep -v ".gitkeep" || true)
    if [ ! -z "${STAGED_BINS}" ]; then
        log_error "Binary files are staged for commit:"
        echo "${STAGED_BINS}"
        log_error "Please remove these files from staging area:"
        log_error "  git reset HEAD bin/"
        exit 1
    fi
fi

# 3. Run N8N database backup
log_info "Running N8N database backup..."

# Check if backup script exists
if [ ! -f "${BACKUP_SCRIPT}" ]; then
    log_warn "N8N backup script not found at: ${BACKUP_SCRIPT}"
    log_warn "Skipping N8N database backup"
    exit 0
fi

# Check if backup script is executable
if [ ! -x "${BACKUP_SCRIPT}" ]; then
    log_warn "N8N backup script is not executable: ${BACKUP_SCRIPT}"
    log_warn "Making script executable..."
    chmod +x "${BACKUP_SCRIPT}"
fi

# Run the backup script
if "${BACKUP_SCRIPT}"; then
    log_info "N8N database backup completed successfully"
else
    # Don't fail the commit if backup fails - just warn
    log_warn "N8N database backup failed, but continuing with commit"
    log_warn "You may want to check the N8N container status"
fi

# Check if any backup files were added and stage them
BACKUP_DIR="${REPO_ROOT}/backups/n8n"
if [ -d "${BACKUP_DIR}" ]; then
    # Stage any new backup files that were created
    git add "${BACKUP_DIR}/"*.sqlite 2>/dev/null || true
    
    # Check if we have staged backup files
    if git diff --cached --name-only | grep -q "backups/n8n/"; then
        log_info "New N8N backup files have been staged for commit"
    fi
fi

log_info "Pre-commit hook completed"
exit 0