package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: Could not load .env file: %v", err)
	}

	// Get database connection string from environment
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		log.Fatal("DATABASE_URL environment variable is required")
	}

	// Connect to database
	db, err := sql.Open("mysql", databaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	fmt.Println("Connected to database successfully")

	// Get all tables
	rows, err := db.Query("SHOW TABLES")
	if err != nil {
		log.Fatalf("Failed to get tables: %v", err)
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var table string
		if err := rows.Scan(&table); err != nil {
			log.Printf("Error scanning table name: %v", err)
			continue
		}
		tables = append(tables, table)
	}

	if len(tables) == 0 {
		fmt.Println("No tables found in database")
		return
	}

	// Disable foreign key checks
	if _, err := db.Exec("SET FOREIGN_KEY_CHECKS = 0"); err != nil {
		log.Fatalf("Failed to disable foreign key checks: %v", err)
	}

	// Drop all tables
	for _, table := range tables {
		fmt.Printf("Dropping table: %s\n", table)
		if _, err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS `%s`", table)); err != nil {
			log.Printf("Failed to drop table %s: %v", table, err)
		}
	}

	// Re-enable foreign key checks
	if _, err := db.Exec("SET FOREIGN_KEY_CHECKS = 1"); err != nil {
		log.Fatalf("Failed to re-enable foreign key checks: %v", err)
	}

	fmt.Printf("Successfully dropped %d tables\n", len(tables))
}
