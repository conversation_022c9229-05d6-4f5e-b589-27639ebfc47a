# Metrics and Monitoring Configuration
# This configuration file defines the settings for the custom metrics and monitoring system

# Metrics Collector Configuration
collector:
  # Collection interval for metrics
  collection_interval: 60s
  
  # Maximum number of metrics to collect in a single batch
  batch_size: 100
  
  # How long to retain metrics data
  retention_period: 24h
  
  # Enable automatic export of metrics
  enable_auto_export: true
  
  # Prefix for all metrics
  metrics_prefix: "wn_api_v3"
  
  # Storage configuration
  storage:
    # Type of storage: memory, redis, database
    type: "memory"
    
    # Redis configuration (if type is redis)
    redis:
      host: "localhost"
      port: 6379
      password: ""
      db: 0
      
    # Database configuration (if type is database)
    database:
      driver: "mysql"
      host: "localhost"
      port: 3306
      username: "root"
      password: ""
      database: "wn_api_v3_metrics"

# Business Metrics Configuration
business_metrics:
  # Track user activity metrics
  track_user_activity: true
  
  # Track revenue and financial metrics
  track_revenue: true
  
  # Track user engagement metrics
  track_engagement: true
  
  # Aggregation interval for business metrics
  aggregation_interval: 5m
  
  # Enable trending analysis
  enable_trending: true
  
  # KPI definitions
  kpis:
    - name: "user_registrations"
      description: "Total number of user registrations"
      type: "counter"
      labels: ["tenant_id", "registration_method"]
      
    - name: "tenant_creations"
      description: "Total number of tenant creations"
      type: "counter"
      labels: ["plan_type", "domain"]
      
    - name: "website_creations"
      description: "Total number of website creations"
      type: "counter"
      labels: ["tenant_id", "template_type"]
      
    - name: "api_requests"
      description: "Total API requests"
      type: "counter"
      labels: ["endpoint", "method", "status_code", "tenant_id"]
      
    - name: "revenue_generated"
      description: "Total revenue generated"
      type: "counter"
      labels: ["tenant_id", "plan"]
      
    - name: "active_users"
      description: "Number of active users"
      type: "gauge"
      labels: ["tenant_id"]

# Performance Metrics Configuration
performance_metrics:
  # Enable system metrics collection
  enable_system_metrics: true
  
  # Enable database metrics collection
  enable_database_metrics: true
  
  # Enable detailed performance metrics
  enable_detailed_metrics: true
  
  # Collection interval for performance metrics
  collection_interval: 30s
  
  # Threshold for slow queries (in milliseconds)
  slow_query_threshold: 1000
  
  # Memory usage alert threshold (percentage)
  memory_alert_threshold: 80
  
  # CPU usage alert threshold (percentage)
  cpu_alert_threshold: 80
  
  # System metrics to collect
  system_metrics:
    - name: "cpu_usage"
      description: "CPU usage percentage"
      type: "gauge"
      
    - name: "memory_usage"
      description: "Memory usage percentage"
      type: "gauge"
      
    - name: "goroutines"
      description: "Number of goroutines"
      type: "gauge"
      
    - name: "gc_cycles"
      description: "Number of GC cycles"
      type: "counter"
      
    - name: "heap_size"
      description: "Heap size in bytes"
      type: "gauge"
  
  # Database metrics to collect
  database_metrics:
    - name: "connections_opened"
      description: "Number of opened database connections"
      type: "counter"
      
    - name: "connections_closed"
      description: "Number of closed database connections"
      type: "counter"
      
    - name: "queries_executed"
      description: "Number of executed database queries"
      type: "counter"
      
    - name: "query_duration"
      description: "Database query duration"
      type: "histogram"
      
    - name: "slow_queries"
      description: "Number of slow database queries"
      type: "counter"

# Alert Manager Configuration
alert_manager:
  # Evaluation interval for alert rules
  evaluation_interval: 30s
  
  # Maximum number of alerts to keep in history
  max_alert_history: 1000
  
  # Default severity for alerts
  default_severity: "warning"
  
  # Enable alert notifications
  enable_notifications: true
  
  # How long to retain resolved alerts
  alert_retention: 168h # 7 days
  
  # Default alert rules
  default_rules:
    - id: "high_memory_usage"
      name: "High Memory Usage"
      description: "Alert when memory usage exceeds 80%"
      metric_name: "system_memory_usage_percent"
      condition: "gt"
      threshold: 80.0
      duration: 2m
      severity: "warning"
      labels:
        component: "system"
      enabled: true
      
    - id: "high_cpu_usage"
      name: "High CPU Usage"
      description: "Alert when CPU usage exceeds 80%"
      metric_name: "system_cpu_usage_percent"
      condition: "gt"
      threshold: 80.0
      duration: 2m
      severity: "warning"
      labels:
        component: "system"
      enabled: true
      
    - id: "high_error_rate"
      name: "High Error Rate"
      description: "Alert when error rate exceeds 5%"
      metric_name: "http_error_rate_percent"
      condition: "gt"
      threshold: 5.0
      duration: 1m
      severity: "error"
      labels:
        component: "http"
      enabled: true
      
    - id: "slow_response_time"
      name: "Slow Response Time"
      description: "Alert when average response time exceeds 1 second"
      metric_name: "http_request_duration_ms"
      condition: "gt"
      threshold: 1000.0
      duration: 5m
      severity: "warning"
      labels:
        component: "http"
      enabled: true
      
    - id: "database_connection_failure"
      name: "Database Connection Failure"
      description: "Alert when database connection fails"
      metric_name: "database_connection_failures_total"
      condition: "gt"
      threshold: 0.0
      duration: 30s
      severity: "critical"
      labels:
        component: "database"
      enabled: true
  
  # Notification handlers
  notification_handlers:
    - type: "email"
      enabled: true
      config:
        smtp:
          host: "smtp.gmail.com"
          port: 587
          username: "<EMAIL>"
          password: "your-app-password"
          from: "<EMAIL>"
          to: ["<EMAIL>"]
          
    - type: "webhook"
      enabled: true
      config:
        url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
        headers:
          Content-Type: "application/json"
        
    - type: "discord"
      enabled: false
      config:
        webhook_url: "https://discord.com/api/webhooks/YOUR/WEBHOOK"

# Dashboard Configuration
dashboard:
  # Refresh interval for dashboard data
  refresh_interval: 30s
  
  # How long to retain dashboard data
  data_retention: 24h
  
  # Maximum number of data points per panel
  max_data_points: 1000
  
  # Enable real-time data updates
  enable_real_time: true
  
  # Enable dashboard export/import
  enable_export: true
  
  # Default time range for new dashboards
  default_time_range: 1h
  
  # Default dashboards to create
  default_dashboards:
    - id: "system-overview"
      name: "System Overview"
      description: "Overview of system performance and health"
      auto_create: true
      
    - id: "business-metrics"
      name: "Business Metrics"
      description: "Key business metrics and KPIs"
      auto_create: true
      
    - id: "api-metrics"
      name: "API Metrics"
      description: "API performance and usage metrics"
      auto_create: true
      
    - id: "database-metrics"
      name: "Database Metrics"
      description: "Database performance and health metrics"
      auto_create: true

# Export Configuration
export:
  # Enable metrics export to external systems
  enabled: true
  
  # Export interval
  interval: 60s
  
  # Export targets
  targets:
    # Prometheus export
    prometheus:
      enabled: false
      endpoint: "/metrics"
      port: 9090
      
    # Jaeger export (when integrated)
    jaeger:
      enabled: false
      endpoint: "http://localhost:14268/api/traces"
      
    # Custom webhook export
    webhook:
      enabled: false
      url: "https://your-metrics-endpoint.com/metrics"
      headers:
        Authorization: "Bearer your-token"
        Content-Type: "application/json"
      
    # File export
    file:
      enabled: true
      path: "/var/log/wn-api-v3/metrics"
      format: "json" # json, csv, prometheus
      rotation: "daily"

# SLA Configuration
sla:
  # Enable SLA monitoring
  enabled: true
  
  # SLA definitions
  definitions:
    - name: "api_availability"
      description: "API availability SLA"
      target: 99.9
      measurement: "percentage"
      timeframe: "monthly"
      
    - name: "response_time"
      description: "API response time SLA"
      target: 500
      measurement: "milliseconds"
      timeframe: "monthly"
      
    - name: "error_rate"
      description: "API error rate SLA"
      target: 1.0
      measurement: "percentage"
      timeframe: "monthly"
  
  # SLA reporting
  reporting:
    # Generate SLA reports
    enabled: true
    
    # Report generation interval
    interval: "daily"
    
    # Report format
    format: "json"
    
    # Report destination
    destination: "/var/log/wn-api-v3/sla-reports"

# Logging Configuration
logging:
  # Log level for metrics system
  level: "info"
  
  # Log format
  format: "json"
  
  # Log destination
  destination: "/var/log/wn-api-v3/metrics.log"
  
  # Enable structured logging
  structured: true
  
  # Log rotation
  rotation:
    enabled: true
    max_size: "100MB"
    max_age: "30d"
    max_backups: 10