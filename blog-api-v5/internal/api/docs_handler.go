package api

import (
	"net/http"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

// DocsHandler handles documentation serving
type DocsHandler struct {
	docsPath string
}

// NewDocsHandler creates a new documentation handler
func NewDocsHandler(docsPath string) *DocsHandler {
	return &DocsHandler{
		docsPath: docsPath,
	}
}

// ServeSocketIODocs serves Socket.IO documentation
func (h *DocsHandler) ServeSocketIODocs(c *gin.Context) {
	// Get the requested path
	path := c.Param("filepath")
	
	// Handle special /api path to serve JSON index
	if path == "/api" {
		h.GetSocketIODocsIndex(c)
		return
	}
	
	if path == "" || path == "/" {
		path = "/README.md"
	}

	// Ensure path starts with /
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	// Build full file path
	fullPath := filepath.Join(h.docsPath, "socket-io", path)

	// Security check - prevent directory traversal
	if strings.Contains(path, "..") {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"error": "Invalid path",
		})
		return
	}

	// Serve the file
	c.File(fullPath)
}

// GetSocketIODocsIndex returns the documentation index
func (h *DocsHandler) GetSocketIODocsIndex(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"title":       "Socket.IO WebSocket API Documentation",
		"description": "Real-time WebSocket API using Socket.IO for notifications and live updates",
		"version":     "1.0.0",
		"server_url":  "ws://localhost:9077/socket.io/",
		"namespace":   "/notifications",
		"sections": gin.H{
			"overview": gin.H{
				"title": "Overview",
				"path":  "/docs/socket-io/README.md",
				"description": "Getting started with Socket.IO API",
			},
			"api": gin.H{
				"title": "API Reference",
				"items": gin.H{
					"events": gin.H{
						"title": "Events Reference",
						"path":  "/docs/socket-io/api/events.md",
						"description": "Complete events documentation",
					},
					"authentication": gin.H{
						"title": "Authentication",
						"path":  "/docs/socket-io/api/authentication.md",
						"description": "JWT authentication for WebSocket",
					},
					"namespaces": gin.H{
						"title": "Namespaces",
						"path":  "/docs/socket-io/api/namespaces.md",
						"description": "Available namespaces",
					},
					"rooms": gin.H{
						"title": "Rooms",
						"path":  "/docs/socket-io/api/rooms.md",
						"description": "Room management system",
					},
				},
			},
			"examples": gin.H{
				"title": "Examples",
				"items": gin.H{
					"javascript": gin.H{
						"title": "JavaScript Client",
						"path":  "/docs/socket-io/examples/javascript.md",
						"description": "JavaScript client implementations",
					},
					"postman": gin.H{
						"title": "Postman Testing",
						"path":  "/docs/socket-io/examples/postman.md",
						"description": "Test WebSocket with Postman",
					},
					"curl": gin.H{
						"title": "cURL Examples",
						"path":  "/docs/socket-io/examples/curl.md",
						"description": "cURL examples for handshake",
					},
				},
			},
			"guides": gin.H{
				"title": "Guides",
				"items": gin.H{
					"quick_start": gin.H{
						"title": "Quick Start",
						"path":  "/docs/socket-io/guides/quick-start.md",
						"description": "Get started in 5 minutes",
					},
					"integration": gin.H{
						"title": "Integration Guide",
						"path":  "/docs/socket-io/guides/integration.md",
						"description": "Integration with REST API",
					},
					"troubleshooting": gin.H{
						"title": "Troubleshooting",
						"path":  "/docs/socket-io/guides/troubleshooting.md",
						"description": "Common issues and solutions",
					},
				},
			},
		},
		"quick_links": []gin.H{
			{
				"title": "Quick Start Guide",
				"path":  "/docs/socket-io/guides/quick-start.md",
				"description": "Get connected to Socket.IO in 5 minutes",
			},
			{
				"title": "Events Reference",
				"path":  "/docs/socket-io/api/events.md",
				"description": "Complete client/server events documentation",
			},
			{
				"title": "JavaScript Examples",
				"path":  "/docs/socket-io/examples/javascript.md",
				"description": "Production-ready client implementations",
			},
			{
				"title": "Authentication",
				"path":  "/docs/socket-io/api/authentication.md",
				"description": "JWT authentication setup",
			},
		},
		"connection_info": gin.H{
			"url":       "ws://localhost:9077/socket.io/",
			"transport": "websocket, polling",
			"protocol":  "Engine.IO v4 + Socket.IO v2",
			"main_namespace": "/notifications",
			"authentication": "JWT Bearer token required",
		},
		"swagger_docs": "/swagger/index.html",
	})
}

// ServeSocketIODocsUI serves a simple HTML interface for Socket.IO docs
func (h *DocsHandler) ServeSocketIODocsUI(c *gin.Context) {
	html := `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .connection-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .card p {
            margin: 0;
            color: #666;
        }
        .sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        .section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .section-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .section-item:last-child {
            border-bottom: none;
        }
        .section-item h4 {
            margin: 0 0 5px 0;
            color: #34495e;
        }
        .section-item p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }
        .badge {
            background: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 8px;
            vertical-align: top;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
        }
        a {
            color: #3498db;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Socket.IO WebSocket API</h1>
        <p>Real-time notifications and live updates for Blog API v3</p>
    </div>

    <div class="connection-info">
        <h3>📡 Connection Information</h3>
        <p><strong>Server URL:</strong> <code>ws://localhost:9077/socket.io/</code></p>
        <p><strong>Main Namespace:</strong> <code>/notifications</code></p>
        <p><strong>Transport:</strong> WebSocket with HTTP long-polling fallback</p>
        <p><strong>Authentication:</strong> JWT Bearer token required</p>
        <p><strong>Protocol:</strong> Engine.IO v4 + Socket.IO v2</p>
    </div>

    <div class="quick-links">
        <div class="card">
            <h3>⚡ Quick Start</h3>
            <p>Get connected to Socket.IO in 5 minutes with our step-by-step guide.</p>
            <a href="/docs/socket-io/guides/quick-start.md">Start Now →</a>
        </div>
        
        <div class="card">
            <h3>📋 Events Reference</h3>
            <p>Complete documentation of all client/server events and payloads.</p>
            <a href="/docs/socket-io/api/events.md">View Events →</a>
        </div>
        
        <div class="card">
            <h3>🔐 Authentication</h3>
            <p>JWT authentication setup and token management for WebSocket connections.</p>
            <a href="/docs/socket-io/api/authentication.md">Learn More →</a>
        </div>
        
        <div class="card">
            <h3>💻 JavaScript Examples</h3>
            <p>Production-ready client implementations with React, Vue, and Angular.</p>
            <a href="/docs/socket-io/examples/javascript.md">View Examples →</a>
        </div>
    </div>

    <div class="sections">
        <div class="section">
            <h2>📚 API Reference</h2>
            <div class="section-item">
                <h4><a href="/docs/socket-io/api/events.md">Events Reference</a></h4>
                <p>Complete client/server events documentation</p>
            </div>
            <div class="section-item">
                <h4><a href="/docs/socket-io/api/authentication.md">Authentication</a></h4>
                <p>JWT authentication for WebSocket connections</p>
            </div>
            <div class="section-item">
                <h4><a href="/docs/socket-io/api/namespaces.md">Namespaces</a> <span class="badge">Coming Soon</span></h4>
                <p>Available namespaces and their purposes</p>
            </div>
            <div class="section-item">
                <h4><a href="/docs/socket-io/api/rooms.md">Rooms</a> <span class="badge">Coming Soon</span></h4>
                <p>Room management and subscription system</p>
            </div>
        </div>

        <div class="section">
            <h2>💡 Examples</h2>
            <div class="section-item">
                <h4><a href="/docs/socket-io/examples/javascript.md">JavaScript Client</a></h4>
                <p>React, Vue, Angular, and vanilla JavaScript examples</p>
            </div>
            <div class="section-item">
                <h4><a href="/docs/socket-io/examples/postman.md">Postman Testing</a> <span class="badge">Coming Soon</span></h4>
                <p>Test your WebSocket connections with Postman</p>
            </div>
            <div class="section-item">
                <h4><a href="/docs/socket-io/examples/curl.md">cURL Examples</a> <span class="badge">Coming Soon</span></h4>
                <p>Command-line testing with cURL</p>
            </div>
        </div>

        <div class="section">
            <h2>📖 Guides</h2>
            <div class="section-item">
                <h4><a href="/docs/socket-io/guides/quick-start.md">Quick Start</a></h4>
                <p>Get started with Socket.IO in 5 minutes</p>
            </div>
            <div class="section-item">
                <h4><a href="/docs/socket-io/guides/integration.md">Integration Guide</a> <span class="badge">Coming Soon</span></h4>
                <p>Integrate WebSocket with your REST API workflow</p>
            </div>
            <div class="section-item">
                <h4><a href="/docs/socket-io/guides/troubleshooting.md">Troubleshooting</a> <span class="badge">Coming Soon</span></h4>
                <p>Common issues and solutions</p>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>📄 For REST API documentation, visit: <a href="/swagger/index.html">Swagger UI</a></p>
        <p>🔗 Socket.IO docs are part of Blog API v3 documentation system</p>
    </div>

    <script>
        // Add smooth scrolling and simple interactions
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>`

	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// RegisterDocsRoutes registers documentation routes
func (s *Server) RegisterDocsRoutes() {
	docsHandler := NewDocsHandler("./docs")

	// Socket.IO documentation routes
	// Main Socket.IO docs UI
	s.engine.GET("/docs/socket-io", docsHandler.ServeSocketIODocsUI)
	
	// Serve Socket.IO documentation files - wildcard handles all paths including /api
	s.engine.GET("/docs/socket-io/*filepath", docsHandler.ServeSocketIODocs)
}