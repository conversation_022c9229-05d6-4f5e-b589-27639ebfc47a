package api

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"

	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/frontend"
	authmysql "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	authservices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog"
	blogMysql "github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories/mysql"
	blogServices "github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification"
	notificationservices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello"

	// "github.com/tranthanhloi/wn-api-v3/internal/modules/trello"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user"
	usermysql "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/logging"
	"github.com/tranthanhloi/wn-api-v3/pkg/storage"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// Server represents the API server with multi-module support
type Server struct {
	config       *config.Config
	db           *sql.DB
	gormDB       *gorm.DB
	rawValidator validator.Validator
	validator    *validator.RequestValidator
	logger       utils.Logger
	engine       *gin.Engine
	httpServer   *http.Server
	socketIOServer *notificationservices.SocketIOServer
}

// NewServer creates a new API server instance with multi-module support
func NewServer(cfg *config.Config, db *sql.DB, v validator.Validator, logger utils.Logger) (*Server, error) {
	var gormDB *gorm.DB
	var err error

	// Setup GORM from SQL DB only if db is not nil
	if db != nil {
		// Configure logger level based on debug setting
		logLevel := gormLogger.Silent
		if cfg.Database.DebugSQL {
			logLevel = gormLogger.Info
		}

		// Use beautiful custom logger for better SQL formatting
		var gormLoggerInstance gormLogger.Interface
		if cfg.Database.DebugSQL {
			gormLoggerInstance = logging.NewBeautifulGormLogger(gormLogger.Config{
				LogLevel: logLevel,
			})
		} else {
			gormLoggerInstance = gormLogger.Default.LogMode(logLevel)
		}

		gormDB, err = gorm.Open(mysql.New(mysql.Config{
			Conn: db,
		}), &gorm.Config{
			Logger: gormLoggerInstance,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to setup GORM: %w", err)
		}
	} else {
		logger.Warn("Running without database connection (mock mode)")
	}

	server := &Server{
		config:       cfg,
		db:           db,
		gormDB:       gormDB,
		rawValidator: v,
		validator:    validator.NewRequestValidator(v),
		logger:       logger,
	}

	// Setup Gin engine
	server.setupEngine()

	return server, nil
}

// setupEngine configures the Gin engine with multi-module routes and middleware
func (s *Server) setupEngine() {
	// Set Gin mode to debug (since we don't have Environment field)
	gin.SetMode(gin.DebugMode)

	s.engine = gin.New()

	// Global middleware
	s.engine.Use(gin.Recovery())
	s.engine.Use(middleware.GinLoggingMiddleware(s.logger))
	s.engine.Use(middleware.GinRequestIDMiddleware())
	s.engine.Use(middleware.GinCORSMiddleware())
	s.engine.Use(middleware.GinWebsiteMiddleware())
	
	// Error handling middleware
	s.engine.Use(middleware.ErrorResponseMiddleware(middleware.DefaultErrorHandlerConfig()))

	if s.config.Features.EnableRateLimit {
		s.engine.Use(middleware.GinRateLimitMiddleware())
	}

	// Health check endpoints
	if s.config.Features.EnableHealth {
		s.setupHealthRoutes()
	}

	// Swagger documentation routes
	s.engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Socket.IO documentation routes
	s.RegisterDocsRoutes()

	// Redirect root to swagger
	s.engine.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
	})

	// API v1 routes
	v1 := s.engine.Group("/api/cms/v1")

	// Admin API v1 routes
	adminV1 := s.engine.Group("/api/admin/v1")

	// Register module routes with GORM database connection
	if s.gormDB != nil {
		// Initialize RBAC module first as other modules depend on it
		rbacModule, err := rbac.NewRBACModule(s.gormDB, s.logger)
		if err != nil {
			s.logger.WithError(err).Error("Failed to initialize RBAC module")
			return
		}

		// Register RBAC routes
		if err := rbacModule.RegisterRoutes(v1, s.gormDB, s.rawValidator, s.logger); err != nil {
			s.logger.WithError(err).Error("Failed to register RBAC routes")
			return
		}

		// Register tenant module routes
		tenant.RegisterRoutes(v1, s.gormDB, s.rawValidator)

		// Register admin tenant routes
		tenant.RegisterAdminRoutes(adminV1, s.gormDB, s.rawValidator)

		// Register auth module routes
		auth.RegisterRoutes(v1, s.gormDB, s.rawValidator, s.logger)

		// Register user module routes
		user.RegisterRoutes(v1, s.gormDB, s.rawValidator, s.logger)

		// Register onboarding module routes
		onboarding.RegisterRoutes(v1, s.gormDB, s.rawValidator, s.logger)

		// Initialize onboarding event handlers
		onboarding.InitializeOnboardingEventHandlers(s.gormDB, s.logger)

		// Initialize RBAC event handlers
		rbac.RegisterEventHandlers(s.gormDB, s.logger)

		// Register SEO module routes
		seoModule := seo.NewSEOModule(s.gormDB, s.rawValidator, s.logger)
		seoModule.RegisterRoutes(v1)

		// Register blog module routes with SEO integration
		blog.RegisterRoutesWithSEO(v1, s.gormDB, s.rawValidator, s.logger, seoModule.GetMetaService())

		// Register ads module routes
		ads.RegisterRoutes(v1, s.gormDB)

		// Initialize Socket.IO server if notifications are enabled
		if s.config.Features.EnableNotifications && s.config.Notification.SocketProvider == "socketio" {
			s.setupSocketIOServer()
		}

		// Register notification module routes
		notification.RegisterRoutes(v1, s.gormDB, s.config, s.logger)

		// Register trello module routes
		trello.RegisterRoutes(v1, s.gormDB, s.rawValidator, s.logger)

		// Register website module routes
		website.RegisterRoutes(v1, s.gormDB, s.rawValidator, s.logger)

		// Register public website routes (API key protected)
		website.RegisterPublicWebsiteRoutes(s.engine, s.gormDB, s.logger)

		// Register frontend module routes (public API for website)
		// Frontend routes should be under /api/frontend/v1, not /api/cms/v1/frontend/v1
		frontend.RegisterFrontendRoutes(s.engine.Group("/api"), s.gormDB, s.logger)

		// Register media module routes
		s.registerMediaModule(v1)

		// Register API Key module routes
		apikey.RegisterRoutes(s.engine, s.gormDB)
		// Register CMS API Key management routes
		apikey.RegisterCMSRoutes(v1, s.gormDB)
	} else {
		// For demo purposes, register routes even without DB to show them
		s.logger.Info("Registering routes in demo mode (no DB)")
		// We need a mock GORM DB for demo
		// For now, let's just log that routes would be registered
		s.logger.Warn("Routes not registered - no database connection")
	}

	// API testing info endpoint (Bruno)
	s.engine.GET("/api/info", s.handleAPIInfo)

	s.httpServer = &http.Server{
		Handler:      s.engine,
		ReadTimeout:  time.Duration(s.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(s.config.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(s.config.Server.IdleTimeout) * time.Second,
	}
}

// setupHealthRoutes configures health check routes
func (s *Server) setupHealthRoutes() {
	health := s.engine.Group("/health")

	health.GET("/live", s.handleLiveness)
	health.GET("/ready", s.handleReadiness)
	health.GET("/status", s.handleStatus)
}

// setupSocketIOServer initializes and integrates Socket.IO server with Gin
func (s *Server) setupSocketIOServer() {
	// Extract port from Socket.IO URL configuration
	port := "9080"
	if s.config.Notification.SocketIOConfig.ServerURL != "" {
		if u, err := url.Parse(s.config.Notification.SocketIOConfig.ServerURL); err == nil && u.Port() != "" {
			port = u.Port()
		}
	}

	// Create Socket.IO server instance
	s.socketIOServer = notificationservices.NewSocketIOServer(
		port,
		s.config.Notification.SocketIOConfig.Namespace,
		s.logger,
	)

	// Attach Socket.IO server to Gin router
	s.socketIOServer.AttachToGin(s.engine)

	s.logger.WithFields(map[string]interface{}{
		"port":      port,
		"namespace": s.config.Notification.SocketIOConfig.Namespace,
	}).Info("Socket.IO server initialized and attached to Gin router")
}

// Start starts the HTTP server
func (s *Server) Start(addr string) error {
	s.httpServer.Addr = addr
	s.logger.WithField("addr", addr).Info("Starting HTTP server with multi-module support")
	return s.httpServer.ListenAndServe()
}

// Shutdown gracefully shuts down the server
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("Shutting down HTTP server")
	return s.httpServer.Shutdown(ctx)
}

// Health check handlers

func (s *Server) handleLiveness(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"message": "Server is alive",
	})
}

func (s *Server) handleReadiness(c *gin.Context) {
	// Check if all services are ready
	ready := true
	checks := make(map[string]bool)

	// Check database connection
	if s.db != nil {
		if err := s.db.Ping(); err != nil {
			ready = false
			checks["database"] = false
		} else {
			checks["database"] = true
		}
	} else {
		checks["database"] = false
		ready = false
	}

	// Check tenant module availability
	checks["tenant_module"] = s.db != nil

	// Check auth module availability
	checks["auth_module"] = s.db != nil

	// Check user module availability
	checks["user_module"] = s.db != nil

	for _, check := range checks {
		if !check {
			ready = false
			break
		}
	}

	if ready {
		c.JSON(http.StatusOK, gin.H{
			"status": "ready",
			"checks": checks,
		})
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "not ready",
			"checks": checks,
		})
	}
}

func (s *Server) handleStatus(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"service":     "blog-api-v3",
		"version":     "1.0.0",
		"timestamp":   time.Now().UTC().Format(time.RFC3339),
		"environment": "development",
		"modules":     []string{"tenant", "auth", "user", "blog", "onboarding", "ads", "website", "media"},
		"features": gin.H{
			"registration":  s.config.Features.EnableRegistration,
			"comments":      s.config.Features.EnableComments,
			"notifications": s.config.Features.EnableNotifications,
			"analytics":     s.config.Features.EnableAnalytics,
			"cache":         s.config.Features.EnableCache,
			"rate_limit":    s.config.Features.EnableRateLimit,
			"bruno":         true,
			"health":        s.config.Features.EnableHealth,
		},
	})
}

// handleAPIInfo provides API information for Bruno testing
func (s *Server) handleAPIInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"name":        "Blog API v3 - Multi-Module",
		"version":     "1.0.0",
		"description": "Multi-module API with tenant, auth, and user management",
		"base_url":    fmt.Sprintf("http://localhost:%d", s.config.Server.Port),
		"testing":     "Bruno API Testing",
		"docs_url":    "/docs/modules/api-endpoints.md",
		"endpoints": gin.H{
			"tenants":    "/api/cms/v1/tenants",
			"auth":       "/api/cms/v1/auth",
			"users":      "/api/cms/v1/users",
			"blog":       "/api/cms/v1/blog",
			"onboarding": "/api/cms/v1/onboarding",
			"ads":        "/api/cms/v1/ads",
			"templates":  "/api/cms/v1/websites/templates",
			"categories": "/api/cms/v1/websites/template-categories",
			"media":      "/api/cms/v1/media",
		},
	})
}

// ProcessPendingBlogSchedules processes pending blog post schedules
func (s *Server) ProcessPendingBlogSchedules(ctx context.Context) error {
	// Since we don't have a modules map, we need to create the schedule service directly
	if s.gormDB == nil {
		return fmt.Errorf("database not initialized")
	}

	// Create repositories
	repos := blogMysql.NewRepositories(s.gormDB, s.logger)

	// Create schedule service
	scheduleService := blogServices.NewBlogPostScheduleService(repos.Schedule, repos.Post)

	// Process pending schedules
	return scheduleService.ProcessPendingSchedules(ctx)
}

// createJWTService creates a JWT service instance for use across modules
func (s *Server) createJWTService() (authservices.JWTService, error) {
	// Load config
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// Create auth repositories
	tokenBlacklistRepo := authmysql.NewTokenBlacklistRepository(s.gormDB)
	tenantMembershipRepo := usermysql.NewTenantMembershipRepository(s.gormDB, s.logger)

	// Create token blacklist service
	tokenBlacklistService := authservices.NewTokenBlacklistService(tokenBlacklistRepo, nil)

	// Create and return JWT service
	jwtService, err := authservices.NewJWTService(cfg, tokenBlacklistService, tenantMembershipRepo)
	if err != nil {
		return nil, fmt.Errorf("failed to create JWT service: %w", err)
	}

	return jwtService, nil
}

// registerMediaModule initializes and registers the media module with storage providers
func (s *Server) registerMediaModule(v1 *gin.RouterGroup) {
	// Setup storage providers
	storageProviders := make(map[string]storage.Storage)

	// Local storage (always available)
	localStorage, err := storage.NewLocalStorage(&storage.Config{
		Provider: storage.StorageTypeLocal,
		BasePath: "uploads", // Default uploads folder
	})
	if err != nil {
		s.logger.WithError(err).Error("Failed to initialize local storage")
		return
	}
	storageProviders["local"] = localStorage

	// Initialize media module
	mediaModule := media.NewModule(
		s.gormDB,
		s.logger,
		s.rawValidator,
	)

	// Register routes
	mediaModule.RegisterRoutes(v1)

	s.logger.Info("Media module registered successfully")
}
