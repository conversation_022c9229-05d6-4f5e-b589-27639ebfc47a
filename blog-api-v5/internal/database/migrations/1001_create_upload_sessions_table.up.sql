-- Create upload_sessions table for tracking multi-file upload progress
CREATE TABLE IF NOT EXISTS upload_sessions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL UNIQUE,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    total_files INT NOT NULL DEFAULT 0,
    uploaded_files INT NOT NULL DEFAULT 0,
    total_size BIGINT NOT NULL DEFAULT 0,
    uploaded_size BIGINT NOT NULL DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_upload_sessions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_upload_sessions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_upload_sessions_tenant_id (tenant_id),
    INDEX idx_upload_sessions_user_id (user_id),
    INDEX idx_upload_sessions_session_id (session_id),
    INDEX idx_upload_sessions_status (status),
    INDEX idx_upload_sessions_expires_at (expires_at)
);