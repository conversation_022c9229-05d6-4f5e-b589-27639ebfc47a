-- Create upload_chunks table for tracking individual chunks
CREATE TABLE IF NOT EXISTS upload_chunks (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    upload_id VARCHAR(36) NOT NULL,
    chunk_number INT NOT NULL,
    chunk_size BIGINT NOT NULL,
    uploaded BOOLEAN DEFAULT FALSE,
    uploaded_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_upload_chunks_upload_chunk (upload_id, chunk_number),
    INDEX idx_upload_chunks_upload_id (upload_id)
);