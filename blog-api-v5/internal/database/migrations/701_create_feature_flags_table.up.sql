-- Migration: 701_create_feature_flags_table
-- Description: Create feature flags table for feature flag management system
-- Module: Feature Flags (701-799)

CREATE TABLE IF NOT EXISTS feature_flags (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Flag identification
    flag_key VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Flag configuration
    flag_type ENUM('boolean', 'string', 'number', 'json') DEFAULT 'boolean',
    default_value JSON DEFAULT (JSON_OBJECT()),
    
    -- Flag status
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
    is_permanent BOOLEAN DEFAULT FALSE,
    
    -- Environment settings
    environment ENUM('development', 'staging', 'production') DEFAULT 'development',
    
    -- Metadata
    created_by INT UNSIGNED NOT NULL,
    updated_by INT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_feature_flags_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flags_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_feature_flags_updated_by FOREIGN KEY (updated_by) REFERENCES users(id),
    
    -- Indexes
    UNIQUE KEY uk_feature_flags_tenant_key_env (tenant_id, flag_key, environment),
    INDEX idx_feature_flags_tenant_status (tenant_id, status),
    INDEX idx_feature_flags_key (flag_key),
    INDEX idx_feature_flags_environment (environment),
    INDEX idx_feature_flags_created_by (created_by),
    INDEX idx_feature_flags_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;