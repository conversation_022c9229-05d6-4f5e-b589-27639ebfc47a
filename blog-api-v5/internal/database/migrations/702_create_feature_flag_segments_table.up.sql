-- Migration: 702_create_feature_flag_segments_table
-- Description: Create feature flag segments table for user segmentation
-- Module: Feature Flags (701-799)

CREATE TABLE IF NOT EXISTS feature_flag_segments (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Segment identification
    segment_key VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Targeting rules (JSON format)
    rules JSON DEFAULT (JSON_ARRAY()),
    
    -- Segment status
    status ENUM('active', 'inactive') DEFAULT 'active',
    
    -- Metadata
    created_by INT UNSIGNED NOT NULL,
    updated_by INT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_feature_flag_segments_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flag_segments_created_by <PERSON>OR<PERSON><PERSON><PERSON> KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_feature_flag_segments_updated_by FOREIGN KEY (updated_by) REFERENCES users(id),
    
    -- Indexes
    UNIQUE KEY uk_feature_flag_segments_tenant_key (tenant_id, segment_key),
    INDEX idx_feature_flag_segments_tenant_status (tenant_id, status),
    INDEX idx_feature_flag_segments_key (segment_key),
    INDEX idx_feature_flag_segments_status (status),
    INDEX idx_feature_flag_segments_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;