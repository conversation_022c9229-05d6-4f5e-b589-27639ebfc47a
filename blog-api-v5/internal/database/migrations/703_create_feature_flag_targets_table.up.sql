-- Migration: 703_create_feature_flag_targets_table
-- Description: Create feature flag targets table for targeting rules
-- Module: Feature Flags (701-799)

CREATE TABLE IF NOT EXISTS feature_flag_targets (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    flag_id INT UNSIGNED NOT NULL,
    
    -- Target configuration
    target_type ENUM('user', 'segment', 'percentage', 'custom') NOT NULL,
    target_value JSON NOT NULL,
    
    -- Rule configuration
    rule_order INT NOT NULL DEFAULT 0,
    value JSON NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    
    -- Conditions (JSON format)
    conditions JSON DEFAULT (JSON_ARRAY()),
    
    -- Metadata
    created_by INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_feature_flag_targets_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flag_targets_flag_id FOREIGN KEY (flag_id) REFERENCES feature_flags(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flag_targets_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    
    -- Indexes
    INDEX idx_feature_flag_targets_flag (flag_id, rule_order),
    INDEX idx_feature_flag_targets_tenant (tenant_id),
    INDEX idx_feature_flag_targets_type (target_type),
    INDEX idx_feature_flag_targets_enabled (is_enabled),
    INDEX idx_feature_flag_targets_order (rule_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;