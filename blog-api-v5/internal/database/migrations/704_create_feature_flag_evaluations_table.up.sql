-- Migration: 704_create_feature_flag_evaluations_table
-- Description: Create feature flag evaluations table for analytics and tracking
-- Module: Feature Flags (701-799)

CREATE TABLE IF NOT EXISTS feature_flag_evaluations (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    flag_id INT UNSIGNED NOT NULL,
    
    -- Evaluation context
    user_id INT UNSIGNED,
    session_id VARCHAR(255),
    context JSON DEFAULT (JSON_OBJECT()),
    
    -- Evaluation result
    evaluated_value JSON NOT NULL,
    matched_target_id INT UNSIGNED,
    evaluation_reason VARCHAR(255),
    
    -- Environment and timing
    environment VARCHAR(50),
    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Request metadata
    user_agent TEXT,
    ip_address VARCHAR(45),
    
    -- Constraints
    CONSTRAINT fk_feature_flag_evaluations_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flag_evaluations_flag_id FOREIGN KEY (flag_id) REFERENCES feature_flags(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flag_evaluations_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_feature_flag_evaluations_target_id FOREIGN KEY (matched_target_id) REFERENCES feature_flag_targets(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_feature_flag_evaluations_flag_date (flag_id, evaluated_at),
    INDEX idx_feature_flag_evaluations_tenant_date (tenant_id, evaluated_at),
    INDEX idx_feature_flag_evaluations_user (user_id, evaluated_at),
    INDEX idx_feature_flag_evaluations_session (session_id),
    INDEX idx_feature_flag_evaluations_environment (environment),
    INDEX idx_feature_flag_evaluations_target (matched_target_id),
    INDEX idx_feature_flag_evaluations_reason (evaluation_reason)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add partitioning by month for better performance on large datasets
-- This can be enabled later as the table grows
-- ALTER TABLE feature_flag_evaluations 
-- PARTITION BY RANGE (TO_DAYS(evaluated_at)) (
--     PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
--     PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
--     PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
--     PARTITION pfuture VALUES LESS THAN MAXVALUE
-- );