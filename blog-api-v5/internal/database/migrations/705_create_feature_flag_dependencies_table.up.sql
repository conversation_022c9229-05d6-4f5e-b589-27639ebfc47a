-- Migration: 705_create_feature_flag_dependencies_table
-- Description: Create feature flag dependencies table for managing flag relationships
-- Module: Feature Flags (701-799)

CREATE TABLE IF NOT EXISTS feature_flag_dependencies (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Dependency relationship
    parent_flag_id INT UNSIGNED NOT NULL,
    child_flag_id INT UNSIGNED NOT NULL,
    
    -- Dependency rules
    dependency_type ENUM('requires', 'conflicts', 'prerequisite') NOT NULL,
    condition_value JSON,
    
    -- Metadata
    created_by INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_feature_flag_dependencies_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flag_dependencies_parent_flag FOREI<PERSON><PERSON> (parent_flag_id) REFERENCES feature_flags(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flag_dependencies_child_flag FOREIGN KEY (child_flag_id) REFERENCES feature_flags(id) ON DELETE CASCADE,
    CONSTRAINT fk_feature_flag_dependencies_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    
    -- Prevent circular dependencies and duplicate relationships
    CONSTRAINT chk_feature_flag_dependencies_no_self_ref CHECK (parent_flag_id != child_flag_id),
    
    -- Indexes
    UNIQUE KEY uk_feature_flag_dependencies (parent_flag_id, child_flag_id, dependency_type),
    INDEX idx_feature_flag_dependencies_parent (parent_flag_id),
    INDEX idx_feature_flag_dependencies_child (child_flag_id),
    INDEX idx_feature_flag_dependencies_tenant (tenant_id),
    INDEX idx_feature_flag_dependencies_type (dependency_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;