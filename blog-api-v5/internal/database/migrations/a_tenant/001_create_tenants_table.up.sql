-- Migration: 001_create_tenants_table
-- Description: Create the core tenants table for multi-tenancy support (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS tenants (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Basic Information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE COMMENT 'URL-friendly unique identifier for the tenant',
    domain VARCHAR(255) UNIQUE,
    
    -- Status and Configuration
    status VARCHAR(50) NOT NULL DEFAULT 'active' COMMENT 'Current status of the tenant: active, suspended, inactive, trial, or deleted',
    plan_id INT UNSIGNED,
    
    -- Contact Information
    owner_email VARCHAR(255) NOT NULL,
    billing_email VARCHAR(255),
    support_email VARCHAR(255),
    
    -- Settings (JSON in MySQL 5.7+)
    settings JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON storage for tenant-specific settings and configurations',
    metadata JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON storage for additional tenant metadata and custom fields',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Trial Information
    trial_ends_at TIMESTAMP NULL DEFAULT NULL,
    
    -- Constraints
    CONSTRAINT chk_tenants_status CHECK (status IN ('active', 'suspended', 'inactive', 'trial', 'deleted')),
    CONSTRAINT chk_tenants_name_length CHECK (CHAR_LENGTH(name) >= 3),
    CONSTRAINT chk_tenants_slug_format CHECK (slug REGEXP '^[a-z0-9-]+$'),
    
    -- Indexes
    INDEX idx_tenants_slug (slug),
    INDEX idx_tenants_domain (domain),
    INDEX idx_tenants_status (status),
    INDEX idx_tenants_owner_email (owner_email),
    INDEX idx_tenants_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Core table for multi-tenant system. Each tenant represents a separate organization or customer.';