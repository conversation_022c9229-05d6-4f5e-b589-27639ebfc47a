-- Migration: 004_create_tenant_features_table
-- Description: Create tenant features table for feature flag management (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS tenant_features (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Feature identification
    feature_key VARCHAR(255) NOT NULL,
    feature_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Feature configuration
    enabled BOOLEAN NOT NULL DEFAULT FALSE,
    configuration JSON DEFAULT (JSON_OBJECT()),
    
    -- Feature metadata
    category VARCHAR(100),
    tags JSON DEFAULT (JSON_ARRAY()),
    
    -- Rollout configuration
    rollout_percentage INTEGER DEFAULT 100 COMMENT 'Percentage of users who should see this feature (for gradual rollout)',
    rollout_groups JSON DEFAULT (JSON_ARRAY()),
    
    -- Availability
    available_from TIMESTAMP NULL DEFAULT NULL,
    available_until TIMESTAMP NULL DEFAULT NULL,
    
    -- Dependencies
    requires_features JSON DEFAULT (JSON_ARRAY()) COMMENT 'Array of feature keys that must be enabled for this feature to work',
    conflicts_with JSON DEFAULT (JSON_ARRAY()) COMMENT 'Array of feature keys that cannot be enabled simultaneously with this feature',
    
    -- Tracking
    enabled_at TIMESTAMP NULL DEFAULT NULL,
    enabled_by INT UNSIGNED,
    disabled_at TIMESTAMP NULL DEFAULT NULL,
    disabled_by INT UNSIGNED,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_tenant_features_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT uk_tenant_features_key UNIQUE KEY (tenant_id, feature_key),
    CONSTRAINT chk_rollout_percentage CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
    CONSTRAINT chk_availability_dates CHECK (
        (available_from IS NULL OR available_until IS NULL) OR 
        (available_from < available_until)
    ),
    
    -- Indexes
    INDEX idx_tenant_features_tenant_id (tenant_id),
    INDEX idx_tenant_features_key (feature_key),
    INDEX idx_tenant_features_enabled (tenant_id, enabled),
    INDEX idx_tenant_features_category (tenant_id, category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Feature flags and toggles for each tenant';