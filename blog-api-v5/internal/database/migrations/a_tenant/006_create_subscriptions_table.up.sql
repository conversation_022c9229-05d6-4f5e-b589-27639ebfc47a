-- Create tenant_subscriptions table
CREATE TABLE IF NOT EXISTS `tenant_subscriptions` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `tenant_id` INT UNSIGNED NOT NULL,
    `plan_id` INT UNSIGNED NOT NULL,
    `status` ENUM('active', 'trialing', 'past_due', 'canceled', 'expired', 'suspended') NOT NULL DEFAULT 'trialing',
    `billing_cycle` ENUM('monthly', 'yearly') NOT NULL DEFAULT 'monthly',
    `amount` DECIMAL(10,2) NOT NULL,
    `currency` CHAR(3) NOT NULL DEFAULT 'USD',
    `trial_ends_at` TIMESTAMP NULL,
    `current_period_start` TIMESTAMP NOT NULL,
    `current_period_end` TIMESTAMP NOT NULL,
    `next_billing_date` TIMESTAMP NULL,
    `canceled_at` TIMESTAMP NULL,
    `cancel_at_period_end` BOOLEAN NOT NULL DEFAULT FALSE,
    `payment_failure_count` INT NOT NULL DEFAULT 0,
    `last_payment_failure_at` TIMESTAMP NULL,
    `external_subscription_id` VARCHAR(255) NULL,
    `payment_provider_data` JSON DEFAULT (JSON_OBJECT()),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_tenant_subscriptions_tenant_id` (`tenant_id`),
    INDEX `idx_tenant_subscriptions_plan_id` (`plan_id`),
    INDEX `idx_tenant_subscriptions_status` (`status`),
    INDEX `idx_tenant_subscriptions_trial_ends_at` (`trial_ends_at`),
    INDEX `idx_tenant_subscriptions_next_billing_date` (`next_billing_date`),
    INDEX `idx_tenant_subscriptions_external_id` (`external_subscription_id`),
    
    CONSTRAINT `fk_tenant_subscriptions_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_tenant_subscriptions_plan_id` FOREIGN KEY (`plan_id`) REFERENCES `tenant_plans` (`id`) ON DELETE RESTRICT,
    
    CONSTRAINT `chk_tenant_subscriptions_amount` CHECK (`amount` >= 0),
    CONSTRAINT `chk_tenant_subscriptions_payment_failure_count` CHECK (`payment_failure_count` >= 0),
    CONSTRAINT `chk_tenant_subscriptions_period_dates` CHECK (`current_period_end` > `current_period_start`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;