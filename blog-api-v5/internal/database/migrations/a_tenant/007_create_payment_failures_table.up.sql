-- Create tenant_payment_failures table
CREATE TABLE IF NOT EXISTS `tenant_payment_failures` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `tenant_id` INT UNSIGNED NOT NULL,
    `subscription_id` INT UNSIGNED NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `currency` CHAR(3) NOT NULL,
    `failure_reason` TEXT NOT NULL,
    `external_id` VARCHAR(255) NULL,
    `retry_count` INT NOT NULL DEFAULT 0,
    `next_retry_at` TIMESTAMP NULL,
    `resolved_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_tenant_payment_failures_tenant_id` (`tenant_id`),
    INDEX `idx_tenant_payment_failures_subscription_id` (`subscription_id`),
    INDEX `idx_tenant_payment_failures_external_id` (`external_id`),
    INDEX `idx_tenant_payment_failures_next_retry_at` (`next_retry_at`),
    INDEX `idx_tenant_payment_failures_resolved_at` (`resolved_at`),
    
    CONSTRAINT `fk_tenant_payment_failures_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_tenant_payment_failures_subscription_id` FOREIGN KEY (`subscription_id`) REFERENCES `tenant_subscriptions` (`id`) ON DELETE CASCADE,
    
    CONSTRAINT `chk_tenant_payment_failures_amount` CHECK (`amount` >= 0),
    CONSTRAINT `chk_tenant_payment_failures_retry_count` CHECK (`retry_count` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;