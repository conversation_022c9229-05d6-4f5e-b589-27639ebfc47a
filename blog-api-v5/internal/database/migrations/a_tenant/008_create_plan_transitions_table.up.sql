-- Create tenant_plan_transitions table
CREATE TABLE IF NOT EXISTS `tenant_plan_transitions` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `tenant_id` INT UNSIGNED NOT NULL,
    `subscription_id` INT UNSIGNED NOT NULL,
    `from_plan_id` INT UNSIGNED NOT NULL,
    `to_plan_id` INT UNSIGNED NOT NULL,
    `transition_type` VARCHAR(50) NOT NULL,
    `effective_date` TIMESTAMP NOT NULL,
    `prorated_amount` DECIMAL(10,2) NOT NULL DEFAULT 0,
    `notes` TEXT NULL,
    `processed_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_tenant_plan_transitions_tenant_id` (`tenant_id`),
    INDEX `idx_tenant_plan_transitions_subscription_id` (`subscription_id`),
    INDEX `idx_tenant_plan_transitions_from_plan_id` (`from_plan_id`),
    INDEX `idx_tenant_plan_transitions_to_plan_id` (`to_plan_id`),
    INDEX `idx_tenant_plan_transitions_effective_date` (`effective_date`),
    INDEX `idx_tenant_plan_transitions_processed_at` (`processed_at`),
    INDEX `idx_tenant_plan_transitions_transition_type` (`transition_type`),
    
    CONSTRAINT `fk_tenant_plan_transitions_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_tenant_plan_transitions_subscription_id` FOREIGN KEY (`subscription_id`) REFERENCES `tenant_subscriptions` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_tenant_plan_transitions_from_plan_id` FOREIGN KEY (`from_plan_id`) REFERENCES `tenant_plans` (`id`) ON DELETE RESTRICT,
    CONSTRAINT `fk_tenant_plan_transitions_to_plan_id` FOREIGN KEY (`to_plan_id`) REFERENCES `tenant_plans` (`id`) ON DELETE RESTRICT,
    
    CONSTRAINT `chk_tenant_plan_transitions_different_plans` CHECK (`from_plan_id` != `to_plan_id`),
    CONSTRAINT `chk_tenant_plan_transitions_transition_type` CHECK (`transition_type` IN ('upgrade', 'downgrade', 'change'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;