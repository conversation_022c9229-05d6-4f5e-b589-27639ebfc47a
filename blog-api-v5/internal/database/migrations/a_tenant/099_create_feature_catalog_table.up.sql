-- Migration: 099_create_feature_catalog_table
-- Description: Create feature catalog table for global features available to all tenants (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS feature_catalog (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    feature_key VARCHAR(255) NOT NULL UNIQUE,
    feature_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    
    -- Feature metadata
    is_beta BOOLEAN DEFAULT FALSE,
    is_experimental BOOLEAN DEFAULT FALSE,
    is_deprecated BOOLEAN DEFAULT FALSE,
    
    -- Plan restrictions
    required_plans JSON DEFAULT (JSON_ARRAY()),
    excluded_plans JSON DEFAULT (JSON_ARRAY()),
    
    -- Default configuration
    default_enabled BOOLEAN DEFAULT FALSE,
    default_configuration JSON DEFAULT (JSON_OBJECT()),
    
    -- Documentation
    documentation_url TEXT,
    changelog_url TEXT,
    
    -- Status
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_feature_status CHECK (status IN ('active', 'inactive', 'deprecated')),
    
    -- Indexes
    INDEX idx_feature_catalog_key (feature_key),
    INDEX idx_feature_catalog_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Global catalog of available features';

-- Note: Feature catalog data will be populated through the seeding system
-- Feature initialization for new tenants will be handled in application code
-- This allows for more flexibility and easier testing/debugging