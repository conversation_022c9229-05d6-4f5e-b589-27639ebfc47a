-- Migration: 101_create_websites_table
-- Description: Create websites table for multi-tenant website management (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS websites (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Basic Information
    name VARCHAR(255) NOT NULL COMMENT 'Website display name',
    domain VARCHAR(255) UNIQUE COMMENT 'Custom domain (optional)',
    subdomain VARCHAR(100) UNIQUE COMMENT 'Subdomain for the website',
    description TEXT COMMENT 'Website description',
    
    -- Theme and Customization
    active_theme VARCHAR(100) COMMENT 'Currently active theme name',
    custom_css LONGTEXT COMMENT 'Custom CSS styles',
    custom_js LONGTEXT COMMENT 'Custom JavaScript code',
    
    -- SEO and Branding
    site_logo VARCHAR(500) COMMENT 'URL or path to site logo',
    favicon VARCHAR(500) COMMENT 'URL or path to favicon',
    timezone VARCHAR(50) DEFAULT 'UTC' COMMENT 'Website timezone',
    language VARCHAR(10) DEFAULT 'en' COMMENT 'Website language code',
    
    -- Analytics Integration
    google_analytics_id VARCHAR(50) COMMENT 'Google Analytics tracking ID',
    google_tag_manager_id VARCHAR(50) COMMENT 'Google Tag Manager ID',
    facebook_pixel_id VARCHAR(50) COMMENT 'Facebook Pixel ID',
    
    -- Social Media Links
    social_media JSON DEFAULT (JSON_OBJECT()) COMMENT 'Social media links and profiles',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Website status: active, inactive, suspended',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_websites_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT chk_websites_status CHECK (status IN ('active', 'inactive', 'suspended')),
    CONSTRAINT chk_websites_name_length CHECK (CHAR_LENGTH(name) >= 1),
    CONSTRAINT chk_websites_subdomain_format CHECK (subdomain = '' OR subdomain REGEXP '^[a-z0-9-]+$'),
    
    -- Indexes
    INDEX idx_websites_tenant_id (tenant_id),
    INDEX idx_websites_status (status),
    INDEX idx_websites_domain (domain),
    INDEX idx_websites_subdomain (subdomain),
    INDEX idx_websites_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Websites table for multi-tenant website management system';