-- Create website_templates table for storing template definitions
CREATE TABLE IF NOT EXISTS website_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Basic Information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Template Content
    preview_image_url VARCHAR(500),
    template_data JSON DEFAULT (JSON_OBJECT()),
    sections JSON DEFAULT (JSON_ARRAY()),
    styles JSON DEFAULT (JSON_OBJECT()),
    settings JSON DEFAULT (JSON_OBJECT()),
    
    -- Categorization
    category_id INT UNSIGNED,
    tags JSON DEFAULT (JSON_ARRAY()),
    
    -- Template Properties
    is_premium BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    version VARCHAR(20) DEFAULT '1.0.0',
    
    -- Usage Statistics
    download_count INT UNSIGNED DEFAULT 0,
    usage_count INT UNSIGNED DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT UNSIGNED DEFAULT 0,
    
    -- Template Type and Industry
    template_type ENUM('business', 'portfolio', 'blog', 'ecommerce', 'landing', 'personal', 'nonprofit', 'education', 'event', 'restaurant') DEFAULT 'business',
    industry VARCHAR(100),
    
    -- Status and Lifecycle
    status ENUM('draft', 'review', 'published', 'archived', 'deleted') DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    archived_at TIMESTAMP NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE KEY uk_website_templates_slug (slug),
    INDEX idx_website_templates_category_id (category_id),
    INDEX idx_website_templates_status (status),
    INDEX idx_website_templates_type (template_type),
    INDEX idx_website_templates_published (is_published, published_at),
    INDEX idx_website_templates_featured (is_featured),
    INDEX idx_website_templates_premium (is_premium),
    INDEX idx_website_templates_rating (rating_average, rating_count),
    INDEX idx_website_templates_usage (usage_count),
    INDEX idx_website_templates_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;