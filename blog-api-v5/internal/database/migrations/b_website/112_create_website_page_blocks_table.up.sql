CREATE TABLE IF NOT EXISTS website_page_blocks (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    page_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    block_uuid VARCHAR(36) NOT NULL,
    block_type VARCHAR(100) NOT NULL,
    block_name VARCHAR(255) NOT NULL,
    block_data JSON DEFAULT (JSON_OBJECT()),
    block_props JSON DEFAULT (JSON_OBJECT()),
    block_styles JSON DEFAULT (JSON_OBJECT()),
    order_index INT NOT NULL DEFAULT 0,
    parent_block_id INT UNSIGNED NULL,
    container_name VARCHAR(100) NULL DEFAULT 'main',
    is_visible BOOLEAN NOT NULL DEFAULT TRUE,
    is_locked BOOLEAN NOT NULL DEFAULT FALSE,
    is_global BOOLEAN NOT NULL DEFAULT FALSE,
    block_version VARCHAR(20) NOT NULL DEFAULT '1.0.0',
    block_category VARCHAR(50) NULL,
    desktop_visible BOOLEAN NOT NULL DEFAULT TRUE,
    tablet_visible BOOLEAN NOT NULL DEFAULT TRUE,
    mobile_visible BOOLEAN NOT NULL DEFAULT TRUE,
    variant_group VARCHAR(100) NULL,
    variant_weight INT NULL DEFAULT 100,
    cache_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    cache_duration INT NULL DEFAULT 3600,
    lazy_load BOOLEAN NOT NULL DEFAULT FALSE,
    impression_count INT UNSIGNED NOT NULL DEFAULT 0,
    click_count INT UNSIGNED NOT NULL DEFAULT 0,
    last_impression_at TIMESTAMP NULL,
    created_by INT UNSIGNED NOT NULL,
    updated_by INT UNSIGNED NULL,
    custom_fields JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_website_page_blocks_page_id FOREIGN KEY (page_id) REFERENCES website_pages(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_page_blocks_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_page_blocks_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_page_blocks_parent_block_id FOREIGN KEY (parent_block_id) REFERENCES website_page_blocks(id) ON DELETE CASCADE,
    
    -- Unique constraints
    UNIQUE KEY uk_website_page_blocks_uuid (block_uuid),
    UNIQUE KEY uk_website_page_blocks_page_order (page_id, container_name, order_index),
    
    -- Indexes for performance
    INDEX idx_website_page_blocks_tenant_id (tenant_id),
    INDEX idx_website_page_blocks_website_id (website_id),
    INDEX idx_website_page_blocks_page_id (page_id),
    INDEX idx_website_page_blocks_block_type (block_type),
    INDEX idx_website_page_blocks_parent_block_id (parent_block_id),
    INDEX idx_website_page_blocks_container (page_id, container_name),
    INDEX idx_website_page_blocks_visibility (is_visible, desktop_visible, tablet_visible, mobile_visible),
    INDEX idx_website_page_blocks_global (is_global),
    INDEX idx_website_page_blocks_order (page_id, order_index),
    INDEX idx_website_page_blocks_variant (variant_group, variant_weight),
    INDEX idx_website_page_blocks_created_by (created_by),
    INDEX idx_website_page_blocks_analytics (impression_count, click_count),
    
    -- Check constraints
    CONSTRAINT chk_website_page_blocks_uuid_format CHECK (block_uuid REGEXP '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$'),
    CONSTRAINT chk_website_page_blocks_order_index CHECK (order_index >= 0),
    CONSTRAINT chk_website_page_blocks_variant_weight CHECK (variant_weight IS NULL OR variant_weight BETWEEN 1 AND 1000),
    CONSTRAINT chk_website_page_blocks_cache_duration CHECK (cache_duration IS NULL OR cache_duration > 0),
    CONSTRAINT chk_website_page_blocks_counts CHECK (impression_count >= 0 AND click_count >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;