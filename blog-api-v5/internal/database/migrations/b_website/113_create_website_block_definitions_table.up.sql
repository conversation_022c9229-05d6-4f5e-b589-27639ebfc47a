CREATE TABLE IF NOT EXISTS website_block_definitions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NULL,
    website_id INT UNSIGNED NULL,
    block_type VARCHAR(100) NOT NULL,
    block_name VARCHAR(255) NOT NULL,
    block_description TEXT NULL,
    block_category VARCHAR(50) NOT NULL DEFAULT 'general',
    field_schema JSON DEFAULT (JSON_OBJECT()),
    default_props JSON DEFAULT (JSON_OBJECT()),
    style_schema JSON DEFAULT (JSON_OBJECT()),
    block_icon VARCHAR(100) NULL,
    block_preview VARCHAR(500) NULL,
    block_version VARCHAR(20) NOT NULL DEFAULT '1.0.0',
    supports_nesting BOOLEAN NOT NULL DEFAULT FALSE,
    supports_global BOOLEAN NOT NULL DEFAULT FALSE,
    supports_responsive BO<PERSON>EAN NOT NULL DEFAULT TRUE,
    supports_animation BOOLEAN NOT NULL DEFAULT FALSE,
    css_dependencies JSON DEFAULT (JSON_ARRAY()),
    js_dependencies JSON DEFAULT (JSON_ARRAY()),
    font_dependencies JSON DEFAULT (JSON_ARRAY()),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_premium BOOLEAN NOT NULL DEFAULT FALSE,
    is_deprecated BOOLEAN NOT NULL DEFAULT FALSE,
    usage_count INT UNSIGNED NOT NULL DEFAULT 0,
    last_used_at TIMESTAMP NULL,
    documentation_url VARCHAR(500) NULL,
    example_config JSON DEFAULT (JSON_OBJECT()),
    default_data JSON DEFAULT (JSON_OBJECT()),
    default_styles JSON DEFAULT (JSON_OBJECT()),
    preview_image VARCHAR(500) NULL,
    documentation TEXT NULL,
    dependencies JSON DEFAULT (JSON_ARRAY()),
    max_nesting_level INT NOT NULL DEFAULT 0,
    allowed_parents JSON DEFAULT (JSON_ARRAY()),
    allowed_children JSON DEFAULT (JSON_ARRAY()),
    created_by INT UNSIGNED NOT NULL,
    updated_by INT UNSIGNED NULL,
    custom_fields JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_website_block_definitions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_website_block_definitions_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    
    -- Unique constraints
    UNIQUE KEY uk_website_block_definitions_tenant_website_type (tenant_id, website_id, block_type),
    
    -- Indexes for performance
    INDEX idx_website_block_definitions_tenant_id (tenant_id),
    INDEX idx_website_block_definitions_website_id (website_id),
    INDEX idx_website_block_definitions_block_type (block_type),
    INDEX idx_website_block_definitions_block_category (block_category),
    INDEX idx_website_block_definitions_is_active (is_active),
    INDEX idx_website_block_definitions_is_premium (is_premium),
    INDEX idx_website_block_definitions_is_deprecated (is_deprecated),
    INDEX idx_website_block_definitions_supports (supports_nesting, supports_global, supports_responsive, supports_animation),
    INDEX idx_website_block_definitions_usage (usage_count, last_used_at),
    INDEX idx_website_block_definitions_created_by (created_by),
    
    -- Check constraints
    CONSTRAINT chk_website_block_definitions_block_type_format CHECK (block_type REGEXP '^[a-z][a-z0-9_-]*$'),
    CONSTRAINT chk_website_block_definitions_category_format CHECK (block_category REGEXP '^[a-z][a-z0-9_-]*$'),
    CONSTRAINT chk_website_block_definitions_usage_count CHECK (usage_count >= 0),
    CONSTRAINT chk_website_block_definitions_version_format CHECK (block_version REGEXP '^[0-9]+\.[0-9]+\.[0-9]+$')
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;