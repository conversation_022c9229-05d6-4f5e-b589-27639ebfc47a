-- Migration: 114_fix_domain_unique_constraint (rollback)
-- Description: Rollback domain unique constraint fix
-- Author: System
-- Date: 2025-07-28

-- Drop the conditional unique index
DROP INDEX idx_websites_domain_unique ON websites;

-- Restore the original unique constraint on domain
-- Note: This may fail if there are multiple NULL domains
ALTER TABLE websites ADD CONSTRAINT UNIQUE (domain);

-- Revert domain column back to NOT NULL if needed
-- Note: This may fail if there are NULL domains
-- ALTER TABLE websites MODIFY COLUMN domain VARCHAR(255) NOT NULL COMMENT 'Custom domain (optional)';