-- Migration: 011_create_user_preferences_table
-- Description: Create the user preferences table for user settings and preferences (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS user_preferences (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Notification Preferences
    email_notifications BOOLEAN DEFAULT TRUE,
    push_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    
    -- Communication Preferences
    marketing_emails BOOLEAN DEFAULT FALSE,
    newsletter_subscription BOOLEAN DEFAULT FALSE,
    product_updates B<PERSON><PERSON><PERSON>N DEFAULT TRUE,
    security_alerts BOOLEAN DEFAULT TRUE,
    
    -- Privacy Settings
    profile_visibility VARCHAR(50) DEFAULT 'public' COMMENT 'public, private, friends_only',
    show_online_status BOOLEAN DEFAULT TRUE,
    allow_search B<PERSON>OLEAN DEFAULT TRUE,
    data_processing_consent BOOLEAN DEFAULT FALSE,
    
    -- UI/UX Preferences
    theme VARCHAR(50) DEFAULT 'light' COMMENT 'light, dark, system',
    dashboard_layout VARCHAR(50) DEFAULT 'default',
    items_per_page INT UNSIGNED DEFAULT 20,
    
    -- Application Settings
    auto_save BOOLEAN DEFAULT TRUE,
    keyboard_shortcuts BOOLEAN DEFAULT TRUE,
    tooltips_enabled BOOLEAN DEFAULT TRUE,
    
    -- Notification Types (JSON for flexibility)
    notification_types JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object mapping notification types to preferences',
    
    -- Feature Flags (JSON for tenant-specific features)
    feature_preferences JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object for feature-specific preferences',
    
    -- Custom Preferences (JSON for tenant-specific settings)
    custom_preferences JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object for tenant-specific custom preferences',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_user_preferences_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_preferences_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_user_preferences_profile_visibility CHECK (profile_visibility IN ('public', 'private', 'friends_only')),
    CONSTRAINT chk_user_preferences_theme CHECK (theme IN ('light', 'dark', 'system')),
    CONSTRAINT chk_user_preferences_items_per_page CHECK (items_per_page >= 10 AND items_per_page <= 100),
    
    -- Unique Constraints
    UNIQUE KEY uk_user_preferences_tenant_user (tenant_id, user_id),
    
    -- Indexes
    INDEX idx_user_preferences_tenant_id (tenant_id),
    INDEX idx_user_preferences_tenant_user (tenant_id, user_id),
    INDEX idx_user_preferences_user_id (user_id),
    INDEX idx_user_preferences_profile_visibility (profile_visibility),
    INDEX idx_user_preferences_theme (theme),
    INDEX idx_user_preferences_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User preferences and settings including notifications, privacy, and UI preferences.';