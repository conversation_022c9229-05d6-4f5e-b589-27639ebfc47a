-- Migration: 207_create_phone_verification_tokens_table
-- Description: Create the phone verification tokens table for multi-tenant phone verification management (MySQL 8)
-- Author: System
-- Date: 2025-07-25

CREATE TABLE IF NOT EXISTS phone_verification_tokens (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Token Information
    phone VARCHAR(50) NOT NULL COMMENT 'Phone number in E.164 format',
    verification_code VARCHAR(10) NOT NULL COMMENT 'Verification code (usually 6 digits)',
    code_hash VARCHAR(255) NOT NULL COMMENT 'SHA-256 hash of the verification code',
    
    -- Request Context
    user_agent TEXT,
    ip_address VARCHAR(45),
    
    -- Status and Tracking
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP NULL DEFAULT NULL,
    
    -- Rate Limiting
    resend_count INT UNSIGNED DEFAULT 0,
    last_resent_at TIMESTAMP NULL DEFAULT NULL,
    
    -- SMS Provider Info
    provider VARCHAR(50) COMMENT 'SMS provider used: twilio, aws_sns, etc.',
    provider_message_id VARCHAR(255) COMMENT 'Message ID from SMS provider',
    provider_response JSON DEFAULT (JSON_OBJECT()) COMMENT 'Provider response data',
    
    -- Expiration
    expires_at TIMESTAMP NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_phone_verification_tokens_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_phone_verification_tokens_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_phone_verification_tokens_phone_format CHECK (phone REGEXP '^\\+[1-9]\\d{1,14}$'),
    CONSTRAINT chk_phone_verification_tokens_code_format CHECK (verification_code REGEXP '^[0-9]{4,10}$'),
    
    -- Unique Constraint: one active token per tenant-user-phone combination
    UNIQUE KEY uk_phone_verification_tokens_active (tenant_id, user_id, phone, is_used),
    
    -- Indexes
    INDEX idx_phone_verification_tokens_tenant_id (tenant_id),
    INDEX idx_phone_verification_tokens_user_id (user_id),
    INDEX idx_phone_verification_tokens_phone (phone),
    INDEX idx_phone_verification_tokens_expires_at (expires_at),
    INDEX idx_phone_verification_tokens_is_used (is_used),
    INDEX idx_phone_verification_tokens_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Phone verification tokens table for multi-tenant phone verification management.';