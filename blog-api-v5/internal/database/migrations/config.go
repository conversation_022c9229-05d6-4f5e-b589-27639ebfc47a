package migrations

import (
	"fmt"
	"path/filepath"
	"runtime"
)

// Config holds migration configuration
type Config struct {
	MigrationsPath string
}

// GetMigrationsPath returns the migrations path
func GetMigrationsPath() (string, error) {
	// Get the directory of this file
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		return "", fmt.Errorf("failed to get current file path")
	}

	return filepath.Dir(filename), nil
}

// MigrationNotes provides information about database migrations
const MigrationNotes = `
Database Migrations for MySQL 8:

This project uses MySQL 8 as the database system.

Module Structure:
1. Tenant Module (001-004):
   - Core multi-tenancy tables
   - Subscription plans
   - Settings management
   - Feature flags

2. Website Module (005-008):
   - Website instances
   - Themes and customization
   - Analytics tracking

3. User Module (009-013):
   - User accounts (multi-tenant aware)
   - Profiles and preferences
   - Social connections

4. Auth Module (014-018):
   - Session management
   - Token storage
   - OAuth providers

5. RBAC Module (019-023):
   - Roles and permissions
   - Website-scoped access control

6. Onboarding Module (024-028):
   - User journeys
   - Progress tracking
   - Analytics

MySQL 8 Features Used:
- Native JSON data type
- CHECK constraints (8.0.16+)
- Stored procedures and triggers
- ON UPDATE CURRENT_TIMESTAMP
- InnoDB engine for all tables
- utf8mb4 charset for full Unicode support

Configuration:
The database connection is configured in config.yml with MySQL settings.
`
