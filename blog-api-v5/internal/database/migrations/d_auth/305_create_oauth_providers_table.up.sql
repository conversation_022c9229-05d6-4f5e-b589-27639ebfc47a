-- Migration: 005_create_auth_oauth_providers_table
-- Description: Create OAuth providers table for third-party authentication (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_oauth_providers (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    website_id INT UNSIGNED NOT NULL,
    
    -- Provider Information
    name VARCHAR(100) NOT NULL COMMENT 'Provider name: google, facebook, github, etc.',
    display_name VARCHAR(100) NOT NULL COMMENT 'Display name for UI',
    provider_type VARCHAR(50) NOT NULL DEFAULT 'oauth2' COMMENT 'Provider type: oauth2, saml, oidc',
    
    -- Configuration
    client_id VARCHAR(255) NOT NULL COMMENT 'OAuth client ID',
    client_secret VARCHAR(500) NOT NULL COMMENT 'OAuth client secret (encrypted)',
    scopes JSON DEFAULT (JSON_ARRAY()) COMMENT 'OAuth scopes to request',
    
    -- Endpoints
    auth_url VARCHAR(500) NOT NULL COMMENT 'Authorization URL',
    token_url VARCHAR(500) NOT NULL COMMENT 'Token exchange URL',
    user_info_url VARCHAR(500) COMMENT 'User info endpoint URL',
    redirect_url VARCHAR(500) COMMENT 'Primary redirect URL for OAuth callback',
    redirect_urls JSON DEFAULT (JSON_ARRAY()) COMMENT 'Multiple redirect URLs for multi-domain support',
    
    -- Settings
    is_enabled BOOLEAN DEFAULT TRUE COMMENT 'Whether provider is enabled',
    is_default BOOLEAN DEFAULT FALSE COMMENT 'Whether this is the default provider',
    auto_create_users BOOLEAN DEFAULT TRUE COMMENT 'Auto-create users on first login',
    
    -- User Mapping
    user_mapping JSON DEFAULT (JSON_OBJECT()) COMMENT 'Field mapping for user data',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_auth_oauth_providers_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT chk_auth_oauth_providers_provider_type CHECK (provider_type IN ('oauth2', 'saml', 'oidc')),
    CONSTRAINT chk_auth_oauth_providers_name CHECK (name IN ('google', 'facebook', 'github', 'microsoft', 'apple', 'twitter', 'linkedin', 'discord', 'slack', 'custom')),
    
    -- Unique constraints
    UNIQUE KEY uk_auth_oauth_providers_website_name (website_id, name),
    
    -- Indexes for Performance
    INDEX idx_auth_oauth_providers_website_id (website_id),
    INDEX idx_auth_oauth_providers_name (name),
    INDEX idx_auth_oauth_providers_is_enabled (is_enabled),
    INDEX idx_auth_oauth_providers_is_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth providers configuration for third-party authentication';

