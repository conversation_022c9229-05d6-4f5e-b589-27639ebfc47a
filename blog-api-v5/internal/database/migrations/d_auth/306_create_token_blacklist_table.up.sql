-- Migration: 306_create_token_blacklist_table
-- Description: Create token blacklist table for JWT invalidation (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS auth_token_blacklist (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Token Information
    token_jti VARCHAR(255) NOT NULL UNIQUE COMMENT 'JWT token ID (jti claim)',
    token_type VARCHAR(50) NOT NULL DEFAULT 'jwt' COMMENT 'Token type: jwt, refresh, access',
    
    -- Blacklist Reason
    reason VARCHAR(100) COMMENT 'Reason for blacklisting: logout, revoked, expired, etc.',
    
    -- Expiration (for cleanup)
    expires_at TIMESTAMP NOT NULL COMMENT 'When token expires (for cleanup)',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_auth_token_blacklist_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_token_blacklist_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_token_blacklist_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT chk_auth_token_blacklist_token_type CHECK (token_type IN ('jwt', 'refresh', 'access')),
    
    -- Indexes
    INDEX idx_auth_token_blacklist_tenant_id (tenant_id),
    INDEX idx_auth_token_blacklist_website_id (website_id),
    INDEX idx_auth_token_blacklist_user_id (user_id),
    INDEX idx_auth_token_blacklist_token_jti (token_jti),
    INDEX idx_auth_token_blacklist_expires_at (expires_at),
    INDEX idx_auth_token_blacklist_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Token blacklist for JWT invalidation';