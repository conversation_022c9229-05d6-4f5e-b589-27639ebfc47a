-- Migration: 309_create_auth_email_verification_tokens_table
-- Description: Create email verification tokens table for email verification flow (MySQL 8)
-- Author: System
-- Date: 2025-01-18

CREATE TABLE IF NOT EXISTS auth_email_verification_tokens (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Token Information
    token VARCHAR(255) NOT NULL UNIQUE COMMENT 'Email verification token value',
    token_hash VARCHAR(255) NOT NULL COMMENT 'Hashed version of token for security',
    
    -- Email and User Context
    email VARCHAR(255) NOT NULL COMMENT 'Email address being verified',
    user_agent TEXT NULL COMMENT 'User agent string from verification request',
    ip_address VARCHAR(45) NULL COMMENT 'IP address from verification request',
    
    -- Status and Tracking
    is_used BOOLEAN DEFAULT FALSE COMMENT 'Whether token has been used',
    used_at TIMESTAMP NULL COMMENT 'When token was used for verification',
    
    -- Rate Limiting
    resend_count INT UNSIGNED DEFAULT 0 COMMENT 'Number of times token was resent',
    last_resent_at TIMESTAMP NULL COMMENT 'Last time token was resent',
    
    -- Expiration (24 hour default)
    expires_at TIMESTAMP NOT NULL COMMENT 'Token expiration time',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_auth_email_verification_tokens_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_auth_email_verification_tokens_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_auth_email_verification_tokens_tenant_id (tenant_id),
    INDEX idx_auth_email_verification_tokens_user_id (user_id),
    INDEX idx_auth_email_verification_tokens_token (token),
    INDEX idx_auth_email_verification_tokens_token_hash (token_hash),
    INDEX idx_auth_email_verification_tokens_email (email),
    INDEX idx_auth_email_verification_tokens_expires_at (expires_at),
    INDEX idx_auth_email_verification_tokens_is_used (is_used),
    INDEX idx_auth_email_verification_tokens_created_at (created_at),
    
    -- Composite indexes for common queries
    INDEX idx_auth_email_verification_tokens_tenant_user (tenant_id, user_id),
    INDEX idx_auth_email_verification_tokens_user_active (user_id, is_used, expires_at),
    INDEX idx_auth_email_verification_tokens_email_active (email, is_used, expires_at),
    INDEX idx_auth_email_verification_tokens_resend_limit (user_id, created_at, resend_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Email verification tokens for user email verification flow';