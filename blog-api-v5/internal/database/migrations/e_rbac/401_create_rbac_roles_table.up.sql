-- Migration: 019_create_rbac_roles_table
-- Description: Create the RBAC roles table for role-based access control (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS rbac_roles (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Role Information
    name VARCHAR(100) NOT NULL COMMENT 'Role name (e.g., admin, editor, viewer)',
    display_name VARCHAR(255) NOT NULL COMMENT 'Human-readable role name',
    description TEXT COMMENT 'Role description and purpose',
    
    -- Role Configuration
    is_system_role BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a system-defined role',
    is_default_role BOOLEAN DEFAULT FALSE COMMENT 'Whether this is the default role for new users',
    level INT UNSIGNED DEFAULT 0 COMMENT 'Role hierarchy level (higher = more permissions)',
    
    -- Role Scope and Context
    scope VARCHAR(50) DEFAULT 'tenant' COMMENT 'Role scope: tenant, website, global',
    context_id INT UNSIGNED NULL COMMENT 'Context ID for scoped roles (e.g., website_id)',
    
    -- Role Metadata
    color VARCHAR(7) COMMENT 'Role color for UI display (hex format)',
    icon VARCHAR(100) COMMENT 'Role icon identifier',
    
    -- Role Capabilities
    capabilities JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of role capabilities',
    restrictions JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object of role restrictions',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Role status: active, inactive, deprecated',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_rbac_roles_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_rbac_roles_status CHECK (status IN ('active', 'inactive', 'deprecated')),
    CONSTRAINT chk_rbac_roles_scope CHECK (scope IN ('tenant', 'website', 'global')),
    CONSTRAINT chk_rbac_roles_name_format CHECK (name REGEXP '^[a-z0-9_-]+$'),
    CONSTRAINT chk_rbac_roles_color_format CHECK (color IS NULL OR color REGEXP '^#[0-9A-Fa-f]{6}$'),
    CONSTRAINT chk_rbac_roles_level CHECK (level >= 0 AND level <= 100),
    
    -- Unique Constraints (role names unique per tenant and scope)
    UNIQUE KEY uk_rbac_roles_tenant_name_scope (tenant_id, name, scope, context_id),
    
    -- Indexes
    INDEX idx_rbac_roles_tenant_id (tenant_id, status),
    INDEX idx_rbac_roles_scope (scope, context_id),
    INDEX idx_rbac_roles_system_role (is_system_role),
    INDEX idx_rbac_roles_default_role (is_default_role),
    INDEX idx_rbac_roles_level (level),
    INDEX idx_rbac_roles_name (name),
    INDEX idx_rbac_roles_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RBAC roles table for role-based access control with tenant scoping.';