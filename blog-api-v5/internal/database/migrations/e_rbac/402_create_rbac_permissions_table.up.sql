-- Migration: 020_create_rbac_permissions_table
-- Description: Create the RBAC permissions table for permission definitions (MySQL 8)
-- Author: System
-- Date: 2025-01-15

CREATE TABLE IF NOT EXISTS rbac_permissions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Permission Information
    name VARCHAR(100) NOT NULL COMMENT 'Permission name (e.g., users.create, posts.edit)',
    display_name VARCHAR(255) NOT NULL COMMENT 'Human-readable permission name',
    description TEXT COMMENT 'Permission description and purpose',
    
    -- Permission Categorization
    module VARCHAR(50) NOT NULL COMMENT 'Module this permission belongs to (user, website, auth, etc.)',
    resource VARCHAR(100) NOT NULL COMMENT 'Resource this permission applies to (users, posts, settings, etc.)',
    action VARCHAR(50) NOT NULL COMMENT 'Action this permission allows (create, read, update, delete, etc.)',
    
    -- Permission Scope and Context
    scope VARCHAR(50) DEFAULT 'tenant' COMMENT 'Permission scope: tenant, website, global',
    requires_ownership BOOLEAN DEFAULT FALSE COMMENT 'Whether permission requires resource ownership',
    
    -- Permission Metadata
    is_system_permission BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a system-defined permission',
    risk_level VARCHAR(20) DEFAULT 'low' COMMENT 'Risk level: low, medium, high, critical',
    
    -- Permission Constraints
    conditions JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object defining permission conditions',
    limitations JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object defining permission limitations',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Permission status: active, inactive, deprecated',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_rbac_permissions_status CHECK (status IN ('active', 'inactive', 'deprecated')),
    CONSTRAINT chk_rbac_permissions_scope CHECK (scope IN ('tenant', 'website', 'global')),
    CONSTRAINT chk_rbac_permissions_risk_level CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    CONSTRAINT chk_rbac_permissions_name_format CHECK (name REGEXP '^[a-z0-9_.-]+$'),
    CONSTRAINT chk_rbac_permissions_module_format CHECK (module REGEXP '^[a-z0-9_-]+$'),
    CONSTRAINT chk_rbac_permissions_resource_format CHECK (resource REGEXP '^[a-z0-9_-]+$'),
    CONSTRAINT chk_rbac_permissions_action_format CHECK (action REGEXP '^[a-z0-9_-]+$'),
    
    -- Unique Constraints (permission names must be unique)
    UNIQUE KEY uk_rbac_permissions_name (name),
    UNIQUE KEY uk_rbac_permissions_module_resource_action (module, resource, action, scope),
    
    -- Indexes
    INDEX idx_rbac_permissions_module (module, status),
    INDEX idx_rbac_permissions_resource (resource, status),
    INDEX idx_rbac_permissions_action (action, status),
    INDEX idx_rbac_permissions_scope (scope, status),
    INDEX idx_rbac_permissions_system_permission (is_system_permission),
    INDEX idx_rbac_permissions_risk_level (risk_level),
    INDEX idx_rbac_permissions_ownership (requires_ownership),
    INDEX idx_rbac_permissions_created_at (created_at),
    
    -- Composite indexes for common queries
    INDEX idx_rbac_permissions_module_action (module, action, status),
    INDEX idx_rbac_permissions_resource_scope (resource, scope, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RBAC permissions table for defining system permissions and capabilities.';