-- Migration: 450_seed_rbac_permissions
-- Description: Seed all system-wide RBAC permissions for all modules
-- Author: System
-- Date: 2025-01-31
-- Note: This migration seeds core permissions that are required for the system to function properly

-- Important: Use INSERT IGNORE or ON DUPLICATE KEY UPDATE to make this migration idempotent

-- ====================
-- USER MODULE PERMISSIONS
-- ====================

-- User Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('users.create', 'Create User', 'Create new user accounts', 'user', 'users', 'create', 'tenant', FALSE, TRUE, 'medium'),
('users.read', 'View User', 'View user details and information', 'user', 'users', 'read', 'tenant', FALSE, TRUE, 'low'),
('users.update', 'Update User', 'Update user information', 'user', 'users', 'update', 'tenant', FALSE, TRUE, 'medium'),
('users.delete', 'Delete User', 'Delete user accounts', 'user', 'users', 'delete', 'tenant', FALSE, TRUE, 'high'),
('users.list', 'List Users', 'View list of users', 'user', 'users', 'list', 'tenant', FALSE, TRUE, 'low'),
('users.export', 'Export Users', 'Export user data', 'user', 'users', 'export', 'tenant', FALSE, TRUE, 'medium'),
('users.import', 'Import Users', 'Import user data', 'user', 'users', 'import', 'tenant', FALSE, TRUE, 'high'),
('users.suspend', 'Suspend User', 'Suspend user accounts', 'user', 'users', 'suspend', 'tenant', FALSE, TRUE, 'medium'),
('users.activate', 'Activate User', 'Activate user accounts', 'user', 'users', 'activate', 'tenant', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- User Profile Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('user-profiles.create', 'Create Profile', 'Create user profiles', 'user', 'user-profiles', 'create', 'tenant', FALSE, TRUE, 'low'),
('user-profiles.read', 'View Profile', 'View user profiles', 'user', 'user-profiles', 'read', 'tenant', FALSE, TRUE, 'low'),
('user-profiles.update', 'Update Profile', 'Update user profiles', 'user', 'user-profiles', 'update', 'tenant', FALSE, TRUE, 'low'),
('user-profiles.delete', 'Delete Profile', 'Delete user profiles', 'user', 'user-profiles', 'delete', 'tenant', FALSE, TRUE, 'medium'),
('user-profiles.update-own', 'Update Own Profile', 'Update own user profile', 'user', 'user-profiles', 'update-own', 'tenant', TRUE, TRUE, 'low')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- BLOG MODULE PERMISSIONS
-- ====================

-- Blog Post Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('blog.posts.create', 'Create Post', 'Create new blog posts', 'blog', 'posts', 'create', 'website', FALSE, TRUE, 'low'),
('blog.posts.read', 'View Post', 'View blog posts', 'blog', 'posts', 'read', 'website', FALSE, TRUE, 'low'),
('blog.posts.update', 'Update Post', 'Update blog posts', 'blog', 'posts', 'update', 'website', FALSE, TRUE, 'low'),
('blog.posts.delete', 'Delete Post', 'Delete blog posts', 'blog', 'posts', 'delete', 'website', FALSE, TRUE, 'medium'),
('blog.posts.publish', 'Publish Post', 'Publish blog posts', 'blog', 'posts', 'publish', 'website', FALSE, TRUE, 'medium'),
('blog.posts.unpublish', 'Unpublish Post', 'Unpublish blog posts', 'blog', 'posts', 'unpublish', 'website', FALSE, TRUE, 'medium'),
('blog.posts.schedule', 'Schedule Post', 'Schedule blog posts for future publication', 'blog', 'posts', 'schedule', 'website', FALSE, TRUE, 'low'),
('blog.posts.export', 'Export Posts', 'Export blog posts', 'blog', 'posts', 'export', 'website', FALSE, TRUE, 'low'),
('blog.posts.import', 'Import Posts', 'Import blog posts', 'blog', 'posts', 'import', 'website', FALSE, TRUE, 'high')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- Blog Category Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('blog.categories.create', 'Create Category', 'Create blog categories', 'blog', 'categories', 'create', 'website', FALSE, TRUE, 'low'),
('blog.categories.read', 'View Category', 'View blog categories', 'blog', 'categories', 'read', 'website', FALSE, TRUE, 'low'),
('blog.categories.update', 'Update Category', 'Update blog categories', 'blog', 'categories', 'update', 'website', FALSE, TRUE, 'low'),
('blog.categories.delete', 'Delete Category', 'Delete blog categories', 'blog', 'categories', 'delete', 'website', FALSE, TRUE, 'medium'),
('blog.categories.manage', 'Manage Categories', 'Manage category hierarchy and ordering', 'blog', 'categories', 'manage', 'website', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- Blog Tag Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('blog.tags.create', 'Create Tag', 'Create blog tags', 'blog', 'tags', 'create', 'website', FALSE, TRUE, 'low'),
('blog.tags.read', 'View Tag', 'View blog tags', 'blog', 'tags', 'read', 'website', FALSE, TRUE, 'low'),
('blog.tags.update', 'Update Tag', 'Update blog tags', 'blog', 'tags', 'update', 'website', FALSE, TRUE, 'low'),
('blog.tags.delete', 'Delete Tag', 'Delete blog tags', 'blog', 'tags', 'delete', 'website', FALSE, TRUE, 'medium'),
('blog.tags.merge', 'Merge Tags', 'Merge blog tags', 'blog', 'tags', 'merge', 'website', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- Blog Comment Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('blog.comments.create', 'Create Comment', 'Create blog comments', 'blog', 'comments', 'create', 'website', FALSE, TRUE, 'low'),
('blog.comments.read', 'View Comment', 'View blog comments', 'blog', 'comments', 'read', 'website', FALSE, TRUE, 'low'),
('blog.comments.update', 'Update Comment', 'Update blog comments', 'blog', 'comments', 'update', 'website', FALSE, TRUE, 'low'),
('blog.comments.delete', 'Delete Comment', 'Delete blog comments', 'blog', 'comments', 'delete', 'website', FALSE, TRUE, 'medium'),
('blog.comments.moderate', 'Moderate Comments', 'Moderate blog comments', 'blog', 'comments', 'moderate', 'website', FALSE, TRUE, 'medium'),
('blog.comments.approve', 'Approve Comments', 'Approve blog comments', 'blog', 'comments', 'approve', 'website', FALSE, TRUE, 'low'),
('blog.comments.reject', 'Reject Comments', 'Reject blog comments', 'blog', 'comments', 'reject', 'website', FALSE, TRUE, 'low')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- WEBSITE MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('websites.create', 'Create Website', 'Create new websites', 'website', 'websites', 'create', 'tenant', FALSE, TRUE, 'high'),
('websites.read', 'View Website', 'View website details', 'website', 'websites', 'read', 'tenant', FALSE, TRUE, 'low'),
('websites.update', 'Update Website', 'Update website information', 'website', 'websites', 'update', 'tenant', FALSE, TRUE, 'medium'),
('websites.delete', 'Delete Website', 'Delete websites', 'website', 'websites', 'delete', 'tenant', FALSE, TRUE, 'critical'),
('websites.list', 'List Websites', 'View list of websites', 'website', 'websites', 'list', 'tenant', FALSE, TRUE, 'low'),
('websites.manage-settings', 'Manage Website Settings', 'Manage website configuration and settings', 'website', 'websites', 'manage-settings', 'tenant', FALSE, TRUE, 'medium'),
('websites.manage-domains', 'Manage Domains', 'Manage website domains', 'website', 'websites', 'manage-domains', 'tenant', FALSE, TRUE, 'high'),
('websites.activate', 'Activate Website', 'Activate websites', 'website', 'websites', 'activate', 'tenant', FALSE, TRUE, 'medium'),
('websites.deactivate', 'Deactivate Website', 'Deactivate websites', 'website', 'websites', 'deactivate', 'tenant', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- AUTH MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('auth.sessions.read', 'View Sessions', 'View authentication sessions', 'auth', 'sessions', 'read', 'tenant', FALSE, TRUE, 'low'),
('auth.sessions.delete', 'Delete Sessions', 'Terminate authentication sessions', 'auth', 'sessions', 'delete', 'tenant', FALSE, TRUE, 'medium'),
('auth.sessions.manage', 'Manage Sessions', 'Manage all authentication sessions', 'auth', 'sessions', 'manage', 'tenant', FALSE, TRUE, 'high'),
('auth.tokens.read', 'View Tokens', 'View authentication tokens', 'auth', 'tokens', 'read', 'tenant', FALSE, TRUE, 'medium'),
('auth.tokens.revoke', 'Revoke Tokens', 'Revoke authentication tokens', 'auth', 'tokens', 'revoke', 'tenant', FALSE, TRUE, 'high'),
('auth.tokens.manage', 'Manage Tokens', 'Manage all authentication tokens', 'auth', 'tokens', 'manage', 'tenant', FALSE, TRUE, 'critical'),
('auth.providers.read', 'View Auth Providers', 'View authentication providers', 'auth', 'providers', 'read', 'tenant', FALSE, TRUE, 'low'),
('auth.providers.manage', 'Manage Auth Providers', 'Configure authentication providers', 'auth', 'providers', 'manage', 'tenant', FALSE, TRUE, 'critical'),
('auth.logs.read', 'View Auth Logs', 'View authentication logs', 'auth', 'logs', 'read', 'tenant', FALSE, TRUE, 'medium'),
('auth.logs.export', 'Export Auth Logs', 'Export authentication logs', 'auth', 'logs', 'export', 'tenant', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- RBAC MODULE PERMISSIONS
-- ====================

-- Role Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('rbac.roles.create', 'Create Role', 'Create new roles', 'rbac', 'roles', 'create', 'tenant', FALSE, TRUE, 'high'),
('rbac.roles.read', 'View Role', 'View role details', 'rbac', 'roles', 'read', 'tenant', FALSE, TRUE, 'low'),
('rbac.roles.update', 'Update Role', 'Update role information', 'rbac', 'roles', 'update', 'tenant', FALSE, TRUE, 'high'),
('rbac.roles.delete', 'Delete Role', 'Delete roles', 'rbac', 'roles', 'delete', 'tenant', FALSE, TRUE, 'critical'),
('rbac.roles.list', 'List Roles', 'View list of roles', 'rbac', 'roles', 'list', 'tenant', FALSE, TRUE, 'low'),
('rbac.roles.assign-permissions', 'Assign Permissions to Role', 'Assign permissions to roles', 'rbac', 'roles', 'assign-permissions', 'tenant', FALSE, TRUE, 'critical'),
('rbac.roles.revoke-permissions', 'Revoke Permissions from Role', 'Revoke permissions from roles', 'rbac', 'roles', 'revoke-permissions', 'tenant', FALSE, TRUE, 'critical')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- Permission Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('rbac.permissions.read', 'View Permission', 'View permission details', 'rbac', 'permissions', 'read', 'tenant', FALSE, TRUE, 'low'),
('rbac.permissions.list', 'List Permissions', 'View list of permissions', 'rbac', 'permissions', 'list', 'tenant', FALSE, TRUE, 'low')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- User Role Management
INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('rbac.user-roles.assign', 'Assign Role to User', 'Assign roles to users', 'rbac', 'user-roles', 'assign', 'tenant', FALSE, TRUE, 'high'),
('rbac.user-roles.revoke', 'Revoke Role from User', 'Revoke roles from users', 'rbac', 'user-roles', 'revoke', 'tenant', FALSE, TRUE, 'high'),
('rbac.user-roles.read', 'View User Roles', 'View user role assignments', 'rbac', 'user-roles', 'read', 'tenant', FALSE, TRUE, 'low'),
('rbac.user-roles.list', 'List User Roles', 'List all user role assignments', 'rbac', 'user-roles', 'list', 'tenant', FALSE, TRUE, 'low')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- TENANT MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('tenants.read', 'View Tenant', 'View tenant information', 'tenant', 'tenants', 'read', 'tenant', FALSE, TRUE, 'low'),
('tenants.update', 'Update Tenant', 'Update tenant information', 'tenant', 'tenants', 'update', 'tenant', FALSE, TRUE, 'high'),
('tenants.manage-settings', 'Manage Tenant Settings', 'Manage tenant configuration and settings', 'tenant', 'tenants', 'manage-settings', 'tenant', FALSE, TRUE, 'high'),
('tenants.manage-billing', 'Manage Billing', 'Manage tenant billing and subscriptions', 'tenant', 'tenants', 'manage-billing', 'tenant', FALSE, TRUE, 'critical'),
('tenants.manage-members', 'Manage Members', 'Manage tenant members', 'tenant', 'tenants', 'manage-members', 'tenant', FALSE, TRUE, 'high'),
('tenants.invite-members', 'Invite Members', 'Invite new members to tenant', 'tenant', 'tenants', 'invite-members', 'tenant', FALSE, TRUE, 'medium'),
('tenants.remove-members', 'Remove Members', 'Remove members from tenant', 'tenant', 'tenants', 'remove-members', 'tenant', FALSE, TRUE, 'high')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- MEDIA MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('media.files.upload', 'Upload Files', 'Upload media files', 'media', 'files', 'upload', 'website', FALSE, TRUE, 'low'),
('media.files.read', 'View Files', 'View media files', 'media', 'files', 'read', 'website', FALSE, TRUE, 'low'),
('media.files.update', 'Update Files', 'Update media file information', 'media', 'files', 'update', 'website', FALSE, TRUE, 'low'),
('media.files.delete', 'Delete Files', 'Delete media files', 'media', 'files', 'delete', 'website', FALSE, TRUE, 'medium'),
('media.files.download', 'Download Files', 'Download media files', 'media', 'files', 'download', 'website', FALSE, TRUE, 'low'),
('media.folders.create', 'Create Folders', 'Create media folders', 'media', 'folders', 'create', 'website', FALSE, TRUE, 'low'),
('media.folders.update', 'Update Folders', 'Update media folders', 'media', 'folders', 'update', 'website', FALSE, TRUE, 'low'),
('media.folders.delete', 'Delete Folders', 'Delete media folders', 'media', 'folders', 'delete', 'website', FALSE, TRUE, 'medium'),
('media.folders.manage', 'Manage Folders', 'Manage folder structure and organization', 'media', 'folders', 'manage', 'website', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- NOTIFICATION MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('notifications.templates.create', 'Create Notification Template', 'Create notification templates', 'notification', 'templates', 'create', 'tenant', FALSE, TRUE, 'medium'),
('notifications.templates.read', 'View Notification Template', 'View notification templates', 'notification', 'templates', 'read', 'tenant', FALSE, TRUE, 'low'),
('notifications.templates.update', 'Update Notification Template', 'Update notification templates', 'notification', 'templates', 'update', 'tenant', FALSE, TRUE, 'medium'),
('notifications.templates.delete', 'Delete Notification Template', 'Delete notification templates', 'notification', 'templates', 'delete', 'tenant', FALSE, TRUE, 'medium'),
('notifications.send', 'Send Notifications', 'Send notifications to users', 'notification', 'notifications', 'send', 'tenant', FALSE, TRUE, 'medium'),
('notifications.logs.read', 'View Notification Logs', 'View notification logs', 'notification', 'logs', 'read', 'tenant', FALSE, TRUE, 'low'),
('notifications.logs.export', 'Export Notification Logs', 'Export notification logs', 'notification', 'logs', 'export', 'tenant', FALSE, TRUE, 'low')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- SETTINGS MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('settings.general.read', 'View General Settings', 'View general settings', 'settings', 'general', 'read', 'tenant', FALSE, TRUE, 'low'),
('settings.general.update', 'Update General Settings', 'Update general settings', 'settings', 'general', 'update', 'tenant', FALSE, TRUE, 'medium'),
('settings.security.read', 'View Security Settings', 'View security settings', 'settings', 'security', 'read', 'tenant', FALSE, TRUE, 'medium'),
('settings.security.update', 'Update Security Settings', 'Update security settings', 'settings', 'security', 'update', 'tenant', FALSE, TRUE, 'critical'),
('settings.integrations.read', 'View Integration Settings', 'View integration settings', 'settings', 'integrations', 'read', 'tenant', FALSE, TRUE, 'low'),
('settings.integrations.manage', 'Manage Integrations', 'Manage third-party integrations', 'settings', 'integrations', 'manage', 'tenant', FALSE, TRUE, 'high')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- ANALYTICS MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('analytics.dashboard.read', 'View Analytics Dashboard', 'View analytics dashboards', 'analytics', 'dashboard', 'read', 'website', FALSE, TRUE, 'low'),
('analytics.reports.read', 'View Reports', 'View analytics reports', 'analytics', 'reports', 'read', 'website', FALSE, TRUE, 'low'),
('analytics.reports.create', 'Create Reports', 'Create custom analytics reports', 'analytics', 'reports', 'create', 'website', FALSE, TRUE, 'low'),
('analytics.reports.export', 'Export Reports', 'Export analytics reports', 'analytics', 'reports', 'export', 'website', FALSE, TRUE, 'low'),
('analytics.data.export', 'Export Analytics Data', 'Export raw analytics data', 'analytics', 'data', 'export', 'website', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- API KEY MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('apikeys.create', 'Create API Key', 'Create API keys', 'apikey', 'apikeys', 'create', 'tenant', FALSE, TRUE, 'high'),
('apikeys.read', 'View API Key', 'View API key details', 'apikey', 'apikeys', 'read', 'tenant', FALSE, TRUE, 'medium'),
('apikeys.update', 'Update API Key', 'Update API key settings', 'apikey', 'apikeys', 'update', 'tenant', FALSE, TRUE, 'high'),
('apikeys.delete', 'Delete API Key', 'Delete API keys', 'apikey', 'apikeys', 'delete', 'tenant', FALSE, TRUE, 'high'),
('apikeys.list', 'List API Keys', 'View list of API keys', 'apikey', 'apikeys', 'list', 'tenant', FALSE, TRUE, 'medium'),
('apikeys.rotate', 'Rotate API Key', 'Rotate API key secrets', 'apikey', 'apikeys', 'rotate', 'tenant', FALSE, TRUE, 'high'),
('apikeys.revoke', 'Revoke API Key', 'Revoke API key access', 'apikey', 'apikeys', 'revoke', 'tenant', FALSE, TRUE, 'high')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- SEO MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('seo.metadata.read', 'View SEO Metadata', 'View SEO metadata', 'seo', 'metadata', 'read', 'website', FALSE, TRUE, 'low'),
('seo.metadata.update', 'Update SEO Metadata', 'Update SEO metadata', 'seo', 'metadata', 'update', 'website', FALSE, TRUE, 'low'),
('seo.sitemap.generate', 'Generate Sitemap', 'Generate XML sitemaps', 'seo', 'sitemap', 'generate', 'website', FALSE, TRUE, 'low'),
('seo.robots.manage', 'Manage Robots.txt', 'Manage robots.txt file', 'seo', 'robots', 'manage', 'website', FALSE, TRUE, 'medium'),
('seo.redirects.create', 'Create Redirects', 'Create URL redirects', 'seo', 'redirects', 'create', 'website', FALSE, TRUE, 'medium'),
('seo.redirects.update', 'Update Redirects', 'Update URL redirects', 'seo', 'redirects', 'update', 'website', FALSE, TRUE, 'medium'),
('seo.redirects.delete', 'Delete Redirects', 'Delete URL redirects', 'seo', 'redirects', 'delete', 'website', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- CONTENT MODULE PERMISSIONS (Generic)
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('content.create', 'Create Content', 'Create generic content', 'content', 'content', 'create', 'website', FALSE, TRUE, 'low'),
('content.read', 'View Content', 'View generic content', 'content', 'content', 'read', 'website', FALSE, TRUE, 'low'),
('content.update', 'Update Content', 'Update generic content', 'content', 'content', 'update', 'website', FALSE, TRUE, 'low'),
('content.delete', 'Delete Content', 'Delete generic content', 'content', 'content', 'delete', 'website', FALSE, TRUE, 'medium'),
('content.publish', 'Publish Content', 'Publish generic content', 'content', 'content', 'publish', 'website', FALSE, TRUE, 'medium')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ====================
-- SYSTEM MODULE PERMISSIONS
-- ====================

INSERT INTO rbac_permissions (name, display_name, description, module, resource, action, scope, requires_ownership, is_system_permission, risk_level)
VALUES 
('system.config.read', 'View System Config', 'View system configuration', 'system', 'config', 'read', 'global', FALSE, TRUE, 'high'),
('system.config.update', 'Update System Config', 'Update system configuration', 'system', 'config', 'update', 'global', FALSE, TRUE, 'critical'),
('system.maintenance.manage', 'Manage Maintenance Mode', 'Enable/disable maintenance mode', 'system', 'maintenance', 'manage', 'global', FALSE, TRUE, 'critical'),
('system.logs.read', 'View System Logs', 'View system logs', 'system', 'logs', 'read', 'global', FALSE, TRUE, 'high'),
('system.logs.export', 'Export System Logs', 'Export system logs', 'system', 'logs', 'export', 'global', FALSE, TRUE, 'high'),
('system.cache.manage', 'Manage Cache', 'Manage system cache', 'system', 'cache', 'manage', 'global', FALSE, TRUE, 'high'),
('system.jobs.manage', 'Manage Background Jobs', 'Manage background jobs and queues', 'system', 'jobs', 'manage', 'global', FALSE, TRUE, 'high')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- Update the timestamp for tracking when permissions were last seeded
UPDATE rbac_permissions 
SET updated_at = CURRENT_TIMESTAMP 
WHERE is_system_permission = TRUE;