-- Migration: 451_add_rbac_initialization_tracking
-- Description: Add RBAC initialization status tracking table for monitoring tenant RBAC setup
-- Author: System
-- Date: 2025-01-31

-- Table to track RBAC initialization status for each tenant
CREATE TABLE IF NOT EXISTS rbac_initialization_status (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Status tracking
    status ENUM('pending', 'in_progress', 'completed', 'failed', 'partial') NOT NULL DEFAULT 'pending' 
        COMMENT 'Current status of RBAC initialization',
    initialized_at TIMESTAMP NULL COMMENT 'Timestamp when initialization was completed successfully',
    
    -- Progress tracking
    roles_created INT UNSIGNED DEFAULT 0 COMMENT 'Number of roles created during initialization',
    permissions_assigned INT UNSIGNED DEFAULT 0 COMMENT 'Number of permissions assigned to roles',
    owner_role_assigned BOOLEAN DEFAULT FALSE COMMENT 'Whether admin role was assigned to owner',
    
    -- Error tracking
    error_message TEXT NULL COMMENT 'Error message if initialization failed',
    error_code VARCHAR(50) NULL COMMENT 'Error code for categorizing failures',
    retry_count INT UNSIGNED DEFAULT 0 COMMENT 'Number of retry attempts',
    last_retry_at TIMESTAMP NULL COMMENT 'Timestamp of last retry attempt',
    
    -- Metadata
    initialization_version VARCHAR(50) DEFAULT 'v1' COMMENT 'Version of initialization logic used',
    metadata JSON DEFAULT (JSON_OBJECT()) COMMENT 'Additional metadata about initialization',
    
    -- Timing
    started_at TIMESTAMP NULL COMMENT 'When initialization started',
    completed_at TIMESTAMP NULL COMMENT 'When initialization completed (success or failure)',
    duration_seconds INT UNSIGNED NULL COMMENT 'Time taken for initialization in seconds',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE KEY uk_rbac_init_tenant (tenant_id),
    CONSTRAINT fk_rbac_init_status_tenant FOREIGN KEY (tenant_id) 
        REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Indexes for queries
    INDEX idx_rbac_init_status (status),
    INDEX idx_rbac_init_retry (retry_count, last_retry_at),
    INDEX idx_rbac_init_error (status, error_code),
    INDEX idx_rbac_init_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Tracks RBAC initialization status and progress for each tenant';
-- -- View for monitoring RBAC health across all tenants
-- CREATE OR REPLACE VIEW v_rbac_health_overview AS
-- SELECT 
--     t.id as tenant_id,
--     t.name as tenant_name,
--     t.domain as tenant_domain,
--     t.status as tenant_status,
--     COALESCE(ris.status, 'not_initialized') as rbac_status,
--     ris.initialized_at,
--     ris.roles_created,
--     ris.permissions_assigned,
--     ris.owner_role_assigned,
--     ris.retry_count,
--     ris.error_code,
--     ris.error_message,
--     ris.duration_seconds,
--     CASE 
--         WHEN ris.status = 'completed' THEN 'healthy'
--         WHEN ris.status = 'failed' AND ris.retry_count >= 3 THEN 'critical'
--         WHEN ris.status = 'failed' THEN 'unhealthy'
--         WHEN ris.status = 'partial' THEN 'degraded'
--         WHEN ris.status = 'in_progress' AND 
--              TIMESTAMPDIFF(MINUTE, ris.started_at, NOW()) > 5 THEN 'stuck'
--         WHEN ris.status IS NULL THEN 'not_initialized'
--         ELSE 'unknown'
--     END as health_status,
--     CASE 
--         WHEN ris.status = 'failed' AND ris.retry_count < 3 THEN 
--             TIMESTAMPADD(MINUTE, POWER(2, ris.retry_count) * 5, ris.last_retry_at)
--         ELSE NULL
--     END as next_retry_at
-- FROM tenants t
-- LEFT JOIN rbac_initialization_status ris ON t.id = ris.tenant_id
-- WHERE t.status = 'active';

-- -- View for failed initializations that need attention
-- CREATE OR REPLACE VIEW v_rbac_failed_initializations AS
-- SELECT 
--     ris.*,
--     t.name as tenant_name,
--     t.domain as tenant_domain,
--     TIMESTAMPDIFF(MINUTE, ris.last_retry_at, NOW()) as minutes_since_last_retry,
--     CASE 
--         WHEN ris.retry_count >= 3 THEN 'max_retries_reached'
--         WHEN ris.last_retry_at IS NULL THEN 'ready_for_retry'
--         WHEN TIMESTAMPDIFF(MINUTE, ris.last_retry_at, NOW()) >= POWER(2, ris.retry_count) * 5 THEN 'ready_for_retry'
--         ELSE 'cooling_down'
--     END as retry_status
-- FROM rbac_initialization_status ris
-- JOIN tenants t ON ris.tenant_id = t.id
-- WHERE ris.status = 'failed'
-- AND t.status = 'active'
-- ORDER BY ris.retry_count DESC, ris.last_retry_at ASC;