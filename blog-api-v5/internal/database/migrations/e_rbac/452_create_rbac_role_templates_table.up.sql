-- +migrate Up
-- Create table for RBAC role templates (default roles that can be applied to new tenants)

-- Role Templates Table - stores template roles that can be applied across tenants
CREATE TABLE IF NOT EXISTS rbac_role_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Template Information
    name VARCHAR(100) NOT NULL COMMENT 'Template name (e.g., admin, editor, viewer)',
    display_name VARCHAR(255) NOT NULL COMMENT 'Human-readable template name',
    description TEXT COMMENT 'Template description and purpose',
    
    -- Template Configuration
    is_system_template BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a system-defined template',
    is_default_template BOOLEAN DEFAULT FALSE COMMENT 'Whether this template is applied by default to new tenants',
    level INT UNSIGNED DEFAULT 0 COMMENT 'Role hierarchy level (higher = more permissions)',
    
    -- Template Scope and Context
    scope VARCHAR(50) DEFAULT 'tenant' COMMENT 'Template scope: tenant, website, global',
    
    -- Template Metadata
    color VARCHAR(7) COMMENT 'Template color for UI display (hex format)',
    icon VARCHAR(100) COMMENT 'Template icon identifier',
    
    -- Template Capabilities
    capabilities JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of role capabilities',
    restrictions JSON DEFAULT (JSON_OBJECT()) COMMENT 'JSON object of role restrictions',
    
    -- Template Permissions
    default_permissions JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of permission IDs or names to assign',
    
    -- Template Configuration
    auto_assign_on_tenant_creation BOOLEAN DEFAULT FALSE COMMENT 'Whether to auto-assign this role when creating new tenants',
    auto_assign_to_owner BOOLEAN DEFAULT FALSE COMMENT 'Whether to auto-assign this role to tenant owner',
    
    -- Template Metadata
    category VARCHAR(50) DEFAULT 'general' COMMENT 'Template category (core, feature, administrative, etc.)',
    tags JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of tags for categorization',
    
    -- Status and Timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Template status: active, inactive, deprecated',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED COMMENT 'User who created this template',
    
    -- Foreign Keys
    CONSTRAINT fk_rbac_role_templates_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT chk_rbac_role_templates_status CHECK (status IN ('active', 'inactive', 'deprecated')),
    CONSTRAINT chk_rbac_role_templates_scope CHECK (scope IN ('tenant', 'website', 'global')),
    CONSTRAINT chk_rbac_role_templates_category CHECK (category IN ('core', 'feature', 'administrative', 'integration', 'general')),
    CONSTRAINT chk_rbac_role_templates_name_format CHECK (name REGEXP '^[a-z0-9_-]+$'),
    CONSTRAINT chk_rbac_role_templates_color_format CHECK (color IS NULL OR color REGEXP '^#[0-9A-Fa-f]{6}$'),
    CONSTRAINT chk_rbac_role_templates_level CHECK (level >= 0 AND level <= 100),
    
    -- Unique Constraints
    UNIQUE KEY uk_rbac_role_templates_name (name),
    
    -- Indexes
    INDEX idx_rbac_role_templates_system (is_system_template),
    INDEX idx_rbac_role_templates_default (is_default_template),
    INDEX idx_rbac_role_templates_scope (scope),
    INDEX idx_rbac_role_templates_category (category, status),
    INDEX idx_rbac_role_templates_auto_assign (auto_assign_on_tenant_creation),
    INDEX idx_rbac_role_templates_owner (auto_assign_to_owner),
    INDEX idx_rbac_role_templates_level (level),
    INDEX idx_rbac_role_templates_name (name),
    INDEX idx_rbac_role_templates_created (created_at),
    
    -- Composite indexes for common queries
    INDEX idx_rbac_role_templates_active_defaults (is_default_template, status),
    INDEX idx_rbac_role_templates_auto_creation (auto_assign_on_tenant_creation, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Template roles that can be applied to new tenants automatically';

-- Insert default role templates
INSERT INTO rbac_role_templates (
    name, display_name, description, is_system_template, is_default_template, 
    level, scope, auto_assign_on_tenant_creation, auto_assign_to_owner, 
    category, status
) VALUES 
(
    'owner', 'Tenant Owner', 'Full administrative access to tenant resources', 
    true, true, 100, 'tenant', true, true, 'core', 'active'
),
(
    'admin', 'Administrator', 'Administrative access with most permissions', 
    true, false, 90, 'tenant', false, false, 'core', 'active'
),
(
    'editor', 'Editor', 'Content creation and management permissions', 
    true, true, 50, 'tenant', true, false, 'feature', 'active'
),
(
    'viewer', 'Viewer', 'Read-only access to tenant resources', 
    true, true, 10, 'tenant', true, false, 'core', 'active'
),
(
    'website-admin', 'Website Administrator', 'Full access to specific website', 
    true, false, 80, 'website', false, false, 'feature', 'active'
),
(
    'content-manager', 'Content Manager', 'Content management for specific website', 
    true, false, 40, 'website', false, false, 'feature', 'active'
);