-- +migrate Up
-- Create table for RBAC admin audit logging

-- Admin Audit Log Table - tracks all admin operations for security and compliance
CREATE TABLE IF NOT EXISTS rbac_admin_audit_log (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    
    -- Audit Information
    admin_user_id INT UNSIGNED NOT NULL COMMENT 'Admin user who performed the action',
    action VARCHAR(100) NOT NULL COMMENT 'Action performed (e.g., create_role, assign_permission, etc.)',
    resource_type VARCHAR(50) NOT NULL COMMENT 'Type of resource (role, permission, user_role, role_template, etc.)',
    resource_id INT UNSIGNED COMMENT 'ID of the affected resource',
    
    -- Context Information
    tenant_id INT UNSIGNED COMMENT 'Tenant ID if action was tenant-specific',
    website_id INT UNSIGNED COMMENT 'Website ID if action was website-specific',
    target_user_id INT UNSIGNED COMMENT 'Target user ID if action affected a specific user',
    
    -- Action Details
    action_description TEXT COMMENT 'Human-readable description of the action',
    old_values JSON DEFAULT (JSON_OBJECT()) COMMENT 'Previous values before the change',
    new_values JSON DEFAULT (JSON_OBJECT()) COMMENT 'New values after the change',
    
    -- Request Information
    ip_address VARCHAR(45) COMMENT 'IP address of the admin user',
    user_agent TEXT COMMENT 'User agent of the request',
    request_id VARCHAR(100) COMMENT 'Request ID for tracing',
    
    -- Audit Metadata
    severity VARCHAR(20) DEFAULT 'info' COMMENT 'Severity level: info, warning, error, critical',
    risk_level VARCHAR(20) DEFAULT 'low' COMMENT 'Risk level: low, medium, high, critical',
    
    -- Result Information
    success BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether the action was successful',
    error_message TEXT COMMENT 'Error message if action failed',
    error_code VARCHAR(50) COMMENT 'Error code for categorizing failures',
    
    -- Additional Metadata
    metadata JSON DEFAULT (JSON_OBJECT()) COMMENT 'Additional audit metadata',
    tags JSON DEFAULT (JSON_ARRAY()) COMMENT 'JSON array of tags for categorization',
    
    -- Timestamps
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When the action was performed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_rbac_audit_admin_user_id FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_rbac_audit_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_rbac_audit_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_rbac_audit_target_user_id FOREIGN KEY (target_user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Constraints
    CONSTRAINT chk_rbac_audit_severity CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    CONSTRAINT chk_rbac_audit_risk_level CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    CONSTRAINT chk_rbac_audit_resource_type CHECK (resource_type IN (
        'role', 'permission', 'user_role', 'role_permission', 'role_template', 
        'permission_group', 'system_config', 'bulk_operation'
    )),
    
    -- Indexes
    INDEX idx_rbac_audit_admin_user (admin_user_id, performed_at),
    INDEX idx_rbac_audit_action (action, performed_at),
    INDEX idx_rbac_audit_resource (resource_type, resource_id),
    INDEX idx_rbac_audit_tenant (tenant_id, performed_at),
    INDEX idx_rbac_audit_website (website_id, performed_at),
    INDEX idx_rbac_audit_target_user (target_user_id, performed_at),
    INDEX idx_rbac_audit_severity (severity, performed_at),
    INDEX idx_rbac_audit_risk_level (risk_level, performed_at),
    INDEX idx_rbac_audit_success (success, performed_at),
    INDEX idx_rbac_audit_performed_at (performed_at),
    INDEX idx_rbac_audit_request_id (request_id),
    
    -- Composite indexes for common queries
    INDEX idx_rbac_audit_admin_action (admin_user_id, action, performed_at),
    INDEX idx_rbac_audit_tenant_action (tenant_id, action, performed_at),
    INDEX idx_rbac_audit_resource_action (resource_type, action, performed_at),
    INDEX idx_rbac_audit_high_risk (risk_level, severity, performed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Audit log for all RBAC admin operations for security and compliance tracking';