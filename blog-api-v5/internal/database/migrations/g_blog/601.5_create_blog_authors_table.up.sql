-- Create blog_authors table
CREATE TABLE IF NOT EXISTS blog_authors (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    slug VARCHAR(255) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    pen_name <PERSON><PERSON><PERSON><PERSON>(255) NULL,
    bio TEXT,
    avatar VARCHAR(500) NULL,
    email VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    bank_account VARCHAR(50) NULL,
    bank_name VARCHAR(255) NULL,
    bank_branch VARCHAR(255) NULL,
    tax_code VARCHAR(20) NULL,
    address TEXT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    display_email BOOLEAN DEFAULT FALSE,
    display_bio BOOLEAN DEFAULT TRUE,
    post_count INT UNSIGNED DEFAULT 0,
    total_royalty_earned DECIMAL(12,2) DEFAULT 0.00,
    total_royalty_paid DECIMAL(12,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    
    CONSTRAINT fk_blog_authors_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_authors_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_authors_tenant_website_slug (tenant_id, website_id, slug),
    INDEX idx_blog_authors_tenant_id (tenant_id),
    INDEX idx_blog_authors_website_id (website_id),
    INDEX idx_blog_authors_tenant_website (tenant_id, website_id),
    INDEX idx_blog_authors_status (status),
    INDEX idx_blog_authors_tenant_status (tenant_id, status),
    INDEX idx_blog_authors_website_status (website_id, status),
    INDEX idx_blog_authors_verified (is_verified),
    INDEX idx_blog_authors_created_at (created_at),
    FULLTEXT idx_blog_authors_search (name, bio)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;