-- Create blog_categories table with nested set support
CREATE TABLE IF NOT EXISTS blog_categories (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    slug VARCHAR(255) NOT NULL,
    name VARCHA<PERSON>(255) NOT NULL,
    description TEXT,
    
    -- Nested set fields
    parent_id INT UNSIGNED NULL,
    lft INT NOT NULL DEFAULT 1,
    rgt INT NOT NULL DEFAULT 2,
    depth INT NOT NULL DEFAULT 0,
    children_count INT NOT NULL DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    
    -- Foreign keys
    CONSTRAINT fk_blog_categories_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_categories_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_categories_parent FOREIGN KEY (parent_id) REFERENCES blog_categories(id) ON DELETE CASCADE,
    
    -- Unique constraints
    UNIQUE KEY uk_blog_categories_tenant_website_slug (tenant_id, website_id, slug),
    
    -- Indexes for performance
    INDEX idx_blog_categories_tenant_id (tenant_id),
    INDEX idx_blog_categories_website_id (website_id),
    INDEX idx_blog_categories_tenant_website (tenant_id, website_id),
    INDEX idx_blog_categories_website_status (website_id, status),
    INDEX idx_blog_categories_parent (parent_id),
    
    -- Nested set indexes (critical for performance)
    INDEX idx_blog_categories_lft (lft),
    INDEX idx_blog_categories_rgt (rgt),
    INDEX idx_blog_categories_lft_rgt (lft, rgt),
    
    -- Multi-tenant nested set queries
    INDEX idx_blog_categories_tenant_lft_rgt (tenant_id, website_id, lft, rgt),
    INDEX idx_blog_categories_tenant_parent (tenant_id, website_id, parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;