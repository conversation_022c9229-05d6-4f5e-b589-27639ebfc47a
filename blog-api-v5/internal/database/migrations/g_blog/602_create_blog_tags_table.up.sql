-- Create blog_tags table
CREATE TABLE IF NOT EXISTS blog_tags (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    slug VARCHAR(255) NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#000000',
    usage_count INT UNSIGNED DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    
    CONSTRAINT fk_blog_tags_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_tags_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_tags_tenant_website_slug (tenant_id, website_id, slug),
    INDEX idx_blog_tags_tenant_id (tenant_id),
    INDEX idx_blog_tags_website_id (website_id),
    INDEX idx_blog_tags_tenant_website (tenant_id, website_id),
    INDEX idx_blog_tags_website_status (website_id, status),
    INDEX idx_blog_tags_usage_count (usage_count),
    INDEX idx_blog_tags_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;