-- Create blog_posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    slug VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT NOT NULL,
    excerpt TEXT,
    user_id INT UNSIGNED NOT NULL,
    type ENUM('post', 'page', 'announcement') NOT NULL DEFAULT 'post',
    is_featured BOOLEAN DEFAULT FALSE,
    is_sticky BOOLEAN DEFAULT FALSE,
    allow_comments BOOLEAN DEFAULT TRUE,
    password VARCHAR(255) NULL,
    featured_image VARCHAR(500) NULL,
    view_count INT UNSIGNED DEFAULT 0,
    comment_count INT UNSIGNED DEFAULT 0,
    scheduled_at TIMESTAMP NULL,
    published_at TIMESTAMP NULL,
    seo_metadata JSON DEFAULT (JSO<PERSON>_OBJECT()),
    related_post_ids JSON DEFAULT (JSON_ARRAY()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('draft', 'published', 'archived', 'scheduled', 'deleted') NOT NULL DEFAULT 'draft',
    workflow_state VARCHAR(50) DEFAULT 'creation',
    workflow_assigned_to INT UNSIGNED DEFAULT NULL,
    workflow_assigned_at TIMESTAMP NULL,
    workflow_due_at TIMESTAMP NULL,
    workflow_notes TEXT DEFAULT NULL,
    
    CONSTRAINT fk_blog_posts_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_posts_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_posts_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_posts_workflow_assigned_to FOREIGN KEY (workflow_assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_blog_posts_tenant_website_slug (tenant_id, website_id, slug),
    INDEX idx_blog_posts_tenant_id (tenant_id),
    INDEX idx_blog_posts_tenant_website (tenant_id, website_id),
    INDEX idx_blog_posts_tenant_status (tenant_id, status),
    INDEX idx_blog_posts_tenant_published (tenant_id, published_at),
    INDEX idx_blog_posts_tenant_user (tenant_id, user_id),
    INDEX idx_blog_posts_website_status (website_id, status),
    INDEX idx_blog_posts_published_at (published_at),
    INDEX idx_blog_posts_user (user_id),
    INDEX idx_blog_posts_featured_status (is_featured, status),
    INDEX idx_blog_posts_type (type),
    INDEX idx_blog_posts_scheduled (scheduled_at),
    INDEX idx_blog_posts_workflow_state (tenant_id, workflow_state),
    INDEX idx_blog_posts_workflow_assigned (tenant_id, workflow_assigned_to),
    INDEX idx_blog_posts_workflow_due (tenant_id, workflow_due_at),
    INDEX idx_blog_posts_workflow_status_state (tenant_id, status, workflow_state),
    FULLTEXT idx_blog_posts_search (title, content, excerpt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;