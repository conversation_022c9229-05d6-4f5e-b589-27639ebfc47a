-- Create blog_post_tags table
CREATE TABLE IF NOT EXISTS blog_post_tags (
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    tag_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (tenant_id, website_id, post_id, tag_id),
    CONSTRAINT fk_blog_post_tags_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_tags_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_tags_post FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_tags_tag FOREIGN KEY (tag_id) REFERENCES blog_tags(id) ON DELETE CASCADE,
    INDEX idx_blog_post_tags_tenant_id (tenant_id),
    INDEX idx_blog_post_tags_website_id (website_id),
    INDEX idx_blog_post_tags_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_tags_post (post_id),
    INDEX idx_blog_post_tags_tag (tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;