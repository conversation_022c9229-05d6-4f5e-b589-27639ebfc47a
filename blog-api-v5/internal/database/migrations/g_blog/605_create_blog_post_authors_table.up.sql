-- Create blog_post_authors table
CREATE TABLE IF NOT EXISTS blog_post_authors (
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    role ENUM('primary', 'co-author', 'contributor', 'editor') NOT NULL DEFAULT 'primary',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY <PERSON>E<PERSON> (tenant_id, website_id, post_id, user_id),
    CONSTRAINT fk_blog_post_authors_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_authors_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_authors_post FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_authors_user FOREIG<PERSON> (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_blog_post_authors_tenant_id (tenant_id),
    INDEX idx_blog_post_authors_website_id (website_id),
    INDEX idx_blog_post_authors_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_authors_post (post_id),
    INDEX idx_blog_post_authors_user (user_id),
    INDEX idx_blog_post_authors_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;