-- Create blog_post_categories table for many-to-many relationship
CREATE TABLE IF NOT EXISTS blog_post_categories (
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    category_id INT UNSIGNED NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE COMMENT 'Indicates if this is the primary category',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (tenant_id, website_id, post_id, category_id),
    CONSTRAINT fk_blog_post_categories_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_categories_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_categories_post FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_categories_category FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE CASCADE,
    INDEX idx_blog_post_categories_tenant_id (tenant_id),
    INDEX idx_blog_post_categories_website_id (website_id),
    INDEX idx_blog_post_categories_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_categories_post (post_id),
    INDEX idx_blog_post_categories_category (category_id),
    INDEX idx_blog_post_categories_primary (tenant_id, website_id, post_id, is_primary)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;