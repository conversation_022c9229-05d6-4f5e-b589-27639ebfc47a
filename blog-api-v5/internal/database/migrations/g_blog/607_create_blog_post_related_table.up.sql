-- Create blog_post_related table
CREATE TABLE IF NOT EXISTS blog_post_related (
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    related_post_id INT UNSIGNED NOT NULL,
    relation_type ENUM('similar_content', 'same_category', 'shared_tags', 'sequential', 'manual', 'featured', 'series', 'follow-up') NOT NULL DEFAULT 'similar_content',
    relevance_score FLOAT DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (tenant_id, website_id, post_id, related_post_id),
    CONSTRAINT fk_blog_post_related_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_related_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_related_post FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_related_related_post FOREIGN KEY (related_post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    INDEX idx_blog_post_related_tenant_id (tenant_id),
    INDEX idx_blog_post_related_website_id (website_id),
    INDEX idx_blog_post_related_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_related_post (post_id),
    INDEX idx_blog_post_related_related_post (related_post_id),
    INDEX idx_blog_post_related_type (relation_type),
    INDEX idx_blog_post_related_score (relevance_score DESC),
    CHECK (post_id != related_post_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;