-- Create blog_post_autosaves table for auto-saving blog post drafts
CREATE TABLE IF NOT EXISTS blog_post_autosaves (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    
    -- Auto-saved content
    title VARCHAR(255),
    content LONGTEXT,
    excerpt TEXT,
    featured_image VARCHAR(500),
    
    -- Metadata
    version INT UNSIGNED NOT NULL DEFAULT 1,
    is_conflicted BOOLEAN DEFAULT FALSE,
    last_saved_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_blog_post_autosaves_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_autosaves_post_id FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_autosaves_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_autosaves_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    
    -- Unique constraint: one auto-save per post per user
    UNIQUE KEY uk_blog_post_autosaves_post_user (post_id, user_id),
    
    -- Indexes for performance
    INDEX idx_blog_post_autosaves_tenant_id (tenant_id),
    INDEX idx_blog_post_autosaves_post_id (post_id),
    INDEX idx_blog_post_autosaves_user_id (user_id),
    INDEX idx_blog_post_autosaves_website_id (website_id),
    INDEX idx_blog_post_autosaves_is_conflicted (is_conflicted),
    INDEX idx_blog_post_autosaves_last_saved_at (last_saved_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;