-- Migration: 610_create_blog_post_reading_progress_table
-- Description: Create the reading progress tracking table for blog posts (MySQL 8)
-- Author: System
-- Date: 2025-07-25

CREATE TABLE IF NOT EXISTS blog_post_reading_progress (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    
    -- Post and User Information
    blog_post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Reading Progress Data
    reading_progress_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Reading progress as percentage (0.00 to 100.00)',
    time_spent_seconds INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Total time spent reading in seconds',
    last_read_position INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Last character position read in the content',
    scroll_depth_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT 'Maximum scroll depth reached as percentage',
    
    -- Reading Session Data
    session_count INT UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Number of reading sessions',
    first_read_at TIMESTAMP NULL DEFAULT NULL COMMENT 'First time the user started reading',
    last_read_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Last time progress was updated',
    
    -- Reading Behavior Analytics
    is_completed BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether the post has been fully read',
    completed_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When the post was marked as completed',
    read_speed_wpm DECIMAL(6,2) NULL DEFAULT NULL COMMENT 'Estimated reading speed in words per minute',
    
    -- Device and Context
    device_type VARCHAR(20) NOT NULL DEFAULT 'unknown' COMMENT 'Device type: mobile, desktop, tablet',
    ip_address VARCHAR(45) NULL COMMENT 'IP address of the reader',
    user_agent TEXT NULL COMMENT 'User agent string',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    CONSTRAINT fk_blog_post_reading_progress_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_reading_progress_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_reading_progress_blog_post_id FOREIGN KEY (blog_post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_reading_progress_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints (one progress record per user per post)
    UNIQUE KEY uk_blog_post_reading_progress_user_post (tenant_id, website_id, blog_post_id, user_id),
    
    -- Indexes for Performance
    INDEX idx_blog_post_reading_progress_tenant_id (tenant_id),
    INDEX idx_blog_post_reading_progress_website_id (website_id),
    INDEX idx_blog_post_reading_progress_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_reading_progress_blog_post_id (blog_post_id),
    INDEX idx_blog_post_reading_progress_user_id (user_id),
    INDEX idx_blog_post_reading_progress_is_completed (is_completed),
    INDEX idx_blog_post_reading_progress_last_read_at (last_read_at),
    INDEX idx_blog_post_reading_progress_reading_progress (reading_progress_percentage),
    
    -- Check Constraints
    CONSTRAINT chk_blog_post_reading_progress_percentage CHECK (reading_progress_percentage >= 0.00 AND reading_progress_percentage <= 100.00),
    CONSTRAINT chk_blog_post_reading_progress_scroll_depth CHECK (scroll_depth_percentage >= 0.00 AND scroll_depth_percentage <= 100.00),
    CONSTRAINT chk_blog_post_reading_progress_device_type CHECK (device_type IN ('mobile', 'desktop', 'tablet', 'unknown')),
    CONSTRAINT chk_blog_post_reading_progress_session_count CHECK (session_count >= 1),
    CONSTRAINT chk_blog_post_reading_progress_time_spent CHECK (time_spent_seconds >= 0),
    CONSTRAINT chk_blog_post_reading_progress_read_speed CHECK (read_speed_wpm IS NULL OR read_speed_wpm > 0)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Reading progress tracking for blog posts';