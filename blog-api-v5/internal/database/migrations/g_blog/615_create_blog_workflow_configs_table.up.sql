-- Create blog_workflow_configs table
CREATE TABLE IF NOT EXISTS blog_workflow_configs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    workflow_type VARCHAR(50) DEFAULT 'standard',
    role_mappings JSON DEFAULT (JSON_OBJECT()),
    state_transitions JSON DEFAULT (JSON_OBJECT()),
    auto_assignments JSON DEFAULT (JSON_OBJECT()),
    notifications JSON DEFAULT (JSON_OBJECT()),
    sla_settings JSON DEFAULT (JSON_OBJECT()),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_blog_workflow_configs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_workflow_configs_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_workflow_configs_tenant_website (tenant_id, website_id),
    INDEX idx_blog_workflow_configs_tenant_id (tenant_id),
    INDEX idx_blog_workflow_configs_website_id (website_id),
    INDEX idx_blog_workflow_configs_tenant_website (tenant_id, website_id),
    INDEX idx_blog_workflow_configs_active (is_active)
);