-- Create blog_workflow_logs table
CREATE TABLE IF NOT EXISTS blog_workflow_logs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    action VARCHAR(50) NOT NULL,
    from_status VARCHAR(50) DEFAULT NULL,
    to_status VARCHAR(50) DEFAULT NULL,
    from_workflow_state VARCHAR(50) DEFAULT NULL,
    to_workflow_state VARCHAR(50) DEFAULT NULL,
    reason TEXT DEFAULT NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_blog_workflow_logs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_workflow_logs_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_workflow_logs_post_id FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_workflow_logs_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_blog_workflow_logs_tenant_id (tenant_id),
    INDEX idx_blog_workflow_logs_website_id (website_id),
    INDEX idx_blog_workflow_logs_tenant_website (tenant_id, website_id),
    INDEX idx_blog_workflow_logs_post_id (tenant_id, post_id),
    INDEX idx_blog_workflow_logs_user_id (tenant_id, user_id),
    INDEX idx_blog_workflow_logs_action (tenant_id, action),
    INDEX idx_blog_workflow_logs_created_at (tenant_id, created_at DESC),
    INDEX idx_blog_workflow_logs_workflow_state (tenant_id, from_workflow_state, to_workflow_state)
);