-- Create blog_post_templates table for reusable blog post templates
CREATE TABLE IF NOT EXISTS blog_post_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED DEFAULT NULL,
    website_id INT UNSIGNED DEFAULT NULL,
    created_by INT UNSIGNED NOT NULL,
    
    -- Basic Information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL DEFAULT 'standard',
    scope VARCHAR(20) NOT NULL DEFAULT 'user',
    
    -- Template Configuration
    icon VARCHAR(100),
    color VARCHAR(7),
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    
    -- Template Content
    title_template VARCHAR(500),
    content_template LONGTEXT,
    excerpt_template TEXT,
    structure JSON DEFAULT (JSON_OBJECT()),
    
    -- Default Settings
    default_type VARCHAR(20),
    default_status VARCHAR(20),
    default_category_id INT UNSIGNED,
    default_tags JSON,
    default_allow_comments BOOLEAN DEFAULT TRUE,
    
        -- Usage Statistics
    usage_count INT UNSIGNED DEFAULT 0,
    last_used_at TIMESTAMP NULL,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_blog_post_templates_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_templates_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_templates_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_templates_default_category_id FOREIGN KEY (default_category_id) REFERENCES blog_categories(id) ON DELETE SET NULL,
    
    -- Unique constraint for slug within scope
    UNIQUE KEY uk_blog_post_templates_slug_scope (slug, scope, tenant_id, website_id),
    
    -- Indexes for performance
    INDEX idx_blog_post_templates_tenant_id (tenant_id),
    INDEX idx_blog_post_templates_website_id (website_id),
    INDEX idx_blog_post_templates_created_by (created_by),
    INDEX idx_blog_post_templates_type (type),
    INDEX idx_blog_post_templates_scope (scope),
    INDEX idx_blog_post_templates_is_active (is_active),
    INDEX idx_blog_post_templates_is_featured (is_featured),
    INDEX idx_blog_post_templates_usage_count (usage_count),
    INDEX idx_blog_post_templates_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;