-- Create blog_post_ratings table
CREATE TABLE IF NOT EXISTS blog_post_ratings (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    rating TINYINT UNSIGNED NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_blog_post_ratings_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_ratings_website_id FOREI<PERSON><PERSON> KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_ratings_post_id FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_ratings_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_blog_post_ratings_tenant_website_post_user (tenant_id, website_id, post_id, user_id),
    INDEX idx_blog_post_ratings_tenant_id (tenant_id),
    INDEX idx_blog_post_ratings_website_id (website_id),
    INDEX idx_blog_post_ratings_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_ratings_post_id (post_id),
    INDEX idx_blog_post_ratings_user_id (user_id),
    INDEX idx_blog_post_ratings_tenant_post (tenant_id, post_id),
    INDEX idx_blog_post_ratings_rating (rating),
    INDEX idx_blog_post_ratings_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;