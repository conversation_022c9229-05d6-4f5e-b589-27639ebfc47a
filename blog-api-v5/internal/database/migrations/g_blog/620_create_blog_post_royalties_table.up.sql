CREATE TABLE IF NOT EXISTS blog_post_royalties (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    author_id INT UNSIGNED NOT NULL,
    word_count INT UNSIGNED DEFAULT 0,
    view_count INT UNSIGNED DEFAULT 0,
    base_rate DECIMAL(10,2) DEFAULT 0.00,
    category_coefficient DECIMAL(3,2) DEFAULT 1.00,
    content_type_coefficient DECIMAL(3,2) DEFAULT 1.00,
    quality_coefficient DECIMAL(3,2) DEFAULT 1.00,
    cms_royalty DECIMAL(10,2) DEFAULT 0.00,
    editor_royalty DECIMAL(10,2) DEFAULT 0.00,
    final_royalty DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) DEFAULT 0.00,
    payment_status ENUM('pending', 'approved', 'paid', 'cancelled', 'hold') NOT NULL DEFAULT 'pending',
    notes TEXT NULL,
    calculated_by INT UNSIGNED NULL,
    calculated_at TIMESTAMP NULL,
    approved_by INT UNSIGNED NULL,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_post_royalties_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_post_royalties_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_post_royalties_post_id FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_post_royalties_author_id FOREIGN KEY (author_id) REFERENCES blog_authors(id) ON DELETE CASCADE,
    CONSTRAINT fk_post_royalties_calculated_by FOREIGN KEY (calculated_by) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_post_royalties_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_post_royalties_tenant_post (tenant_id, post_id),
    INDEX idx_post_royalties_tenant_id (tenant_id),
    INDEX idx_post_royalties_website_id (website_id),
    INDEX idx_post_royalties_post_id (post_id),
    INDEX idx_post_royalties_author_id (author_id),
    INDEX idx_post_royalties_payment_status (payment_status),
    INDEX idx_post_royalties_tenant_status (tenant_id, payment_status),
    INDEX idx_post_royalties_author_status (author_id, payment_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;