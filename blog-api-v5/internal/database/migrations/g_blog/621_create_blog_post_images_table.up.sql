CREATE TABLE IF NOT EXISTS blog_post_images (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    blog_post_id INT UNSIGNED NOT NULL,
    media_file_id INT UNSIGNED NOT NULL,
    usage_context VARCHAR(50) NOT NULL DEFAULT 'content' COMMENT 'content, featured, thumbnail, gallery',
    display_order INT UNSIGNED DEFAULT 0,
    caption TEXT COMMENT 'Image caption specific to this usage',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_blog_post_images_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_images_website_id FOREIG<PERSON> KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_images_blog_post_id FOREIGN KEY (blog_post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_blog_post_images_media_file_id FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    -- Note: This foreign key will be added after media_files table is created in migration 802
    
    UNIQUE KEY uk_blog_post_images_post_media_context (blog_post_id, media_file_id, usage_context),
    INDEX idx_blog_post_images_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_images_post_id (blog_post_id),
    INDEX idx_blog_post_images_media_id (media_file_id),
    INDEX idx_blog_post_images_usage_context (usage_context)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;