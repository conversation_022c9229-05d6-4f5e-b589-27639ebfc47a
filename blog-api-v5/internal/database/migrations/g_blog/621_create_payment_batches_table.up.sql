-- Create payment_batches table for managing royalty payment batches
CREATE TABLE IF NOT EXISTS payment_batches (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    batch_number VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    from_date DATE NOT NULL,
    to_date DATE NOT NULL,
    total_amount DECIMAL(12,2) DEFAULT 0.00,
    payment_count INT UNSIGNED DEFAULT 0,
    status ENUM('draft', 'pending_approval', 'approved', 'processing', 'completed', 'cancelled') NOT NULL DEFAULT 'draft',
    notes TEXT NULL,
    created_by INT UNSIGNED NOT NULL,
    approved_by INT UNSIGNED NULL,
    approved_at TIMESTAMP NULL,
    processed_by INT UNSIGNED NULL,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    cancelled_by INT UNSIGNED NULL,
    cancelled_at TIMESTAMP NULL,
    cancellation_reason TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_payment_batches_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_payment_batches_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_payment_batches_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    CONSTRAINT fk_payment_batches_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_payment_batches_processed_by FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_payment_batches_cancelled_by FOREIGN KEY (cancelled_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_payment_batches_batch_number (tenant_id, website_id, batch_number),
    INDEX idx_payment_batches_tenant_id (tenant_id),
    INDEX idx_payment_batches_website_id (website_id),
    INDEX idx_payment_batches_tenant_website (tenant_id, website_id),
    INDEX idx_payment_batches_status (status),
    INDEX idx_payment_batches_tenant_status (tenant_id, status),
    INDEX idx_payment_batches_date_range (from_date, to_date),
    INDEX idx_payment_batches_created_at (created_at),
    INDEX idx_payment_batches_approved_at (approved_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;