-- Create author_royalty_payments table for tracking individual royalty payments
CREATE TABLE IF NOT EXISTS author_royalty_payments (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    royalty_id INT UNSIGNED NOT NULL,
    batch_id INT UNSIGNED NULL,
    author_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    amount_calculated DECIMAL(12,2) NOT NULL,
    amount_paid DECIMAL(12,2) NOT NULL,
    payment_method ENUM('bank_transfer', 'cash', 'check', 'e_wallet', 'other') NOT NULL DEFAULT 'bank_transfer',
    transaction_reference VARCHAR(100) NULL,
    bank_account VARCHAR(50) NULL,
    bank_name VARCHAR(255) NULL,
    payment_date DATE NOT NULL,
    payment_notes TEXT NULL,
    receipt_url VARCHAR(500) NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by INT UNSIGNED NULL,
    verified_at TIMESTAMP NULL,
    paid_by INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_royalty_payments_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_royalty_payments_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_royalty_payments_royalty_id FOREIGN KEY (royalty_id) REFERENCES blog_post_royalties(id) ON DELETE CASCADE,
    CONSTRAINT fk_royalty_payments_batch_id FOREIGN KEY (batch_id) REFERENCES payment_batches(id) ON DELETE SET NULL,
    CONSTRAINT fk_royalty_payments_author_id FOREIGN KEY (author_id) REFERENCES blog_authors(id) ON DELETE CASCADE,
    CONSTRAINT fk_royalty_payments_post_id FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_royalty_payments_paid_by FOREIGN KEY (paid_by) REFERENCES users(id) ON DELETE RESTRICT,
    CONSTRAINT fk_royalty_payments_verified_by FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_royalty_payments_royalty (royalty_id),
    INDEX idx_royalty_payments_tenant_id (tenant_id),
    INDEX idx_royalty_payments_website_id (website_id),
    INDEX idx_royalty_payments_tenant_website (tenant_id, website_id),
    INDEX idx_royalty_payments_batch_id (batch_id),
    INDEX idx_royalty_payments_author_id (author_id),
    INDEX idx_royalty_payments_post_id (post_id),
    INDEX idx_royalty_payments_payment_date (payment_date),
    INDEX idx_royalty_payments_payment_method (payment_method),
    INDEX idx_royalty_payments_is_verified (is_verified),
    INDEX idx_royalty_payments_tenant_author (tenant_id, author_id),
    INDEX idx_royalty_payments_tenant_batch (tenant_id, batch_id),
    INDEX idx_royalty_payments_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;