-- Create royalty_rate_configs table for managing royalty rates and coefficients
CREATE TABLE IF NOT EXISTS royalty_rate_configs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    config_type ENUM('base_rate', 'category_multiplier', 'content_type_multiplier', 'quality_multiplier', 'bonus_rate') NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_name VARCHAR(255) NOT NULL,
    config_value DECIMAL(10,4) NOT NULL,
    min_value DECIMAL(10,4) NULL,
    max_value DECIMAL(10,4) NULL,
    unit VARCHAR(20) NULL COMMENT 'VND/word, percentage, multiplier, etc.',
    description TEXT NULL,
    conditions JSON DEFAULT (JSON_OBJECT()) COMMENT 'Additional conditions for applying this rate',
    effective_from DATE NOT NULL,
    effective_to DATE NULL,
    is_active BOOLEAN DEFAULT TRUE,
    priority INT UNSIGNED DEFAULT 0 COMMENT 'Higher priority configs override lower ones',
    created_by INT UNSIGNED NOT NULL,
    updated_by INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_rate_configs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_rate_configs_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_rate_configs_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    CONSTRAINT fk_rate_configs_updated_by FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_rate_configs_key (tenant_id, website_id, config_type, config_key, effective_from),
    INDEX idx_rate_configs_tenant_id (tenant_id),
    INDEX idx_rate_configs_website_id (website_id),
    INDEX idx_rate_configs_tenant_website (tenant_id, website_id),
    INDEX idx_rate_configs_config_type (config_type),
    INDEX idx_rate_configs_config_key (config_key),
    INDEX idx_rate_configs_is_active (is_active),
    INDEX idx_rate_configs_effective_dates (effective_from, effective_to),
    INDEX idx_rate_configs_tenant_type_active (tenant_id, config_type, is_active),
    INDEX idx_rate_configs_priority (priority DESC),
    CHECK (effective_to IS NULL OR effective_to > effective_from),
    CHECK (min_value IS NULL OR max_value IS NULL OR min_value <= max_value)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

