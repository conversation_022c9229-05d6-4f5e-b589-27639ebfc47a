-- Create blog_post_activities table for activity logging and audit trail
CREATE TABLE IF NOT EXISTS blog_post_activities (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    action ENUM('created', 'updated', 'deleted', 'published', 'unpublished', 'scheduled', 'archived') NOT NULL,
    field_name VARCHAR(100) NULL COMMENT 'Specific field name for update actions',
    old_value TEXT NULL COMMENT 'Previous value before the change',
    new_value TEXT NULL COMMENT 'New value after the change',
    ip_address VARCHAR(45) NULL COMMENT 'User IP address (IPv6 compatible)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for query performance
    INDEX idx_blog_post_activities_post_id_created (post_id, created_at DESC),
    INDEX idx_blog_post_activities_tenant_website (tenant_id, website_id),
    INDEX idx_blog_post_activities_user_created (user_id, created_at DESC),
    INDEX idx_blog_post_activities_action_created (action, created_at DESC),
    
    -- Foreign key constraints
    CONSTRAINT fk_blog_post_activities_tenant_id 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_activities_website_id 
        FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_activities_post_id 
        FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    CONSTRAINT fk_blog_post_activities_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Activity log for blog post actions and changes';