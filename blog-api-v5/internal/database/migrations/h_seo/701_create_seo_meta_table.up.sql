-- Migration: 701_create_seo_meta_table.up.sql
-- Description: Create seo_meta table for website SEO metadata management
-- Module: h_seo
-- Dependencies: websites table from b_website module

CREATE TABLE IF NOT EXISTS `seo_meta` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `website_id` INT UNSIGNED NOT NULL,
    `tenant_id` INT UNSIGNED NOT NULL,
    
    -- Page Information
    `page_type` VARCHAR(50) NOT NULL DEFAULT 'page' COMMENT 'Type of page (page, post, category, product, etc.)',
    `page_id` INT UNSIGNED NULL COMMENT 'ID of the associated page/post/category',
    `page_url` VARCHAR(500) NOT NULL COMMENT 'Full URL of the page',
    `page_path` VARCHAR(500) NOT NULL COMMENT 'Path of the page relative to domain',
    
    -- SEO Meta Tags
    `meta_title` VARCHAR(255) NULL COMMENT 'SEO title tag',
    `meta_description` TEXT NULL COMMENT 'SEO description tag',
    `meta_keywords` TEXT NULL COMMENT 'SEO keywords (comma-separated)',
    `meta_robots` VARCHAR(100) NOT NULL DEFAULT 'index,follow' COMMENT 'Robots meta tag directive',
    `canonical_url` VARCHAR(500) NULL COMMENT 'Canonical URL for the page',
    
    -- Open Graph Meta Tags
    `og_title` VARCHAR(255) NULL COMMENT 'Open Graph title',
    `og_description` TEXT NULL COMMENT 'Open Graph description',
    `og_image` VARCHAR(500) NULL COMMENT 'Open Graph image URL',
    `og_type` VARCHAR(50) NOT NULL DEFAULT 'website' COMMENT 'Open Graph type',
    `og_url` VARCHAR(500) NULL COMMENT 'Open Graph URL',
    `og_site_name` VARCHAR(255) NULL COMMENT 'Open Graph site name',
    `og_locale` VARCHAR(10) NOT NULL DEFAULT 'en_US' COMMENT 'Open Graph locale',
    
    -- Twitter Card Meta Tags
    `twitter_card` VARCHAR(50) NOT NULL DEFAULT 'summary' COMMENT 'Twitter card type',
    `twitter_title` VARCHAR(255) NULL COMMENT 'Twitter card title',
    `twitter_description` TEXT NULL COMMENT 'Twitter card description',
    `twitter_image` VARCHAR(500) NULL COMMENT 'Twitter card image URL',
    `twitter_creator` VARCHAR(255) NULL COMMENT 'Twitter creator handle',
    `twitter_site` VARCHAR(255) NULL COMMENT 'Twitter site handle',
    
    -- Schema.org Structured Data
    `schema_type` VARCHAR(100) NULL COMMENT 'Schema.org type (Article, Product, etc.)',
    `schema_data` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Schema.org structured data',
    
    -- Additional Meta Tags
    `additional_meta` JSON DEFAULT (JSON_ARRAY()) COMMENT 'Additional custom meta tags',
    
    -- SEO Settings
    `focus_keyword` VARCHAR(255) NULL COMMENT 'Primary focus keyword for the page',
    `seo_score` DECIMAL(3,1) NOT NULL DEFAULT 0.0 COMMENT 'SEO score (0.0-100.0)',
    `readability_score` DECIMAL(3,1) NOT NULL DEFAULT 0.0 COMMENT 'Readability score (0.0-100.0)',
    
    -- Status and Indexing
    `is_indexed` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether page should be indexed',
    `is_sitemap_included` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether page should be included in sitemap',
    `priority` DECIMAL(2,1) NOT NULL DEFAULT 0.5 COMMENT 'Sitemap priority (0.0-1.0)',
    `change_frequency` ENUM('always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never') NOT NULL DEFAULT 'monthly' COMMENT 'Sitemap change frequency',
    
    -- Status and Timestamps
    `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Status of the SEO meta',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT `fk_seo_meta_website_id` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_meta_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `chk_seo_meta_status` CHECK (`status` IN ('active', 'inactive', 'deleted')),
    CONSTRAINT `chk_seo_meta_page_type` CHECK (`page_type` IN ('page', 'post', 'category', 'tag', 'product', 'custom')),
    CONSTRAINT `chk_seo_meta_seo_score` CHECK (`seo_score` >= 0.0 AND `seo_score` <= 100.0),
    CONSTRAINT `chk_seo_meta_readability_score` CHECK (`readability_score` >= 0.0 AND `readability_score` <= 100.0),
    CONSTRAINT `chk_seo_meta_priority` CHECK (`priority` >= 0.0 AND `priority` <= 1.0),
    
    -- Indexes
    INDEX `idx_seo_meta_website_id` (`website_id`),
    INDEX `idx_seo_meta_tenant_id` (`tenant_id`),
    INDEX `idx_seo_meta_page_type` (`page_type`),
    INDEX `idx_seo_meta_page_id` (`page_id`),
    INDEX `idx_seo_meta_page_url` (`page_url`(191)),
    INDEX `idx_seo_meta_page_path` (`page_path`(191)),
    INDEX `idx_seo_meta_focus_keyword` (`focus_keyword`),
    INDEX `idx_seo_meta_status` (`status`),
    INDEX `idx_seo_meta_is_indexed` (`is_indexed`),
    INDEX `idx_seo_meta_is_sitemap_included` (`is_sitemap_included`),
    INDEX `idx_seo_meta_created_at` (`created_at`),
    INDEX `idx_seo_meta_updated_at` (`updated_at`),
    
    -- Unique Constraints
    UNIQUE KEY `uk_seo_meta_website_page` (`website_id`, `page_type`, `page_id`),
    UNIQUE KEY `uk_seo_meta_website_url` (`website_id`, `page_url`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SEO metadata for website pages';