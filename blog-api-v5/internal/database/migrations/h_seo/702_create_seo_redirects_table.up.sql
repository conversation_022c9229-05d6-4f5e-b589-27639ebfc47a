-- Migration: 702_create_seo_redirects_table.up.sql
-- Description: Create seo_redirects table for managing URL redirects with hit tracking
-- Module: h_seo
-- Dependencies: websites table from b_website module

CREATE TABLE IF NOT EXISTS `seo_redirects` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `website_id` INT UNSIGNED NOT NULL,
    `tenant_id` INT UNSIGNED NOT NULL,
    
    -- Redirect Information
    `source_url` VARCHAR(500) NOT NULL COMMENT 'Original URL to redirect from',
    `source_path` VARCHAR(500) NOT NULL COMMENT 'Original path to redirect from',
    `destination_url` VARCHAR(500) NOT NULL COMMENT 'Destination URL to redirect to',
    `destination_path` VARCHAR(500) NULL COMMENT 'Destination path (if internal redirect)',
    
    -- Redirect Configuration
    `redirect_type` ENUM('301', '302', '303', '307', '308') NOT NULL DEFAULT '301' COMMENT 'HTTP redirect status code',
    `redirect_match` ENUM('exact', 'regex', 'wildcard') NOT NULL DEFAULT 'exact' COMMENT 'How to match the source URL',
    `regex_pattern` VARCHAR(1000) NULL COMMENT 'Regex pattern for regex matching',
    `replacement_pattern` VARCHAR(1000) NULL COMMENT 'Replacement pattern for regex redirects',
    
    -- Conditional Redirects
    `conditions` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Conditional redirect rules (user-agent, referrer, etc.)',
    `query_string_handling` ENUM('ignore', 'preserve', 'append') NOT NULL DEFAULT 'preserve' COMMENT 'How to handle query strings',
    
    -- Tracking and Analytics
    `hit_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of times this redirect was triggered',
    `last_hit_at` TIMESTAMP NULL COMMENT 'Timestamp of the last redirect hit',
    `last_hit_ip` VARCHAR(45) NULL COMMENT 'IP address of the last redirect hit',
    `last_hit_user_agent` TEXT NULL COMMENT 'User agent of the last redirect hit',
    `last_hit_referrer` VARCHAR(500) NULL COMMENT 'Referrer of the last redirect hit',
    
    -- SEO Information
    `seo_reason` VARCHAR(100) NULL COMMENT 'SEO reason for the redirect (moved-permanently, page-removed, etc.)',
    `notes` TEXT NULL COMMENT 'Internal notes about the redirect',
    `created_by` INT UNSIGNED NULL COMMENT 'User ID who created the redirect',
    `updated_by` INT UNSIGNED NULL COMMENT 'User ID who last updated the redirect',
    
    -- Scheduling
    `active_from` TIMESTAMP NULL COMMENT 'When the redirect becomes active',
    `active_until` TIMESTAMP NULL COMMENT 'When the redirect expires',
    
    -- Status and Timestamps
    `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Status of the redirect',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT `fk_seo_redirects_website_id` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_redirects_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_redirects_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_redirects_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `chk_seo_redirects_status` CHECK (`status` IN ('active', 'inactive', 'expired', 'deleted')),
    CONSTRAINT `chk_seo_redirects_hit_count` CHECK (`hit_count` >= 0),
    CONSTRAINT `chk_seo_redirects_active_dates` CHECK (`active_from` IS NULL OR `active_until` IS NULL OR `active_from` <= `active_until`),
    
    -- Indexes
    INDEX `idx_seo_redirects_website_id` (`website_id`),
    INDEX `idx_seo_redirects_tenant_id` (`tenant_id`),
    INDEX `idx_seo_redirects_source_url` (`source_url`(191)),
    INDEX `idx_seo_redirects_source_path` (`source_path`(191)),
    INDEX `idx_seo_redirects_destination_url` (`destination_url`(191)),
    INDEX `idx_seo_redirects_redirect_type` (`redirect_type`),
    INDEX `idx_seo_redirects_redirect_match` (`redirect_match`),
    INDEX `idx_seo_redirects_status` (`status`),
    INDEX `idx_seo_redirects_hit_count` (`hit_count`),
    INDEX `idx_seo_redirects_last_hit_at` (`last_hit_at`),
    INDEX `idx_seo_redirects_active_from` (`active_from`),
    INDEX `idx_seo_redirects_active_until` (`active_until`),
    INDEX `idx_seo_redirects_created_by` (`created_by`),
    INDEX `idx_seo_redirects_updated_by` (`updated_by`),
    INDEX `idx_seo_redirects_created_at` (`created_at`),
    INDEX `idx_seo_redirects_updated_at` (`updated_at`),
    
    -- Unique Constraints
    UNIQUE KEY `uk_seo_redirects_website_source` (`website_id`, `source_url`(191), `status`),
    
    -- Composite Indexes for Performance
    INDEX `idx_seo_redirects_lookup` (`website_id`, `source_path`(191), `status`, `active_from`, `active_until`),
    INDEX `idx_seo_redirects_analytics` (`website_id`, `hit_count`, `last_hit_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='URL redirects for websites with hit tracking';