-- Migration: 703_create_seo_audits_table.up.sql
-- Description: Create seo_audits table for SEO audit results with scoring system
-- Module: h_seo
-- Dependencies: websites table from b_website module

CREATE TABLE IF NOT EXISTS `seo_audits` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `website_id` INT UNSIGNED NOT NULL,
    `tenant_id` INT UNSIGNED NOT NULL,
    
    -- Audit Information
    `audit_type` ENUM('full', 'page', 'technical', 'content', 'performance', 'accessibility') NOT NULL DEFAULT 'full' COMMENT 'Type of SEO audit',
    `audit_name` VARCHAR(255) NOT NULL COMMENT 'Name/title of the audit',
    `audit_description` TEXT NULL COMMENT 'Description of the audit',
    `audit_url` VARCHAR(500) NULL COMMENT 'Specific URL audited (if page audit)',
    
    -- Audit Execution
    `audit_started_at` TIMESTAMP NOT NULL COMMENT 'When the audit started',
    `audit_completed_at` TIMESTAMP NULL COMMENT 'When the audit completed',
    `audit_duration` INT UNSIGNED NULL COMMENT 'Audit duration in seconds',
    `audit_tool` VARCHAR(100) NOT NULL DEFAULT 'internal' COMMENT 'Tool used for the audit',
    `audit_version` VARCHAR(50) NULL COMMENT 'Version of the audit tool',
    
    -- Overall Scores
    `overall_score` DECIMAL(3,1) NOT NULL DEFAULT 0.0 COMMENT 'Overall SEO score (0.0-100.0)',
    `technical_score` DECIMAL(3,1) NOT NULL DEFAULT 0.0 COMMENT 'Technical SEO score (0.0-100.0)',
    `content_score` DECIMAL(3,1) NOT NULL DEFAULT 0.0 COMMENT 'Content SEO score (0.0-100.0)',
    `performance_score` DECIMAL(3,1) NOT NULL DEFAULT 0.0 COMMENT 'Performance score (0.0-100.0)',
    `accessibility_score` DECIMAL(3,1) NOT NULL DEFAULT 0.0 COMMENT 'Accessibility score (0.0-100.0)',
    `user_experience_score` DECIMAL(3,1) NOT NULL DEFAULT 0.0 COMMENT 'User experience score (0.0-100.0)',
    
    -- Detailed Metrics
    `pages_crawled` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of pages crawled',
    `pages_indexed` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of pages indexed',
    `issues_found` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Total number of issues found',
    `critical_issues` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of critical issues',
    `major_issues` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of major issues',
    `minor_issues` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of minor issues',
    `warnings` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of warnings',
    
    -- Performance Metrics
    `load_time` DECIMAL(5,2) NULL COMMENT 'Average page load time in seconds',
    `first_contentful_paint` DECIMAL(5,2) NULL COMMENT 'First Contentful Paint in seconds',
    `largest_contentful_paint` DECIMAL(5,2) NULL COMMENT 'Largest Contentful Paint in seconds',
    `cumulative_layout_shift` DECIMAL(4,3) NULL COMMENT 'Cumulative Layout Shift score',
    `first_input_delay` DECIMAL(5,2) NULL COMMENT 'First Input Delay in milliseconds',
    `total_blocking_time` DECIMAL(5,2) NULL COMMENT 'Total Blocking Time in milliseconds',
    
    -- Technical SEO Metrics
    `mobile_friendly` BOOLEAN NULL COMMENT 'Whether the site is mobile-friendly',
    `https_enabled` BOOLEAN NULL COMMENT 'Whether HTTPS is enabled',
    `xml_sitemap_exists` BOOLEAN NULL COMMENT 'Whether XML sitemap exists',
    `robots_txt_exists` BOOLEAN NULL COMMENT 'Whether robots.txt exists',
    `structured_data_valid` BOOLEAN NULL COMMENT 'Whether structured data is valid',
    `canonical_urls_proper` BOOLEAN NULL COMMENT 'Whether canonical URLs are properly set',
    `meta_titles_optimized` BOOLEAN NULL COMMENT 'Whether meta titles are optimized',
    `meta_descriptions_optimized` BOOLEAN NULL COMMENT 'Whether meta descriptions are optimized',
    `heading_structure_proper` BOOLEAN NULL COMMENT 'Whether heading structure is proper',
    `images_optimized` BOOLEAN NULL COMMENT 'Whether images are optimized',
    `internal_links_healthy` BOOLEAN NULL COMMENT 'Whether internal links are healthy',
    `external_links_healthy` BOOLEAN NULL COMMENT 'Whether external links are healthy',
    
    -- Content Quality Metrics
    `content_uniqueness` DECIMAL(3,1) NULL COMMENT 'Content uniqueness score (0.0-100.0)',
    `keyword_density` DECIMAL(3,1) NULL COMMENT 'Average keyword density',
    `readability_score` DECIMAL(3,1) NULL COMMENT 'Content readability score (0.0-100.0)',
    `word_count_average` INT UNSIGNED NULL COMMENT 'Average word count per page',
    `duplicate_content_issues` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of duplicate content issues',
    `thin_content_pages` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of thin content pages',
    
    -- Audit Results
    `audit_results` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Detailed audit results and recommendations',
    `issues_summary` JSON DEFAULT (JSON_ARRAY()) COMMENT 'Summary of all issues found',
    `recommendations` JSON DEFAULT (JSON_ARRAY()) COMMENT 'SEO recommendations',
    `comparison_data` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Comparison with previous audits',
    
    -- Audit Configuration
    `audit_config` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Configuration used for the audit',
    `crawl_depth` INT UNSIGNED NOT NULL DEFAULT 3 COMMENT 'Crawl depth for the audit',
    `max_pages` INT UNSIGNED NOT NULL DEFAULT 1000 COMMENT 'Maximum pages to audit',
    `include_external_links` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether to check external links',
    `check_images` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether to check images',
    `check_performance` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether to check performance',
    
    -- Status and Metadata
    `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT 'Status of the audit',
    `error_message` TEXT NULL COMMENT 'Error message if audit failed',
    `created_by` INT UNSIGNED NULL COMMENT 'User ID who created the audit',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT `fk_seo_audits_website_id` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_audits_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_audits_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `chk_seo_audits_status` CHECK (`status` IN ('pending', 'running', 'completed', 'failed', 'cancelled', 'deleted')),
    CONSTRAINT `chk_seo_audits_overall_score` CHECK (`overall_score` >= 0.0 AND `overall_score` <= 100.0),
    CONSTRAINT `chk_seo_audits_technical_score` CHECK (`technical_score` >= 0.0 AND `technical_score` <= 100.0),
    CONSTRAINT `chk_seo_audits_content_score` CHECK (`content_score` >= 0.0 AND `content_score` <= 100.0),
    CONSTRAINT `chk_seo_audits_performance_score` CHECK (`performance_score` >= 0.0 AND `performance_score` <= 100.0),
    CONSTRAINT `chk_seo_audits_accessibility_score` CHECK (`accessibility_score` >= 0.0 AND `accessibility_score` <= 100.0),
    CONSTRAINT `chk_seo_audits_user_experience_score` CHECK (`user_experience_score` >= 0.0 AND `user_experience_score` <= 100.0),
    CONSTRAINT `chk_seo_audits_content_uniqueness` CHECK (`content_uniqueness` IS NULL OR (`content_uniqueness` >= 0.0 AND `content_uniqueness` <= 100.0)),
    CONSTRAINT `chk_seo_audits_readability_score` CHECK (`readability_score` IS NULL OR (`readability_score` >= 0.0 AND `readability_score` <= 100.0)),
    CONSTRAINT `chk_seo_audits_audit_times` CHECK (`audit_completed_at` IS NULL OR `audit_started_at` <= `audit_completed_at`),
    CONSTRAINT `chk_seo_audits_counts` CHECK (`pages_crawled` >= 0 AND `pages_indexed` >= 0 AND `issues_found` >= 0),
    CONSTRAINT `chk_seo_audits_issue_counts` CHECK (`critical_issues` >= 0 AND `major_issues` >= 0 AND `minor_issues` >= 0 AND `warnings` >= 0),
    CONSTRAINT `chk_seo_audits_crawl_config` CHECK (`crawl_depth` > 0 AND `max_pages` > 0),
    
    -- Indexes
    INDEX `idx_seo_audits_website_id` (`website_id`),
    INDEX `idx_seo_audits_tenant_id` (`tenant_id`),
    INDEX `idx_seo_audits_audit_type` (`audit_type`),
    INDEX `idx_seo_audits_status` (`status`),
    INDEX `idx_seo_audits_overall_score` (`overall_score`),
    INDEX `idx_seo_audits_audit_started_at` (`audit_started_at`),
    INDEX `idx_seo_audits_audit_completed_at` (`audit_completed_at`),
    INDEX `idx_seo_audits_created_by` (`created_by`),
    INDEX `idx_seo_audits_created_at` (`created_at`),
    INDEX `idx_seo_audits_updated_at` (`updated_at`),
    
    -- Composite Indexes for Performance
    INDEX `idx_seo_audits_website_status` (`website_id`, `status`, `audit_started_at`),
    INDEX `idx_seo_audits_website_type` (`website_id`, `audit_type`, `audit_started_at`),
    INDEX `idx_seo_audits_scores` (`website_id`, `overall_score`, `technical_score`, `content_score`),
    INDEX `idx_seo_audits_performance` (`website_id`, `performance_score`, `load_time`, `audit_started_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SEO audits with comprehensive scoring system';