-- Migration: 704_create_seo_sitemaps_table.up.sql
-- Description: Create seo_sitemaps table for sitemap file management
-- Module: h_seo
-- Dependencies: websites table from b_website module

CREATE TABLE IF NOT EXISTS `seo_sitemaps` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `website_id` INT UNSIGNED NOT NULL,
    `tenant_id` INT UNSIGNED NOT NULL,
    
    -- Sitemap Information
    `sitemap_name` VARCHAR(255) NOT NULL COMMENT 'Name of the sitemap',
    `sitemap_type` ENUM('xml', 'html', 'rss', 'news', 'video', 'image', 'mobile') NOT NULL DEFAULT 'xml' COMMENT 'Type of sitemap',
    `sitemap_url` VARCHAR(500) NOT NULL COMMENT 'URL where the sitemap is accessible',
    `sitemap_path` VARCHAR(500) NOT NULL COMMENT 'File path of the sitemap',
    `sitemap_filename` VARCHA<PERSON>(255) NOT NULL COMMENT 'Filename of the sitemap',
    
    -- Sitemap Content
    `content_type` ENUM('static', 'dynamic', 'mixed') NOT NULL DEFAULT 'dynamic' COMMENT 'How sitemap content is generated',
    `include_pages` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Include regular pages',
    `include_posts` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Include blog posts',
    `include_categories` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Include categories',
    `include_tags` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Include tags',
    `include_archives` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Include archive pages',
    `include_images` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Include image references',
    `include_videos` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Include video references',
    
    -- Sitemap Configuration
    `max_entries` INT UNSIGNED NOT NULL DEFAULT 50000 COMMENT 'Maximum number of entries',
    `max_file_size` INT UNSIGNED NOT NULL DEFAULT 52428800 COMMENT 'Maximum file size in bytes (50MB)',
    `compression_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Enable gzip compression',
    `split_large_sitemaps` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Split large sitemaps into multiple files',
    `update_frequency` ENUM('always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never') NOT NULL DEFAULT 'daily' COMMENT 'How often sitemap is updated',
    `priority_calculation` ENUM('manual', 'auto', 'mixed') NOT NULL DEFAULT 'auto' COMMENT 'How to calculate URL priorities',
    
    -- Content Filters
    `exclude_patterns` JSON DEFAULT (JSON_ARRAY()) COMMENT 'URL patterns to exclude from sitemap',
    `include_patterns` JSON DEFAULT (JSON_ARRAY()) COMMENT 'URL patterns to include in sitemap',
    `min_word_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Minimum word count for inclusion',
    `exclude_no_index` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Exclude pages with noindex meta tag',
    `exclude_password_protected` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Exclude password-protected pages',
    `exclude_drafts` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Exclude draft content',
    
    -- Sitemap Statistics
    `total_entries` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Total number of entries in sitemap',
    `file_size` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Current file size in bytes',
    `last_entry_added` TIMESTAMP NULL COMMENT 'When last entry was added',
    `last_entry_removed` TIMESTAMP NULL COMMENT 'When last entry was removed',
    
    -- Generation Information
    `last_generated_at` TIMESTAMP NULL COMMENT 'When sitemap was last generated',
    `generation_duration` INT UNSIGNED NULL COMMENT 'Generation duration in seconds',
    `generation_method` ENUM('manual', 'scheduled', 'triggered', 'webhook') NOT NULL DEFAULT 'scheduled' COMMENT 'How sitemap was generated',
    `next_generation_at` TIMESTAMP NULL COMMENT 'When sitemap should be generated next',
    
    -- Validation and Submission
    `is_valid` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether sitemap is valid XML',
    `validation_errors` JSON DEFAULT (JSON_ARRAY()) COMMENT 'XML validation errors',
    `submitted_to_google` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether submitted to Google Search Console',
    `submitted_to_bing` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether submitted to Bing Webmaster Tools',
    `submitted_to_yandex` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether submitted to Yandex Webmaster',
    `last_submission_at` TIMESTAMP NULL COMMENT 'When last submitted to search engines',
    `submission_response` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Response from search engine submissions',
    
    -- Search Engine Indexing
    `google_indexed_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of URLs indexed by Google',
    `bing_indexed_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of URLs indexed by Bing',
    `yandex_indexed_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of URLs indexed by Yandex',
    `last_index_check_at` TIMESTAMP NULL COMMENT 'When index status was last checked',
    
    -- File Management
    `file_exists` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether sitemap file exists on disk',
    `file_readable` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether sitemap file is readable',
    `file_permissions` VARCHAR(10) NULL COMMENT 'File permissions (e.g., 644)',
    `backup_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether to backup old sitemaps',
    `backup_retention_days` INT UNSIGNED NOT NULL DEFAULT 30 COMMENT 'Days to retain backup files',
    
    -- Sitemap Index
    `is_index_sitemap` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this is a sitemap index file',
    `parent_sitemap_id` INT UNSIGNED NULL COMMENT 'Parent sitemap ID (if this is a child sitemap)',
    `child_sitemaps_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of child sitemaps',
    
    -- Custom Settings
    `custom_settings` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Custom sitemap settings',
    `template_id` INT UNSIGNED NULL COMMENT 'Template used for sitemap generation',
    `notes` TEXT NULL COMMENT 'Internal notes about the sitemap',
    
    -- Status and Metadata
    `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Status of the sitemap',
    `created_by` INT UNSIGNED NULL COMMENT 'User ID who created the sitemap',
    `updated_by` INT UNSIGNED NULL COMMENT 'User ID who last updated the sitemap',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT `fk_seo_sitemaps_website_id` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_sitemaps_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_sitemaps_parent_sitemap_id` FOREIGN KEY (`parent_sitemap_id`) REFERENCES `seo_sitemaps` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_sitemaps_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_sitemaps_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `chk_seo_sitemaps_status` CHECK (`status` IN ('active', 'inactive', 'generating', 'failed', 'deleted')),
    CONSTRAINT `chk_seo_sitemaps_max_entries` CHECK (`max_entries` > 0 AND `max_entries` <= 50000),
    CONSTRAINT `chk_seo_sitemaps_max_file_size` CHECK (`max_file_size` > 0),
    CONSTRAINT `chk_seo_sitemaps_total_entries` CHECK (`total_entries` >= 0),
    CONSTRAINT `chk_seo_sitemaps_file_size` CHECK (`file_size` >= 0),
    CONSTRAINT `chk_seo_sitemaps_generation_duration` CHECK (`generation_duration` IS NULL OR `generation_duration` >= 0),
    CONSTRAINT `chk_seo_sitemaps_indexed_counts` CHECK (`google_indexed_count` >= 0 AND `bing_indexed_count` >= 0 AND `yandex_indexed_count` >= 0),
    CONSTRAINT `chk_seo_sitemaps_backup_retention` CHECK (`backup_retention_days` > 0),
    CONSTRAINT `chk_seo_sitemaps_child_count` CHECK (`child_sitemaps_count` >= 0),
    
    -- Indexes
    INDEX `idx_seo_sitemaps_website_id` (`website_id`),
    INDEX `idx_seo_sitemaps_tenant_id` (`tenant_id`),
    INDEX `idx_seo_sitemaps_sitemap_type` (`sitemap_type`),
    INDEX `idx_seo_sitemaps_sitemap_url` (`sitemap_url`(191)),
    INDEX `idx_seo_sitemaps_sitemap_path` (`sitemap_path`(191)),
    INDEX `idx_seo_sitemaps_content_type` (`content_type`),
    INDEX `idx_seo_sitemaps_update_frequency` (`update_frequency`),
    INDEX `idx_seo_sitemaps_status` (`status`),
    INDEX `idx_seo_sitemaps_is_index_sitemap` (`is_index_sitemap`),
    INDEX `idx_seo_sitemaps_parent_sitemap_id` (`parent_sitemap_id`),
    INDEX `idx_seo_sitemaps_last_generated_at` (`last_generated_at`),
    INDEX `idx_seo_sitemaps_next_generation_at` (`next_generation_at`),
    INDEX `idx_seo_sitemaps_is_valid` (`is_valid`),
    INDEX `idx_seo_sitemaps_file_exists` (`file_exists`),
    INDEX `idx_seo_sitemaps_created_by` (`created_by`),
    INDEX `idx_seo_sitemaps_updated_by` (`updated_by`),
    INDEX `idx_seo_sitemaps_created_at` (`created_at`),
    INDEX `idx_seo_sitemaps_updated_at` (`updated_at`),
    
    -- Unique Constraints
    UNIQUE KEY `uk_seo_sitemaps_website_name` (`website_id`, `sitemap_name`),
    UNIQUE KEY `uk_seo_sitemaps_website_url` (`website_id`, `sitemap_url`(191)),
    UNIQUE KEY `uk_seo_sitemaps_website_path` (`website_id`, `sitemap_path`(191)),
    
    -- Composite Indexes for Performance
    INDEX `idx_seo_sitemaps_generation_schedule` (`website_id`, `status`, `next_generation_at`),
    INDEX `idx_seo_sitemaps_validation` (`website_id`, `is_valid`, `last_generated_at`),
    INDEX `idx_seo_sitemaps_submission` (`website_id`, `submitted_to_google`, `submitted_to_bing`, `last_submission_at`),
    INDEX `idx_seo_sitemaps_hierarchy` (`website_id`, `is_index_sitemap`, `parent_sitemap_id`),
    INDEX `idx_seo_sitemaps_file_management` (`website_id`, `file_exists`, `file_readable`, `backup_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SEO sitemaps with comprehensive file management';