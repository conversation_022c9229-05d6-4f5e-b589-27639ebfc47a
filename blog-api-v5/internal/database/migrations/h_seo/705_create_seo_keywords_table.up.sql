-- Migration: 705_create_seo_keywords_table.up.sql
-- Description: Create seo_keywords table for keyword tracking and ranking metrics
-- Module: h_seo
-- Dependencies: websites table from b_website module

CREATE TABLE IF NOT EXISTS `seo_keywords` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `website_id` INT UNSIGNED NOT NULL,
    `tenant_id` INT UNSIGNED NOT NULL,
    
    -- Keyword Information
    `keyword` VARCHAR(255) NOT NULL COMMENT 'The keyword or phrase being tracked',
    `keyword_type` ENUM('primary', 'secondary', 'long_tail', 'branded', 'local', 'competitor') NOT NULL DEFAULT 'primary' COMMENT 'Type of keyword',
    `keyword_category` VARCHAR(100) NULL COMMENT 'Category or group the keyword belongs to',
    `keyword_intent` ENUM('informational', 'navigational', 'transactional', 'commercial') NULL COMMENT 'Search intent of the keyword',
    `keyword_difficulty` DECIMAL(3,1) NULL COMMENT 'Keyword difficulty score (0.0-100.0)',
    `keyword_volume` INT UNSIGNED NULL COMMENT 'Monthly search volume',
    `keyword_competition` ENUM('low', 'medium', 'high') NULL COMMENT 'Competition level for the keyword',
    `keyword_cpc` DECIMAL(8,2) NULL COMMENT 'Cost per click for the keyword',
    `keyword_trends` JSON DEFAULT (JSON_ARRAY()) COMMENT 'Search volume trends over time',
    
    -- Target Page Information
    `target_page_id` INT UNSIGNED NULL COMMENT 'ID of the target page for this keyword',
    `target_url` VARCHAR(500) NULL COMMENT 'Target URL for this keyword',
    `target_page_type` VARCHAR(50) NULL COMMENT 'Type of target page (page, post, category, etc.)',
    `target_page_title` VARCHAR(255) NULL COMMENT 'Title of the target page',
    
    -- Current Rankings
    `google_rank` INT UNSIGNED NULL COMMENT 'Current rank in Google search results',
    `bing_rank` INT UNSIGNED NULL COMMENT 'Current rank in Bing search results',
    `yandex_rank` INT UNSIGNED NULL COMMENT 'Current rank in Yandex search results',
    `local_rank` INT UNSIGNED NULL COMMENT 'Current rank in local search results',
    `mobile_rank` INT UNSIGNED NULL COMMENT 'Current rank in mobile search results',
    `desktop_rank` INT UNSIGNED NULL COMMENT 'Current rank in desktop search results',
    
    -- Ranking History
    `best_rank` INT UNSIGNED NULL COMMENT 'Best ranking ever achieved',
    `worst_rank` INT UNSIGNED NULL COMMENT 'Worst ranking recorded',
    `previous_rank` INT UNSIGNED NULL COMMENT 'Previous ranking',
    `rank_change` INT NULL COMMENT 'Change in ranking (positive = improvement)',
    `rank_change_percentage` DECIMAL(5,2) NULL COMMENT 'Percentage change in ranking',
    
    -- Ranking Dates
    `last_rank_check` TIMESTAMP NULL COMMENT 'When ranking was last checked',
    `best_rank_date` TIMESTAMP NULL COMMENT 'When best ranking was achieved',
    `worst_rank_date` TIMESTAMP NULL COMMENT 'When worst ranking was recorded',
    `first_rank_date` TIMESTAMP NULL COMMENT 'When first ranking was recorded',
    
    -- Performance Metrics
    `impressions` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of search impressions',
    `clicks` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of clicks from search results',
    `ctr` DECIMAL(5,2) NOT NULL DEFAULT 0.0 COMMENT 'Click-through rate percentage',
    `average_position` DECIMAL(5,2) NULL COMMENT 'Average position in search results',
    `bounce_rate` DECIMAL(5,2) NULL COMMENT 'Bounce rate for traffic from this keyword',
    `conversion_rate` DECIMAL(5,2) NULL COMMENT 'Conversion rate for traffic from this keyword',
    `conversion_value` DECIMAL(10,2) NULL COMMENT 'Total conversion value from this keyword',
    
    -- Tracking Configuration
    `tracking_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether to track this keyword',
    `tracking_frequency` ENUM('daily', 'weekly', 'monthly') NOT NULL DEFAULT 'weekly' COMMENT 'How often to check rankings',
    `tracking_locations` JSON DEFAULT (JSON_ARRAY()) COMMENT 'Locations to track rankings for',
    `tracking_devices` JSON DEFAULT (JSON_ARRAY()) COMMENT 'Devices to track rankings for',
    `tracking_search_engines` JSON DEFAULT (JSON_ARRAY()) COMMENT 'Search engines to track rankings for',
    
    -- Competitor Analysis
    `competitor_keywords` JSON DEFAULT (JSON_ARRAY()) COMMENT 'Competitor data for this keyword',
    `competitor_ranks` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Competitor rankings for this keyword',
    `market_share` DECIMAL(5,2) NULL COMMENT 'Market share for this keyword',
    `opportunity_score` DECIMAL(3,1) NULL COMMENT 'Opportunity score for this keyword (0.0-100.0)',
    
    -- Content Analysis
    `content_optimization_score` DECIMAL(3,1) NULL COMMENT 'Content optimization score (0.0-100.0)',
    `keyword_density` DECIMAL(3,1) NULL COMMENT 'Keyword density in target content',
    `title_optimization` BOOLEAN NULL COMMENT 'Whether title is optimized for this keyword',
    `meta_description_optimization` BOOLEAN NULL COMMENT 'Whether meta description is optimized',
    `header_optimization` BOOLEAN NULL COMMENT 'Whether headers are optimized',
    `content_length` INT UNSIGNED NULL COMMENT 'Length of content for this keyword',
    `internal_links` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of internal links to target page',
    `external_links` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Number of external links to target page',
    
    -- SERP Features
    `featured_snippet` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether page appears in featured snippet',
    `knowledge_graph` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether page appears in knowledge graph',
    `local_pack` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether page appears in local pack',
    `image_pack` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether page appears in image pack',
    `video_pack` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether page appears in video pack',
    `news_box` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether page appears in news box',
    `shopping_results` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether page appears in shopping results',
    
    -- Goals and Targets
    `target_rank` INT UNSIGNED NULL COMMENT 'Target ranking position',
    `target_traffic` INT UNSIGNED NULL COMMENT 'Target monthly traffic',
    `target_ctr` DECIMAL(5,2) NULL COMMENT 'Target click-through rate',
    `target_conversions` INT UNSIGNED NULL COMMENT 'Target monthly conversions',
    `target_date` DATE NULL COMMENT 'Target date to achieve goals',
    
    -- Alerts and Notifications
    `alert_enabled` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether to send alerts for this keyword',
    `alert_rank_drop` INT UNSIGNED NULL COMMENT 'Rank drop threshold for alerts',
    `alert_rank_gain` INT UNSIGNED NULL COMMENT 'Rank gain threshold for alerts',
    `alert_traffic_drop` DECIMAL(5,2) NULL COMMENT 'Traffic drop percentage for alerts',
    `alert_ctr_drop` DECIMAL(5,2) NULL COMMENT 'CTR drop percentage for alerts',
    `last_alert_sent` TIMESTAMP NULL COMMENT 'When last alert was sent',
    
    -- Custom Fields
    `custom_fields` JSON DEFAULT (JSON_OBJECT()) COMMENT 'Custom fields for additional data',
    `tags` JSON DEFAULT (JSON_ARRAY()) COMMENT 'Tags for organizing keywords',
    `notes` TEXT NULL COMMENT 'Internal notes about the keyword',
    
    -- Status and Metadata
    `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT 'Status of the keyword tracking',
    `created_by` INT UNSIGNED NULL COMMENT 'User ID who created the keyword',
    `updated_by` INT UNSIGNED NULL COMMENT 'User ID who last updated the keyword',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT `fk_seo_keywords_website_id` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_keywords_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_keywords_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `fk_seo_keywords_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `chk_seo_keywords_status` CHECK (`status` IN ('active', 'inactive', 'paused', 'deleted')),
    CONSTRAINT `chk_seo_keywords_difficulty` CHECK (`keyword_difficulty` IS NULL OR (`keyword_difficulty` >= 0.0 AND `keyword_difficulty` <= 100.0)),
    CONSTRAINT `chk_seo_keywords_volume` CHECK (`keyword_volume` IS NULL OR `keyword_volume` >= 0),
    CONSTRAINT `chk_seo_keywords_cpc` CHECK (`keyword_cpc` IS NULL OR `keyword_cpc` >= 0),
    CONSTRAINT `chk_seo_keywords_ranks` CHECK (`google_rank` IS NULL OR `google_rank` > 0),
    CONSTRAINT `chk_seo_keywords_performance` CHECK (`impressions` >= 0 AND `clicks` >= 0 AND `ctr` >= 0.0),
    CONSTRAINT `chk_seo_keywords_bounce_rate` CHECK (`bounce_rate` IS NULL OR (`bounce_rate` >= 0.0 AND `bounce_rate` <= 100.0)),
    CONSTRAINT `chk_seo_keywords_conversion_rate` CHECK (`conversion_rate` IS NULL OR (`conversion_rate` >= 0.0 AND `conversion_rate` <= 100.0)),
    CONSTRAINT `chk_seo_keywords_conversion_value` CHECK (`conversion_value` IS NULL OR `conversion_value` >= 0),
    CONSTRAINT `chk_seo_keywords_market_share` CHECK (`market_share` IS NULL OR (`market_share` >= 0.0 AND `market_share` <= 100.0)),
    CONSTRAINT `chk_seo_keywords_opportunity_score` CHECK (`opportunity_score` IS NULL OR (`opportunity_score` >= 0.0 AND `opportunity_score` <= 100.0)),
    CONSTRAINT `chk_seo_keywords_optimization_score` CHECK (`content_optimization_score` IS NULL OR (`content_optimization_score` >= 0.0 AND `content_optimization_score` <= 100.0)),
    CONSTRAINT `chk_seo_keywords_density` CHECK (`keyword_density` IS NULL OR (`keyword_density` >= 0.0 AND `keyword_density` <= 100.0)),
    CONSTRAINT `chk_seo_keywords_links` CHECK (`internal_links` >= 0 AND `external_links` >= 0),
    CONSTRAINT `chk_seo_keywords_target_rank` CHECK (`target_rank` IS NULL OR `target_rank` > 0),
    CONSTRAINT `chk_seo_keywords_target_traffic` CHECK (`target_traffic` IS NULL OR `target_traffic` >= 0),
    CONSTRAINT `chk_seo_keywords_target_ctr` CHECK (`target_ctr` IS NULL OR (`target_ctr` >= 0.0 AND `target_ctr` <= 100.0)),
    CONSTRAINT `chk_seo_keywords_target_conversions` CHECK (`target_conversions` IS NULL OR `target_conversions` >= 0),
    CONSTRAINT `chk_seo_keywords_alert_thresholds` CHECK (`alert_rank_drop` IS NULL OR `alert_rank_drop` > 0),
    
    -- Indexes
    INDEX `idx_seo_keywords_website_id` (`website_id`),
    INDEX `idx_seo_keywords_tenant_id` (`tenant_id`),
    INDEX `idx_seo_keywords_keyword` (`keyword`),
    INDEX `idx_seo_keywords_keyword_type` (`keyword_type`),
    INDEX `idx_seo_keywords_keyword_category` (`keyword_category`),
    INDEX `idx_seo_keywords_keyword_intent` (`keyword_intent`),
    INDEX `idx_seo_keywords_keyword_difficulty` (`keyword_difficulty`),
    INDEX `idx_seo_keywords_keyword_volume` (`keyword_volume`),
    INDEX `idx_seo_keywords_target_url` (`target_url`(191)),
    INDEX `idx_seo_keywords_google_rank` (`google_rank`),
    INDEX `idx_seo_keywords_bing_rank` (`bing_rank`),
    INDEX `idx_seo_keywords_local_rank` (`local_rank`),
    INDEX `idx_seo_keywords_mobile_rank` (`mobile_rank`),
    INDEX `idx_seo_keywords_desktop_rank` (`desktop_rank`),
    INDEX `idx_seo_keywords_best_rank` (`best_rank`),
    INDEX `idx_seo_keywords_rank_change` (`rank_change`),
    INDEX `idx_seo_keywords_impressions` (`impressions`),
    INDEX `idx_seo_keywords_clicks` (`clicks`),
    INDEX `idx_seo_keywords_ctr` (`ctr`),
    INDEX `idx_seo_keywords_conversion_rate` (`conversion_rate`),
    INDEX `idx_seo_keywords_tracking_enabled` (`tracking_enabled`),
    INDEX `idx_seo_keywords_tracking_frequency` (`tracking_frequency`),
    INDEX `idx_seo_keywords_opportunity_score` (`opportunity_score`),
    INDEX `idx_seo_keywords_content_optimization_score` (`content_optimization_score`),
    INDEX `idx_seo_keywords_featured_snippet` (`featured_snippet`),
    INDEX `idx_seo_keywords_status` (`status`),
    INDEX `idx_seo_keywords_last_rank_check` (`last_rank_check`),
    INDEX `idx_seo_keywords_target_date` (`target_date`),
    INDEX `idx_seo_keywords_alert_enabled` (`alert_enabled`),
    INDEX `idx_seo_keywords_created_by` (`created_by`),
    INDEX `idx_seo_keywords_updated_by` (`updated_by`),
    INDEX `idx_seo_keywords_created_at` (`created_at`),
    INDEX `idx_seo_keywords_updated_at` (`updated_at`),
    
    -- Unique Constraints
    UNIQUE KEY `uk_seo_keywords_website_keyword` (`website_id`, `keyword`, `target_url`(191)),
    
    -- Composite Indexes for Performance
    INDEX `idx_seo_keywords_website_type` (`website_id`, `keyword_type`, `status`),
    INDEX `idx_seo_keywords_website_category` (`website_id`, `keyword_category`, `status`),
    INDEX `idx_seo_keywords_ranking_performance` (`website_id`, `google_rank`, `impressions`, `clicks`),
    INDEX `idx_seo_keywords_tracking_schedule` (`website_id`, `tracking_enabled`, `tracking_frequency`, `last_rank_check`),
    INDEX `idx_seo_keywords_opportunity_analysis` (`website_id`, `opportunity_score`, `keyword_difficulty`, `keyword_volume`),
    INDEX `idx_seo_keywords_serp_features` (`website_id`, `featured_snippet`, `knowledge_graph`, `local_pack`),
    INDEX `idx_seo_keywords_performance_metrics` (`website_id`, `ctr`, `conversion_rate`, `bounce_rate`),
    INDEX `idx_seo_keywords_alerts` (`website_id`, `alert_enabled`, `rank_change`, `last_alert_sent`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SEO keywords with comprehensive ranking and performance metrics';