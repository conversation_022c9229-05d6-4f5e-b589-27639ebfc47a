-- Create media_folders table for organizing media files
CREATE TABLE IF NOT EXISTS media_folders (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    parent_id INT UNSIGNED NULL,
    user_id INT UNSIGNED NOT NULL, -- Creator of the folder
    
    -- Folder information
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    path VARCHAR(500) NOT NULL, -- Full path including parents
    description TEXT NULL,
    
    -- Folder type and settings
    type ENUM('user', 'system', 'public', 'private', 'shared') NOT NULL DEFAULT 'user',
    color VARCHAR(7) DEFAULT '#6b7280', -- Hex color for UI
    icon VARCHAR(50) DEFAULT 'folder',
    
    -- Access control
    visibility ENUM('public', 'private', 'shared') NOT NULL DEFAULT 'public',
    access_permissions JSON DEFAULT (JSON_ARRAY()),
    
    -- Nested set model for hierarchy (optional, can use path for simplicity)
    lft INT UNSIGNED NOT NULL DEFAULT 1,
    rgt INT UNSIGNED NOT NULL DEFAULT 2,
    level INT UNSIGNED NOT NULL DEFAULT 0,
    
    -- Folder settings
    sort_order INT UNSIGNED DEFAULT 0,
    settings JSON DEFAULT (JSON_OBJECT()),
    
    -- Usage statistics
    file_count INT UNSIGNED DEFAULT 0,
    total_size BIGINT UNSIGNED DEFAULT 0,
    
    -- Status and timestamps
    status ENUM('active', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_media_folders_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_folders_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_folders_parent FOREIGN KEY (parent_id) REFERENCES media_folders(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_folders_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes for performance
    INDEX idx_media_folders_tenant_website (tenant_id, website_id),
    INDEX idx_media_folders_parent (parent_id),
    INDEX idx_media_folders_user (user_id),
    INDEX idx_media_folders_status (status),
    INDEX idx_media_folders_type (type),
    INDEX idx_media_folders_level (level),
    INDEX idx_media_folders_lft_rgt (lft, rgt),
    INDEX idx_media_folders_sort (sort_order),
    INDEX idx_media_folders_visibility (visibility),
    
    -- Unique constraints
    UNIQUE KEY uk_media_folders_tenant_website_slug (tenant_id, website_id, slug),
    UNIQUE KEY uk_media_folders_tenant_website_path (tenant_id, website_id, path),
    
    -- Full-text search index
    FULLTEXT idx_media_folders_search (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;