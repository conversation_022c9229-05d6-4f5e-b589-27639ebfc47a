-- Create media_files table with tenant and website isolation
CREATE TABLE IF NOT EXISTS media_files (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    folder_id INT UNSIGNED NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- File information
    filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    original_filename VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT UNSIGNED NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA256 hash for duplicate detection
    
    -- Storage information
    storage_type ENUM('local', 'minio', 's3', 'gcs') NOT NULL DEFAULT 'local',
    storage_path VARCHAR(500) NOT NULL,
    public_url VARCHAR(500) NOT NULL,
    
    -- Media metadata
    width INT UNSIGNED NULL,
    height INT UNSIGNED NULL,
    duration INT UNSIGNED NULL, -- in seconds for video/audio
    metadata JSON DEFAULT (JSON_OBJECT()),
    
    -- File categorization
    file_type ENUM('image', 'video', 'audio', 'document', 'archive', 'other') NOT NULL,
    category VARCHAR(100) DEFAULT 'general',
    
    -- SEO and accessibility
    alt_text VARCHAR(255) NULL,
    title VARCHAR(255) NULL,
    description TEXT NULL,
    
    -- Access control
    visibility ENUM('public', 'private', 'shared') NOT NULL DEFAULT 'public',
    access_permissions JSON DEFAULT (JSON_ARRAY()),
    
    -- Usage tracking
    view_count INT UNSIGNED DEFAULT 0,
    download_count INT UNSIGNED DEFAULT 0,
    last_accessed_at TIMESTAMP NULL,
    
    -- Processing status
    status ENUM('uploading', 'processing', 'ready', 'error', 'deleted') NOT NULL DEFAULT 'uploading',
    processing_status JSON DEFAULT (JSON_OBJECT()),
    error_message TEXT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_media_files_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_files_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_files_folder FOREIGN KEY (folder_id) REFERENCES media_folders(id) ON DELETE SET NULL,
    CONSTRAINT fk_media_files_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes for performance
    INDEX idx_media_files_tenant_website (tenant_id, website_id),
    INDEX idx_media_files_folder (folder_id),
    INDEX idx_media_files_user (user_id),
    INDEX idx_media_files_status (status),
    INDEX idx_media_files_type (file_type),
    INDEX idx_media_files_mime (mime_type),
    INDEX idx_media_files_created (created_at),
    INDEX idx_media_files_hash (file_hash),
    INDEX idx_media_files_slug (slug),
    INDEX idx_media_files_category (category),
    INDEX idx_media_files_visibility (visibility),
    
    -- Unique constraints
    UNIQUE KEY uk_media_files_tenant_website_slug (tenant_id, website_id, slug),
    UNIQUE KEY uk_media_files_tenant_website_path (tenant_id, website_id, storage_path),
    
    -- Full-text search index
    FULLTEXT idx_media_files_search (filename, original_filename, alt_text, title, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;