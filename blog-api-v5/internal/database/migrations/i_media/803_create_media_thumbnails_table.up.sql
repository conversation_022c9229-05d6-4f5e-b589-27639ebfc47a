-- Create media_thumbnails table for different image sizes
CREATE TABLE IF NOT EXISTS media_thumbnails (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    file_id INT UNSIGNED NOT NULL,
    
    -- Thumbnail information
    size_name VARCHAR(50) NOT NULL, -- small, medium, large, xl, custom
    width INT UNSIGNED NOT NULL,
    height INT UNSIGNED NOT NULL,
    quality INT UNSIGNED DEFAULT 85, -- JPEG quality 1-100
    
    -- Storage information
    storage_type ENUM('local', 'minio', 's3', 'gcs') NOT NULL DEFAULT 'local',
    storage_path VARCHAR(500) NOT NULL,
    public_url VARCHAR(500) NOT NULL,
    
    -- File details
    file_size BIGINT UNSIGNED NOT NULL,
    file_hash VARCHAR(64) NOT NULL, -- SHA256 hash
    mime_type VARCHAR(100) NOT NULL,
    
    -- Processing information
    processing_method ENUM('crop', 'resize', 'fit', 'fill') NOT NULL DEFAULT 'resize',
    processing_options JSON DEFAULT (JSON_OBJECT()),
    
    -- Optimization settings
    format VARCHAR(10) NOT NULL, -- jpg, png, webp, avif
    is_optimized BOOLEAN DEFAULT FALSE,
    optimization_savings FLOAT DEFAULT 0.0, -- percentage saved
    
    -- Status and timestamps
    status ENUM('generating', 'ready', 'error', 'deleted') NOT NULL DEFAULT 'generating',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_media_thumbnails_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_thumbnails_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_thumbnails_file FOREIGN KEY (file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_media_thumbnails_tenant_id (tenant_id),
    INDEX idx_media_thumbnails_website_id (website_id),
    INDEX idx_media_thumbnails_file (file_id),
    INDEX idx_media_thumbnails_size (size_name),
    INDEX idx_media_thumbnails_status (status),
    INDEX idx_media_thumbnails_hash (file_hash),
    INDEX idx_media_thumbnails_format (format),
    INDEX idx_media_thumbnails_created (created_at),
    
    -- Unique constraints
    UNIQUE KEY uk_media_thumbnails_tenant_website_file_size (tenant_id, website_id, file_id, size_name),
    UNIQUE KEY uk_media_thumbnails_tenant_website_file_path (tenant_id, website_id, file_id, storage_path)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;