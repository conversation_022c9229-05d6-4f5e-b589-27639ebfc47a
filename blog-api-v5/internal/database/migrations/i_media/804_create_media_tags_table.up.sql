-- Create media_tags table for tagging media files
CREATE TABLE IF NOT EXISTS media_tags (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL, -- Creator of the tag
    
    -- Tag information
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT NULL,
    
    -- Tag appearance
    color VARCHAR(7) DEFAULT '#3b82f6', -- Hex color for UI
    icon VARCHAR(50) NULL,
    
    -- Tag categorization
    category VARCHAR(50) DEFAULT 'general',
    type ENUM('user', 'system', 'auto') NOT NULL DEFAULT 'user',
    
    -- Usage statistics
    usage_count INT UNSIGNED DEFAULT 0,
    popularity_score FLOAT DEFAULT 0.0,
    
    -- Tag settings
    is_featured BOOLEAN DEFAULT FALSE,
    is_private BOOLEAN DEFAULT FALSE,
    settings JSON DEFAULT (JSON_OBJECT()),
    
    -- Status and timestamps
    status ENUM('active', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_media_tags_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_tags_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_tags_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes for performance
    INDEX idx_media_tags_tenant_website (tenant_id, website_id),
    INDEX idx_media_tags_user (user_id),
    INDEX idx_media_tags_status (status),
    INDEX idx_media_tags_type (type),
    INDEX idx_media_tags_category (category),
    INDEX idx_media_tags_usage (usage_count),
    INDEX idx_media_tags_popularity (popularity_score),
    INDEX idx_media_tags_featured (is_featured),
    INDEX idx_media_tags_private (is_private),
    
    -- Unique constraints
    UNIQUE KEY uk_media_tags_tenant_website_slug (tenant_id, website_id, slug),
    UNIQUE KEY uk_media_tags_tenant_website_name (tenant_id, website_id, name),
    
    -- Full-text search index
    FULLTEXT idx_media_tags_search (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;