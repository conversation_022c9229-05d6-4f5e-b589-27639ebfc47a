-- Create media_file_tags table for many-to-many relationship between files and tags
CREATE TABLE IF NOT EXISTS media_file_tags (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    file_id INT UNSIGNED NOT NULL,
    tag_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL, -- Who tagged the file
    
    -- Tagging information
    tagged_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tag_source ENUM('manual', 'auto', 'ai', 'import') NOT NULL DEFAULT 'manual',
    confidence_score FLOAT DEFAULT 1.0, -- For AI-generated tags (0.0 to 1.0)
    
    -- Tag metadata
    tag_context JSON DEFAULT (JSON_OBJECT()), -- Additional context for this tag on this file
    
    -- Foreign key constraints
    CONSTRAINT fk_media_file_tags_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_file_tags_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_file_tags_file FOREIGN KEY (file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_file_tags_tag FOREIGN KEY (tag_id) REFERENCES media_tags(id) ON DELETE CASCADE,
    CONSTRAINT fk_media_file_tags_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes for performance
    INDEX idx_media_file_tags_tenant_id (tenant_id),
    INDEX idx_media_file_tags_website_id (website_id),
    INDEX idx_media_file_tags_file (file_id),
    INDEX idx_media_file_tags_tag (tag_id),
    INDEX idx_media_file_tags_user (user_id),
    INDEX idx_media_file_tags_source (tag_source),
    INDEX idx_media_file_tags_confidence (confidence_score),
    INDEX idx_media_file_tags_tagged (tagged_at),
    
    -- Unique constraints
    UNIQUE KEY uk_media_file_tags_tenant_website_file_tag (tenant_id, website_id, file_id, tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;