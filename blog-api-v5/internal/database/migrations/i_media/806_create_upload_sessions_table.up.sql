-- Create upload_sessions table for managing file uploads
CREATE TABLE IF NOT EXISTS upload_sessions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Session information
    session_id VARCHAR(64) NOT NULL UNIQUE,
    upload_type <PERSON>NUM('single', 'chunked', 'multipart') NOT NULL DEFAULT 'single',
    
    -- File information
    filename VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    total_size BIGINT UNSIGNED NOT NULL,
    chunk_size INT UNSIGNED NULL,
    total_chunks INT UNSIGNED NULL,
    
    -- Upload progress
    uploaded_chunks INT UNSIGNED DEFAULT 0,
    uploaded_bytes BIGINT UNSIGNED DEFAULT 0,
    
    -- Storage information
    storage_type ENUM('local', 'minio', 's3', 'gcs') NOT NULL DEFAULT 'local',
    temp_path VARCHAR(500) NULL,
    final_path VARCHAR(500) NULL,
    
    -- Status tracking
    status ENUM('pending', 'uploading', 'processing', 'completed', 'failed', 'expired', 'cancelled') NOT NULL DEFAULT 'pending',
    error_message TEXT NULL,
    
    -- Metadata
    metadata JSON DEFAULT (JSON_OBJECT()),
    
    -- Timestamps
    expires_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_upload_sessions_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_upload_sessions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_upload_sessions_session_id (session_id),
    INDEX idx_upload_sessions_tenant_user (tenant_id, user_id),
    INDEX idx_upload_sessions_status (status),
    INDEX idx_upload_sessions_expires (expires_at),
    INDEX idx_upload_sessions_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;