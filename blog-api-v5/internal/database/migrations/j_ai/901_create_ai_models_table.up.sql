-- Create AI Models configuration table
CREATE TABLE ai_models (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    provider ENUM('openai', 'anthropic', 'gemini', 'ollama', 'custom') NOT NULL,
    model_id VARCHAR(100) NOT NULL,
    capabilities JSON DEFAULT (JSON_ARRAY()),
    configuration JSON DEFAULT (JSON_OBJECT()),
    api_key_encrypted TEXT,
    status ENUM('active', 'inactive', 'error') DEFAULT 'active',
    usage_limits JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_ai_models_tenant_provider (tenant_id, provider),
    INDEX idx_ai_models_status (status),
    INDEX idx_ai_models_tenant_status (tenant_id, status)
);