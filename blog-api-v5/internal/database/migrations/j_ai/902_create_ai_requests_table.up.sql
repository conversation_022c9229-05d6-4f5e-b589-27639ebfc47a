-- Create AI Requests tracking table
CREATE TABLE ai_requests (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    model_id INT UNSIGNED NOT NULL,
    request_type ENUM('content_generation', 'chat', 'design', 'optimization', 'analysis') NOT NULL,
    prompt_text TEXT NOT NULL,
    response_text LONGTEXT,
    tokens_used INT DEFAULT 0,
    processing_time_ms INT DEFAULT 0,
    cost_cents INT DEFAULT 0,
    status ENUM('pending', 'completed', 'failed', 'timeout') DEFAULT 'pending',
    error_message TEXT,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (model_id) REFERENCES ai_models(id) ON DELETE CASCADE,
    INDEX idx_ai_requests_tenant_type (tenant_id, request_type),
    INDEX idx_ai_requests_user_created (user_id, created_at),
    INDEX idx_ai_requests_model_created (model_id, created_at),
    INDEX idx_ai_requests_status (status),
    INDEX idx_ai_requests_tenant_status (tenant_id, status)
);