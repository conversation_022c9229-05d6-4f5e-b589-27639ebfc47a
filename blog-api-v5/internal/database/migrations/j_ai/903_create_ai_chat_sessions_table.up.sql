-- Create AI Chat Sessions table
CREATE TABLE ai_chat_sessions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    title VARCHAR(255) NOT NULL,
    model_id INT UNSIGNED NOT NULL,
    context JSO<PERSON> DEFAULT (JSON_OBJECT()),
    system_prompt TEXT,
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
    message_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIG<PERSON> KEY (model_id) REFERENCES ai_models(id) ON DELETE CASCADE,
    INDEX idx_ai_chat_sessions_tenant_user (tenant_id, user_id),
    INDEX idx_ai_chat_sessions_status (status),
    INDEX idx_ai_chat_sessions_tenant_status (tenant_id, status),
    INDEX idx_ai_chat_sessions_updated (updated_at)
);