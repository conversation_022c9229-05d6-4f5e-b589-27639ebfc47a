-- Create AI Chat Messages table
CREATE TABLE ai_chat_messages (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    session_id INT UNSIGNED NOT NULL,
    role ENUM('user', 'assistant', 'system') NOT NULL,
    content TEXT NOT NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    tokens_used INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_ai_chat_messages_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ai_chat_messages_website FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_ai_chat_messages_session FOREIGN KEY (session_id) REFERENCES ai_chat_sessions(id) ON DELETE CASCADE,
    
    INDEX idx_ai_chat_messages_tenant_id (tenant_id),
    INDEX idx_ai_chat_messages_website_id (website_id),
    INDEX idx_ai_chat_messages_session_created (session_id, created_at),
    INDEX idx_ai_chat_messages_role (role),
    INDEX idx_ai_chat_messages_created (created_at)
);