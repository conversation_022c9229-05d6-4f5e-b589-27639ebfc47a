-- Create AI Content Templates table
CREATE TABLE ai_content_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    content_type ENUM('blog_post', 'article', 'social_media', 'email', 'product_description') NOT NULL,
    prompt_template TEXT NOT NULL,
    parameters JSON DEFAULT (JSON_ARRAY()),
    example_output TEXT,
    usage_count INT DEFAULT 0,
    status ENUM('active', 'draft', 'archived') DEFAULT 'active',
    created_by INT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_ai_content_templates_tenant_type (tenant_id, content_type),
    INDEX idx_ai_content_templates_status (status),
    INDEX idx_ai_content_templates_tenant_status (tenant_id, status),
    INDEX idx_ai_content_templates_created_by (created_by)
);