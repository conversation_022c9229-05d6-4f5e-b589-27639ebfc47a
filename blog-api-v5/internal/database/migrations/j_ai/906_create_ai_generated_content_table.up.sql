-- Create AI Generated Content table
CREATE TABLE ai_generated_content (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    template_id INT UNSIGNED,
    content_type ENUM('blog_post', 'article', 'social_media', 'email', 'product_description') NOT NULL,
    title VARCHAR(500),
    content LONGTEXT NOT NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    quality_score DECIMAL(3,2),
    used_in_post_id INT UNSIGNED,
    status ENUM('draft', 'approved', 'rejected', 'published') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIG<PERSON> KEY (template_id) REFERENCES ai_content_templates(id) ON DELETE SET NULL,
    INDEX idx_ai_generated_content_tenant_type (tenant_id, content_type),
    INDEX idx_ai_generated_content_user_created (user_id, created_at),
    INDEX idx_ai_generated_content_status (status),
    INDEX idx_ai_generated_content_tenant_status (tenant_id, status),
    INDEX idx_ai_generated_content_template (template_id)
);