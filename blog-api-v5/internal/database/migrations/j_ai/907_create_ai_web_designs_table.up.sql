-- Create AI Web Designs table
CREATE TABLE ai_web_designs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    design_type ENUM('landing_page', 'blog_layout', 'product_page', 'portfolio', 'business_card') NOT NULL,
    html_content LONGTEXT,
    css_content LONGTEXT,
    js_content LONGTEXT,
    design_config JSON DEFAULT (JSON_OBJECT()),
    preview_image_url VARCHAR(500),
    usage_count INT DEFAULT 0,
    status ENUM('active', 'draft', 'archived') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_ai_web_designs_tenant_type (tenant_id, design_type),
    INDEX idx_ai_web_designs_status (status),
    INDEX idx_ai_web_designs_tenant_status (tenant_id, status),
    INDEX idx_ai_web_designs_user (user_id)
);