-- Remove website_id from AI tables

-- Remove website_id from ai_models table
ALTER TABLE ai_models 
DROP FOREIGN KEY fk_ai_models_website,
DROP INDEX idx_ai_models_website,
DROP INDEX idx_ai_models_tenant_website,
DROP COLUMN website_id;

-- Remove website_id from ai_requests table
ALTER TABLE ai_requests 
DROP FOREIGN KEY fk_ai_requests_website,
DROP INDEX idx_ai_requests_website,
DROP INDEX idx_ai_requests_tenant_website,
DROP COLUMN website_id;

-- Remove website_id from ai_chat_sessions table
ALTER TABLE ai_chat_sessions 
DROP FOREIGN KEY fk_ai_chat_sessions_website,
DROP INDEX idx_ai_chat_sessions_website,
DROP INDEX idx_ai_chat_sessions_tenant_website,
DROP COLUMN website_id;

-- Remove website_id from ai_content_templates table
ALTER TABLE ai_content_templates 
DROP FOREIGN KEY fk_ai_content_templates_website,
DROP INDEX idx_ai_content_templates_website,
DROP INDEX idx_ai_content_templates_tenant_website,
DROP COLUMN website_id;

-- Remove website_id from ai_generated_content table
ALTER TABLE ai_generated_content 
DROP FOREIGN KEY fk_ai_generated_content_website,
DROP INDEX idx_ai_generated_content_website,
DROP INDEX idx_ai_generated_content_tenant_website,
DROP COLUMN website_id;

-- Remove website_id from ai_web_designs table
ALTER TABLE ai_web_designs 
DROP FOREIGN KEY fk_ai_web_designs_website,
DROP INDEX idx_ai_web_designs_website,
DROP INDEX idx_ai_web_designs_tenant_website,
DROP COLUMN website_id;