# AI Integration Multi-Tenant Support

## Overview

This document describes the multi-tenant website-level support added to the AI integration module to align with the multi-tenant user management architecture.

## Architecture Alignment

### Multi-Tenant Hierarchy
```
Global User
└── Tenant Membership (TenantUser)
    └── Website Roles (WebsiteUserRole)
        └── Website Context
            └── AI Resources (scoped to website)
```

### Website-Level Scoping
All AI resources are now scoped to the website level, enabling:
- **Permission isolation**: Different AI permissions per website
- **Resource management**: Separate AI quotas and usage tracking
- **Content scoping**: AI-generated content belongs to specific websites
- **Role-based access**: AI features controlled by website-specific roles

## Database Schema Changes

### Migration 908: Add website_id to AI Tables

All AI tables now include `website_id` field with proper foreign key constraints:

```sql
-- Example for ai_models table
ALTER TABLE ai_models 
ADD COLUMN website_id INT UNSIGNED NULL AFTER tenant_id,
ADD CONSTRAINT fk_ai_models_website 
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
ADD INDEX idx_ai_models_website (website_id),
ADD INDEX idx_ai_models_tenant_website (tenant_id, website_id);
```

### Updated Table Structure

#### ai_models
- **website_id**: Links AI model configurations to specific websites
- **Use case**: Website-specific AI model settings and API keys

#### ai_requests
- **website_id**: Tracks which website generated the AI request
- **Use case**: Website-level usage analytics and billing

#### ai_chat_sessions
- **website_id**: Associates chat sessions with specific websites
- **Use case**: Website-specific chat contexts and permissions

#### ai_content_templates
- **website_id**: Scopes content templates to specific websites
- **Use case**: Website-specific content generation templates

#### ai_generated_content
- **website_id**: Associates generated content with target websites
- **Use case**: Content publishing and website-specific SEO

#### ai_web_designs
- **website_id**: Links web designs to specific websites
- **Use case**: Website-specific design templates and themes

## API Context Integration

### Request Context Structure
```go
type AIRequestContext struct {
    UserID      uint32 `json:"user_id"`      // Global user ID
    TenantID    uint32 `json:"tenant_id"`    // Tenant ID
    WebsiteID   uint32 `json:"website_id"`   // Website ID
    Permissions []string `json:"permissions"` // Website-specific permissions
}
```

### Context Validation
All AI API endpoints now validate:
1. User membership in tenant
2. User roles on specific website
3. AI permissions for the website context
4. Resource ownership verification

## Permission System

### AI-Specific Permissions
```go
const (
    // AI Model Management
    PermissionAIModelCreate = "ai.model.create"
    PermissionAIModelRead   = "ai.model.read"
    PermissionAIModelUpdate = "ai.model.update"
    PermissionAIModelDelete = "ai.model.delete"
    
    // Content Generation
    PermissionAIContentGenerate = "ai.content.generate"
    PermissionAIContentRead     = "ai.content.read"
    PermissionAIContentPublish  = "ai.content.publish"
    
    // Chat Features
    PermissionAIChatCreate = "ai.chat.create"
    PermissionAIChatRead   = "ai.chat.read"
    PermissionAIChatDelete = "ai.chat.delete"
    
    // Web Design
    PermissionAIDesignGenerate = "ai.design.generate"
    PermissionAIDesignRead     = "ai.design.read"
    PermissionAIDesignPublish  = "ai.design.publish"
    
    // Analytics
    PermissionAIAnalyticsRead = "ai.analytics.read"
    PermissionAIQuotaManage   = "ai.quota.manage"
)
```

### Role-Based Access Examples
```go
// Editor role can generate content but not manage models
var EditorAIPermissions = []string{
    PermissionAIContentGenerate,
    PermissionAIContentRead,
    PermissionAIChatCreate,
    PermissionAIChatRead,
    PermissionAIDesignGenerate,
    PermissionAIDesignRead,
}

// Admin role has full AI permissions
var AdminAIPermissions = []string{
    PermissionAIModelCreate,
    PermissionAIModelRead,
    PermissionAIModelUpdate,
    PermissionAIModelDelete,
    PermissionAIContentGenerate,
    PermissionAIContentRead,
    PermissionAIContentPublish,
    PermissionAIChatCreate,
    PermissionAIChatRead,
    PermissionAIChatDelete,
    PermissionAIDesignGenerate,
    PermissionAIDesignRead,
    PermissionAIDesignPublish,
    PermissionAIAnalyticsRead,
    PermissionAIQuotaManage,
}
```

## Usage Patterns

### 1. Website-Scoped AI Model Configuration
```go
// Create AI model for specific website
aiModel := &models.AIModel{
    TenantID:    userContext.TenantID,
    WebsiteID:   userContext.WebsiteID,
    Name:        "Website Content Generator",
    Provider:    "openai",
    ModelID:     "gpt-4",
    Capabilities: []string{"content_generation"},
}
```

### 2. Content Generation with Website Context
```go
// Generate content for specific website
request := &models.ContentGenerationRequest{
    TenantID:    userContext.TenantID,
    WebsiteID:   userContext.WebsiteID,
    UserID:      userContext.UserID,
    ContentType: "blog_post",
    Prompt:      "Write about AI in blogging",
}
```

### 3. Website-Specific Usage Analytics
```go
// Get AI usage statistics for website
stats, err := aiService.GetUsageStats(ctx, UsageStatsRequest{
    TenantID:  userContext.TenantID,
    WebsiteID: userContext.WebsiteID,
    Period:    "monthly",
})
```

## Benefits

### 1. **Granular Access Control**
- Users can have different AI permissions on different websites
- Website admins can control AI features independently
- Role-based AI feature access

### 2. **Resource Isolation**
- AI models and configurations are website-specific
- Usage tracking and billing per website
- Content generation scoped to website context

### 3. **Scalability**
- Efficient querying with proper indexing
- Website-level caching strategies
- Horizontal scaling by website sharding

### 4. **Compliance**
- Data sovereignty per website
- Audit trails for website-specific AI usage
- Permission-based feature access

## Migration Strategy

### Existing Data
- All existing AI data will have `website_id` as NULL initially
- Migration script can populate website_id based on tenant's default website
- Applications should handle NULL website_id gracefully during transition

### Backward Compatibility
- NULL website_id indicates tenant-level resources
- API endpoints support both tenant-level and website-level operations
- Gradual migration path for existing integrations

## Security Considerations

### 1. **Context Validation**
```go
func ValidateAIContext(ctx context.Context, userContext UserContext, resourceID uint32) error {
    // Validate user has access to website
    if !userContext.HasWebsiteAccess(userContext.WebsiteID) {
        return errors.New("access denied to website")
    }
    
    // Validate resource belongs to website
    resource, err := aiService.GetResource(ctx, resourceID)
    if err != nil {
        return err
    }
    
    if resource.WebsiteID != userContext.WebsiteID {
        return errors.New("resource not found in current website context")
    }
    
    return nil
}
```

### 2. **Permission Checks**
```go
func RequireAIPermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userContext := c.MustGet("user_context").(UserContext)
        
        if !userContext.HasPermission(permission) {
            c.JSON(403, gin.H{"error": "insufficient AI permissions"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 3. **Audit Logging**
```go
func LogAIActivity(ctx context.Context, userContext UserContext, action string, resourceID uint32) {
    auditLog := &models.AuditLog{
        UserID:      userContext.UserID,
        TenantID:    userContext.TenantID,
        WebsiteID:   userContext.WebsiteID,
        Action:      action,
        ResourceID:  resourceID,
        IPAddress:   extractIP(ctx),
        UserAgent:   extractUserAgent(ctx),
        Timestamp:   time.Now(),
    }
    
    auditService.Log(ctx, auditLog)
}
```

## Testing Strategy

### 1. **Multi-Tenant Test Cases**
- User access to multiple websites
- Permission isolation between websites
- Resource scoping validation
- Context switching functionality

### 2. **Migration Testing**
- Verify foreign key constraints
- Test rollback scenarios
- Validate index performance
- Check data integrity

### 3. **Integration Testing**
- API endpoints with website context
- Permission middleware functionality
- Cross-website access prevention
- Usage analytics accuracy

## Conclusion

The addition of website-level scoping to the AI integration module provides:

1. **Proper multi-tenant isolation** at the website level
2. **Granular permission control** for AI features
3. **Scalable resource management** with efficient querying
4. **Compliance-ready architecture** with proper audit trails
5. **Flexible deployment options** supporting various business models

This architecture ensures that AI features integrate seamlessly with the multi-tenant user management system while maintaining security, performance, and scalability.