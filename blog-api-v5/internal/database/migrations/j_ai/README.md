# AI Integration Database Migrations

This directory contains database migrations for the AI integration module.

## Migration Files

### 908_add_website_id_to_ai_tables
- **Purpose**: Add website_id to all AI tables for multi-tenant website-level scoping
- **Key Features**:
  - Website-level AI resource isolation
  - Proper foreign key constraints to websites table
  - Composite indexes for efficient querying
  - Support for website-specific AI configurations
  - Enables website-level permissions and quotas

## Migration Files

### 901_create_ai_models_table
- **Purpose**: Stores AI model configurations and settings
- **Key Features**:
  - Support for multiple AI providers (OpenAI, Anthropic, Gemini, Ollama, Custom)
  - Encrypted API key storage
  - Configurable capabilities and usage limits
  - Tenant-based isolation

### 902_create_ai_requests_table
- **Purpose**: Tracks all AI requests and their responses
- **Key Features**:
  - Request type categorization (content_generation, chat, design, optimization, analysis)
  - Token usage and cost tracking
  - Performance metrics (processing time)
  - Error handling and logging
  - Metadata storage for request context

### 903_create_ai_chat_sessions_table
- **Purpose**: Manages AI chat sessions
- **Key Features**:
  - Session context and system prompt storage
  - Message count tracking
  - Session status management (active, archived, deleted)
  - User and tenant association

### 904_create_ai_chat_messages_table
- **Purpose**: Stores individual chat messages
- **Key Features**:
  - Role-based messaging (user, assistant, system)
  - Token usage tracking per message
  - Message metadata storage
  - Efficient session-based indexing

### 905_create_ai_content_templates_table
- **Purpose**: Manages content generation templates
- **Key Features**:
  - Template categorization by content type
  - Parameterized prompt templates
  - Usage statistics tracking
  - Version control and status management

### 906_create_ai_generated_content_table
- **Purpose**: Stores AI-generated content
- **Key Features**:
  - Content type classification
  - Quality scoring system
  - Content approval workflow
  - Integration with blog posts
  - Metadata for content analysis

### 907_create_ai_web_designs_table
- **Purpose**: Manages AI-generated web designs
- **Key Features**:
  - Design type categorization
  - HTML, CSS, and JavaScript content storage
  - Design configuration and settings
  - Preview image management
  - Usage tracking and analytics

## Database Schema Features

### MySQL 8 Compatibility
- Uses `INT UNSIGNED` for all ID fields
- Proper `VARCHAR(255)` specifications for indexed string fields
- JSON columns with proper default values
- Appropriate timestamp handling

### Foreign Key Relationships
- All tables properly reference `tenants(id)` for multi-tenancy
- All tables properly reference `websites(id)` for website-level scoping
- User associations with `ON DELETE SET NULL` for data preservation
- Model references with `ON DELETE CASCADE` for cleanup
- Template relationships for content generation

### Indexing Strategy
- Composite indexes for common query patterns
- Tenant-based isolation indexes
- Website-level scoping indexes
- Performance optimized for filtering and sorting
- Status-based indexes for workflow management

### Security Considerations
- Encrypted API key storage
- Tenant isolation at database level
- Website-level access control
- Proper foreign key constraints
- Status-based soft deletes

## Usage

### Run AI Migrations
```bash
# Run all AI migrations
make migrate-up MODULE=j_ai

# Check migration status
make migrate-status MODULE=j_ai

# Rollback last migration
make migrate-down MODULE=j_ai
```

### Database Operations
```sql
-- Check AI tables
SELECT TABLE_NAME 
FROM information_schema.tables 
WHERE table_schema = 'blog_api_v3' 
AND table_name LIKE 'ai_%' 
ORDER BY table_name;

-- View AI models
SELECT id, name, provider, model_id, status, website_id
FROM ai_models 
WHERE tenant_id = 1 AND website_id = 1;

-- Check AI usage statistics
SELECT 
    request_type,
    website_id,
    COUNT(*) as total_requests,
    SUM(tokens_used) as total_tokens,
    SUM(cost_cents) as total_cost_cents,
    AVG(processing_time_ms) as avg_processing_time
FROM ai_requests 
WHERE tenant_id = 1 
GROUP BY request_type, website_id;
```

## Performance Considerations

### Query Optimization
- Indexes designed for common access patterns
- Tenant-based partitioning support
- Efficient JSON field usage
- Proper timestamp indexing

### Scaling Strategies
- Horizontal scaling with tenant sharding
- Archive strategy for old requests
- Caching layer for frequently accessed data
- Background job processing for heavy operations

## Monitoring and Maintenance

### Key Metrics to Monitor
- Request volume and response times
- Token usage and costs
- Error rates by provider
- Template usage patterns
- Content generation quality scores

### Maintenance Tasks
- Regular cleanup of old requests
- Archive completed chat sessions
- Monitor API key usage limits
- Update model configurations
- Analyze usage patterns for optimization

## Security Notes

- API keys are encrypted at rest
- Tenant isolation enforced at database level
- User permissions controlled through RBAC
- Audit trails for all AI operations
- Cost tracking for usage monitoring

## Future Enhancements

- Multi-modal content support (images, audio, video)
- Advanced analytics and reporting
- Fine-tuning model management
- Custom model deployment support
- Real-time collaboration features