CREATE TABLE IF NOT EXISTS ads_campaigns (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    budget DECIMAL(10,2) DEFAULT 0.00,
    status ENUM('draft', 'active', 'paused', 'completed', 'cancelled', 'deleted') NOT NULL DEFAULT 'draft',
    start_date DATETIME NOT NULL,
    end_date DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_campaigns_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_campaigns_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_ads_campaigns_tenant_website_name (tenant_id, website_id, name),
    
    -- Indexes
    INDEX idx_ads_campaigns_tenant_id (tenant_id),
    INDEX idx_ads_campaigns_website_id (website_id),
    INDEX idx_ads_campaigns_tenant_website (tenant_id, website_id),
    INDEX idx_ads_campaigns_status (status),
    INDEX idx_ads_campaigns_tenant_status (tenant_id, status),
    INDEX idx_ads_campaigns_dates (start_date, end_date),
    
    -- Check Constraints
    CONSTRAINT chk_ads_campaigns_dates CHECK (end_date > start_date),
    CONSTRAINT chk_ads_campaigns_budget CHECK (budget >= 0)
);