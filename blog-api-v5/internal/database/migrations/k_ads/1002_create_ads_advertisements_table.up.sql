CREATE TABLE IF NOT EXISTS ads_advertisements (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    campaign_id INT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(500),
    link_url VARCHAR(500) NOT NULL,
    ad_type ENUM('banner', 'text', 'rich_media', 'native', 'video') NOT NULL DEFAULT 'banner',
    device_targeting ENUM('web_pc', 'web_mobile', 'both') NOT NULL DEFAULT 'both',
    page_targeting JSON DEFAULT (JSON_ARRAY()),
    position ENUM('header', 'sidebar', 'footer', 'inline', 'popup') NOT NULL DEFAULT 'sidebar',
    priority INT NOT NULL DEFAULT 5,
    status ENUM('draft', 'active', 'paused', 'expired', 'deleted') NOT NULL DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_advertisements_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_advertisements_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_advertisements_campaign_id FOREIGN KEY (campaign_id) REFERENCES ads_campaigns(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_ads_advertisements_tenant_id (tenant_id),
    INDEX idx_ads_advertisements_website_id (website_id),
    INDEX idx_ads_advertisements_tenant_website (tenant_id, website_id),
    INDEX idx_ads_advertisements_campaign_id (campaign_id),
    INDEX idx_ads_advertisements_status (status),
    INDEX idx_ads_advertisements_tenant_status (tenant_id, status),
    INDEX idx_ads_advertisements_device_targeting (device_targeting),
    INDEX idx_ads_advertisements_position (position),
    INDEX idx_ads_advertisements_priority (priority),
    
    -- Check Constraints
    CONSTRAINT chk_ads_advertisements_priority CHECK (priority >= 1 AND priority <= 10)
);