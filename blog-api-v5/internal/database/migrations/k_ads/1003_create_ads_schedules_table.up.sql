CREATE TABLE IF NOT EXISTS ads_schedules (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    timezone VARCHAR(50) DEFAULT 'UTC',
    recurring_pattern JSON DEFAULT (JSON_OBJECT()),
    status ENUM('active', 'paused', 'expired', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_schedules_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_schedules_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_schedules_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_ads_schedules_tenant_id (tenant_id),
    INDEX idx_ads_schedules_website_id (website_id),
    INDEX idx_ads_schedules_tenant_website (tenant_id, website_id),
    INDEX idx_ads_schedules_advertisement_id (advertisement_id),
    INDEX idx_ads_schedules_status (status),
    INDEX idx_ads_schedules_time_range (start_time, end_time),
    INDEX idx_ads_schedules_active_time (status, start_time, end_time),
    
    -- Check Constraints
    CONSTRAINT chk_ads_schedules_time CHECK (end_time > start_time)
);