CREATE TABLE IF NOT EXISTS ads_placements (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    page_type ENUM('homepage', 'category', 'article', 'tag', 'custom') NOT NULL,
    position ENUM('header', 'sidebar', 'footer', 'inline', 'popup') NOT NULL,
    targeting_rules JSON DEFAULT (JSON_OBJECT()),
    max_ads INT NOT NULL DEFAULT 1,
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_placements_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_placements_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_ads_placements_tenant_website_page_position (tenant_id, website_id, page_type, position),
    
    -- Indexes
    INDEX idx_ads_placements_tenant_id (tenant_id),
    INDEX idx_ads_placements_website_id (website_id),
    INDEX idx_ads_placements_tenant_website (tenant_id, website_id),
    INDEX idx_ads_placements_page_type (page_type),
    INDEX idx_ads_placements_position (position),
    INDEX idx_ads_placements_status (status),
    
    -- Check Constraints
    CONSTRAINT chk_ads_placements_max_ads CHECK (max_ads >= 1 AND max_ads <= 10)
);