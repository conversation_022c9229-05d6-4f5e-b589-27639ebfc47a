CREATE TABLE IF NOT EXISTS ads_impressions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    placement_id INT UNSIGNED NOT NULL,
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    referrer VARCHAR(500),
    device_type ENUM('web_pc', 'web_mobile', 'unknown') NOT NULL DEFAULT 'unknown',
    page_url VARCHAR(500),
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    view_duration INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_impressions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_impressions_website_id FOREIG<PERSON> KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_impressions_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_impressions_placement_id FOREIGN KEY (placement_id) REFERENCES ads_placements(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_ads_impressions_tenant_id (tenant_id),
    INDEX idx_ads_impressions_website_id (website_id),
    INDEX idx_ads_impressions_tenant_website (tenant_id, website_id),
    INDEX idx_ads_impressions_advertisement_id (advertisement_id),
    INDEX idx_ads_impressions_placement_id (placement_id),
    INDEX idx_ads_impressions_viewed_at (viewed_at),
    INDEX idx_ads_impressions_device_type (device_type),
    INDEX idx_ads_impressions_tenant_date (tenant_id, viewed_at),
    
    -- Check Constraints
    CONSTRAINT chk_ads_impressions_view_duration CHECK (view_duration >= 0)
);