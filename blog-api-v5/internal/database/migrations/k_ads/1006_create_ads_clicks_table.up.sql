CREATE TABLE IF NOT EXISTS ads_clicks (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    impression_id INT UNSIGNED,
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    referrer VARCHAR(500),
    device_type ENUM('web_pc', 'web_mobile', 'unknown') NOT NULL DEFAULT 'unknown',
    page_url VARCHAR(500),
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    destination_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_clicks_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_clicks_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_clicks_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_clicks_impression_id FOREIGN KEY (impression_id) REFERENCES ads_impressions(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_ads_clicks_tenant_id (tenant_id),
    INDEX idx_ads_clicks_website_id (website_id),
    INDEX idx_ads_clicks_tenant_website (tenant_id, website_id),
    INDEX idx_ads_clicks_advertisement_id (advertisement_id),
    INDEX idx_ads_clicks_impression_id (impression_id),
    INDEX idx_ads_clicks_clicked_at (clicked_at),
    INDEX idx_ads_clicks_device_type (device_type),
    INDEX idx_ads_clicks_tenant_date (tenant_id, clicked_at)
);