CREATE TABLE IF NOT EXISTS ads_analytics (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    analytics_date DATE NOT NULL,
    impressions_count INT DEFAULT 0,
    clicks_count INT DEFAULT 0,
    ctr_rate DECIMAL(5,4) DEFAULT 0.0000,
    cost_per_click DECIMAL(10,2) DEFAULT 0.00,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    device_breakdown JSON DEFAULT (JSON_OBJECT()),
    page_breakdown JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_analytics_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_analytics_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_analytics_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_ads_analytics_tenant_website_ad_date (tenant_id, website_id, advertisement_id, analytics_date),
    
    -- Indexes
    INDEX idx_ads_analytics_tenant_id (tenant_id),
    INDEX idx_ads_analytics_website_id (website_id),
    INDEX idx_ads_analytics_tenant_website (tenant_id, website_id),
    INDEX idx_ads_analytics_advertisement_id (advertisement_id),
    INDEX idx_ads_analytics_date (analytics_date),
    INDEX idx_ads_analytics_tenant_date (tenant_id, analytics_date),
    
    -- Check Constraints
    CONSTRAINT chk_ads_analytics_counts CHECK (impressions_count >= 0 AND clicks_count >= 0),
    CONSTRAINT chk_ads_analytics_ctr CHECK (ctr_rate >= 0.0000 AND ctr_rate <= 1.0000),
    CONSTRAINT chk_ads_analytics_financial CHECK (cost_per_click >= 0 AND revenue >= 0)
);