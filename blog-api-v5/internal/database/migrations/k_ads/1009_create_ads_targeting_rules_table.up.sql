CREATE TABLE IF NOT EXISTS ads_targeting_rules (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    advertisement_id INT UNSIGNED NOT NULL,
    rule_type ENUM('page_url', 'referrer', 'device', 'time', 'location', 'custom') NOT NULL,
    rule_key VARCHAR(255) NOT NULL,
    rule_value TEXT NOT NULL,
    operator ENUM('equals', 'not_equals', 'contains', 'not_contains', 'starts_with', 'ends_with', 'regex') NOT NULL DEFAULT 'equals',
    priority INT NOT NULL DEFAULT 1,
    status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_ads_targeting_rules_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_targeting_rules_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_ads_targeting_rules_advertisement_id FOREIGN KEY (advertisement_id) REFERENCES ads_advertisements(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_ads_targeting_rules_tenant_id (tenant_id),
    INDEX idx_ads_targeting_rules_website_id (website_id),
    INDEX idx_ads_targeting_rules_tenant_website (tenant_id, website_id),
    INDEX idx_ads_targeting_rules_advertisement_id (advertisement_id),
    INDEX idx_ads_targeting_rules_rule_type (rule_type),
    INDEX idx_ads_targeting_rules_status (status),
    INDEX idx_ads_targeting_rules_priority (priority),
    
    -- Check Constraints
    CONSTRAINT chk_ads_targeting_rules_priority CHECK (priority >= 1 AND priority <= 100)
);