CREATE TABLE IF NOT EXISTS notification_templates (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    code VARCHAR(100) NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    type ENUM('transactional', 'marketing', 'system', 'custom') NOT NULL DEFAULT 'transactional',
    channel ENUM('email', 'socket', 'push', 'sms') NOT NULL,
    description TEXT NULL,
    variables JSON DEFAULT (JSON_ARRAY()),
    is_active BOOLEAN NOT NULL DEFAULT false,
    version_count INT UNSIGNED NOT NULL DEFAULT 0,
    active_version_id INT UNSIGNED NULL,
    created_by INT UNSIGNED NULL,
    updated_by INT UNSIGNED NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_notification_templates_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_templates_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    CONSTRAINT fk_notification_templates_updated_by FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Unique Constraints
    UNIQUE KEY uk_notification_templates_tenant_code (tenant_id, code),
    
    -- Indexes
    INDEX idx_notification_templates_tenant_id (tenant_id),
    INDEX idx_notification_templates_tenant_type (tenant_id, type),
    INDEX idx_notification_templates_tenant_channel (tenant_id, channel),
    INDEX idx_notification_templates_tenant_active (tenant_id, is_active),
    INDEX idx_notification_templates_code (code),
    INDEX idx_notification_templates_type (type),
    INDEX idx_notification_templates_channel (channel),
    INDEX idx_notification_templates_is_active (is_active),
    INDEX idx_notification_templates_created_by (created_by),
    INDEX idx_notification_templates_created_at (created_at)
);