CREATE TABLE IF NOT EXISTS notification_preferences (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    notification_type VARCHAR(100) NOT NULL,
    channel ENUM('email', 'socket', 'push', 'sms') NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    frequency ENUM('instant', 'daily', 'weekly', 'monthly', 'never') NOT NULL DEFAULT 'instant',
    quiet_hours_start TIME NULL,
    quiet_hours_end TIME NULL,
    timezone VARCHAR(50) NULL DEFAULT 'UTC',
    language VARCHAR(10) NOT NULL DEFAULT 'en',
    delivery_settings JSON DEFAULT (JSON_OBJECT()),
    preferences JSON DEFAULT (JSON_OBJECT()),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_notification_preferences_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_preferences_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_notification_preferences_tenant_user_type_channel (tenant_id, user_id, notification_type, channel),
    
    -- Indexes
    INDEX idx_notification_preferences_tenant_id (tenant_id),
    INDEX idx_notification_preferences_tenant_user (tenant_id, user_id),
    INDEX idx_notification_preferences_tenant_type (tenant_id, notification_type),
    INDEX idx_notification_preferences_tenant_channel (tenant_id, channel),
    INDEX idx_notification_preferences_user_id (user_id),
    INDEX idx_notification_preferences_notification_type (notification_type),
    INDEX idx_notification_preferences_channel (channel),
    INDEX idx_notification_preferences_is_enabled (is_enabled),
    INDEX idx_notification_preferences_frequency (frequency),
    INDEX idx_notification_preferences_language (language)
);