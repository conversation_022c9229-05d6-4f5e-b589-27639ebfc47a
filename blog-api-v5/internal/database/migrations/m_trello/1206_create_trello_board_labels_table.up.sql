CREATE TABLE IF NOT EXISTS trello_board_labels (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    board_id INT UNSIGNED NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    color VARCHAR(7) NOT NULL DEFAULT '#61bd4f',
    uses INT UNSIGNED NOT NULL DEFAULT 0,
    created_by INT UNSIGNED NOT NULL,
    status ENUM('active', 'inactive', 'archived', 'deleted') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_board_labels_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_board_labels_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_board_labels_board_id FOREIGN KEY (board_id) REFERENCES trello_boards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_board_labels_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_board_labels_board_name (board_id, name),
    
    -- Indexes
    INDEX idx_trello_board_labels_tenant_id (tenant_id),
    INDEX idx_trello_board_labels_website_id (website_id),
    INDEX idx_trello_board_labels_board_id (board_id),
    INDEX idx_trello_board_labels_tenant_website (tenant_id, website_id),
    INDEX idx_trello_board_labels_tenant_board (tenant_id, board_id),
    INDEX idx_trello_board_labels_tenant_status (tenant_id, status),
    INDEX idx_trello_board_labels_created_by (created_by),
    INDEX idx_trello_board_labels_color (color),
    INDEX idx_trello_board_labels_uses (uses)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;