CREATE TABLE IF NOT EXISTS trello_card_labels (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    card_id INT UNSIGNED NOT NULL,
    label_id INT UNSIGNED NOT NULL,
    applied_by INT UNSIGNED NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_card_labels_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_labels_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_labels_card_id FOREIGN KEY (card_id) REFERENCES trello_cards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_labels_label_id FOREIGN KEY (label_id) REFERENCES trello_board_labels(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_labels_applied_by FOREIGN KEY (applied_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique Constraints
    UNIQUE KEY uk_trello_card_labels_card_label (card_id, label_id),
    
    -- Indexes
    INDEX idx_trello_card_labels_tenant_id (tenant_id),
    INDEX idx_trello_card_labels_website_id (website_id),
    INDEX idx_trello_card_labels_card_id (card_id),
    INDEX idx_trello_card_labels_label_id (label_id),
    INDEX idx_trello_card_labels_tenant_website (tenant_id, website_id),
    INDEX idx_trello_card_labels_tenant_card (tenant_id, card_id),
    INDEX idx_trello_card_labels_tenant_label (tenant_id, label_id),
    INDEX idx_trello_card_labels_applied_by (applied_by),
    INDEX idx_trello_card_labels_applied_at (applied_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;