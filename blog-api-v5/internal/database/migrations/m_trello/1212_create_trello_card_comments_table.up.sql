CREATE TABLE IF NOT EXISTS trello_card_comments (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    card_id INT UNSIGNED NOT NULL,
    board_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    content TEXT NOT NULL,
    type ENUM('comment', 'action') NOT NULL DEFAULT 'comment',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_card_comments_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_comments_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_comments_card_id FOREIGN KEY (card_id) REFERENCES trello_cards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_comments_board_id FOREIGN KEY (board_id) REFERENCES trello_boards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_comments_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_trello_card_comments_tenant_id (tenant_id),
    INDEX idx_trello_card_comments_website_id (website_id),
    INDEX idx_trello_card_comments_card_id (card_id),
    INDEX idx_trello_card_comments_board_id (board_id),
    INDEX idx_trello_card_comments_user_id (user_id),
    INDEX idx_trello_card_comments_tenant_website (tenant_id, website_id),
    INDEX idx_trello_card_comments_tenant_card (tenant_id, card_id),
    INDEX idx_trello_card_comments_tenant_board (tenant_id, board_id),
    INDEX idx_trello_card_comments_tenant_user (tenant_id, user_id),
    INDEX idx_trello_card_comments_type (type),
    INDEX idx_trello_card_comments_created_at (created_at),
    
    -- Full-text search index
    FULLTEXT INDEX idx_trello_card_comments_search (content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;