CREATE TABLE IF NOT EXISTS trello_card_activities (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    board_id INT UNSIGNED NOT NULL,
    card_id INT UNSIGNED NOT NULL,
    list_id INT UNSIGNED NULL,
    user_id INT UNSIGNED NOT NULL,
    type VARCHAR(50) NOT NULL,
    data JSON DEFAULT (JSON_OBJECT()),
    old_value JSON NULL,
    new_value JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Keys
    CONSTRAINT fk_trello_card_activities_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_activities_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_activities_board_id FOREIGN KEY (board_id) REFERENCES trello_boards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_activities_card_id FOREIGN KEY (card_id) REFERENCES trello_cards(id) ON DELETE CASCADE,
    CONSTRAINT fk_trello_card_activities_list_id FOREIGN KEY (list_id) REFERENCES trello_lists(id) ON DELETE SET NULL,
    CONSTRAINT fk_trello_card_activities_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_trello_card_activities_tenant_id (tenant_id),
    INDEX idx_trello_card_activities_website_id (website_id),
    INDEX idx_trello_card_activities_board_id (board_id),
    INDEX idx_trello_card_activities_card_id (card_id),
    INDEX idx_trello_card_activities_list_id (list_id),
    INDEX idx_trello_card_activities_user_id (user_id),
    INDEX idx_trello_card_activities_tenant_website (tenant_id, website_id),
    INDEX idx_trello_card_activities_tenant_board (tenant_id, board_id),
    INDEX idx_trello_card_activities_tenant_card (tenant_id, card_id),
    INDEX idx_trello_card_activities_tenant_user (tenant_id, user_id),
    INDEX idx_trello_card_activities_type (type),
    INDEX idx_trello_card_activities_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;