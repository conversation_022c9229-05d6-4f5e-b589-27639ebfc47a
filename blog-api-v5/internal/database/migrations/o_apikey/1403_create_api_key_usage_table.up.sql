-- Create API Key Usage table
CREATE TABLE IF NOT EXISTS api_key_usage (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    api_key_id INT UNSIGNED NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    method ENUM('GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS') NOT NULL,
    response_code SMALLINT UNSIGNED NOT NULL,
    response_time INT UNSIGNED NOT NULL COMMENT 'Response time in milliseconds',
    request_size INT UNSIGNED DEFAULT 0 COMMENT 'Request size in bytes',
    response_size INT UNSIGNED DEFAULT 0 COMMENT 'Response size in bytes',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IPv4 or IPv6 address',
    user_agent TEXT,
    referer <PERSON><PERSON><PERSON><PERSON>(500),
    request_id VARCHAR(36),
    error_message TEXT,
    metadata JSON DEFAULT (JSON_OBJECT()),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Primary key with partitioning column
    PRIMARY KEY (id, created_at),
    
    -- Indexes
    INDEX idx_api_key_usage_tenant_id (tenant_id),
    INDEX idx_api_key_usage_website_id (website_id),
    INDEX idx_api_key_usage_api_key_id (api_key_id),
    INDEX idx_api_key_usage_endpoint (endpoint),
    INDEX idx_api_key_usage_method (method),
    INDEX idx_api_key_usage_response_code (response_code),
    INDEX idx_api_key_usage_created_at (created_at),
    INDEX idx_api_key_usage_ip_address (ip_address),
    INDEX idx_api_key_usage_request_id (request_id),
    
    -- Composite indexes for common queries
    INDEX idx_api_key_usage_key_date (api_key_id, created_at),
    INDEX idx_api_key_usage_key_endpoint (api_key_id, endpoint),
    INDEX idx_api_key_usage_key_status (api_key_id, response_code),
    INDEX idx_api_key_usage_tenant_date (tenant_id, created_at),
    INDEX idx_api_key_usage_website_date (website_id, created_at),
    
    -- Foreign key constraints - disabled for partitioned tables
    -- CONSTRAINT fk_api_key_usage_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_api_key_usage_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    -- CONSTRAINT fk_api_key_usage_api_key_id FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    
    -- Check constraints
    CHECK (response_code BETWEEN 100 AND 599),
    CHECK (response_time >= 0),
    CHECK (request_size >= 0),
    CHECK (response_size >= 0),
    CHECK (LENGTH(endpoint) > 0),
    CHECK (LENGTH(ip_address) > 0),
    CHECK (JSON_VALID(metadata))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p2027 VALUES LESS THAN (2028),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);