-- Create API Key Rotation table
CREATE TABLE IF NOT EXISTS api_key_rotation (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    api_key_id INT UNSIGNED NOT NULL,
    old_key_hash VARCHAR(64) NOT NULL,
    new_key_hash VARCHAR(64) NOT NULL,
    rotation_type ENUM('manual', 'automatic', 'emergency', 'scheduled') NOT NULL DEFAULT 'manual',
    reason VARCHAR(255),
    grace_period_hours INT UNSIGNED DEFAULT 24,
    grace_period_expires_at DATETIME NULL,
    status ENUM('initiated', 'in_progress', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'initiated',
    rotated_by INT UNSIGNED,
    rotated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME NULL,
    metadata JSON DEFAULT (JSON_OBJECT()),
    
    -- Indexes
    INDEX idx_api_key_rotation_tenant_id (tenant_id),
    INDEX idx_api_key_rotation_website_id (website_id),
    INDEX idx_api_key_rotation_api_key_id (api_key_id),
    INDEX idx_api_key_rotation_old_key_hash (old_key_hash),
    INDEX idx_api_key_rotation_new_key_hash (new_key_hash),
    INDEX idx_api_key_rotation_type (rotation_type),
    INDEX idx_api_key_rotation_status (status),
    INDEX idx_api_key_rotation_rotated_by (rotated_by),
    INDEX idx_api_key_rotation_rotated_at (rotated_at),
    INDEX idx_api_key_rotation_grace_expires (grace_period_expires_at),
    
    -- Composite indexes
    INDEX idx_api_key_rotation_key_date (api_key_id, rotated_at),
    INDEX idx_api_key_rotation_tenant_date (tenant_id, rotated_at),
    INDEX idx_api_key_rotation_status_date (status, rotated_at),
    
    -- Foreign key constraints
    CONSTRAINT fk_api_key_rotation_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_rotation_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_rotation_api_key_id FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key_rotation_rotated_by FOREIGN KEY (rotated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Check constraints
    CHECK (LENGTH(old_key_hash) = 64),
    CHECK (LENGTH(new_key_hash) = 64),
    CHECK (old_key_hash != new_key_hash),
    CHECK (grace_period_hours >= 0 AND grace_period_hours <= 168), -- Max 1 week
    CHECK (JSON_VALID(metadata))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;