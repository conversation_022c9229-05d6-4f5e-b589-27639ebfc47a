# API Key Module Migrations

This directory contains database migrations for the API Key module (o_apikey).

## Migration Overview

The API Key module provides comprehensive API key management for the blog API system with the following components:

### 1401 - API Keys Table
- **Purpose**: Core API key storage and management
- **Features**: 
  - Secure key hash storage (SHA-256)
  - Multi-tenant isolation
  - Flexible permission system
  - Rate limiting configuration
  - Expiration and usage tracking
  - Status management (active, inactive, expired, revoked, deleted)

### 1402 - API Key Permissions Table
- **Purpose**: Granular permission management for API keys
- **Features**:
  - Resource-based permissions
  - Action-specific access control
  - Conditional permission logic
  - Tenant-scoped permissions

### 1403 - API Key Usage Table
- **Purpose**: Track API key usage and request analytics
- **Features**:
  - Request/response tracking
  - Performance metrics
  - IP address logging
  - Error tracking
  - Partitioned by year for performance

### 1404 - API Key Rotation Table
- **Purpose**: Manage API key rotation lifecycle
- **Features**:
  - Rotation history tracking
  - Grace period management
  - Multiple rotation types (manual, automatic, emergency)
  - Status tracking throughout rotation process

### 1405 - API Key Rate Limits Table
- **Purpose**: Advanced rate limiting management
- **Features**:
  - Multiple rate limit types
  - Sliding window implementation
  - Burst allowance support
  - Usage tracking and reset management

### 1406 - API Key Scopes Table
- **Purpose**: Scope-based access control
- **Features**:
  - Fine-grained scope definitions
  - Resource and action mapping
  - Expiring scopes support
  - Conditional scope logic

### 1407 - API Key Webhooks Table
- **Purpose**: Webhook management for API key events
- **Features**:
  - Event-driven webhook triggers
  - Retry logic and failure tracking
  - Success/failure analytics
  - Webhook security with secrets

### 1408 - API Key Analytics Table
- **Purpose**: Comprehensive analytics and reporting
- **Features**:
  - Hourly aggregated metrics
  - Performance analytics
  - Request type breakdown
  - Status code distribution
  - Partitioned by year for performance

## Database Design Principles

### Multi-Tenancy
- All tables include `tenant_id` and `website_id` for complete isolation
- Foreign key constraints ensure data integrity
- Composite indexes optimize tenant-scoped queries

### Performance Optimization
- Strategic indexing for common query patterns
- Partitioning for high-volume tables (usage, analytics)
- Proper data types for optimal storage

### Security
- SHA-256 key hashing for secure storage
- IP whitelisting support
- Audit trail for all key operations
- Secure webhook implementation

### Scalability
- Partitioned tables for time-series data
- Efficient rate limiting implementation
- Analytics aggregation for reporting

## Migration Dependencies

These migrations depend on the following existing tables:
- `tenants` - Multi-tenant isolation
- `websites` - Website-specific key scoping
- `users` - User association for key creation

## Usage Notes

### Key Generation
- Keys are generated with environment-specific prefixes (`sk_live_`, `sk_test_`)
- Only key hashes are stored in the database
- Keys are shown only once during creation

### Rate Limiting
- Sliding window algorithm implementation
- Support for burst allowances
- Multiple rate limit types (per minute, hour, day)

### Analytics
- Hourly aggregation for performance
- Automatic partitioning by year
- Comprehensive metrics collection

### Rotation
- Grace period support for zero-downtime rotation
- Multiple rotation triggers (manual, automatic, emergency)
- Complete audit trail

## Performance Considerations

### Indexing Strategy
- Primary keys on all tables
- Foreign key indexes for joins
- Composite indexes for common query patterns
- Covering indexes for read-heavy operations

### Partitioning
- Usage table partitioned by year
- Analytics table partitioned by year
- Automatic partition management

### Data Retention
- Usage data retention policies
- Analytics data archival strategies
- Automatic cleanup procedures

## Security Considerations

### Key Storage
- Keys are hashed with SHA-256
- Original keys are never stored
- Key prefixes for environment identification

### Access Control
- Tenant-scoped data access
- Permission-based API access
- IP whitelisting support

### Audit Trail
- Complete usage tracking
- Rotation history
- Permission changes log

## Maintenance

### Regular Tasks
- Partition management for time-series tables
- Analytics data archival
- Rate limit cleanup
- Expired key cleanup

### Monitoring
- Key usage patterns
- Rate limit violations
- Webhook delivery failures
- Performance metrics

## Migration Command

To run these migrations:

```bash
make migrate-up MODULE=o_apikey
```

To rollback:

```bash
make migrate-down MODULE=o_apikey
```

## Related Documentation

- [API Key Module Documentation](../../../docs/modules/api-key.md)
- [Authentication Documentation](../../../docs/modules/auth.md)
- [Rate Limiting Documentation](../../../docs/modules/rate-limiting.md)
- [Security Best Practices](../../../docs/security/best-practices.md)