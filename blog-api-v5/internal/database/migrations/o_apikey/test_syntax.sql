-- Test syntax of API Key migrations
-- This file can be used to verify migration syntax

-- Test 1: Basic table creation syntax
-- Should create tables in correct order due to foreign key dependencies

-- Dependencies: tenants, websites, users tables must exist

-- Test basic API key table
CREATE TABLE IF NOT EXISTS test_api_keys (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    key_hash VARCHAR(64) NOT NULL UNIQUE,
    key_prefix VARCHAR(12) NOT NULL,
    name VARCHAR(100) NOT NULL,
    CHECK (LENGTH(key_hash) = 64),
    CHECK (LENGTH(key_prefix) BETWEEN 8 AND 12)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Test JSON validation
CREATE TABLE IF NOT EXISTS test_json_fields (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    permissions JSON DEFAULT (J<PERSON><PERSON>_OBJECT()),
    scopes JSON DEFAULT (JSON_ARRAY()),
    CHECK (JSON_VALID(permissions)),
    CHECK (JSON_VALID(scopes))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Test partitioning syntax
CREATE TABLE IF NOT EXISTS test_partitioned (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- Cleanup
DROP TABLE IF EXISTS test_partitioned;
DROP TABLE IF EXISTS test_json_fields;
DROP TABLE IF EXISTS test_api_keys;