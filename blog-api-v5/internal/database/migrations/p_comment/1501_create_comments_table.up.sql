-- Migration: 1501_create_comments_table
-- Description: Create the main comments table with ENUM commentable_type
-- Module: p_comment
-- Range: 1501-1599

CREATE TABLE IF NOT EXISTS comments (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Polymorphic fields with ENU<PERSON> for type safety
    commentable_type ENUM('blog_post', 'media_file', 'user', 'product', 'page', 'website') NOT NULL,
    commentable_id INT UNSIGNED NOT NULL,
    
    -- Comment data
    content TEXT NOT NULL,
    parent_id INT UNSIGNED NULL, -- For nested comments (1 level only)
    
    -- Status and metadata
    status ENUM('pending', 'approved', 'rejected', 'spam', 'deleted') DEFAULT 'pending',
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_comments_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_comments_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_comments_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_comments_parent_id FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_comments_tenant_id (tenant_id),
    INDEX idx_comments_website_id (website_id),
    INDEX idx_comments_tenant_website (tenant_id, website_id),
    INDEX idx_comments_commentable (commentable_type, commentable_id),
    INDEX idx_comments_user_id (user_id),
    INDEX idx_comments_parent_id (parent_id),
    INDEX idx_comments_status (status),
    INDEX idx_comments_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;