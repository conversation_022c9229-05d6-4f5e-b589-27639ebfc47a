-- Migration: 1502_create_comment_reactions_table
-- Description: Create table for comment reactions (likes, dislikes, etc.)
-- Module: p_comment

CREATE TABLE IF NOT EXISTS comment_reactions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    comment_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    reaction_type ENUM('like', 'dislike', 'love', 'haha', 'wow', 'sad', 'angry') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_comment_reactions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_comment_reactions_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_comment_reactions_comment_id FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    CONSTRAINT fk_comment_reactions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint: one reaction per user per comment per tenant+website
    UNIQUE KEY uk_comment_reactions_user_comment (tenant_id, website_id, comment_id, user_id),
    
    -- Indexes for performance
    INDEX idx_comment_reactions_comment_id (comment_id),
    INDEX idx_comment_reactions_tenant_website (tenant_id, website_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;