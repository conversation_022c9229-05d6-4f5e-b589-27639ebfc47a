-- Migration: 1503_create_comment_mentions_table
-- Description: Create table for tracking user mentions (@mentions) in comments
-- Module: p_comment

CREATE TABLE IF NOT EXISTS comment_mentions (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    comment_id INT UNSIGNED NOT NULL,
    mentioned_user_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_comment_mentions_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_comment_mentions_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_comment_mentions_comment_id FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    CONSTRAINT fk_comment_mentions_user_id FOREI<PERSON><PERSON> KEY (mentioned_user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint: prevent duplicate mentions in same comment
    UNIQUE KEY uk_comment_mentions (tenant_id, website_id, comment_id, mentioned_user_id),
    
    -- Indexes for performance
    INDEX idx_comment_mentions_user_id (mentioned_user_id),
    INDEX idx_comment_mentions_tenant_website (tenant_id, website_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;