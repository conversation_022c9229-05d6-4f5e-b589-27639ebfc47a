-- Migration: 1504_create_comment_moderation_logs_table
-- Description: Create audit trail table for all moderation actions on comments
-- Module: p_comment

CREATE TABLE IF NOT EXISTS comment_moderation_logs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    comment_id INT UNSIGNED NOT NULL,
    moderator_id INT UNSIGNED NOT NULL,
    action ENUM('approve', 'reject', 'mark_spam', 'delete') NOT NULL,
    reason TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_moderation_logs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    CONSTRAINT fk_moderation_logs_website_id FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    CONSTRAINT fk_moderation_logs_comment_id FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    CONSTRAINT fk_moderation_logs_moderator_id FOREIGN KEY (moderator_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_moderation_logs_comment_id (comment_id),
    INDEX idx_moderation_logs_moderator_id (moderator_id),
    INDEX idx_moderation_logs_tenant_website (tenant_id, website_id),
    INDEX idx_moderation_logs_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;