package apikey

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/models"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// APIKeySeeder handles seeding of API keys
type APIKeySeeder struct {
	db *gorm.DB
}

// NewAPIKeySeeder creates a new API key seeder
func NewAPIKeySeeder(db *gorm.DB) *APIKeySeeder {
	return &APIKeySeeder{db: db}
}

// generateAPIKey generates a secure API key
func generateAPIKey() (string, string, error) {
	// Generate 32 bytes of random data
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", "", err
	}

	// Create the API key with prefix
	apiKey := fmt.Sprintf("sk_live_%s", hex.EncodeToString(bytes))
	
	// Create hash for storage
	hash := sha256.Sum256([]byte(apiKey))
	keyHash := hex.EncodeToString(hash[:])
	
	return apiKey, keyHash, nil
}

// Seed creates initial API keys
func (s *APIKeySeeder) Seed() error {
	// Generate API key for tenant 6, website 1
	apiKey, keyHash, err := generateAPIKey()
	if err != nil {
		return fmt.Errorf("failed to generate API key: %w", err)
	}

	// Create permissions JSON
	permissionsJSON, _ := datatypes.JSON([]byte(`{
		"pages": ["read"],
		"blocks": ["read"],
		"posts": ["read"],
		"media": ["read"]
	}`)).MarshalJSON()

	// Create scopes JSON
	scopesJSON, _ := datatypes.JSON([]byte(`["read:pages", "read:blocks", "read:posts", "read:media"]`)).MarshalJSON()

	// Create IP whitelist JSON (empty = allow all)
	ipWhitelistJSON, _ := datatypes.JSON([]byte(`[]`)).MarshalJSON()

	apiKeys := []models.APIKey{
		{
			TenantID:    1,
			WebsiteID:   2,
			KeyHash:     keyHash,
			KeyPrefix:   apiKey[:12], // Store first 12 chars as prefix for identification
			Name:        "Frontend Production Key",
			Description: "API key for frontend application to access public data",
			Status:      models.APIKeyStatusActive,
			Permissions: permissionsJSON,
			Scopes:      scopesJSON,
			IPWhitelist: ipWhitelistJSON,
			RateLimit:   10000, // 10,000 requests
			RateWindow:  3600,  // per hour
			CreatedBy:   1,     // Admin user
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	// Check if API key already exists
	var existingKey models.APIKey
	result := s.db.Where("tenant_id = ? AND website_id = ? AND name = ?", 1, 2, "Frontend Production Key").First(&existingKey)
	
	if result.Error == nil {
		// Delete existing key to recreate it
		s.db.Delete(&existingKey)
		fmt.Println("Deleted existing API key for tenant 1, website 2")
	}

	// Create the API key
	for _, key := range apiKeys {
		if err := s.db.Create(&key).Error; err != nil {
			return fmt.Errorf("failed to create API key: %w", err)
		}
	}

	// Print the generated API key (this is the only time it will be shown)
	fmt.Println("=================================================")
	fmt.Println("API KEY GENERATED FOR TENANT 1, WEBSITE 2:")
	fmt.Println("=================================================")
	fmt.Printf("API Key: %s\n", apiKey)
	fmt.Println("=================================================")
	fmt.Println("IMPORTANT: Save this key securely. It won't be shown again!")
	fmt.Println("=================================================")

	// Also create some sample scopes for the API key
	apiKeyScopes := []models.APIKeyScope{
		{
			TenantID:  1,
			WebsiteID: 2,
			APIKeyID:  1, // This will be auto-corrected after creation
			Scope:     "read:pages",
			Resource:  "pages",
			Actions:   datatypes.JSON([]byte(`["read", "list"]`)),
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			TenantID:  1,
			WebsiteID: 2,
			APIKeyID:  1,
			Scope:     "read:blocks",
			Resource:  "blocks",
			Actions:   datatypes.JSON([]byte(`["read", "list"]`)),
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	// Get the actual API key ID that was created
	var createdKey models.APIKey
	if err := s.db.Where("tenant_id = ? AND website_id = ? AND key_hash = ?", 1, 2, keyHash).First(&createdKey).Error; err == nil {
		// Update scope API key IDs
		for i := range apiKeyScopes {
			apiKeyScopes[i].APIKeyID = createdKey.ID
		}

		// Create scopes
		for _, scope := range apiKeyScopes {
			if err := s.db.Create(&scope).Error; err != nil {
				fmt.Printf("Warning: Failed to create scope %s: %v\n", scope.Scope, err)
			}
		}
	}

	return nil
}

// Run executes the seeder
func (s *APIKeySeeder) Run() error {
	fmt.Println("Seeding API keys...")
	return s.Seed()
}