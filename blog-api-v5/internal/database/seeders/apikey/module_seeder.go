package apikey

import (
	"context"
	"database/sql"
	"fmt"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// ModuleSeeder handles API key module seeding
type ModuleSeeder struct {
	db *sql.DB
}

// NewAPIKeyModuleSeeder creates a new API key module seeder
func NewAPIKeyModuleSeeder(db *sql.DB) *ModuleSeeder {
	return &ModuleSeeder{db: db}
}

// Name returns the name of the seeder
func (s *ModuleSeeder) Name() string {
	return "APIKeyModule"
}

// Seed runs all API key module seeders
func (s *ModuleSeeder) Seed(ctx context.Context) error {
	// Convert sql.DB to GORM DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: s.db,
	}), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to create GORM DB: %w", err)
	}

	// Run API key seeder
	apiKeySeeder := NewAPIKeySeeder(gormDB)
	if err := apiKeySeeder.Run(); err != nil {
		return fmt.Errorf("failed to run API key seeder: %w", err)
	}

	return nil
}