package auth

import (
	"context"
	"database/sql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// AuthModuleSeeder wraps all auth seeders
type AuthModuleSeeder struct {
	db                   *sql.DB
	oauthProvidersSeeder *OAuthProvidersSeeder
}

// NewAuthModuleSeeder creates a new auth module seeder
func NewAuthModuleSeeder(db *sql.DB) *AuthModuleSeeder {
	return &AuthModuleSeeder{
		db:                   db,
		oauthProvidersSeeder: &OAuthProvidersSeeder{},
	}
}

// Name returns the name of this seeder
func (s *AuthModuleSeeder) Name() string {
	return "auth_module"
}

// Seed runs all auth seeders in the correct order
func (s *AuthModuleSeeder) Seed(ctx context.Context) error {
	// Convert sql.DB to gorm.DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: s.db,
	}), &gorm.Config{})
	if err != nil {
		return err
	}

	// Run seeders in dependency order
	seeders := []interface {
		Seed(*gorm.DB) error
	}{
		s.oauthProvidersSeeder, // OAuth providers
	}

	for _, seeder := range seeders {
		if err := seeder.Seed(gormDB); err != nil {
			return err
		}
	}

	return nil
}

// GetAllSeeders returns all individual auth seeders for registration
func GetAllSeeders() map[string]interface{} {
	return map[string]interface{}{
		"oauth_providers": &OAuthProvidersSeeder{},
	}
}
