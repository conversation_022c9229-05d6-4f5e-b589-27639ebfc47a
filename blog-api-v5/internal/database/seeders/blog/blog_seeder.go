package blog

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"strings"
	"time"
)

// BlogSeeder implements the blog data seeding
type BlogSeeder struct {
	db *sql.DB
}

// NewBlogSeeder creates a new blog seeder
func NewBlogSeeder(db *sql.DB) *BlogSeeder {
	return &BlogSeeder{db: db}
}

// Name returns the seeder name
func (s *BlogSeeder) Name() string {
	return "blog_seeder"
}

// Dependencies returns the seeders that must run before this one
func (s *BlogSeeder) Dependencies() []string {
	return []string{"website_seeder", "user_seeder"}
}

// Seed executes the blog seeding process
func (s *BlogSeeder) Seed(ctx context.Context) error {
	log.Println("Starting blog module seeding...")

	// Get available websites
	websiteIDs, err := s.getAvailableWebsites(ctx)
	if err != nil {
		return fmt.Errorf("failed to get available websites: %w", err)
	}

	if len(websiteIDs) == 0 {
		return fmt.Errorf("no websites available for seeding blog content")
	}

	// Create blog content for each website
	for _, websiteID := range websiteIDs {
		if err := s.seedBlogForWebsite(ctx, websiteID); err != nil {
			return fmt.Errorf("failed to seed blog for website %d: %w", websiteID, err)
		}
	}

	log.Println("Blog module seeding completed successfully")
	return nil
}

// getAvailableWebsites retrieves active website IDs
func (s *BlogSeeder) getAvailableWebsites(ctx context.Context) ([]int, error) {
	query := `SELECT id FROM websites WHERE status = 'active' ORDER BY id LIMIT 5`
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var websiteIDs []int
	for rows.Next() {
		var id int
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		websiteIDs = append(websiteIDs, id)
	}

	return websiteIDs, nil
}

// seedBlogForWebsite creates sample blog content for a specific website
func (s *BlogSeeder) seedBlogForWebsite(ctx context.Context, websiteID int) error {
	// Create categories
	categoryIDs, err := s.seedCategories(ctx, websiteID)
	if err != nil {
		return fmt.Errorf("failed to seed categories: %w", err)
	}

	// Create tags
	tagIDs, err := s.seedTags(ctx, websiteID)
	if err != nil {
		return fmt.Errorf("failed to seed tags: %w", err)
	}

	// Get available authors for this website
	authorIDs, err := s.getAvailableAuthors(ctx, websiteID)
	if err != nil {
		return fmt.Errorf("failed to get authors: %w", err)
	}

	if len(authorIDs) == 0 {
		return fmt.Errorf("no authors available for website %d", websiteID)
	}

	// Create posts
	if err := s.seedPosts(ctx, websiteID, categoryIDs, tagIDs, authorIDs); err != nil {
		return fmt.Errorf("failed to seed posts: %w", err)
	}

	log.Printf("Created blog content for website ID: %d", websiteID)
	return nil
}

// getAvailableAuthors retrieves user IDs for a website's tenant
func (s *BlogSeeder) getAvailableAuthors(ctx context.Context, websiteID int) ([]int, error) {
	query := `
		SELECT u.id 
		FROM users u 
		JOIN tenant_memberships tm ON u.id = tm.user_id
		JOIN websites w ON tm.tenant_id = w.tenant_id 
		WHERE w.id = ? AND u.status = 'active' AND tm.status = 'active'
		LIMIT 5
	`

	rows, err := s.db.QueryContext(ctx, query, websiteID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var authorIDs []int
	for rows.Next() {
		var id int
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		authorIDs = append(authorIDs, id)
	}

	return authorIDs, nil
}

// seedCategories creates sample categories
func (s *BlogSeeder) seedCategories(ctx context.Context, websiteID int) ([]int, error) {
	categories := []struct {
		Slug        string
		Name        string
		Description string
		Color       string
	}{
		{
			Slug:        "technology",
			Name:        "Technology",
			Description: "Articles about latest technology trends and innovations",
			Color:       "#3498db",
		},
		{
			Slug:        "programming",
			Name:        "Programming",
			Description: "Programming tutorials, tips, and best practices",
			Color:       "#9b59b6",
		},
		{
			Slug:        "web-development",
			Name:        "Web Development",
			Description: "Everything about web development, frontend and backend",
			Color:       "#e74c3c",
		},
		{
			Slug:        "mobile-development",
			Name:        "Mobile Development",
			Description: "iOS, Android, and cross-platform mobile development",
			Color:       "#1abc9c",
		},
		{
			Slug:        "devops",
			Name:        "DevOps",
			Description: "DevOps practices, CI/CD, and infrastructure automation",
			Color:       "#f39c12",
		},
	}

	var categoryIDs []int

	for i, cat := range categories {
		// Check if category already exists
		var existingID int
		err := s.db.QueryRowContext(ctx, `
			SELECT id FROM blog_categories 
			WHERE website_id = ? AND slug = ? 
			LIMIT 1
		`, websiteID, cat.Slug).Scan(&existingID)

		if err == nil {
			// Category already exists, use existing ID
			categoryIDs = append(categoryIDs, existingID)
			continue
		} else if !errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("failed to check existing category: %w", err)
		}

		// Get tenant_id for this website
		var tenantID int
		if err := s.db.QueryRowContext(ctx, `SELECT tenant_id FROM websites WHERE id = ?`, websiteID).Scan(&tenantID); err != nil {
			return nil, fmt.Errorf("failed to get tenant_id for website %d: %w", websiteID, err)
		}

		// Insert category
		result, err := s.db.ExecContext(ctx, `
			INSERT INTO blog_categories (
				tenant_id, website_id, slug, name, description,
				parent_id, lft, rgt, level, color, 
				is_active, sort_order, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, NULL, ?, ?, 0, ?, TRUE, ?, 'active', NOW(), NOW())
		`, tenantID, websiteID, cat.Slug, cat.Name, cat.Description,
			i*2+1, i*2+2, cat.Color, i+1)

		if err != nil {
			return nil, err
		}

		categoryID, err := result.LastInsertId()
		if err != nil {
			return nil, err
		}

		categoryIDs = append(categoryIDs, int(categoryID))
	}

	// Create nested categories under Programming
	if len(categoryIDs) > 1 {
		programmingID := categoryIDs[1] // Programming category

		// Update Programming category's lft/rgt for nested structure
		_, err := s.db.ExecContext(ctx, `
			UPDATE blog_categories SET lft = 3, rgt = 10 WHERE id = ?
		`, programmingID)
		if err != nil {
			return nil, err
		}

		// Add Frontend subcategory
		var frontendID int64
		err = s.db.QueryRowContext(ctx, `
			SELECT id FROM blog_categories 
			WHERE website_id = ? AND slug = ? 
			LIMIT 1
		`, websiteID, "frontend").Scan(&frontendID)

		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("failed to check existing frontend category: %w", err)
		}

		if errors.Is(err, sql.ErrNoRows) {
			// Frontend category doesn't exist, create it
			result, err := s.db.ExecContext(ctx, `
				INSERT INTO blog_categories (
					tenant_id, website_id, slug, name, description,
					parent_id, lft, rgt, level, color,
					is_active, sort_order, status, created_at, updated_at
				) VALUES (?, ?, ?, ?, ?, 4, 5, 1, ?, TRUE, 1, 'active', NOW(), NOW())
			`, websiteID, "frontend", "Frontend", "Frontend development with modern frameworks",
				programmingID, "#2ecc71")

			if err != nil {
				return nil, err
			}

			frontendID, _ = result.LastInsertId()
		}

		categoryIDs = append(categoryIDs, int(frontendID))

		// Add Backend subcategory
		var backendID int64
		err = s.db.QueryRowContext(ctx, `
			SELECT id FROM blog_categories 
			WHERE website_id = ? AND slug = ? 
			LIMIT 1
		`, websiteID, "backend").Scan(&backendID)

		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("failed to check existing backend category: %w", err)
		}

		if errors.Is(err, sql.ErrNoRows) {
			// Backend category doesn't exist, create it
			result, err := s.db.ExecContext(ctx, `
				INSERT INTO blog_categories (
					tenant_id, website_id, slug, name, description,
					parent_id, lft, rgt, level, color,
					is_active, sort_order, status, created_at, updated_at
				) VALUES (?, ?, ?, ?, ?, 6, 7, 1, ?, TRUE, 2, 'active', NOW(), NOW())
			`, websiteID, "backend", "Backend", "Backend development and API design",
				programmingID, "#e67e22")

			if err != nil {
				return nil, err
			}

			backendID, _ = result.LastInsertId()
		}

		categoryIDs = append(categoryIDs, int(backendID))
	}

	return categoryIDs, nil
}

// seedTags creates sample tags
func (s *BlogSeeder) seedTags(ctx context.Context, websiteID int) ([]int, error) {
	tags := []struct {
		Slug  string
		Name  string
		Desc  string
		Color string
	}{
		{"golang", "Go", "Go programming language", "#00ADD8"},
		{"javascript", "JavaScript", "JavaScript programming", "#F7DF1E"},
		{"react", "React", "React framework", "#61DAFB"},
		{"nodejs", "Node.js", "Node.js runtime", "#339933"},
		{"docker", "Docker", "Docker containerization", "#2496ED"},
		{"kubernetes", "Kubernetes", "Container orchestration", "#326CE5"},
		{"mysql", "MySQL", "MySQL database", "#4479A1"},
		{"redis", "Redis", "Redis cache", "#DC382D"},
		{"api", "API", "API development", "#FF6C37"},
		{"microservices", "Microservices", "Microservices architecture", "#00897B"},
		{"security", "Security", "Security best practices", "#F44336"},
		{"performance", "Performance", "Performance optimization", "#4CAF50"},
		{"testing", "Testing", "Software testing", "#9C27B0"},
		{"ci-cd", "CI/CD", "Continuous Integration/Deployment", "#FF9800"},
		{"cloud", "Cloud", "Cloud computing", "#2196F3"},
	}

	var tagIDs []int

	for _, tag := range tags {
		// Check if tag already exists
		var existingID int
		err := s.db.QueryRowContext(ctx, `
			SELECT id FROM blog_tags 
			WHERE website_id = ? AND slug = ? 
			LIMIT 1
		`, websiteID, tag.Slug).Scan(&existingID)

		if err == nil {
			// Tag already exists, use existing ID
			tagIDs = append(tagIDs, existingID)
			continue
		} else if !errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("failed to check existing tag: %w", err)
		}

		// Get tenant_id for this website
		var tenantID int
		if err := s.db.QueryRowContext(ctx, `SELECT tenant_id FROM websites WHERE id = ?`, websiteID).Scan(&tenantID); err != nil {
			return nil, fmt.Errorf("failed to get tenant_id for website %d: %w", websiteID, err)
		}

		// Insert tag
		result, err := s.db.ExecContext(ctx, `
			INSERT INTO blog_tags (
				tenant_id, website_id, slug, name, description, color, 
				usage_count, is_active, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, 0, TRUE, 'active', NOW(), NOW())
		`, tenantID, websiteID, tag.Slug, tag.Name, tag.Desc, tag.Color)

		if err != nil {
			return nil, err
		}

		tagID, err := result.LastInsertId()
		if err != nil {
			return nil, err
		}

		tagIDs = append(tagIDs, int(tagID))
	}

	return tagIDs, nil
}

// seedPosts creates sample blog posts
func (s *BlogSeeder) seedPosts(ctx context.Context, websiteID int, categoryIDs, tagIDs, authorIDs []int) error {
	// Get tenant_id for this website
	var tenantID int
	err := s.db.QueryRowContext(ctx, `SELECT tenant_id FROM websites WHERE id = ?`, websiteID).Scan(&tenantID)
	if err != nil {
		return fmt.Errorf("failed to get tenant_id for website %d: %w", websiteID, err)
	}
	posts := []struct {
		Slug          string
		Title         string
		Content       string
		Excerpt       string
		CategoryIdx   int
		TagIndices    []int
		IsFeatured    bool
		Status        string
		WorkflowState string
		AssignedToIdx *int // Index into authorIDs for assignment
		WorkflowNotes string
		PublishedAgo  int // days ago
	}{
		{
			Slug:  "getting-started-with-go-programming",
			Title: "Getting Started with Go Programming",
			Content: `# Getting Started with Go Programming

Go, also known as Golang, is a statically typed, compiled programming language designed at Google. It's known for its simplicity, efficiency, and excellent support for concurrent programming.

## Why Choose Go?

1. **Simple and Clean Syntax**: Go's syntax is clean and easy to learn
2. **Built-in Concurrency**: Goroutines and channels make concurrent programming easy
3. **Fast Compilation**: Go compiles quickly to machine code
4. **Standard Library**: Rich standard library for common tasks

## Installation

To get started with Go, download it from the official website and follow the installation instructions for your operating system.

## Your First Go Program

Here's the traditional "Hello, World!" program in Go:

` + "```go" + `
package main

import "fmt"

func main() {
    fmt.Println("Hello, World!")
}
` + "```" + `

## Next Steps

- Learn about Go's type system
- Understand packages and imports
- Explore goroutines and channels
- Build your first web server`,
			Excerpt:       "Learn the basics of Go programming language, its features, and how to write your first Go program.",
			CategoryIdx:   1,        // Programming
			TagIndices:    []int{0}, // golang
			IsFeatured:    true,
			Status:        "published",
			WorkflowState: "completed",
			AssignedToIdx: nil,
			WorkflowNotes: "Excellent introductory content, approved for publication",
			PublishedAgo:  5,
		},
		{
			Slug:  "building-rest-api-with-go-and-gin",
			Title: "Building a REST API with Go and Gin",
			Content: `# Building a REST API with Go and Gin

Gin is a high-performance HTTP web framework written in Go. It features a martini-like API with much better performance.

## Setting Up Gin

First, install Gin using go get:

` + "```bash" + `
go get -u github.com/gin-gonic/gin
` + "```" + `

## Creating a Simple API

Here's how to create a basic REST API:

` + "```go" + `
package main

import (
    "net/http"
    "github.com/gin-gonic/gin"
)

type Book struct {
    ID     string ` + "`json:\"id\"`" + `
    Title  string ` + "`json:\"title\"`" + `
    Author string ` + "`json:\"author\"`" + `
}

var books = []Book{
    {ID: "1", Title: "Clean Code", Author: "Robert Martin"},
    {ID: "2", Title: "The Go Programming Language", Author: "Alan Donovan"},
}

func main() {
    router := gin.Default()
    
    router.GET("/books", getBooks)
    router.GET("/books/:id", getBookByID)
    router.POST("/books", createBook)
    
    router.Run(":8080")
}

func getBooks(c *gin.Context) {
    c.JSON(http.StatusOK, books)
}
` + "```" + `

## Best Practices

- Use proper HTTP status codes
- Implement proper error handling
- Add authentication and authorization
- Version your API
- Document your endpoints`,
			Excerpt:      "Learn how to build a RESTful API using Go and the Gin web framework with practical examples.",
			CategoryIdx:  2,           // Web Development
			TagIndices:   []int{0, 8}, // golang, api
			IsFeatured:   false,
			Status:       "published",
			PublishedAgo: 3,
		},
		{
			Slug:  "react-hooks-complete-guide",
			Title: "React Hooks: A Complete Guide",
			Content: `# React Hooks: A Complete Guide

React Hooks revolutionized how we write React components by allowing us to use state and other React features in functional components.

## Understanding useState

The useState Hook lets you add state to functional components:

` + "```jsx" + `
import React, { useState } from 'react';

function Counter() {
    const [count, setCount] = useState(0);
    
    return (
        <div>
            <p>You clicked {count} times</p>
            <button onClick={() => setCount(count + 1)}>
                Click me
            </button>
        </div>
    );
}
` + "```" + `

## The useEffect Hook

useEffect lets you perform side effects in functional components:

` + "```jsx" + `
useEffect(() => {
    document.title = ` + "`You clicked ${count} times`" + `;
    
    return () => {
        // Cleanup
    };
}, [count]);
` + "```" + `

## Custom Hooks

You can create your own hooks to reuse stateful logic:

` + "```jsx" + `
function useCounter(initialValue = 0) {
    const [count, setCount] = useState(initialValue);
    
    const increment = () => setCount(count + 1);
    const decrement = () => setCount(count - 1);
    const reset = () => setCount(initialValue);
    
    return { count, increment, decrement, reset };
}
` + "```",
			Excerpt:      "Master React Hooks with this comprehensive guide covering useState, useEffect, and custom hooks.",
			CategoryIdx:  5,           // Frontend (nested under Programming)
			TagIndices:   []int{1, 2}, // javascript, react
			IsFeatured:   true,
			Status:       "published",
			PublishedAgo: 7,
		},
		{
			Slug:  "docker-containerization-best-practices",
			Title: "Docker Containerization Best Practices",
			Content: `# Docker Containerization Best Practices

Docker has become the de facto standard for containerization. Here are some best practices to follow when working with Docker.

## 1. Keep Images Small

- Use alpine-based images when possible
- Multi-stage builds to reduce final image size
- Remove unnecessary dependencies

## 2. Security Best Practices

- Don't run containers as root
- Scan images for vulnerabilities
- Use official base images
- Keep secrets out of images

## 3. Dockerfile Best Practices

` + "```dockerfile" + `
# Good practice: Use specific versions
FROM node:16-alpine

# Set working directory
WORKDIR /app

# Copy package files first (better caching)
COPY package*.json ./
RUN npm ci --only=production

# Copy application files
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
USER nodejs

# Expose port
EXPOSE 3000

# Start application
CMD ["node", "server.js"]
` + "```" + `

## 4. Container Orchestration

When running containers in production, consider:
- Using Kubernetes for orchestration
- Implementing health checks
- Setting resource limits
- Using container registries`,
			Excerpt:      "Learn Docker containerization best practices for building secure, efficient, and maintainable containers.",
			CategoryIdx:  4,           // DevOps
			TagIndices:   []int{4, 5}, // docker, kubernetes
			IsFeatured:   false,
			Status:       "published",
			PublishedAgo: 10,
		},
		{
			Slug:  "microservices-architecture-patterns",
			Title: "Microservices Architecture Patterns",
			Content: `# Microservices Architecture Patterns

Microservices architecture has become increasingly popular for building scalable and maintainable applications. Let's explore key patterns.

## Service Communication Patterns

### 1. API Gateway Pattern
- Single entry point for clients
- Handles routing, authentication, and rate limiting
- Reduces coupling between clients and services

### 2. Service Mesh
- Dedicated infrastructure layer for service-to-service communication
- Provides observability, security, and reliability

## Data Management Patterns

### Database per Service
- Each microservice owns its data
- Ensures loose coupling
- Enables independent scaling

### Event Sourcing
- Store events instead of current state
- Provides audit trail
- Enables temporal queries

## Resilience Patterns

### Circuit Breaker
` + "```go" + `
type CircuitBreaker struct {
    maxFailures  int
    resetTimeout time.Duration
    // ... implementation
}
` + "```" + `

### Retry with Backoff
- Exponential backoff for failed requests
- Prevents cascading failures

## Best Practices

1. Design for failure
2. Implement proper monitoring
3. Use containerization
4. Automate everything
5. Keep services small and focused`,
			Excerpt:      "Explore essential microservices architecture patterns for building scalable distributed systems.",
			CategoryIdx:  0,               // Technology
			TagIndices:   []int{9, 8, 14}, // microservices, api, cloud
			IsFeatured:   true,
			Status:       "published",
			PublishedAgo: 2,
		},
		{
			Slug:  "nodejs-performance-optimization",
			Title: "Node.js Performance Optimization Techniques",
			Content: `# Node.js Performance Optimization Techniques

Node.js is known for its performance, but there's always room for optimization. Here are key techniques to improve your Node.js application performance.

## 1. Asynchronous Programming

Always use asynchronous methods:

` + "```javascript" + `
// Bad
const data = fs.readFileSync('./large-file.json');

// Good
const data = await fs.promises.readFile('./large-file.json');
` + "```" + `

## 2. Caching Strategies

### In-Memory Caching
` + "```javascript" + `
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 600 });

async function getCachedData(key) {
    let data = cache.get(key);
    if (!data) {
        data = await fetchFromDatabase(key);
        cache.set(key, data);
    }
    return data;
}
` + "```" + `

### Redis Caching
- Use Redis for distributed caching
- Implement cache invalidation strategies

## 3. Database Optimization

- Use connection pooling
- Index your queries properly
- Implement pagination
- Use projections to limit data

## 4. Monitoring and Profiling

- Use APM tools (New Relic, DataDog)
- Profile CPU and memory usage
- Monitor event loop lag
- Track response times

## 5. Clustering

` + "```javascript" + `
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }
} else {
    // Worker process
    require('./server');
}
` + "```",
			Excerpt:      "Discover proven techniques to optimize Node.js application performance and scalability.",
			CategoryIdx:  6,               // Backend (nested under Programming)
			TagIndices:   []int{1, 3, 11}, // javascript, nodejs, performance
			IsFeatured:   false,
			Status:       "published",
			PublishedAgo: 15,
		},
		{
			Slug:  "mysql-query-optimization-guide",
			Title: "MySQL Query Optimization Guide",
			Content: `# MySQL Query Optimization Guide

Database performance is crucial for application scalability. This guide covers MySQL query optimization techniques.

## Understanding Query Execution

Use EXPLAIN to understand query execution:

` + "```sql" + `
EXPLAIN SELECT u.name, COUNT(p.id) as post_count
FROM users u
LEFT JOIN posts p ON u.id = p.user_id
WHERE u.status = 'active'
GROUP BY u.id;
` + "```" + `

## Indexing Strategies

### Primary Indexes
- Always have a primary key
- Use AUTO_INCREMENT for surrogate keys

### Composite Indexes
` + "```sql" + `
-- Good for queries filtering by status and created_at
CREATE INDEX idx_posts_status_created 
ON posts(status, created_at);
` + "```" + `

### Covering Indexes
Include all columns needed by the query:
` + "```sql" + `
CREATE INDEX idx_users_covering 
ON users(status, email, name);
` + "```" + `

## Query Optimization Tips

1. **Avoid SELECT ***
   - Only select needed columns
   - Reduces network traffic

2. **Use LIMIT for large results**
   ` + "```sql" + `
   SELECT * FROM posts 
   ORDER BY created_at DESC 
   LIMIT 20;
   ` + "```" + `

3. **Optimize JOINs**
   - Index foreign keys
   - Join on indexed columns
   - Consider denormalization for read-heavy workloads

4. **Batch Operations**
   ` + "```sql" + `
   INSERT INTO users (name, email) VALUES 
   ('John', '<EMAIL>'),
   ('Jane', '<EMAIL>'),
   ('Bob', '<EMAIL>');
   ` + "```" + `

## Performance Monitoring

- Enable slow query log
- Monitor query execution time
- Use performance schema
- Regular ANALYZE TABLE`,
			Excerpt:      "Master MySQL query optimization with indexing strategies, query analysis, and performance tips.",
			CategoryIdx:  6,            // Backend
			TagIndices:   []int{6, 11}, // mysql, performance
			IsFeatured:   false,
			Status:       "published",
			PublishedAgo: 20,
		},
		{
			Slug:  "implementing-ci-cd-pipeline",
			Title: "Implementing a Modern CI/CD Pipeline",
			Content: `# Implementing a Modern CI/CD Pipeline

Continuous Integration and Continuous Deployment are essential for modern software development. Let's build a complete pipeline.

## Pipeline Architecture

### 1. Source Control (GitHub)
- Feature branches
- Pull requests
- Code reviews

### 2. CI Pipeline (GitHub Actions)

` + "```yaml" + `
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.19
      
      - name: Run Tests
        run: |
          go test -v ./...
          go test -race -coverprofile=coverage.txt ./...
      
      - name: Run Linter
        uses: golangci/golangci-lint-action@v3
        
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Build Docker Image
        run: |
          docker build -t myapp:${{ github.sha }} .
          docker push myregistry/myapp:${{ github.sha }}
` + "```" + `

### 3. Deployment Strategies

#### Blue-Green Deployment
- Zero downtime deployments
- Easy rollback
- Full environment switch

#### Canary Deployment
- Gradual rollout
- Monitor metrics
- Automatic rollback on errors

## Best Practices

1. **Automate Everything**
   - Tests, builds, deployments
   - Infrastructure as Code
   - Configuration management

2. **Security Integration**
   - SAST/DAST scanning
   - Dependency scanning
   - Container scanning

3. **Monitoring**
   - Deployment metrics
   - Application performance
   - Error rates`,
			Excerpt:      "Learn how to implement a modern CI/CD pipeline with automated testing, building, and deployment.",
			CategoryIdx:  4,               // DevOps
			TagIndices:   []int{13, 4, 5}, // ci-cd, docker, kubernetes
			IsFeatured:   true,
			Status:       "published",
			PublishedAgo: 1,
		},
		{
			Slug:  "web-application-security-fundamentals",
			Title: "Web Application Security Fundamentals",
			Content: `# Web Application Security Fundamentals

Security should be a primary concern in web development. This guide covers essential security practices.

## Common Vulnerabilities

### 1. SQL Injection
Prevent SQL injection with parameterized queries:

` + "```go" + `
// Bad
query := fmt.Sprintf("SELECT * FROM users WHERE id = %s", userID)

// Good
query := "SELECT * FROM users WHERE id = ?"
rows, err := db.Query(query, userID)
` + "```" + `

### 2. Cross-Site Scripting (XSS)
Always escape user input:

` + "```javascript" + `
// React automatically escapes
<div>{userInput}</div>

// Manual escaping for dynamic HTML
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}
` + "```" + `

### 3. CSRF Protection
Implement CSRF tokens:

` + "```go" + `
// Generate token
token := generateCSRFToken()
session.Set("csrf_token", token)

// Verify token
if request.FormValue("csrf_token") != session.Get("csrf_token") {
    return errors.New("invalid CSRF token")
}
` + "```" + `

## Security Headers

` + "```go" + `
func securityHeaders(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("X-Content-Type-Options", "nosniff")
        w.Header().Set("X-Frame-Options", "DENY")
        w.Header().Set("X-XSS-Protection", "1; mode=block")
        w.Header().Set("Strict-Transport-Security", "max-age=31536000")
        w.Header().Set("Content-Security-Policy", "default-src 'self'")
        next.ServeHTTP(w, r)
    })
}
` + "```" + `

## Authentication Best Practices

1. Use strong password policies
2. Implement MFA
3. Secure session management
4. Use HTTPS everywhere
5. Implement rate limiting`,
			Excerpt:      "Essential web application security practices to protect against common vulnerabilities and attacks.",
			CategoryIdx:  2,            // Web Development
			TagIndices:   []int{10, 8}, // security, api
			IsFeatured:   false,
			Status:       "published",
			PublishedAgo: 8,
		},
		{
			Slug:  "redis-caching-strategies",
			Title: "Redis Caching Strategies for High-Performance Apps",
			Content: `# Redis Caching Strategies for High-Performance Apps

Redis is a powerful in-memory data structure store. Let's explore caching strategies to boost application performance.

## Basic Caching Patterns

### 1. Cache-Aside (Lazy Loading)
` + "```go" + `
func GetUser(userID string) (*User, error) {
    // Try cache first
    cached, err := redis.Get(ctx, "user:"+userID).Result()
    if err == nil {
        var user User
        json.Unmarshal([]byte(cached), &user)
        return &user, nil
    }
    
    // Load from database
    user, err := db.GetUser(userID)
    if err != nil {
        return nil, err
    }
    
    // Cache for future
    userData, _ := json.Marshal(user)
    redis.Set(ctx, "user:"+userID, userData, 1*time.Hour)
    
    return user, nil
}
` + "```" + `

### 2. Write-Through Cache
Update cache when data changes:

` + "```go" + `
func UpdateUser(user *User) error {
    // Update database
    if err := db.UpdateUser(user); err != nil {
        return err
    }
    
    // Update cache
    userData, _ := json.Marshal(user)
    redis.Set(ctx, "user:"+user.ID, userData, 1*time.Hour)
    
    return nil
}
` + "```" + `

## Advanced Patterns

### Cache Invalidation
` + "```go" + `
// Tag-based invalidation
func InvalidateUserCache(userID string) {
    keys := []string{
        "user:" + userID,
        "user:posts:" + userID,
        "user:profile:" + userID,
    }
    redis.Del(ctx, keys...)
}
` + "```" + `

### Distributed Locking
` + "```go" + `
// Prevent cache stampede
lock := redis.SetNX(ctx, "lock:user:"+userID, "1", 30*time.Second)
if !lock.Val() {
    // Another process is updating
    time.Sleep(100 * time.Millisecond)
    return GetUser(userID) // Retry
}
defer redis.Del(ctx, "lock:user:"+userID)
` + "```" + `

## Performance Tips

1. Use pipelining for bulk operations
2. Set appropriate TTLs
3. Monitor memory usage
4. Use Redis Cluster for scaling
5. Implement cache warming`,
			Excerpt:      "Learn Redis caching strategies and patterns to dramatically improve application performance.",
			CategoryIdx:  6,            // Backend
			TagIndices:   []int{7, 11}, // redis, performance
			IsFeatured:   false,
			Status:       "published",
			PublishedAgo: 12,
		},
		{
			Slug:  "mobile-app-development-react-native",
			Title: "Building Cross-Platform Mobile Apps with React Native",
			Content: `# Building Cross-Platform Mobile Apps with React Native

React Native enables building native mobile apps using React. Let's explore how to create high-quality cross-platform applications.

## Getting Started

### Setup Development Environment

` + "```bash" + `
# Install React Native CLI
npm install -g react-native-cli

# Create new project
npx react-native init MyAwesomeApp

# Run on iOS
cd MyAwesomeApp
npx react-native run-ios

# Run on Android
npx react-native run-android
` + "```" + `

## Core Components

### Basic UI Components
` + "```jsx" + `
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView
} from 'react-native';

const App = () => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Welcome to React Native</Text>
      </View>
      
      <TouchableOpacity style={styles.button}>
        <Text style={styles.buttonText}>Get Started</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    margin: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
` + "```" + `

## Navigation

Using React Navigation:

` + "```jsx" + `
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

const Stack = createStackNavigator();

function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator>
        <Stack.Screen name="Home" component={HomeScreen} />
        <Stack.Screen name="Details" component={DetailsScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
` + "```" + `

## Performance Optimization

1. Use FlatList for long lists
2. Implement lazy loading
3. Optimize images
4. Use Hermes engine
5. Profile with Flipper`,
			Excerpt:      "Learn how to build native mobile applications for iOS and Android using React Native framework.",
			CategoryIdx:  3,           // Mobile Development
			TagIndices:   []int{1, 2}, // javascript, react
			IsFeatured:   true,
			Status:       "published",
			PublishedAgo: 4,
		},
		{
			Slug:  "automated-testing-best-practices",
			Title: "Automated Testing Best Practices",
			Content: `# Automated Testing Best Practices

Quality software requires comprehensive testing. Let's explore best practices for automated testing.

## Testing Pyramid

### Unit Tests (Base)
Fast, isolated tests for individual components:

` + "```go" + `
func TestCalculateDiscount(t *testing.T) {
    tests := []struct {
        name     string
        price    float64
        discount float64
        expected float64
    }{
        {"no discount", 100, 0, 100},
        {"10% discount", 100, 0.1, 90},
        {"50% discount", 100, 0.5, 50},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := CalculateDiscount(tt.price, tt.discount)
            if result != tt.expected {
                t.Errorf("got %f, want %f", result, tt.expected)
            }
        })
    }
}
` + "```" + `

### Integration Tests (Middle)
Test component interactions:

` + "```javascript" + `
describe('User API', () => {
    it('should create and retrieve user', async () => {
        const userData = {
            name: 'John Doe',
            email: '<EMAIL>'
        };
        
        // Create user
        const createResponse = await request(app)
            .post('/api/users')
            .send(userData)
            .expect(201);
            
        const userId = createResponse.body.id;
        
        // Retrieve user
        const getResponse = await request(app)
            .get(` + "`/api/users/${userId}`" + `)
            .expect(200);
            
        expect(getResponse.body.name).toBe(userData.name);
        expect(getResponse.body.email).toBe(userData.email);
    });
});
` + "```" + `

### E2E Tests (Top)
Test complete user workflows:

` + "```javascript" + `
describe('User Registration Flow', () => {
    it('should complete registration process', async () => {
        await page.goto('http://localhost:3000/register');
        
        await page.fill('#email', '<EMAIL>');
        await page.fill('#password', 'SecurePass123!');
        await page.click('button[type="submit"]');
        
        await page.waitForNavigation();
        expect(page.url()).toBe('http://localhost:3000/dashboard');
    });
});
` + "```" + `

## Best Practices

1. **Test Naming**: Use descriptive names
2. **Arrange-Act-Assert**: Structure tests clearly
3. **Test Independence**: Tests shouldn't depend on each other
4. **Mock External Dependencies**: Isolate what you're testing
5. **Continuous Testing**: Run tests in CI/CD pipeline`,
			Excerpt:      "Master automated testing with unit tests, integration tests, and end-to-end testing strategies.",
			CategoryIdx:  1,         // Programming
			TagIndices:   []int{12}, // testing
			IsFeatured:   false,
			Status:       "published",
			PublishedAgo: 6,
		},
		{
			Slug:  "cloud-native-application-development",
			Title: "Cloud-Native Application Development",
			Content: `# Cloud-Native Application Development

Cloud-native applications are designed to leverage cloud computing benefits. Let's explore the principles and practices.

## 12-Factor App Principles

### 1. Codebase
One codebase tracked in revision control, many deploys

### 2. Dependencies
Explicitly declare and isolate dependencies

### 3. Config
Store config in the environment

` + "```go" + `
// Good: Read from environment
dbHost := os.Getenv("DB_HOST")
dbPort := os.Getenv("DB_PORT")

// Bad: Hardcoded values
dbHost := "localhost"
dbPort := "5432"
` + "```" + `

## Containerization

### Dockerfile for Go Application
` + "```dockerfile" + `
# Build stage
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o main .

# Final stage
FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
EXPOSE 8080
CMD ["./main"]
` + "```" + `

## Kubernetes Deployment

` + "```yaml" + `
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: myapp
  template:
    metadata:
      labels:
        app: myapp
    spec:
      containers:
      - name: myapp
        image: myregistry/myapp:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: host
        resources:
          limits:
            memory: "256Mi"
            cpu: "500m"
          requests:
            memory: "128Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
` + "```" + `

## Cloud-Native Patterns

1. **Service Mesh**: Istio for traffic management
2. **Observability**: Distributed tracing with Jaeger
3. **Autoscaling**: HPA and VPA
4. **GitOps**: Flux or ArgoCD for deployments`,
			Excerpt:      "Build cloud-native applications with containers, Kubernetes, and modern deployment practices.",
			CategoryIdx:  4,               // DevOps
			TagIndices:   []int{14, 4, 5}, // cloud, docker, kubernetes
			IsFeatured:   false,
			Status:       "published",
			PublishedAgo: 9,
		},
		{
			Slug:  "golang-concurrency-patterns",
			Title: "Go Concurrency Patterns",
			Content: `# Go Concurrency Patterns

Go's concurrency model is one of its strongest features. Let's explore common patterns for writing concurrent Go programs.

## Goroutines and Channels

### Basic Channel Communication
` + "```go" + `
func worker(id int, jobs <-chan int, results chan<- int) {
    for job := range jobs {
        fmt.Printf("Worker %d processing job %d\n", id, job)
        time.Sleep(time.Second)
        results <- job * 2
    }
}

func main() {
    jobs := make(chan int, 100)
    results := make(chan int, 100)
    
    // Start workers
    for w := 1; w <= 3; w++ {
        go worker(w, jobs, results)
    }
    
    // Send jobs
    for j := 1; j <= 9; j++ {
        jobs <- j
    }
    close(jobs)
    
    // Collect results
    for r := 1; r <= 9; r++ {
        <-results
    }
}
` + "```" + `

## Common Patterns

### 1. Fan-Out/Fan-In
` + "```go" + `
func fanOut(in <-chan int, workers int) []<-chan int {
    channels := make([]<-chan int, workers)
    
    for i := 0; i < workers; i++ {
        ch := make(chan int)
        channels[i] = ch
        
        go func() {
            for val := range in {
                ch <- process(val)
            }
            close(ch)
        }()
    }
    
    return channels
}

func fanIn(channels ...<-chan int) <-chan int {
    out := make(chan int)
    var wg sync.WaitGroup
    
    for _, ch := range channels {
        wg.Add(1)
        go func(c <-chan int) {
            for val := range c {
                out <- val
            }
            wg.Done()
        }(ch)
    }
    
    go func() {
        wg.Wait()
        close(out)
    }()
    
    return out
}
` + "```" + `

### 2. Worker Pool Pattern
` + "```go" + `
type Pool struct {
    tasks   chan Task
    workers int
    wg      sync.WaitGroup
}

func NewPool(workers int) *Pool {
    return &Pool{
        tasks:   make(chan Task, 100),
        workers: workers,
    }
}

func (p *Pool) Start() {
    for i := 0; i < p.workers; i++ {
        p.wg.Add(1)
        go p.worker()
    }
}

func (p *Pool) worker() {
    defer p.wg.Done()
    for task := range p.tasks {
        task.Execute()
    }
}

func (p *Pool) Submit(task Task) {
    p.tasks <- task
}

func (p *Pool) Stop() {
    close(p.tasks)
    p.wg.Wait()
}
` + "```" + `

## Best Practices

1. Don't communicate by sharing memory; share memory by communicating
2. Goroutines are cheap but not free
3. Always handle channel closing
4. Use context for cancellation
5. Avoid goroutine leaks`,
			Excerpt:      "Master Go's powerful concurrency patterns including goroutines, channels, and synchronization.",
			CategoryIdx:  1,            // Programming
			TagIndices:   []int{0, 11}, // golang, performance
			IsFeatured:   true,
			Status:       "published",
			PublishedAgo: 14,
		},
		{
			Slug:  "draft-upcoming-features-roadmap",
			Title: "Upcoming Features Roadmap",
			Content: `# Upcoming Features Roadmap

This is a draft post outlining our upcoming features for the next quarter.

## Q3 2024 Features

- Enhanced API Gateway
- Machine Learning Integration
- Real-time Collaboration
- Advanced Analytics Dashboard

More details coming soon...`,
			Excerpt:       "Preview of upcoming features and improvements planned for the next quarter.",
			CategoryIdx:   0, // Technology
			TagIndices:    []int{},
			IsFeatured:    false,
			Status:        "draft",
			WorkflowState: "creation",
			AssignedToIdx: nil,
			WorkflowNotes: "Still working on detailed specifications",
			PublishedAgo:  0,
		},
		// Additional workflow example posts
		{
			Slug:  "kubernetes-security-best-practices",
			Title: "Kubernetes Security Best Practices",
			Content: `# Kubernetes Security Best Practices

Security in Kubernetes environments requires attention to multiple layers and components.

## Pod Security Standards

### Restricted Security Context
- Run as non-root user
- Use read-only root filesystem
- Drop all capabilities
- No privilege escalation

## Network Policies

Control pod-to-pod communication:

` + "```yaml" + `
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
` + "```" + `

## RBAC Configuration

Implement least privilege access:

` + "```yaml" + `
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pod-reader
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "watch", "list"]
` + "```" + `

More content to be added...`,
			Excerpt:       "Comprehensive guide to securing Kubernetes clusters and workloads.",
			CategoryIdx:   4,            // DevOps
			TagIndices:    []int{5, 10}, // kubernetes, security
			IsFeatured:    false,
			Status:        "draft",
			WorkflowState: "pending_review",
			AssignedToIdx: func() *int { idx := 1; return &idx }(), // Assigned to second author
			WorkflowNotes: "Ready for technical review, please check security recommendations",
			PublishedAgo:  0,
		},
		{
			Slug:  "advanced-react-performance-tips",
			Title: "Advanced React Performance Optimization Tips",
			Content: `# Advanced React Performance Optimization Tips

Performance optimization in React goes beyond basic best practices.

## Advanced Memoization Strategies

### Custom Hooks with useMemo
` + "```jsx" + `
function useExpensiveValue(dependency) {
  return useMemo(() => {
    return computeExpensiveValue(dependency);
  }, [dependency]);
}
` + "```" + `

## Virtualization for Large Lists

Use react-window for large data sets:

` + "```jsx" + `
import { FixedSizeList as List } from 'react-window';

const VirtualizedList = ({ items }) => (
  <List
    height={600}
    itemCount={items.length}
    itemSize={35}
  >
    {({ index, style }) => (
      <div style={style}>
        {items[index]}
      </div>
    )}
  </List>
);
` + "```" + `

## Bundle Analysis and Code Splitting

Content needs expansion with more examples...`,
			Excerpt:       "Deep dive into advanced React performance optimization techniques.",
			CategoryIdx:   5,               // Frontend
			TagIndices:    []int{1, 2, 11}, // javascript, react, performance
			IsFeatured:    false,
			Status:        "draft",
			WorkflowState: "in_review",
			AssignedToIdx: func() *int { idx := 0; return &idx }(), // Assigned to first author
			WorkflowNotes: "Currently under editorial review, needs more code examples",
			PublishedAgo:  0,
		},
		{
			Slug:  "database-migration-strategies",
			Title: "Database Migration Strategies for Production",
			Content: `# Database Migration Strategies for Production

Managing database changes in production requires careful planning and execution.

## Zero-Downtime Migration Patterns

### Backward Compatible Changes
- Add new columns with default values
- Create new tables alongside existing ones
- Add new indexes online

### Forward Compatible Changes
- Use feature flags for schema changes
- Implement dual-write patterns
- Gradual data migration

## Blue-Green Database Deployments

` + "```sql" + `
-- Step 1: Create new schema version
CREATE TABLE users_v2 LIKE users;
ALTER TABLE users_v2 ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;

-- Step 2: Sync data
INSERT INTO users_v2 SELECT *, FALSE FROM users;

-- Step 3: Switch application
-- (Application deployment with connection string change)

-- Step 4: Cleanup old table
DROP TABLE users;
RENAME TABLE users_v2 TO users;
` + "```" + `

Needs content review before publication...`,
			Excerpt:       "Learn production-ready database migration strategies with zero downtime.",
			CategoryIdx:   6,            // Backend
			TagIndices:    []int{6, 11}, // mysql, performance
			IsFeatured:    false,
			Status:        "draft",
			WorkflowState: "pending_approval",
			AssignedToIdx: func() *int { idx := 2; return &idx }(), // Assigned to third author
			WorkflowNotes: "Technical review completed, awaiting editorial secretary approval",
			PublishedAgo:  0,
		},
		{
			Slug:  "api-design-guidelines-restful",
			Title: "RESTful API Design Guidelines and Best Practices",
			Content: `# RESTful API Design Guidelines and Best Practices

Well-designed APIs are crucial for maintainable and scalable applications.

## Resource Naming Conventions

### Good Examples:
- GET /api/v1/users (collection)
- GET /api/v1/users/{id} (specific resource)
- GET /api/v1/users/{id}/posts (nested resource)

### Bad Examples:
- GET /api/v1/getUsers
- GET /api/v1/user_list  
- POST /api/v1/createNewUser

## HTTP Status Codes

Use appropriate status codes:

` + "```" + `
200 OK - Request succeeded
201 Created - Resource created successfully
204 No Content - Successful request with no response body
400 Bad Request - Client error in request
401 Unauthorized - Authentication required
403 Forbidden - Access denied
404 Not Found - Resource doesn't exist
422 Unprocessable Entity - Validation errors
500 Internal Server Error - Server error
` + "```" + `

## Pagination Strategies

Implement cursor-based pagination:

` + "```json" + `
{
  "data": [...],
  "meta": {
    "next_cursor": "eyJpZCI6MTI0fQ==",
    "has_more": true,
    "total_count": 1500
  }
}
` + "```" + `

Content needs editor-in-chief review for publication standards...`,
			Excerpt:       "Comprehensive guide to designing RESTful APIs following industry best practices.",
			CategoryIdx:   2,           // Web Development
			TagIndices:    []int{8, 0}, // api, golang
			IsFeatured:    true,
			Status:        "draft",
			WorkflowState: "pending_eic",
			AssignedToIdx: nil,
			WorkflowNotes: "Editorial secretary approved, needs final EIC review before publication",
			PublishedAgo:  0,
		},
		{
			Slug:  "machine-learning-deployment-practices",
			Title: "Machine Learning Model Deployment Best Practices",
			Content: `# Machine Learning Model Deployment Best Practices

Deploying ML models to production requires different considerations than traditional software.

## Model Versioning and Management

### Semantic Versioning for Models
- Major: Breaking changes in model interface
- Minor: Performance improvements, new features
- Patch: Bug fixes, small adjustments

### Model Registry
- Centralized model storage
- Metadata tracking
- Performance metrics
- Deployment history

## Deployment Patterns

### Blue-Green Deployment
- Deploy new model alongside existing
- Switch traffic gradually
- Quick rollback capability

### A/B Testing
` + "```python" + `
import random

def route_prediction_request(user_id, request_data):
    # Route 10% of traffic to new model
    if random.random() < 0.1:
        return new_model.predict(request_data)
    else:
        return current_model.predict(request_data)
` + "```" + `

## Monitoring and Observability

Track key metrics:
- Prediction latency
- Model accuracy drift
- Data quality issues
- Resource utilization

Content needs technical corrections before approval...`,
			Excerpt:       "Learn how to deploy machine learning models to production environments safely.",
			CategoryIdx:   0,             // Technology
			TagIndices:    []int{14, 11}, // cloud, performance
			IsFeatured:    false,
			Status:        "draft",
			WorkflowState: "returned",
			AssignedToIdx: func() *int { idx := 1; return &idx }(), // Returned to second author
			WorkflowNotes: "Returned to author - technical accuracy issues in deployment section, please revise",
			PublishedAgo:  0,
		},
		{
			Slug:  "rejected-cryptocurrency-trading-bot",
			Title: "Building Automated Cryptocurrency Trading Algorithms",
			Content: `# Building Automated Cryptocurrency Trading Algorithms

This article covers building automated trading systems for cryptocurrency markets.

## Technical Analysis Implementation

### Moving Averages Strategy
` + "```python" + `
def moving_average_strategy(prices, short_window=10, long_window=30):
    short_ma = prices.rolling(window=short_window).mean()
    long_ma = prices.rolling(window=long_window).mean()
    
    signals = []
    for i in range(len(prices)):
        if short_ma[i] > long_ma[i]:
            signals.append('BUY')
        else:
            signals.append('SELL')
    
    return signals
` + "```" + `

## Risk Management

- Stop-loss orders
- Position sizing
- Portfolio diversification
- Maximum drawdown limits

Content rejected due to policy violations...`,
			Excerpt:       "Guide to building cryptocurrency trading bots with risk management.",
			CategoryIdx:   0,        // Technology
			TagIndices:    []int{1}, // javascript
			IsFeatured:    false,
			Status:        "draft",
			WorkflowState: "rejected",
			AssignedToIdx: nil,
			WorkflowNotes: "Rejected - content promotes financial speculation and may violate content policy",
			PublishedAgo:  0,
		},
	}

	for _, post := range posts {
		// Check if post already exists
		var existingID int
		err := s.db.QueryRowContext(ctx, `
			SELECT id FROM blog_posts 
			WHERE tenant_id = ? AND website_id = ? AND slug = ? 
			LIMIT 1
		`, tenantID, websiteID, post.Slug).Scan(&existingID)

		if err == nil {
			// Post already exists, skip
			continue
		} else if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to check existing post: %w", err)
		}

		// Get category ID
		categoryID := categoryIDs[post.CategoryIdx%len(categoryIDs)]

		// Get author ID (random from available authors)
		authorID := authorIDs[rand.Intn(len(authorIDs))]

		// Get assigned user ID if specified
		// Workflow fields removed - not using workflow functionality

		// Calculate published date
		var publishedAt *time.Time
		var scheduledAt *time.Time

		if post.Status == "published" && post.PublishedAgo > 0 {
			pubDate := time.Now().AddDate(0, 0, -post.PublishedAgo)
			publishedAt = &pubDate
		} else if post.Status == "scheduled" {
			schedDate := time.Now().AddDate(0, 0, 7) // Schedule for 7 days from now
			scheduledAt = &schedDate
		}

		// Insert post
		result, err := s.db.ExecContext(ctx, `
			INSERT INTO blog_posts (
				tenant_id, website_id, slug, title, content, excerpt,
				author_id, category_id, type, is_featured,
				allow_comments, featured_image, view_count, scheduled_at,
				published_at, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'post', ?, TRUE, ?, ?, ?, ?, ?, NOW(), NOW())
		`, tenantID, websiteID, post.Slug, post.Title, post.Content, post.Excerpt,
			authorID, categoryID, post.IsFeatured,
			fmt.Sprintf("https://source.unsplash.com/800x400/?%s", strings.ReplaceAll(post.Slug, "-", ",")),
			rand.Intn(1000), scheduledAt, publishedAt, post.Status)

		if err != nil {
			return err
		}

		postID, err := result.LastInsertId()
		if err != nil {
			return err
		}

		// Add tags
		for _, tagIdx := range post.TagIndices {
			if tagIdx < len(tagIDs) {
				_, err = s.db.ExecContext(ctx, `
					INSERT INTO blog_post_tags (post_id, tag_id, created_at)
					VALUES (?, ?, NOW())
				`, postID, tagIDs[tagIdx])

				if err != nil {
					continue // Skip if tag association fails
				}

				// Update tag usage count
				_, _ = s.db.ExecContext(ctx, `
					UPDATE blog_tags SET usage_count = usage_count + 1
					WHERE id = ?
				`, tagIDs[tagIdx])
			}
		}

		// Add primary author to blog_post_authors
		_, err = s.db.ExecContext(ctx, `
			INSERT INTO blog_post_authors (post_id, user_id, role, created_at)
			VALUES (?, ?, 'primary', NOW())
		`, postID, authorID)

		if err != nil {
			return err
		}

		// Create schedule for scheduled posts
		if post.Status == "scheduled" && scheduledAt != nil {
			_, err = s.db.ExecContext(ctx, `
				INSERT INTO blog_post_schedules (
					post_id, schedule_type, scheduled_at, timezone,
					recurring_config, status, created_at, updated_at
				) VALUES (?, 'one-time', ?, 'UTC', '{}', 'pending', NOW(), NOW())
			`, postID, scheduledAt)

			if err != nil {
				return err
			}
		}

		// Add some related posts (for published posts only)
		if post.Status == "published" && len(posts) > 1 {
			// Get 2-3 random other posts as related
			numRelated := 2 + rand.Intn(2)
			for i := 0; i < numRelated; i++ {
				relatedIdx := rand.Intn(len(posts))
				if relatedIdx != len(posts)-1 && posts[relatedIdx].Status == "published" {
					relatedSlug := posts[relatedIdx].Slug

					// Get related post ID
					var relatedID int
					err := s.db.QueryRowContext(ctx, `
						SELECT id FROM blog_posts 
						WHERE website_id = ? AND slug = ? AND status = 'published'
						LIMIT 1
					`, websiteID, relatedSlug).Scan(&relatedID)

					if err == nil && relatedID != int(postID) {
						relevanceScore := 0.5 + rand.Float64()*0.5 // 0.5-1.0
						_, _ = s.db.ExecContext(ctx, `
							INSERT IGNORE INTO blog_post_related (
								post_id, related_post_id, relation_type,
								relevance_score, created_at, updated_at
							) VALUES (?, ?, 'similar_content', ?, NOW(), NOW())
						`, postID, relatedID, relevanceScore)
					}
				}
			}
		}
	}

	log.Printf("Created %d blog posts for website %d", len(posts), websiteID)
	return nil
}

// Rollback removes the seeded blog data
func (s *BlogSeeder) Rollback(ctx context.Context) error {
	// Delete in reverse order of dependencies
	tables := []string{
		"blog_post_related",
		"blog_post_schedules",
		"blog_post_authors",
		"blog_post_tags",
		"blog_posts",
		"blog_tags",
		"blog_categories",
	}

	for _, table := range tables {
		query := fmt.Sprintf("DELETE FROM %s WHERE 1=1", table)
		if _, err := s.db.ExecContext(ctx, query); err != nil {
			return fmt.Errorf("failed to clean %s: %w", table, err)
		}
	}

	return nil
}
