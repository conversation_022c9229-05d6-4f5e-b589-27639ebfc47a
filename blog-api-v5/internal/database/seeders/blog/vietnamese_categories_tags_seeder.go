package blog

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
)

// VietnameseCategoriesTagsSeeder implements Vietnamese categories and tags seeding
type VietnameseCategoriesTagsSeeder struct {
	db *sql.DB
}

// NewVietnameseCategoriesTagsSeeder creates a new Vietnamese categories and tags seeder
func NewVietnameseCategoriesTagsSeeder(db *sql.DB) *VietnameseCategoriesTagsSeeder {
	return &VietnameseCategoriesTagsSeeder{db: db}
}

// Name returns the seeder name
func (s *VietnameseCategoriesTagsSeeder) Name() string {
	return "vietnamese_categories_tags_seeder"
}

// Dependencies returns the seeders that must run before this one
func (s *VietnameseCategoriesTagsSeeder) Dependencies() []string {
	return []string{"website_seeder"}
}

// Seed executes the Vietnamese categories and tags seeding process
func (s *VietnameseCategoriesTagsSeeder) Seed(ctx context.Context) error {
	log.Println("Starting Vietnamese categories and tags seeding...")

	// Fixed tenant_id = 6, website_id = 1 as requested
	tenantID := 6
	websiteID := 1

	// Verify that the tenant and website exist
	var exists int
	err := s.db.QueryRowContext(ctx, `SELECT 1 FROM websites WHERE tenant_id = ? AND id = ? LIMIT 1`, tenantID, websiteID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("tenant_id %d and website_id %d combination not found: %w", tenantID, websiteID, err)
	}

	// Seed categories first
	categoryIDs, err := s.seedVietnameseCategories(ctx, tenantID, websiteID)
	if err != nil {
		return fmt.Errorf("failed to seed Vietnamese categories: %w", err)
	}

	// Seed tags
	_, err = s.seedVietnameseTags(ctx, tenantID, websiteID)
	if err != nil {
		return fmt.Errorf("failed to seed Vietnamese tags: %w", err)
	}

	log.Printf("Vietnamese categories and tags seeding completed for tenant_id=%d, website_id=%d", tenantID, websiteID)
	log.Printf("Created %d categories", len(categoryIDs))
	return nil
}

// seedVietnameseCategories creates Vietnamese multi-level categories
func (s *VietnameseCategoriesTagsSeeder) seedVietnameseCategories(ctx context.Context, tenantID, websiteID int) ([]int, error) {
	categories := []struct {
		Slug        string
		Name        string
		Description string
		ParentSlug  *string // Parent category slug for nested structure
		Lft         int
		Rgt         int
		Depth       int
	}{
		// Root level categories (depth 0)
		{
			Slug:        "cong-nghe",
			Name:        "Công nghệ",
			Description: "Tin tức và xu hướng công nghệ mới nhất",
			ParentSlug:  nil,
			Lft:         1,
			Rgt:         20,
			Depth:       0,
		},
		{
			Slug:        "lap-trinh",
			Name:        "Lập trình",
			Description: "Hướng dẫn lập trình, ngôn ngữ và framework",
			ParentSlug:  nil,
			Lft:         21,
			Rgt:         50,
			Depth:       0,
		},
		{
			Slug:        "khoi-nghiep",
			Name:        "Khởi nghiệp",
			Description: "Câu chuyện khởi nghiệp và kinh doanh",
			ParentSlug:  nil,
			Lft:         51,
			Rgt:         70,
			Depth:       0,
		},
		{
			Slug:        "thiet-ke",
			Name:        "Thiết kế",
			Description: "Thiết kế UI/UX và đồ họa",
			ParentSlug:  nil,
			Lft:         71,
			Rgt:         90,
			Depth:       0,
		},
		{
			Slug:        "tien-dien-tu",
			Name:        "Tiền điện tử",
			Description: "Blockchain, Bitcoin và các loại tiền điện tử",
			ParentSlug:  nil,
			Lft:         91,
			Rgt:         110,
			Depth:       0,
		},

		// Level 1 children under "Công nghệ" (depth 1)
		{
			Slug:        "tri-tue-nhan-tao",
			Name:        "Trí tuệ nhân tạo",
			Description: "AI, Machine Learning và Deep Learning",
			ParentSlug:  stringPtr("cong-nghe"),
			Lft:         2,
			Rgt:         9,
			Depth:       1,
		},
		{
			Slug:        "dien-toan-dam-may",
			Name:        "Điện toán đám mây",
			Description: "Cloud Computing, AWS, Google Cloud, Azure",
			ParentSlug:  stringPtr("cong-nghe"),
			Lft:         10,
			Rgt:         19,
			Depth:       1,
		},

		// Level 2 children under "Trí tuệ nhân tạo" (depth 2)
		{
			Slug:        "machine-learning",
			Name:        "Machine Learning",
			Description: "Thuật toán và ứng dụng Machine Learning",
			ParentSlug:  stringPtr("tri-tue-nhan-tao"),
			Lft:         3,
			Rgt:         4,
			Depth:       2,
		},
		{
			Slug:        "deep-learning",
			Name:        "Deep Learning",
			Description: "Mạng neural và deep learning",
			ParentSlug:  stringPtr("tri-tue-nhan-tao"),
			Lft:         5,
			Rgt:         6,
			Depth:       2,
		},
		{
			Slug:        "xu-ly-ngon-ngu-tu-nhien",
			Name:        "Xử lý ngôn ngữ tự nhiên",
			Description: "NLP và các ứng dụng chatbot",
			ParentSlug:  stringPtr("tri-tue-nhan-tao"),
			Lft:         7,
			Rgt:         8,
			Depth:       2,
		},

		// Level 2 children under "Điện toán đám mây" (depth 2)
		{
			Slug:        "aws-cloud",
			Name:        "Amazon Web Services",
			Description: "Dịch vụ và giải pháp AWS",
			ParentSlug:  stringPtr("dien-toan-dam-may"),
			Lft:         11,
			Rgt:         12,
			Depth:       2,
		},
		{
			Slug:        "google-cloud",
			Name:        "Google Cloud Platform",
			Description: "GCP và các dịch vụ Google Cloud",
			ParentSlug:  stringPtr("dien-toan-dam-may"),
			Lft:         13,
			Rgt:         14,
			Depth:       2,
		},
		{
			Slug:        "azure-cloud",
			Name:        "Microsoft Azure",
			Description: "Azure cloud services và solutions",
			ParentSlug:  stringPtr("dien-toan-dam-may"),
			Lft:         15,
			Rgt:         16,
			Depth:       2,
		},
		{
			Slug:        "devops-cloud",
			Name:        "DevOps & Cloud",
			Description: "CI/CD, containerization và orchestration",
			ParentSlug:  stringPtr("dien-toan-dam-may"),
			Lft:         17,
			Rgt:         18,
			Depth:       2,
		},

		// Level 1 children under "Lập trình" (depth 1)
		{
			Slug:        "web-development",
			Name:        "Phát triển Web",
			Description: "Frontend, Backend và Full-stack development",
			ParentSlug:  stringPtr("lap-trinh"),
			Lft:         22,
			Rgt:         35,
			Depth:       1,
		},
		{
			Slug:        "mobile-development",
			Name:        "Phát triển Mobile",
			Description: "iOS, Android và cross-platform apps",
			ParentSlug:  stringPtr("lap-trinh"),
			Lft:         36,
			Rgt:         43,
			Depth:       1,
		},
		{
			Slug:        "game-development",
			Name:        "Phát triển Game",
			Description: "Game engines, Unity, Unreal Engine",
			ParentSlug:  stringPtr("lap-trinh"),
			Lft:         44,
			Rgt:         49,
			Depth:       1,
		},

		// Level 2 children under "Phát triển Web" (depth 2)
		{
			Slug:        "frontend-development",
			Name:        "Frontend Development",
			Description: "HTML, CSS, JavaScript và các framework",
			ParentSlug:  stringPtr("web-development"),
			Lft:         23,
			Rgt:         28,
			Depth:       2,
		},
		{
			Slug:        "backend-development",
			Name:        "Backend Development",
			Description: "Server-side programming và databases",
			ParentSlug:  stringPtr("web-development"),
			Lft:         29,
			Rgt:         34,
			Depth:       2,
		},

		// Level 3 children under "Frontend Development" (depth 3)
		{
			Slug:        "react-js",
			Name:        "React.js",
			Description: "React library và ecosystem",
			ParentSlug:  stringPtr("frontend-development"),
			Lft:         24,
			Rgt:         25,
			Depth:       3,
		},
		{
			Slug:        "vue-js",
			Name:        "Vue.js",
			Description: "Vue.js framework và tools",
			ParentSlug:  stringPtr("frontend-development"),
			Lft:         26,
			Rgt:         27,
			Depth:       3,
		},

		// Level 3 children under "Backend Development" (depth 3)
		{
			Slug:        "nodejs-backend",
			Name:        "Node.js",
			Description: "Node.js runtime và server development",
			ParentSlug:  stringPtr("backend-development"),
			Lft:         30,
			Rgt:         31,
			Depth:       3,
		},
		{
			Slug:        "python-backend",
			Name:        "Python Backend",
			Description: "Django, Flask và Python web frameworks",
			ParentSlug:  stringPtr("backend-development"),
			Lft:         32,
			Rgt:         33,
			Depth:       3,
		},

		// Level 2 children under "Mobile Development" (depth 2)
		{
			Slug:        "ios-development",
			Name:        "iOS Development",
			Description: "Swift, Objective-C và iOS apps",
			ParentSlug:  stringPtr("mobile-development"),
			Lft:         37,
			Rgt:         38,
			Depth:       2,
		},
		{
			Slug:        "android-development",
			Name:        "Android Development",
			Description: "Java, Kotlin và Android apps",
			ParentSlug:  stringPtr("mobile-development"),
			Lft:         39,
			Rgt:         40,
			Depth:       2,
		},
		{
			Slug:        "react-native",
			Name:        "React Native",
			Description: "Cross-platform mobile development",
			ParentSlug:  stringPtr("mobile-development"),
			Lft:         41,
			Rgt:         42,
			Depth:       2,
		},

		// Level 2 children under "Game Development" (depth 2)
		{
			Slug:        "unity-game",
			Name:        "Unity Game Engine",
			Description: "Unity development và C# scripting",
			ParentSlug:  stringPtr("game-development"),
			Lft:         45,
			Rgt:         46,
			Depth:       2,
		},
		{
			Slug:        "unreal-engine",
			Name:        "Unreal Engine",
			Description: "Unreal Engine và Blueprint",
			ParentSlug:  stringPtr("game-development"),
			Lft:         47,
			Rgt:         48,
			Depth:       2,
		},

		// Level 1 children under "Khởi nghiệp" (depth 1)
		{
			Slug:        "startup-stories",
			Name:        "Câu chuyện Startup",
			Description: "Success stories và case studies",
			ParentSlug:  stringPtr("khoi-nghiep"),
			Lft:         52,
			Rgt:         57,
			Depth:       1,
		},
		{
			Slug:        "dau-tu-venture",
			Name:        "Đầu tư Venture",
			Description: "Venture capital và angel investment",
			ParentSlug:  stringPtr("khoi-nghiep"),
			Lft:         58,
			Rgt:         63,
			Depth:       1,
		},
		{
			Slug:        "marketing-digital",
			Name:        "Marketing Digital",
			Description: "Digital marketing strategies và tools",
			ParentSlug:  stringPtr("khoi-nghiep"),
			Lft:         64,
			Rgt:         69,
			Depth:       1,
		},

		// Level 2 children under Marketing Digital
		{
			Slug:        "seo-marketing",
			Name:        "SEO & SEM",
			Description: "Search Engine Optimization và Marketing",
			ParentSlug:  stringPtr("marketing-digital"),
			Lft:         65,
			Rgt:         66,
			Depth:       2,
		},
		{
			Slug:        "social-media-marketing",
			Name:        "Social Media Marketing",
			Description: "Facebook, Instagram, TikTok marketing",
			ParentSlug:  stringPtr("marketing-digital"),
			Lft:         67,
			Rgt:         68,
			Depth:       2,
		},

		// Additional categories
		{
			Slug:        "bao-mat-cyber",
			Name:        "Bảo mật Cyber",
			Description: "Cybersecurity, ethical hacking, privacy",
			ParentSlug:  nil,
			Lft:         111,
			Rgt:         130,
			Depth:       0,
		},
	}

	var categoryIDs []int
	categorySlugToID := make(map[string]int)

	// First pass: Create all categories
	for _, cat := range categories {
		// Check if category already exists
		var existingID int
		err := s.db.QueryRowContext(ctx, `
			SELECT id FROM blog_categories 
			WHERE tenant_id = ? AND website_id = ? AND slug = ? 
			LIMIT 1
		`, tenantID, websiteID, cat.Slug).Scan(&existingID)

		if err == nil {
			// Category already exists, use existing ID
			categoryIDs = append(categoryIDs, existingID)
			categorySlugToID[cat.Slug] = existingID
			continue
		} else if !errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("failed to check existing category: %w", err)
		}

		// Get parent_id if parent exists
		var parentID *int
		if cat.ParentSlug != nil {
			if pid, exists := categorySlugToID[*cat.ParentSlug]; exists {
				parentID = &pid
			}
		}

		// Insert category
		result, err := s.db.ExecContext(ctx, `
			INSERT INTO blog_categories (
				tenant_id, website_id, slug, name, description,
				parent_id, lft, rgt, depth, children_count, status,
				created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 'active', NOW(), NOW())
		`, tenantID, websiteID, cat.Slug, cat.Name, cat.Description,
			parentID, cat.Lft, cat.Rgt, cat.Depth)

		if err != nil {
			return nil, fmt.Errorf("failed to insert category %s: %w", cat.Slug, err)
		}

		categoryID, err := result.LastInsertId()
		if err != nil {
			return nil, err
		}

		categoryIDs = append(categoryIDs, int(categoryID))
		categorySlugToID[cat.Slug] = int(categoryID)
	}

	// Second pass: Update children_count for parent categories
	for _, cat := range categories {
		if cat.ParentSlug != nil {
			if parentID, exists := categorySlugToID[*cat.ParentSlug]; exists {
				_, err := s.db.ExecContext(ctx, `
					UPDATE blog_categories 
					SET children_count = children_count + 1 
					WHERE id = ?
				`, parentID)
				if err != nil {
					log.Printf("Warning: failed to update children_count for category %d: %v", parentID, err)
				}
			}
		}
	}

	return categoryIDs, nil
}

// seedVietnameseTags creates Vietnamese tags
func (s *VietnameseCategoriesTagsSeeder) seedVietnameseTags(ctx context.Context, tenantID, websiteID int) ([]int, error) {
	tags := []struct {
		Slug        string
		Name        string
		Description string
		Color       string
	}{
		// Programming languages
		{"javascript", "JavaScript", "Ngôn ngữ lập trình JavaScript", "#F7DF1E"},
		{"python", "Python", "Ngôn ngữ lập trình Python", "#3776AB"},
		{"golang", "Go", "Ngôn ngữ lập trình Go", "#00ADD8"},
		{"java", "Java", "Ngôn ngữ lập trình Java", "#ED8B00"},
		{"typescript", "TypeScript", "TypeScript - JavaScript có kiểu", "#3178C6"},
		{"rust", "Rust", "Ngôn ngữ lập trình Rust", "#000000"},
		{"php", "PHP", "Ngôn ngữ lập trình PHP", "#777BB4"},
		{"csharp", "C#", "Ngôn ngữ lập trình C#", "#239120"},
		{"swift", "Swift", "Ngôn ngữ lập trình Swift", "#FA7343"},
		{"kotlin", "Kotlin", "Ngôn ngữ lập trình Kotlin", "#0095D5"},

		// Frontend frameworks/libraries
		{"reactjs", "React.js", "React JavaScript library", "#61DAFB"},
		{"vuejs", "Vue.js", "Vue.js framework", "#4FC08D"},
		{"angular", "Angular", "Angular framework", "#DD0031"},
		{"nextjs", "Next.js", "Next.js React framework", "#000000"},
		{"nuxtjs", "Nuxt.js", "Nuxt.js Vue framework", "#00DC82"},
		{"svelte", "Svelte", "Svelte framework", "#FF3E00"},

		// Backend frameworks
		{"nodejs", "Node.js", "Node.js runtime environment", "#339933"},
		{"express", "Express.js", "Express.js web framework", "#000000"},
		{"django", "Django", "Django Python framework", "#092E20"},
		{"flask", "Flask", "Flask Python framework", "#000000"},
		{"laravel", "Laravel", "Laravel PHP framework", "#FF2D20"},
		{"spring", "Spring", "Spring Java framework", "#6DB33F"},
		{"gin", "Gin", "Gin Go web framework", "#00ADD8"},

		// Databases
		{"mysql", "MySQL", "MySQL database", "#4479A1"},
		{"postgresql", "PostgreSQL", "PostgreSQL database", "#4169E1"},
		{"mongodb", "MongoDB", "MongoDB NoSQL database", "#47A248"},
		{"redis", "Redis", "Redis in-memory database", "#DC382D"},
		{"sqlite", "SQLite", "SQLite database", "#003B57"},
		{"elasticsearch", "Elasticsearch", "Elasticsearch search engine", "#005571"},

		// Cloud platforms
		{"aws", "Amazon AWS", "Amazon Web Services", "#FF9900"},
		{"gcp", "Google Cloud", "Google Cloud Platform", "#4285F4"},
		{"azure", "Microsoft Azure", "Microsoft Azure Cloud", "#0078D4"},
		{"heroku", "Heroku", "Heroku cloud platform", "#430098"},
		{"digitalocean", "DigitalOcean", "DigitalOcean cloud", "#0080FF"},
		{"vercel", "Vercel", "Vercel deployment platform", "#000000"},

		// DevOps & Tools
		{"docker", "Docker", "Docker containerization", "#2496ED"},
		{"kubernetes", "Kubernetes", "Kubernetes orchestration", "#326CE5"},
		{"jenkins", "Jenkins", "Jenkins CI/CD", "#D24939"},
		{"github-actions", "GitHub Actions", "GitHub Actions CI/CD", "#2088FF"},
		{"terraform", "Terraform", "Terraform infrastructure", "#7B42BC"},
		{"ansible", "Ansible", "Ansible automation", "#EE0000"},

		// Mobile development
		{"android", "Android", "Android mobile development", "#3DDC84"},
		{"ios", "iOS", "iOS mobile development", "#000000"},
		{"react-native", "React Native", "React Native framework", "#61DAFB"},
		{"flutter", "Flutter", "Flutter framework", "#02569B"},
		{"xamarin", "Xamarin", "Xamarin cross-platform", "#3498DB"},

		// AI & Machine Learning
		{"machine-learning", "Machine Learning", "Máy học và trí tuệ nhân tạo", "#FF6F00"},
		{"deep-learning", "Deep Learning", "Deep Learning và neural networks", "#FF6F00"},
		{"tensorflow", "TensorFlow", "TensorFlow ML framework", "#FF6F00"},
		{"pytorch", "PyTorch", "PyTorch ML framework", "#EE4C2C"},
		{"chatgpt", "ChatGPT", "ChatGPT và OpenAI", "#412991"},
		{"nlp", "NLP", "Natural Language Processing", "#FF6F00"},

		// Blockchain & Crypto
		{"blockchain", "Blockchain", "Công nghệ blockchain", "#F7931A"},
		{"bitcoin", "Bitcoin", "Bitcoin cryptocurrency", "#F7931A"},
		{"ethereum", "Ethereum", "Ethereum blockchain", "#627EEA"},
		{"smart-contracts", "Smart Contracts", "Hợp đồng thông minh", "#627EEA"},
		{"defi", "DeFi", "Decentralized Finance", "#627EEA"},
		{"nft", "NFT", "Non-Fungible Tokens", "#627EEA"},

		// Web3 & Metaverse
		{"web3", "Web3", "Web3 và internet phi tập trung", "#F16822"},
		{"metaverse", "Metaverse", "Thế giới ảo metaverse", "#0667FF"},

		// Design & UI/UX
		{"ui-design", "UI Design", "User Interface Design", "#FF5722"},
		{"ux-design", "UX Design", "User Experience Design", "#9C27B0"},
		{"figma", "Figma", "Figma design tool", "#F24E1E"},
		{"adobe-xd", "Adobe XD", "Adobe XD design tool", "#FF26BE"},
		{"sketch", "Sketch", "Sketch design tool", "#F7B500"},

		// Business & Startup
		{"startup", "Startup", "Khởi nghiệp và doanh nghiệp", "#4CAF50"},
		{"venture-capital", "Venture Capital", "Đầu tư mạo hiểm", "#2196F3"},
		{"product-management", "Product Management", "Quản lý sản phẩm", "#9C27B0"},
		{"agile", "Agile", "Phương pháp Agile", "#FF5722"},
		{"scrum", "Scrum", "Scrum framework", "#FF5722"},

		// Marketing & SEO
		{"digital-marketing", "Digital Marketing", "Marketing số", "#E91E63"},
		{"seo", "SEO", "Search Engine Optimization", "#4CAF50"},
		{"sem", "SEM", "Search Engine Marketing", "#FF9800"},
		{"social-media", "Social Media", "Mạng xã hội", "#2196F3"},
		{"content-marketing", "Content Marketing", "Marketing nội dung", "#9C27B0"},

		// Security
		{"cybersecurity", "Cybersecurity", "An ninh mạng", "#F44336"},
		{"ethical-hacking", "Ethical Hacking", "Hacking có đạo đức", "#F44336"},
		{"penetration-testing", "Penetration Testing", "Kiểm thử xâm nhập", "#F44336"},
		{"privacy", "Privacy", "Bảo mật thông tin", "#F44336"},

		// Performance & Optimization
		{"performance", "Performance", "Tối ưu hóa hiệu suất", "#4CAF50"},
		{"optimization", "Optimization", "Tối ưu hóa", "#4CAF50"},
		{"monitoring", "Monitoring", "Giám sát hệ thống", "#FF9800"},
		{"analytics", "Analytics", "Phân tích dữ liệu", "#2196F3"},

		// Testing
		{"unit-testing", "Unit Testing", "Kiểm thử đơn vị", "#9C27B0"},
		{"integration-testing", "Integration Testing", "Kiểm thử tích hợp", "#9C27B0"},
		{"e2e-testing", "E2E Testing", "Kiểm thử end-to-end", "#9C27B0"},
		{"test-automation", "Test Automation", "Tự động hóa kiểm thử", "#9C27B0"},

		// Trends & Industry
		{"trends", "Trends", "Xu hướng công nghệ", "#FF5722"},
		{"innovation", "Innovation", "Đổi mới sáng tạo", "#4CAF50"},
		{"remote-work", "Remote Work", "Làm việc từ xa", "#2196F3"},
		{"freelancing", "Freelancing", "Làm việc tự do", "#FF9800"},
		{"career", "Career", "Sự nghiệp IT", "#9C27B0"},
		{"interview", "Interview", "Phỏng vấn việc làm", "#E91E63"},

		// Lifestyle & Productivity
		{"productivity", "Productivity", "Năng suất làm việc", "#4CAF50"},
		{"work-life-balance", "Work-Life Balance", "Cân bằng cuộc sống", "#2196F3"},
		{"time-management", "Time Management", "Quản lý thời gian", "#FF9800"},
		{"personal-development", "Personal Development", "Phát triển bản thân", "#9C27B0"},
	}

	var tagIDs []int

	for _, tag := range tags {
		// Check if tag already exists
		var existingID int
		err := s.db.QueryRowContext(ctx, `
			SELECT id FROM blog_tags 
			WHERE tenant_id = ? AND website_id = ? AND slug = ? 
			LIMIT 1
		`, tenantID, websiteID, tag.Slug).Scan(&existingID)

		if err == nil {
			// Tag already exists, use existing ID
			tagIDs = append(tagIDs, existingID)
			continue
		} else if !errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("failed to check existing tag: %w", err)
		}

		// Insert tag
		result, err := s.db.ExecContext(ctx, `
			INSERT INTO blog_tags (
				tenant_id, website_id, slug, name, description, color, 
				usage_count, is_active, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, 0, TRUE, 'active', NOW(), NOW())
		`, tenantID, websiteID, tag.Slug, tag.Name, tag.Description, tag.Color)

		if err != nil {
			return nil, fmt.Errorf("failed to insert tag %s: %w", tag.Slug, err)
		}

		tagID, err := result.LastInsertId()
		if err != nil {
			return nil, err
		}

		tagIDs = append(tagIDs, int(tagID))
	}

	return tagIDs, nil
}

// Rollback removes the seeded Vietnamese categories and tags
func (s *VietnameseCategoriesTagsSeeder) Rollback(ctx context.Context) error {
	tenantID := 6
	websiteID := 1

	// Delete tags first (no foreign key dependencies)
	_, err := s.db.ExecContext(ctx, `
		DELETE FROM blog_tags 
		WHERE tenant_id = ? AND website_id = ?
	`, tenantID, websiteID)
	if err != nil {
		return fmt.Errorf("failed to rollback tags: %w", err)
	}

	// Delete categories (handle nested structure by deleting in reverse order)
	_, err = s.db.ExecContext(ctx, `
		DELETE FROM blog_categories 
		WHERE tenant_id = ? AND website_id = ?
		ORDER BY depth DESC, lft DESC
	`, tenantID, websiteID)
	if err != nil {
		return fmt.Errorf("failed to rollback categories: %w", err)
	}

	return nil
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}