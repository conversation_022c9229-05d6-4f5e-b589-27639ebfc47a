[{"id": 1, "tenant_id": 1, "website_id": 1, "folder_id": 3, "user_id": 1, "filename": "logo.png", "original_filename": "company-logo.png", "slug": "logo", "mime_type": "image/png", "file_size": 15360, "file_hash": "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456", "storage_type": "local", "storage_path": "/tenant_1/website_1/2024/01/images/logo.png", "public_url": "https://example.com/media/tenant_1/website_1/2024/01/images/logo.png", "width": 200, "height": 80, "duration": null, "metadata": {"created_with": "Adobe Photoshop", "dpi": 300, "color_space": "RGB"}, "file_type": "image", "category": "assets", "alt_text": "Company logo", "title": "Main Company Logo", "description": "Primary logo for the company website", "visibility": "public", "access_permissions": [], "view_count": 0, "download_count": 0, "last_accessed_at": null, "status": "ready", "processing_status": {"thumbnails_generated": true, "optimized": true, "webp_generated": true}, "error_message": null}, {"id": 2, "tenant_id": 1, "website_id": 1, "folder_id": 3, "user_id": 1, "filename": "favicon.ico", "original_filename": "favicon.ico", "slug": "favicon", "mime_type": "image/x-icon", "file_size": 1024, "file_hash": "b2c3d4e5f67890123456789012345678901abcdef234567890abcdef1234567", "storage_type": "local", "storage_path": "/tenant_1/website_1/2024/01/images/favicon.ico", "public_url": "https://example.com/media/tenant_1/website_1/2024/01/images/favicon.ico", "width": 32, "height": 32, "duration": null, "metadata": {"format": "ICO", "sizes": ["16x16", "32x32"]}, "file_type": "image", "category": "assets", "alt_text": "Website favicon", "title": "Website Favicon", "description": "Small icon for browser tabs", "visibility": "public", "access_permissions": [], "view_count": 0, "download_count": 0, "last_accessed_at": null, "status": "ready", "processing_status": {"thumbnails_generated": false, "optimized": false}, "error_message": null}, {"id": 3, "tenant_id": 1, "website_id": 1, "folder_id": 2, "user_id": 1, "filename": "blog-hero-image.jpg", "original_filename": "amazing-sunset-photo.jpg", "slug": "blog-hero-image", "mime_type": "image/jpeg", "file_size": 524288, "file_hash": "c3d4e5f6789012345678901234567890abcdef34567890abcdef12345678901", "storage_type": "local", "storage_path": "/tenant_1/website_1/2024/01/images/blog-hero-image.jpg", "public_url": "https://example.com/media/tenant_1/website_1/2024/01/images/blog-hero-image.jpg", "width": 1920, "height": 1080, "duration": null, "metadata": {"camera": "Canon EOS 5D Mark IV", "lens": "EF 24-70mm f/2.8L II USM", "iso": 100, "aperture": "f/8", "shutter_speed": "1/125", "focal_length": "50mm"}, "file_type": "image", "category": "content", "alt_text": "Beautiful sunset over mountains", "title": "Sunset Mountain View", "description": "A stunning sunset photograph taken from mountain peaks", "visibility": "public", "access_permissions": [], "view_count": 0, "download_count": 0, "last_accessed_at": null, "status": "ready", "processing_status": {"thumbnails_generated": true, "optimized": true, "webp_generated": true}, "error_message": null}, {"id": 4, "tenant_id": 1, "website_id": 1, "folder_id": 6, "user_id": 1, "filename": "tutorial-demo.mp4", "original_filename": "screen-recording-tutorial.mp4", "slug": "tutorial-demo", "mime_type": "video/mp4", "file_size": 10485760, "file_hash": "d4e5f6789012345678901234567890abcdef4567890abcdef123456789012345", "storage_type": "local", "storage_path": "/tenant_1/website_1/2024/01/videos/tutorial-demo.mp4", "public_url": "https://example.com/media/tenant_1/website_1/2024/01/videos/tutorial-demo.mp4", "width": 1280, "height": 720, "duration": 120, "metadata": {"codec": "H.264", "bitrate": "2000kbps", "frame_rate": "30fps", "audio_codec": "AAC"}, "file_type": "video", "category": "content", "alt_text": "Tutorial demonstration video", "title": "How to Use Our Platform", "description": "Step-by-step tutorial showing platform features", "visibility": "public", "access_permissions": [], "view_count": 0, "download_count": 0, "last_accessed_at": null, "status": "ready", "processing_status": {"thumbnails_generated": true, "transcoded": true, "streaming_ready": true}, "error_message": null}, {"id": 5, "tenant_id": 1, "website_id": 1, "folder_id": 7, "user_id": 1, "filename": "user-manual.pdf", "original_filename": "Complete User Manual v2.1.pdf", "slug": "user-manual", "mime_type": "application/pdf", "file_size": 2097152, "file_hash": "e5f6789012345678901234567890abcdef567890abcdef1234567890123456", "storage_type": "local", "storage_path": "/tenant_1/website_1/2024/01/documents/user-manual.pdf", "public_url": "https://example.com/media/tenant_1/website_1/2024/01/documents/user-manual.pdf", "width": null, "height": null, "duration": null, "metadata": {"pages": 45, "author": "Support Team", "creation_date": "2024-01-15", "producer": "Adobe Acrobat"}, "file_type": "document", "category": "documentation", "alt_text": "User manual PDF document", "title": "Complete User Manual", "description": "Comprehensive guide for using our platform", "visibility": "public", "access_permissions": [], "view_count": 0, "download_count": 0, "last_accessed_at": null, "status": "ready", "processing_status": {"thumbnails_generated": true, "text_extracted": true, "searchable": true}, "error_message": null}, {"id": 6, "tenant_id": 1, "website_id": 2, "folder_id": 10, "user_id": 2, "filename": "portfolio-showcase.jpg", "original_filename": "client-project-final.jpg", "slug": "portfolio-showcase", "mime_type": "image/jpeg", "file_size": 786432, "file_hash": "f6789012345678901234567890abcdef67890abcdef12345678901234567890", "storage_type": "local", "storage_path": "/tenant_1/website_2/2024/01/images/portfolio-showcase.jpg", "public_url": "https://example.com/media/tenant_1/website_2/2024/01/images/portfolio-showcase.jpg", "width": 1600, "height": 900, "duration": null, "metadata": {"project": "Client Portfolio 2024", "style": "Modern Minimalist", "color_palette": ["#2563eb", "#f59e0b", "#10b981"]}, "file_type": "image", "category": "portfolio", "alt_text": "Modern website design showcase", "title": "Portfolio Website Design", "description": "Clean and modern portfolio website design for client", "visibility": "public", "access_permissions": [], "view_count": 0, "download_count": 0, "last_accessed_at": null, "status": "ready", "processing_status": {"thumbnails_generated": true, "optimized": true, "webp_generated": true, "watermarked": true}, "error_message": null}]