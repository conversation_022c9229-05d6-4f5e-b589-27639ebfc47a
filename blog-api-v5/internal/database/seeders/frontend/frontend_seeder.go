package frontend

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// generateUUID generates a simple UUID v4
func generateUUID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x-%x-%x-%x-%x",
		b[0:4], b[4:6], b[6:8], b[8:10], b[10:])
}

// WebsitePage represents the website_pages table
type WebsitePage struct {
	ID               uint               `gorm:"primaryKey;autoIncrement"`
	TenantID         uint               `gorm:"not null;index:idx_tenant_website"`
	WebsiteID        uint               `gorm:"not null;index:idx_tenant_website"`
	TemplateID       *uint              `gorm:"type:int unsigned"`
	Title            string             `gorm:"type:varchar(255);not null"`
	Slug             string             `gorm:"type:varchar(255);not null"`
	Description      *string            `gorm:"type:text"`
	CanonicalURL     *string            `gorm:"type:varchar(500)"`
	PageType         string             `gorm:"type:enum('static','dynamic','template');default:'static'"`
	LayoutType       string             `gorm:"type:varchar(100);default:'default'"`
	IsHomepage       bool               `gorm:"default:false"`
	IsPublished      bool               `gorm:"default:false"`
	RequiresAuth     bool               `gorm:"default:false"`
	PasswordProtected bool              `gorm:"default:false"`
	PagePassword     *string            `gorm:"type:varchar(255)"`
	Visibility       string             `gorm:"type:enum('public','private','password','draft');default:'draft'"`
	FeaturedImage    *string            `gorm:"type:varchar(500)"`
	Status           string             `gorm:"type:enum('draft','review','published','archived','deleted');default:'draft'"`
	PublishedAt      *time.Time         `gorm:"type:timestamp"`
	CreatedBy        uint               `gorm:"not null"`
	UpdatedBy        uint               `gorm:"not null"`
	CreatedAt        time.Time          `gorm:"not null"`
	UpdatedAt        time.Time          `gorm:"not null"`
	PageBlocks       []WebsitePageBlock `gorm:"foreignKey:PageID"`
}

// WebsitePageBlock represents the website_page_blocks table
type WebsitePageBlock struct {
	ID             uint            `gorm:"primaryKey;autoIncrement"`
	PageID         uint            `gorm:"not null;index:idx_page_block"`
	TenantID       uint            `gorm:"not null"`
	WebsiteID      uint            `gorm:"not null"`
	BlockUUID      string          `gorm:"type:varchar(36);not null;uniqueIndex"`
	BlockType      string          `gorm:"type:varchar(100);not null"`
	BlockName      string          `gorm:"type:varchar(255);not null"`
	BlockData      json.RawMessage `gorm:"type:json"`
	BlockProps     json.RawMessage `gorm:"type:json"`
	BlockStyles    json.RawMessage `gorm:"type:json"`
	OrderIndex     int             `gorm:"default:0"`
	ParentBlockID  *uint           `gorm:"type:int unsigned"`
	ContainerName  string          `gorm:"type:varchar(100);default:'main'"`
	IsVisible      bool            `gorm:"default:true"`
	IsLocked       bool            `gorm:"default:false"`
	IsGlobal       bool            `gorm:"default:false"`
	BlockVersion   string          `gorm:"type:varchar(20);default:'1.0.0'"`
	BlockCategory  *string         `gorm:"type:varchar(50)"`
	DesktopVisible bool            `gorm:"default:true"`
	CreatedBy      uint            `gorm:"not null"`
	CreatedAt      time.Time       `gorm:"not null"`
	UpdatedAt      time.Time       `gorm:"not null"`
}

// TableName for WebsitePage
func (WebsitePage) TableName() string {
	return "website_pages"
}

// TableName for WebsitePageBlock
func (WebsitePageBlock) TableName() string {
	return "website_page_blocks"
}

// SeedFrontendPages seeds frontend pages and blocks for tenant 1, website 2
func SeedFrontendPages(db *gorm.DB) error {
	// Create home page
	description := "Welcome to our website"
	publishedAt := time.Now()
	
	homePage := WebsitePage{
		TenantID:     1,
		WebsiteID:    2,
		Slug:         "home",
		Title:        "Home",
		Description:  &description,
		Status:       "published",
		PageType:     "static",
		LayoutType:   "default",
		IsHomepage:   true,
		IsPublished:  true,
		Visibility:   "public",
		PublishedAt:  &publishedAt,
		CreatedBy:    1,
		UpdatedBy:    1,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Check if page already exists
	var existingPage WebsitePage
	if err := db.Where("tenant_id = ? AND website_id = ? AND slug = ?", 1, 2, "home").First(&existingPage).Error; err == nil {
		fmt.Printf("Found existing home page with ID %d, updating it\n", existingPage.ID)
		// Update the page if it exists
		homePage.ID = existingPage.ID
		db.Model(&existingPage).Updates(&homePage)
	} else {
		// Page doesn't exist, create it
		fmt.Println("Home page doesn't exist, creating new one")
		// Create the page
		tx := db.Begin()
		if err := tx.Create(&homePage).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create home page: %w", err)
		}
		tx.Commit()
		fmt.Printf("Created home page with ID: %d\n", homePage.ID)
	}

	// Delete existing blocks for this page
	db.Where("page_id = ?", homePage.ID).Delete(&WebsitePageBlock{})
	
	// Create blocks for the home page
	blocks := []WebsitePageBlock{
		{
			PageID:         homePage.ID,
			TenantID:       6,
			WebsiteID:      1,
			BlockUUID:      generateUUID(),
			BlockType:      "hero",
			BlockName:      "Hero Section",
			BlockData: json.RawMessage(`{
				"title": "Welcome to Our Blog",
				"subtitle": "Discover amazing content and stories",
				"backgroundImage": "/images/hero-bg.jpg",
				"ctaText": "Get Started",
				"ctaLink": "/blog"
			}`),
			BlockProps:     json.RawMessage(`{}`),
			BlockStyles:    json.RawMessage(`{"fullWidth": true, "padding": "large"}`),
			OrderIndex:     1,
			ContainerName:  "main",
			IsVisible:      true,
			DesktopVisible: true,
			BlockVersion:   "1.0.0",
			CreatedBy:      1,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
		{
			PageID:         homePage.ID,
			TenantID:       6,
			WebsiteID:      1,
			BlockUUID:      generateUUID(),
			BlockType:      "featured-posts",
			BlockName:      "Featured Posts",
			BlockData: json.RawMessage(`{
				"title": "Featured Posts",
				"limit": 3,
				"category": "technology"
			}`),
			BlockProps:     json.RawMessage(`{}`),
			BlockStyles:    json.RawMessage(`{"container": true, "spacing": "medium"}`),
			OrderIndex:     2,
			ContainerName:  "main",
			IsVisible:      true,
			DesktopVisible: true,
			BlockVersion:   "1.0.0",
			CreatedBy:      1,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
		{
			PageID:         homePage.ID,
			TenantID:       6,
			WebsiteID:      1,
			BlockUUID:      generateUUID(),
			BlockType:      "text",
			BlockName:      "About Section",
			BlockData: json.RawMessage(`{
				"content": "<h2>About Our Platform</h2><p>We provide high-quality content on various topics including technology, lifestyle, and business. Join our community of readers and writers today!</p>",
				"alignment": "center"
			}`),
			BlockProps:     json.RawMessage(`{}`),
			BlockStyles:    json.RawMessage(`{"maxWidth": "800px", "margin": "auto"}`),
			OrderIndex:     3,
			ContainerName:  "main",
			IsVisible:      true,
			DesktopVisible: true,
			BlockVersion:   "1.0.0",
			CreatedBy:      1,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
		{
			PageID:         homePage.ID,
			TenantID:       6,
			WebsiteID:      1,
			BlockUUID:      generateUUID(),
			BlockType:      "newsletter",
			BlockName:      "Newsletter",
			BlockData: json.RawMessage(`{
				"title": "Subscribe to Our Newsletter",
				"description": "Get the latest posts delivered right to your inbox",
				"buttonText": "Subscribe",
				"placeholderText": "Enter your email"
			}`),
			BlockProps:     json.RawMessage(`{}`),
			BlockStyles:    json.RawMessage(`{"backgroundColor": "#f8f9fa", "padding": "large"}`),
			OrderIndex:     4,
			ContainerName:  "main",
			IsVisible:      true,
			DesktopVisible: true,
			BlockVersion:   "1.0.0",
			CreatedBy:      1,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
	}

	// Create blocks
	for _, block := range blocks {
		if err := db.Create(&block).Error; err != nil {
			return fmt.Errorf("failed to create block: %w", err)
		}
	}

	// Create index page (for fallback)
	indexDesc := "Index page"
	indexPublishedAt := time.Now()
	
	indexPage := WebsitePage{
		TenantID:     1,
		WebsiteID:    2,
		Slug:         "index",
		Title:        "Index",
		Description:  &indexDesc,
		Status:       "published",
		PageType:     "static",
		LayoutType:   "default",
		IsHomepage:   false,
		IsPublished:  true,
		Visibility:   "public",
		PublishedAt:  &indexPublishedAt,
		CreatedBy:    1,
		UpdatedBy:    1,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Check if index page already exists
	var existingIndexPage WebsitePage
	if err := db.Where("tenant_id = ? AND website_id = ? AND slug = ?", 1, 2, "index").First(&existingIndexPage).Error; err == nil {
		fmt.Printf("Found existing index page with ID %d, updating it\n", existingIndexPage.ID)
		// Delete existing blocks first
		db.Where("page_id = ?", existingIndexPage.ID).Delete(&WebsitePageBlock{})
		// Update the page
		indexPage.ID = existingIndexPage.ID
		db.Model(&existingIndexPage).Updates(&indexPage)
	} else {
		fmt.Println("Index page doesn't exist, creating new one")
		// Create the index page
		if err := db.Create(&indexPage).Error; err != nil {
			return fmt.Errorf("failed to create index page: %w", err)
		}
		fmt.Printf("Created index page with ID: %d\n", indexPage.ID)

		// Create a simple block for index page
		indexBlock := WebsitePageBlock{
			PageID:         indexPage.ID,
			TenantID:       6,
			WebsiteID:      1,
			BlockUUID:      generateUUID(),
			BlockType:      "text",
			BlockName:      "Welcome Text",
			BlockData: json.RawMessage(`{
				"content": "<h1>Welcome</h1><p>This is the index page. Visit our <a href='/home'>home page</a> for more content.</p>",
				"alignment": "center"
			}`),
			BlockProps:     json.RawMessage(`{}`),
			BlockStyles:    json.RawMessage(`{"padding": "large"}`),
			OrderIndex:     1,
			ContainerName:  "main",
			IsVisible:      true,
			DesktopVisible: true,
			BlockVersion:   "1.0.0",
			CreatedBy:      1,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}

		if err := db.Create(&indexBlock).Error; err != nil {
			return fmt.Errorf("failed to create index block: %w", err)
		}
	}

	fmt.Println("Successfully seeded frontend pages and blocks for tenant 6, website 1")
	return nil
}

// Run executes the seeder
func Run(db *gorm.DB) error {
	return SeedFrontendPages(db)
}