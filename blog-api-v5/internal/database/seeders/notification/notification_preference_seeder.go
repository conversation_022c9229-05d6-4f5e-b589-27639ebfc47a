package notification

import (
	"log"

	"gorm.io/gorm"
)

type NotificationPreferenceSeeder struct {
	db *gorm.DB
}

func NewNotificationPreferenceSeeder(db *gorm.DB) *NotificationPreferenceSeeder {
	return &NotificationPreferenceSeeder{db: db}
}

func (s *NotificationPreferenceSeeder) Seed() error {
	// Get first tenant and user for seeding
	var tenantID uint = 1
	var userID uint = 1

	// Default notification preferences for different types and channels
	preferences := []map[string]interface{}{
		// Email Preferences
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "user_welcome",
			"channel":           "email",
			"is_enabled":        true,
			"frequency":         "instant",
			"language":          "en",
			"delivery_settings": `{"priority": "high", "track_opens": true, "track_clicks": true}`,
			"preferences":       `{"send_time": "any", "format": "html"}`,
		},
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "password_reset",
			"channel":           "email",
			"is_enabled":        true,
			"frequency":         "instant",
			"language":          "en",
			"delivery_settings": `{"priority": "urgent", "track_opens": false, "track_clicks": true}`,
			"preferences":       `{"send_time": "any", "format": "html"}`,
		},
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "blog_post_published",
			"channel":           "email",
			"is_enabled":        true,
			"frequency":         "daily",
			"quiet_hours_start": "22:00:00",
			"quiet_hours_end":   "08:00:00",
			"timezone":          "UTC",
			"language":          "en",
			"delivery_settings": `{"priority": "normal", "track_opens": true, "track_clicks": true}`,
			"preferences":       `{"digest": true, "max_per_day": 3, "format": "html"}`,
		},
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "system_maintenance",
			"channel":           "email",
			"is_enabled":        true,
			"frequency":         "instant",
			"language":          "en",
			"delivery_settings": `{"priority": "high", "track_opens": false, "track_clicks": false}`,
			"preferences":       `{"send_time": "any", "format": "text"}`,
		},
		// Socket Preferences
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "welcome_message",
			"channel":           "socket",
			"is_enabled":        true,
			"frequency":         "instant",
			"language":          "en",
			"delivery_settings": `{"persist": true, "sound": true, "auto_dismiss": false}`,
			"preferences":       `{"show_avatar": true, "animation": "slideIn"}`,
		},
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "blog_post_published",
			"channel":           "socket",
			"is_enabled":        true,
			"frequency":         "instant",
			"quiet_hours_start": "22:00:00",
			"quiet_hours_end":   "08:00:00",
			"timezone":          "UTC",
			"language":          "en",
			"delivery_settings": `{"persist": false, "sound": false, "auto_dismiss": true, "dismiss_after": 5000}`,
			"preferences":       `{"show_preview": true, "position": "top-right"}`,
		},
		// Push Notification Preferences
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "blog_post_published",
			"channel":           "push",
			"is_enabled":        false, // Default disabled for marketing
			"frequency":         "daily",
			"quiet_hours_start": "22:00:00",
			"quiet_hours_end":   "08:00:00",
			"timezone":          "UTC",
			"language":          "en",
			"delivery_settings": `{"badge": true, "sound": "default", "vibrate": true}`,
			"preferences":       `{"max_per_day": 2, "deep_link": true}`,
		},
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "system_maintenance",
			"channel":           "push",
			"is_enabled":        true,
			"frequency":         "instant",
			"language":          "en",
			"delivery_settings": `{"badge": true, "sound": "urgent", "vibrate": true, "priority": "high"}`,
			"preferences":       `{"critical": true, "bypass_dnd": true}`,
		},
		// SMS Preferences (mostly disabled by default)
		{
			"tenant_id":         tenantID,
			"user_id":           userID,
			"notification_type": "password_reset",
			"channel":           "sms",
			"is_enabled":        false, // SMS typically opt-in
			"frequency":         "instant",
			"language":          "en",
			"delivery_settings": `{"short_url": true, "sender_name": "BlogAPI"}`,
			"preferences":       `{"format": "text", "include_brand": true}`,
		},
	}

	for _, pref := range preferences {
		if err := s.db.Table("notification_preferences").Create(&pref).Error; err != nil {
			log.Printf("Error seeding notification preference: %v", err)
			return err
		}
	}

	log.Printf("✅ Seeded notification preferences for user %d", userID)
	return nil
}

func (s *NotificationPreferenceSeeder) SeedMultipleUsers() error {
	// Get existing users from database instead of hardcoded IDs
	var users []struct {
		ID uint `json:"id"`
	}
	if err := s.db.Table("users").Select("id").Find(&users).Error; err != nil {
		log.Printf("Error getting user IDs: %v", err)
		return err
	}

	var userIDs []uint
	for _, user := range users {
		userIDs = append(userIDs, user.ID)
	}

	if len(userIDs) == 0 {
		log.Printf("No users found, skipping notification preference seeding")
		return nil
	}

	var tenantID uint = 1

	basePreferences := []struct {
		NotificationType string
		Channel          string
		IsEnabled        bool
		Frequency        string
		Language         string
	}{
		{"user_welcome", "email", true, "instant", "en"},
		{"password_reset", "email", true, "instant", "en"},
		{"blog_post_published", "email", true, "weekly", "en"},
		{"welcome_message", "socket", true, "instant", "en"},
		{"blog_post_published", "socket", true, "instant", "en"},
		{"blog_post_published", "push", false, "never", "en"},
		{"system_maintenance", "push", true, "instant", "en"},
	}

	for _, userID := range userIDs {
		for _, basePref := range basePreferences {
			pref := map[string]interface{}{
				"tenant_id":         tenantID,
				"user_id":           userID,
				"notification_type": basePref.NotificationType,
				"channel":           basePref.Channel,
				"is_enabled":        basePref.IsEnabled,
				"frequency":         basePref.Frequency,
				"language":          basePref.Language,
				"delivery_settings": `{}`,
				"preferences":       `{}`,
			}

			// Check if record already exists to avoid duplicates
			var count int64
			err := s.db.Table("notification_preferences").
				Where("tenant_id = ? AND user_id = ? AND notification_type = ? AND channel = ?",
					tenantID, userID, basePref.NotificationType, basePref.Channel).
				Count(&count).Error

			if err != nil {
				log.Printf("Error checking existing preference for user %d: %v", userID, err)
				return err
			}

			if count == 0 {
				// Record doesn't exist, create it
				if err := s.db.Table("notification_preferences").Create(&pref).Error; err != nil {
					log.Printf("Error seeding preference for user %d: %v", userID, err)
					return err
				}
			}
			// If record exists, skip it
		}
		log.Printf("✅ Seeded notification preferences for user %d", userID)
	}

	return nil
}

func (s *NotificationPreferenceSeeder) SeedAll() error {
	if err := s.Seed(); err != nil {
		return err
	}
	return s.SeedMultipleUsers()
}
