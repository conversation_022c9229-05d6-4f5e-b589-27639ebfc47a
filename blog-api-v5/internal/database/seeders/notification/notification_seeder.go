package notification

import (
	"context"
	"database/sql"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// NotificationSeeder handles notification module seeding
type NotificationSeeder struct {
	db *sql.DB
}

// NewNotificationSeeder creates a new notification seeder
func NewNotificationSeeder(db *sql.DB) *NotificationSeeder {
	return &NotificationSeeder{
		db: db,
	}
}

// Name returns the seeder name
func (s *NotificationSeeder) Name() string {
	return "notification_seeder"
}

// Seed implements the Seeder interface
func (s *NotificationSeeder) Seed(ctx context.Context) error {
	// Setup GORM from SQL DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: s.db,
	}), &gorm.Config{})
	if err != nil {
		return err
	}

	// Run all notification seeders
	return SeedAll(gormDB)
}
