package notification

import (
	"gorm.io/gorm"
)

// RegisterSeeders registers all notification module seeders
func RegisterSeeders(db *gorm.DB) []interface{} {
	return []interface{}{
		NewNotificationTemplateSeeder(db),
		NewNotificationPreferenceSeeder(db),
		NewTenantConfigSeeder(db),
	}
}

// SeedAll runs all notification seeders
func SeedAll(db *gorm.DB) error {
	// Seed templates first
	templateSeeder := NewNotificationTemplateSeeder(db)
	if err := templateSeeder.SeedAll(); err != nil {
		return err
	}

	// Seed tenant configurations
	configSeeder := NewTenantConfigSeeder(db)
	if err := configSeeder.SeedAll(); err != nil {
		return err
	}

	// Seed user preferences
	preferenceSeeder := NewNotificationPreferenceSeeder(db)
	if err := preferenceSeeder.SeedAll(); err != nil {
		return err
	}

	return nil
}
