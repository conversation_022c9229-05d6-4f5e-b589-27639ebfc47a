package notification

import (
	"log"

	"gorm.io/gorm"
)

type TenantConfigSeeder struct {
	db *gorm.DB
}

func NewTenantConfigSeeder(db *gorm.DB) *TenantConfigSeeder {
	return &TenantConfigSeeder{db: db}
}

func (s *TenantConfigSeeder) Seed() error {
	var tenantID uint = 1

	configs := []map[string]interface{}{
		// Email Configuration - SMTP
		{
			"tenant_id":  tenantID,
			"channel":    "email",
			"provider":   "smtp",
			"is_active":  true,
			"is_primary": true,
			"configuration": `{
				"smtp_host": "smtp.gmail.com",
				"smtp_port": 587,
				"smtp_encryption": "tls",
				"smtp_username": "<EMAIL>",
				"smtp_from_name": "Blog API Team",
				"smtp_from_email": "<EMAIL>",
				"smtp_reply_to": "<EMAIL>"
			}`,
			"credentials": `{
				"smtp_password": "app_password_here",
				"api_key": null
			}`,
			"settings": `{
				"max_recipients_per_email": 50,
				"retry_attempts": 3,
				"retry_delay_seconds": 300,
				"bounce_handling": true,
				"complaint_handling": true,
				"open_tracking": true,
				"click_tracking": true,
				"unsubscribe_tracking": true
			}`,
			"rate_limits": `{
				"emails_per_minute": 100,
				"emails_per_hour": 1000,
				"emails_per_day": 10000,
				"burst_limit": 20
			}`,
			"test_status": "passed",
		},
		// Email Configuration - SendGrid (Alternative)
		{
			"tenant_id":  tenantID,
			"channel":    "email",
			"provider":   "sendgrid",
			"is_active":  false,
			"is_primary": false,
			"configuration": `{
				"api_endpoint": "https://api.sendgrid.com/v3/mail/send",
				"from_name": "Blog API Team",
				"from_email": "<EMAIL>",
				"reply_to": "<EMAIL>",
				"template_engine": "handlebars"
			}`,
			"credentials": `{
				"api_key": "sendgrid_api_key_here",
				"webhook_secret": "webhook_secret_here"
			}`,
			"settings": `{
				"tracking": {
					"click_tracking": true,
					"open_tracking": true,
					"subscription_tracking": true
				},
				"mail_settings": {
					"spam_check": true,
					"sandbox_mode": false
				}
			}`,
			"rate_limits": `{
				"emails_per_minute": 600,
				"emails_per_hour": 10000,
				"emails_per_day": 100000,
				"burst_limit": 100
			}`,
			"test_status": nil,
		},
		// Socket Configuration
		{
			"tenant_id":  tenantID,
			"channel":    "socket",
			"provider":   "socketio",
			"is_active":  true,
			"is_primary": true,
			"configuration": `{
				"server_url": "http://localhost:3001",
				"namespace": "/notifications",
				"path": "/socket.io",
				"transports": ["websocket", "polling"],
				"cors_origins": ["http://localhost:3000", "https://blogapi.com"],
				"adapter": "redis",
				"redis_url": "redis://localhost:6379/0"
			}`,
			"credentials": `{
				"auth_secret": "socket_auth_secret_here",
				"redis_password": null
			}`,
			"settings": `{
				"connection_timeout": 30000,
				"ping_timeout": 60000,
				"ping_interval": 25000,
				"max_connections_per_user": 5,
				"enable_compression": true,
				"enable_binary": true,
				"room_management": {
					"auto_join_tenant_room": true,
					"auto_join_user_room": true,
					"cleanup_empty_rooms": true
				}
			}`,
			"rate_limits": `{
				"messages_per_minute": 100,
				"messages_per_hour": 1000,
				"connections_per_ip": 10,
				"max_room_size": 1000
			}`,
			"test_status": "passed",
		},
		// Push Notification Configuration - FCM
		{
			"tenant_id":  tenantID,
			"channel":    "push",
			"provider":   "fcm",
			"is_active":  true,
			"is_primary": true,
			"configuration": `{
				"project_id": "blogapi-firebase-project",
				"api_endpoint": "https://fcm.googleapis.com/v1/projects/blogapi-firebase-project/messages:send",
				"service_account_key_path": "/path/to/firebase-service-account.json",
				"default_icon": "https://blogapi.com/icons/notification-icon.png",
				"default_badge": "https://blogapi.com/icons/badge-icon.png"
			}`,
			"credentials": `{
				"service_account_key": "firebase_service_account_json_here",
				"server_key": "firebase_server_key_here"
			}`,
			"settings": `{
				"android": {
					"priority": "high",
					"ttl": "3600s",
					"collapse_key": "blog_notifications",
					"restricted_package_name": "com.blogapi.android"
				},
				"ios": {
					"priority": "10",
					"badge_count": true,
					"sound": "default",
					"content_available": true,
					"mutable_content": true
				},
				"web": {
					"icon": "https://blogapi.com/icons/web-notification.png",
					"badge": "https://blogapi.com/icons/web-badge.png",
					"requireInteraction": false,
					"silent": false
				}
			}`,
			"rate_limits": `{
				"notifications_per_minute": 500,
				"notifications_per_hour": 10000,
				"notifications_per_day": 100000,
				"burst_limit": 100,
				"per_device_limit": 10
			}`,
			"test_status": nil,
		},
		// SMS Configuration - Twilio
		{
			"tenant_id":  tenantID,
			"channel":    "sms",
			"provider":   "twilio",
			"is_active":  false, // SMS disabled by default
			"is_primary": true,
			"configuration": `{
				"account_sid": "twilio_account_sid_here",
				"api_endpoint": "https://api.twilio.com/2010-04-01/Accounts",
				"from_number": "+**********",
				"messaging_service_sid": "twilio_messaging_service_sid",
				"webhook_url": "https://blogapi.com/webhooks/twilio"
			}`,
			"credentials": `{
				"auth_token": "twilio_auth_token_here",
				"api_key": "twilio_api_key_here",
				"api_secret": "twilio_api_secret_here"
			}`,
			"settings": `{
				"delivery_callback": true,
				"status_callback": true,
				"max_message_length": 160,
				"split_long_messages": true,
				"enable_media": false,
				"country_code": "+1",
				"allowed_countries": ["US", "CA", "GB", "AU"]
			}`,
			"rate_limits": `{
				"messages_per_minute": 60,
				"messages_per_hour": 1000,
				"messages_per_day": 10000,
				"burst_limit": 10,
				"per_number_limit": 5
			}`,
			"test_status": nil,
		},
	}

	for _, config := range configs {
		// Check if config exists
		var count int64
		s.db.Table("tenant_notification_configs").
			Where("tenant_id = ? AND channel = ? AND provider = ?",
				config["tenant_id"], config["channel"], config["provider"]).
			Count(&count)

		if count == 0 {
			// Create new config
			if err := s.db.Table("tenant_notification_configs").Create(&config).Error; err != nil {
				log.Printf("Error seeding tenant notification config %s-%s: %v",
					config["channel"], config["provider"], err)
				return err
			}
		} else {
			// Update existing config
			if err := s.db.Table("tenant_notification_configs").
				Where("tenant_id = ? AND channel = ? AND provider = ?",
					config["tenant_id"], config["channel"], config["provider"]).
				Updates(&config).Error; err != nil {
				log.Printf("Error updating tenant notification config %s-%s: %v",
					config["channel"], config["provider"], err)
				return err
			}
		}
		log.Printf("✅ Seeded tenant notification config: %s-%s",
			config["channel"], config["provider"])
	}

	return nil
}

func (s *TenantConfigSeeder) SeedTestConfigs() error {
	// Add test configurations for development
	var tenantID uint = 1

	testConfigs := []map[string]interface{}{
		// Test Email Configuration - Mailtrap
		{
			"tenant_id":  tenantID,
			"channel":    "email",
			"provider":   "mailtrap",
			"is_active":  false,
			"is_primary": false,
			"configuration": `{
				"smtp_host": "smtp.mailtrap.io",
				"smtp_port": 2525,
				"smtp_encryption": "tls",
				"smtp_username": "mailtrap_username",
				"smtp_from_name": "Blog API Test",
				"smtp_from_email": "<EMAIL>",
				"environment": "development"
			}`,
			"credentials": `{
				"smtp_password": "mailtrap_password"
			}`,
			"settings": `{
				"test_mode": true,
				"capture_emails": true,
				"max_recipients_per_email": 10
			}`,
			"rate_limits": `{
				"emails_per_minute": 10,
				"emails_per_hour": 100,
				"emails_per_day": 1000
			}`,
			"test_status": nil,
		},
		// Local Socket Configuration for Development
		{
			"tenant_id":  tenantID,
			"channel":    "socket",
			"provider":   "local",
			"is_active":  false,
			"is_primary": false,
			"configuration": `{
				"server_url": "http://localhost:3001",
				"namespace": "/dev-notifications",
				"cors_origins": ["http://localhost:3000"],
				"environment": "development"
			}`,
			"credentials": `{
				"auth_secret": "dev_socket_secret"
			}`,
			"settings": `{
				"debug_mode": true,
				"log_all_events": true,
				"max_connections_per_user": 2
			}`,
			"rate_limits": `{
				"messages_per_minute": 50,
				"connections_per_ip": 5
			}`,
			"test_status": nil,
		},
	}

	for _, config := range testConfigs {
		// Check if config exists
		var count int64
		s.db.Table("tenant_notification_configs").
			Where("tenant_id = ? AND channel = ? AND provider = ?",
				config["tenant_id"], config["channel"], config["provider"]).
			Count(&count)

		if count == 0 {
			// Create new config
			if err := s.db.Table("tenant_notification_configs").Create(&config).Error; err != nil {
				log.Printf("Error seeding test config %s-%s: %v",
					config["channel"], config["provider"], err)
				return err
			}
		} else {
			// Update existing config
			if err := s.db.Table("tenant_notification_configs").
				Where("tenant_id = ? AND channel = ? AND provider = ?",
					config["tenant_id"], config["channel"], config["provider"]).
				Updates(&config).Error; err != nil {
				log.Printf("Error updating test config %s-%s: %v",
					config["channel"], config["provider"], err)
				return err
			}
		}
		log.Printf("✅ Seeded test notification config: %s-%s",
			config["channel"], config["provider"])
	}

	return nil
}

func (s *TenantConfigSeeder) SeedAll() error {
	if err := s.Seed(); err != nil {
		return err
	}
	return s.SeedTestConfigs()
}
