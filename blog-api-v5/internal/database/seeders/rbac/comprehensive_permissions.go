package rbac

// ComprehensivePermissions returns all permissions for all modules
func GetComprehensivePermissions() []Permission {
	return []Permission{
		// User Management Permissions (existing)
		{Name: "users.create", DisplayName: "Create Users", Description: "Create new user accounts", Module: "user", Resource: "users", Action: "create", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "users.read", DisplayName: "Read Users", Description: "View user accounts and profiles", Module: "user", Resource: "users", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "users.update", DisplayName: "Update Users", Description: "Edit user accounts and profiles", Module: "user", Resource: "users", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "users.delete", DisplayName: "Delete Users", Description: "Delete user accounts", Module: "user", Resource: "users", Action: "delete", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "users.list", DisplayName: "List Users", Description: "View list of all users", Module: "user", Resource: "users", Action: "list", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "users.impersonate", DisplayName: "Impersonate Users", Description: "Login as another user", Module: "user", Resource: "users", Action: "impersonate", Scope: "tenant", RiskLevel: "critical", IsSystemPermission: true},

		// User Profile Permissions
		{Name: "user-profiles.create", DisplayName: "Create User Profiles", Description: "Create user profile information", Module: "user", Resource: "user-profiles", Action: "create", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true, RequiresOwnership: true},
		{Name: "user-profiles.read", DisplayName: "Read User Profiles", Description: "View user profile information", Module: "user", Resource: "user-profiles", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "user-profiles.update", DisplayName: "Update User Profiles", Description: "Edit user profile information", Module: "user", Resource: "user-profiles", Action: "update", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true, RequiresOwnership: true},
		{Name: "user-profiles.delete", DisplayName: "Delete User Profiles", Description: "Delete user profile information", Module: "user", Resource: "user-profiles", Action: "delete", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true, RequiresOwnership: true},

		// Website Management Permissions
		{Name: "websites.create", DisplayName: "Create Websites", Description: "Create new websites", Module: "website", Resource: "websites", Action: "create", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "websites.read", DisplayName: "Read Websites", Description: "View website information", Module: "website", Resource: "websites", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "websites.update", DisplayName: "Update Websites", Description: "Edit website configuration", Module: "website", Resource: "websites", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "websites.delete", DisplayName: "Delete Websites", Description: "Delete websites", Module: "website", Resource: "websites", Action: "delete", Scope: "tenant", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "websites.list", DisplayName: "List Websites", Description: "View list of all websites", Module: "website", Resource: "websites", Action: "list", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "websites.manage-settings", DisplayName: "Manage Website Settings", Description: "Configure website settings", Module: "website", Resource: "websites", Action: "manage-settings", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},

		// Content Management Permissions
		{Name: "content.create", DisplayName: "Create Content", Description: "Create website content", Module: "website", Resource: "content", Action: "create", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "content.read", DisplayName: "Read Content", Description: "View website content", Module: "website", Resource: "content", Action: "read", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "content.update", DisplayName: "Update Content", Description: "Edit website content", Module: "website", Resource: "content", Action: "update", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "content.delete", DisplayName: "Delete Content", Description: "Delete website content", Module: "website", Resource: "content", Action: "delete", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "content.publish", DisplayName: "Publish Content", Description: "Publish website content", Module: "website", Resource: "content", Action: "publish", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},

		// Authentication Permissions
		{Name: "auth.manage-sessions", DisplayName: "Manage Sessions", Description: "Manage user authentication sessions", Module: "auth", Resource: "sessions", Action: "manage", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "auth.manage-tokens", DisplayName: "Manage Auth Tokens", Description: "Manage authentication tokens", Module: "auth", Resource: "tokens", Action: "manage", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "auth.manage-providers", DisplayName: "Manage Auth Providers", Description: "Manage OAuth providers and settings", Module: "auth", Resource: "providers", Action: "manage", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "auth.view-logs", DisplayName: "View Auth Logs", Description: "View authentication logs and audit trails", Module: "auth", Resource: "logs", Action: "read", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},

		// RBAC Management Permissions
		{Name: "rbac.roles.create", DisplayName: "Create Roles", Description: "Create new roles", Module: "rbac", Resource: "roles", Action: "create", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.roles.read", DisplayName: "Read Roles", Description: "View roles and role details", Module: "rbac", Resource: "roles", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "rbac.roles.update", DisplayName: "Update Roles", Description: "Edit roles and role permissions", Module: "rbac", Resource: "roles", Action: "update", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.roles.delete", DisplayName: "Delete Roles", Description: "Delete roles", Module: "rbac", Resource: "roles", Action: "delete", Scope: "tenant", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "rbac.roles.permissions.assign", DisplayName: "Assign Role Permissions", Description: "Assign permissions to roles", Module: "rbac", Resource: "roles", Action: "assign-permissions", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.roles.permissions.revoke", DisplayName: "Revoke Role Permissions", Description: "Remove permissions from roles", Module: "rbac", Resource: "roles", Action: "revoke-permissions", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},

		// RBAC User Management
		{Name: "rbac.users.read", DisplayName: "Read User Roles", Description: "View user role assignments", Module: "rbac", Resource: "users", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "rbac.users.roles.assign", DisplayName: "Assign User Roles", Description: "Assign roles to users", Module: "rbac", Resource: "users", Action: "assign", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.users.roles.revoke", DisplayName: "Revoke User Roles", Description: "Remove roles from users", Module: "rbac", Resource: "users", Action: "revoke", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.users.roles.update", DisplayName: "Update User Roles", Description: "Modify user role assignments", Module: "rbac", Resource: "users", Action: "update", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},

		// RBAC Admin Permissions
		{Name: "rbac.admin.read", DisplayName: "Read Admin Data", Description: "Access RBAC admin dashboard", Module: "rbac", Resource: "admin", Action: "read", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "rbac.admin.users.check", DisplayName: "Check User Permissions", Description: "Check permissions for any user", Module: "rbac", Resource: "admin", Action: "check-users", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "rbac.admin.cache.read", DisplayName: "Read Cache Stats", Description: "View RBAC cache statistics", Module: "rbac", Resource: "admin", Action: "read-cache", Scope: "global", RiskLevel: "low", IsSystemPermission: true},
		{Name: "rbac.admin.cache.clear", DisplayName: "Clear Cache", Description: "Clear RBAC cache", Module: "rbac", Resource: "admin", Action: "clear-cache", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.admin.cache.warmup", DisplayName: "Warmup Cache", Description: "Warmup RBAC cache", Module: "rbac", Resource: "admin", Action: "warmup-cache", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "rbac.admin.cache.preload", DisplayName: "Preload Cache", Description: "Preload user permissions to cache", Module: "rbac", Resource: "admin", Action: "preload-cache", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "rbac.admin.permissions.create", DisplayName: "Create Permissions", Description: "Create new permissions", Module: "rbac", Resource: "admin", Action: "create-permissions", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "rbac.admin.permissions.update", DisplayName: "Update Permissions", Description: "Modify permissions", Module: "rbac", Resource: "admin", Action: "update-permissions", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "rbac.admin.permissions.delete", DisplayName: "Delete Permissions", Description: "Delete permissions", Module: "rbac", Resource: "admin", Action: "delete-permissions", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "rbac.admin.permissions.bulk", DisplayName: "Bulk Manage Permissions", Description: "Bulk operations on permissions", Module: "rbac", Resource: "admin", Action: "bulk-permissions", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "rbac.admin.cleanup", DisplayName: "Cleanup RBAC Data", Description: "Clean up expired roles and data", Module: "rbac", Resource: "admin", Action: "cleanup", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "rbac.admin.config.export", DisplayName: "Export Config", Description: "Export RBAC configuration", Module: "rbac", Resource: "admin", Action: "export-config", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "rbac.admin.config.import", DisplayName: "Import Config", Description: "Import RBAC configuration", Module: "rbac", Resource: "admin", Action: "import-config", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "rbac.admin.config.validate", DisplayName: "Validate Config", Description: "Validate RBAC configuration", Module: "rbac", Resource: "admin", Action: "validate-config", Scope: "global", RiskLevel: "low", IsSystemPermission: true},

		// Tenant Management Permissions (comprehensive)
		{Name: "tenant.create", DisplayName: "Create Tenant", Description: "Create new tenants", Module: "tenant", Resource: "tenants", Action: "create", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "tenant.read", DisplayName: "Read Tenant", Description: "View tenant information", Module: "tenant", Resource: "tenants", Action: "read", Scope: "global", RiskLevel: "low", IsSystemPermission: true},
		{Name: "tenant.list", DisplayName: "List Tenants", Description: "View list of all tenants", Module: "tenant", Resource: "tenants", Action: "list", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "tenant.update", DisplayName: "Update Tenant", Description: "Edit tenant configuration", Module: "tenant", Resource: "tenants", Action: "update", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.delete", DisplayName: "Delete Tenant", Description: "Delete tenants", Module: "tenant", Resource: "tenants", Action: "delete", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "tenant.status.update", DisplayName: "Update Tenant Status", Description: "Change tenant status", Module: "tenant", Resource: "tenants", Action: "update-status", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.plan.assign", DisplayName: "Assign Tenant Plan", Description: "Assign subscription plan to tenant", Module: "tenant", Resource: "tenants", Action: "assign-plan", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.stats.read", DisplayName: "Read Tenant Stats", Description: "View tenant statistics", Module: "tenant", Resource: "tenants", Action: "read-stats", Scope: "global", RiskLevel: "low", IsSystemPermission: true},

		// Tenant Plan Permissions
		{Name: "tenant.plan.create", DisplayName: "Create Plan", Description: "Create subscription plans", Module: "tenant", Resource: "plans", Action: "create", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.plan.read", DisplayName: "Read Plan", Description: "View subscription plans", Module: "tenant", Resource: "plans", Action: "read", Scope: "global", RiskLevel: "low", IsSystemPermission: true},
		{Name: "tenant.plan.update", DisplayName: "Update Plan", Description: "Edit subscription plans", Module: "tenant", Resource: "plans", Action: "update", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.plan.delete", DisplayName: "Delete Plan", Description: "Delete subscription plans", Module: "tenant", Resource: "plans", Action: "delete", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},

		// Tenant Settings Permissions
		{Name: "tenant.settings.read", DisplayName: "Read Tenant Settings", Description: "View tenant settings", Module: "tenant", Resource: "settings", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "tenant.settings.update", DisplayName: "Update Tenant Settings", Description: "Modify tenant settings", Module: "tenant", Resource: "settings", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "tenant.settings.delete", DisplayName: "Delete Tenant Settings", Description: "Remove tenant settings", Module: "tenant", Resource: "settings", Action: "delete", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},

		// Tenant Feature Permissions
		{Name: "tenant.features.read", DisplayName: "Read Features", Description: "View tenant features", Module: "tenant", Resource: "features", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "tenant.features.update", DisplayName: "Update Features", Description: "Enable/disable tenant features", Module: "tenant", Resource: "features", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},

		// Tenant Feature Catalog Permissions
		{Name: "tenant.feature-catalog.create", DisplayName: "Create Feature Catalog", Description: "Add features to catalog", Module: "tenant", Resource: "feature-catalog", Action: "create", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.feature-catalog.update", DisplayName: "Update Feature Catalog", Description: "Modify feature catalog", Module: "tenant", Resource: "feature-catalog", Action: "update", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.feature-catalog.deprecate", DisplayName: "Deprecate Features", Description: "Mark features as deprecated", Module: "tenant", Resource: "feature-catalog", Action: "deprecate", Scope: "global", RiskLevel: "high", IsSystemPermission: true},

		// Tenant Subscription Permissions
		{Name: "tenant.subscription.create", DisplayName: "Create Subscription", Description: "Create new subscriptions", Module: "tenant", Resource: "subscriptions", Action: "create", Scope: "global", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.subscription.read", DisplayName: "Read Subscription", Description: "View subscription details", Module: "tenant", Resource: "subscriptions", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "tenant.subscription.upgrade", DisplayName: "Upgrade Subscription", Description: "Upgrade subscription plan", Module: "tenant", Resource: "subscriptions", Action: "upgrade", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.subscription.downgrade", DisplayName: "Downgrade Subscription", Description: "Downgrade subscription plan", Module: "tenant", Resource: "subscriptions", Action: "downgrade", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.subscription.cancel", DisplayName: "Cancel Subscription", Description: "Cancel subscription", Module: "tenant", Resource: "subscriptions", Action: "cancel", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "tenant.subscription.payment", DisplayName: "Manage Payments", Description: "Handle payment operations", Module: "tenant", Resource: "subscriptions", Action: "payment", Scope: "tenant", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "tenant.subscription.admin", DisplayName: "Admin Subscriptions", Description: "Perform admin subscription operations", Module: "tenant", Resource: "subscriptions", Action: "admin", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},

		// Onboarding Permissions
		{Name: "onboarding.create", DisplayName: "Create Onboarding Flows", Description: "Create onboarding flows", Module: "onboarding", Resource: "flows", Action: "create", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "onboarding.read", DisplayName: "Read Onboarding Flows", Description: "View onboarding flows", Module: "onboarding", Resource: "flows", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "onboarding.update", DisplayName: "Update Onboarding Flows", Description: "Edit onboarding flows", Module: "onboarding", Resource: "flows", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "onboarding.delete", DisplayName: "Delete Onboarding Flows", Description: "Delete onboarding flows", Module: "onboarding", Resource: "flows", Action: "delete", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "onboarding.manage-steps", DisplayName: "Manage Onboarding Steps", Description: "Configure onboarding steps", Module: "onboarding", Resource: "steps", Action: "manage", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},

		// Blog Module Permissions
		{Name: "blog.posts.create", DisplayName: "Create Posts", Description: "Create blog posts", Module: "blog", Resource: "posts", Action: "create", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.posts.read", DisplayName: "Read Posts", Description: "View blog posts", Module: "blog", Resource: "posts", Action: "read", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.posts.update", DisplayName: "Update Posts", Description: "Edit blog posts", Module: "blog", Resource: "posts", Action: "update", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.posts.delete", DisplayName: "Delete Posts", Description: "Delete blog posts", Module: "blog", Resource: "posts", Action: "delete", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "blog.posts.publish", DisplayName: "Publish Posts", Description: "Publish blog posts", Module: "blog", Resource: "posts", Action: "publish", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "blog.posts.schedule", DisplayName: "Schedule Posts", Description: "Schedule blog posts", Module: "blog", Resource: "posts", Action: "schedule", Scope: "website", RiskLevel: "low", IsSystemPermission: true},

		{Name: "blog.categories.create", DisplayName: "Create Categories", Description: "Create blog categories", Module: "blog", Resource: "categories", Action: "create", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.categories.read", DisplayName: "Read Categories", Description: "View blog categories", Module: "blog", Resource: "categories", Action: "read", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.categories.update", DisplayName: "Update Categories", Description: "Edit blog categories", Module: "blog", Resource: "categories", Action: "update", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.categories.delete", DisplayName: "Delete Categories", Description: "Delete blog categories", Module: "blog", Resource: "categories", Action: "delete", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},

		{Name: "blog.tags.create", DisplayName: "Create Tags", Description: "Create blog tags", Module: "blog", Resource: "tags", Action: "create", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.tags.read", DisplayName: "Read Tags", Description: "View blog tags", Module: "blog", Resource: "tags", Action: "read", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.tags.update", DisplayName: "Update Tags", Description: "Edit blog tags", Module: "blog", Resource: "tags", Action: "update", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "blog.tags.delete", DisplayName: "Delete Tags", Description: "Delete blog tags", Module: "blog", Resource: "tags", Action: "delete", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},

		// Comment Module Permissions
		{Name: "comment.comments.create", DisplayName: "Create Comments", Description: "Create comments", Module: "comment", Resource: "comments", Action: "create", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "comment.comments.read", DisplayName: "Read Comments", Description: "View comments", Module: "comment", Resource: "comments", Action: "read", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "comment.comments.update", DisplayName: "Update Comments", Description: "Edit comments", Module: "comment", Resource: "comments", Action: "update", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "comment.comments.delete", DisplayName: "Delete Comments", Description: "Delete comments", Module: "comment", Resource: "comments", Action: "delete", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "comment.moderation.approve", DisplayName: "Approve Comments", Description: "Approve pending comments", Module: "comment", Resource: "moderation", Action: "approve", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "comment.moderation.reject", DisplayName: "Reject Comments", Description: "Reject comments", Module: "comment", Resource: "moderation", Action: "reject", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "comment.moderation.spam", DisplayName: "Mark as Spam", Description: "Mark comments as spam", Module: "comment", Resource: "moderation", Action: "spam", Scope: "website", RiskLevel: "low", IsSystemPermission: true},

		// Media Module Permissions
		{Name: "media.files.upload", DisplayName: "Upload Files", Description: "Upload media files", Module: "media", Resource: "files", Action: "upload", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "media.files.read", DisplayName: "Read Files", Description: "View media files", Module: "media", Resource: "files", Action: "read", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "media.files.update", DisplayName: "Update Files", Description: "Edit media file metadata", Module: "media", Resource: "files", Action: "update", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "media.files.delete", DisplayName: "Delete Files", Description: "Delete media files", Module: "media", Resource: "files", Action: "delete", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "media.files.download", DisplayName: "Download Files", Description: "Download media files", Module: "media", Resource: "files", Action: "download", Scope: "website", RiskLevel: "low", IsSystemPermission: true},

		{Name: "media.folders.create", DisplayName: "Create Folders", Description: "Create media folders", Module: "media", Resource: "folders", Action: "create", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "media.folders.read", DisplayName: "Read Folders", Description: "View media folders", Module: "media", Resource: "folders", Action: "read", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "media.folders.update", DisplayName: "Update Folders", Description: "Edit media folders", Module: "media", Resource: "folders", Action: "update", Scope: "website", RiskLevel: "low", IsSystemPermission: true},
		{Name: "media.folders.delete", DisplayName: "Delete Folders", Description: "Delete media folders", Module: "media", Resource: "folders", Action: "delete", Scope: "website", RiskLevel: "medium", IsSystemPermission: true},

		// Notification Module Permissions
		{Name: "notification.templates.create", DisplayName: "Create Templates", Description: "Create notification templates", Module: "notification", Resource: "templates", Action: "create", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "notification.templates.read", DisplayName: "Read Templates", Description: "View notification templates", Module: "notification", Resource: "templates", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},
		{Name: "notification.templates.update", DisplayName: "Update Templates", Description: "Edit notification templates", Module: "notification", Resource: "templates", Action: "update", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "notification.templates.delete", DisplayName: "Delete Templates", Description: "Delete notification templates", Module: "notification", Resource: "templates", Action: "delete", Scope: "tenant", RiskLevel: "high", IsSystemPermission: true},
		{Name: "notification.send", DisplayName: "Send Notifications", Description: "Send notifications to users", Module: "notification", Resource: "notifications", Action: "send", Scope: "tenant", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "notification.history.read", DisplayName: "Read History", Description: "View notification history", Module: "notification", Resource: "history", Action: "read", Scope: "tenant", RiskLevel: "low", IsSystemPermission: true},

		// System Administration Permissions
		{Name: "system.maintenance", DisplayName: "System Maintenance", Description: "Perform system maintenance operations", Module: "system", Resource: "maintenance", Action: "manage", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
		{Name: "system.monitoring", DisplayName: "System Monitoring", Description: "Access system monitoring and metrics", Module: "system", Resource: "monitoring", Action: "read", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "system.logs", DisplayName: "System Logs", Description: "Access system logs and audit trails", Module: "system", Resource: "logs", Action: "read", Scope: "global", RiskLevel: "medium", IsSystemPermission: true},
		{Name: "system.configuration", DisplayName: "System Configuration", Description: "Configure system-wide settings", Module: "system", Resource: "configuration", Action: "manage", Scope: "global", RiskLevel: "critical", IsSystemPermission: true},
	}
}
