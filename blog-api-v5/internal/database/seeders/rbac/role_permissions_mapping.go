package rbac

// GetPermissionsForRoleComprehensive returns comprehensive permissions for each role
func GetPermissionsForRoleComprehensive(roleName string) []string {
	switch roleName {
	case "super_admin":
		// Super admin has ALL permissions
		allPerms := GetComprehensivePermissions()
		permNames := make([]string, len(allPerms))
		for i, perm := range allPerms {
			permNames[i] = perm.Name
		}
		return permNames

	case "admin":
		return []string{
			// User management
			"users.create", "users.read", "users.update", "users.delete", "users.list", "users.impersonate",
			"user-profiles.create", "user-profiles.read", "user-profiles.update", "user-profiles.delete",

			// Website management
			"websites.create", "websites.read", "websites.update", "websites.delete", "websites.list", "websites.manage-settings",
			"content.create", "content.read", "content.update", "content.delete", "content.publish",

			// Auth management
			"auth.manage-sessions", "auth.manage-tokens", "auth.manage-providers", "auth.view-logs",

			// RBAC management
			"rbac.roles.create", "rbac.roles.read", "rbac.roles.update", "rbac.roles.delete",
			"rbac.roles.permissions.assign", "rbac.roles.permissions.revoke",
			"rbac.users.read", "rbac.users.roles.assign", "rbac.users.roles.revoke", "rbac.users.roles.update",

			// Tenant management (limited)
			"tenant.read", "tenant.update", "tenant.stats.read",
			"tenant.settings.read", "tenant.settings.update", "tenant.settings.delete",
			"tenant.features.read", "tenant.features.update",
			"tenant.subscription.read", "tenant.subscription.upgrade", "tenant.subscription.downgrade",

			// Blog management
			"blog.posts.create", "blog.posts.read", "blog.posts.update", "blog.posts.delete", "blog.posts.publish", "blog.posts.schedule",
			"blog.categories.create", "blog.categories.read", "blog.categories.update", "blog.categories.delete",
			"blog.tags.create", "blog.tags.read", "blog.tags.update", "blog.tags.delete",

			// Comment management
			"comment.comments.read", "comment.comments.update", "comment.comments.delete",
			"comment.moderation.approve", "comment.moderation.reject", "comment.moderation.spam",

			// Media management
			"media.files.upload", "media.files.read", "media.files.update", "media.files.delete", "media.files.download",
			"media.folders.create", "media.folders.read", "media.folders.update", "media.folders.delete",

			// Notification management
			"notification.templates.create", "notification.templates.read", "notification.templates.update", "notification.templates.delete",
			"notification.send", "notification.history.read",

			// Onboarding
			"onboarding.create", "onboarding.read", "onboarding.update", "onboarding.delete", "onboarding.manage-steps",

			// System (read-only)
			"system.monitoring", "system.logs",
		}

	case "editor":
		return []string{
			// User management (limited)
			"users.read", "users.list",
			"user-profiles.read", "user-profiles.update", // Can update own profile

			// Website management (content focused)
			"websites.read", "websites.list",
			"content.create", "content.read", "content.update", "content.delete", "content.publish",

			// Blog management
			"blog.posts.create", "blog.posts.read", "blog.posts.update", "blog.posts.delete", "blog.posts.publish", "blog.posts.schedule",
			"blog.categories.create", "blog.categories.read", "blog.categories.update",
			"blog.tags.create", "blog.tags.read", "blog.tags.update",

			// Comment management
			"comment.comments.create", "comment.comments.read", "comment.comments.update",
			"comment.moderation.approve", "comment.moderation.reject",

			// Media management
			"media.files.upload", "media.files.read", "media.files.update", "media.files.delete", "media.files.download",
			"media.folders.create", "media.folders.read", "media.folders.update",

			// Basic RBAC
			"rbac.roles.read",
			"rbac.users.read",

			// Tenant (read-only)
			"tenant.read",
			"tenant.settings.read",
			"tenant.features.read",
			"tenant.subscription.read",

			// Notification (read-only)
			"notification.templates.read",
			"notification.history.read",

			// Onboarding
			"onboarding.read",
		}

	case "author":
		return []string{
			// User management (very limited)
			"users.read",                                 // Can read user info
			"user-profiles.read", "user-profiles.update", // Can update own profile

			// Website management (read-only)
			"websites.read",
			"content.read",

			// Blog management (own content)
			"blog.posts.create", "blog.posts.read", "blog.posts.update", // Can't delete or publish
			"blog.categories.read",
			"blog.tags.read",

			// Comment management (limited)
			"comment.comments.create", "comment.comments.read",

			// Media management (limited)
			"media.files.upload", "media.files.read", "media.files.download",
			"media.folders.read",

			// Basic reads
			"rbac.roles.read",
			"tenant.read",
			"tenant.features.read",
			"notification.templates.read",
		}

	case "moderator":
		return []string{
			// User management (limited)
			"users.read", "users.list",
			"user-profiles.read",

			// Website management (content moderation)
			"websites.read", "websites.list",
			"content.read", "content.update", // Can moderate content

			// Blog management (moderation)
			"blog.posts.read", "blog.posts.update", // Can moderate posts
			"blog.categories.read",
			"blog.tags.read",

			// Comment management (full moderation)
			"comment.comments.read", "comment.comments.update", "comment.comments.delete",
			"comment.moderation.approve", "comment.moderation.reject", "comment.moderation.spam",

			// Media management (read-only)
			"media.files.read", "media.files.download",
			"media.folders.read",

			// Basic reads
			"rbac.roles.read",
			"rbac.users.read",
			"tenant.read",
			"tenant.features.read",
			"notification.history.read",
		}

	case "viewer":
		return []string{
			// Basic read permissions only
			"users.read",
			"user-profiles.read",
			"websites.read",
			"content.read",
			"blog.posts.read",
			"blog.categories.read",
			"blog.tags.read",
			"comment.comments.read",
			"media.files.read",
			"media.folders.read",
			"rbac.roles.read",
			"tenant.read",
			"tenant.features.read",
		}

	case "guest":
		return []string{
			// Very limited permissions
			"content.read",
			"blog.posts.read",
			"blog.categories.read",
			"blog.tags.read",
			"comment.comments.read",
		}

	default:
		return []string{}
	}
}
