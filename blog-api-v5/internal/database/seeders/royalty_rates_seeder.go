package seeders

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// RoyaltyRateConfig represents the royalty rate configuration
type RoyaltyRateConfig struct {
	ID            uint      `gorm:"primaryKey"`
	TenantID      uint      `gorm:"not null"`
	WebsiteID     uint      `gorm:"not null"`
	ConfigType    string    `gorm:"type:enum('base_rate','category_multiplier','content_type_multiplier','quality_multiplier','bonus_rate');not null"`
	ConfigKey     string    `gorm:"type:varchar(100);not null"`
	ConfigName    string    `gorm:"type:varchar(255);not null"`
	ConfigValue   float64   `gorm:"type:decimal(10,4);not null"`
	Unit          *string   `gorm:"type:varchar(20)"`
	Description   *string   `gorm:"type:text"`
	EffectiveFrom time.Time `gorm:"type:date;not null"`
	CreatedBy     uint      `gorm:"not null"`
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

func (RoyaltyRateConfig) TableName() string {
	return "royalty_rate_configs"
}

// SeedRoyaltyRates seeds the default royalty rate configurations
func SeedRoyaltyRates(db *gorm.DB, tenantID, websiteID, userID uint) error {
	effectiveFrom, _ := time.Parse("2006-01-02", "2025-01-01")
	
	vndPerWord := "VND/word"
	multiplier := "multiplier"
	
	rates := []RoyaltyRateConfig{
		// Base rates per word
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "base_rate",
			ConfigKey:     "standard",
			ConfigName:    "Đơn giá chuẩn",
			ConfigValue:   100,
			Unit:          &vndPerWord,
			Description:   strPtr("Đơn giá cơ bản cho mỗi từ"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "base_rate",
			ConfigKey:     "premium",
			ConfigName:    "Đơn giá cao cấp",
			ConfigValue:   150,
			Unit:          &vndPerWord,
			Description:   strPtr("Đơn giá cho bài viết chất lượng cao"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		// Category multipliers
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "category_multiplier",
			ConfigKey:     "news",
			ConfigName:    "Tin tức",
			ConfigValue:   1.0,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho tin tức thông thường"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "category_multiplier",
			ConfigKey:     "analysis",
			ConfigName:    "Phân tích",
			ConfigValue:   1.5,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho bài phân tích chuyên sâu"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "category_multiplier",
			ConfigKey:     "investigation",
			ConfigName:    "Điều tra",
			ConfigValue:   2.0,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho bài điều tra"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		// Content type multipliers
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "content_type_multiplier",
			ConfigKey:     "original",
			ConfigName:    "Bài viết gốc",
			ConfigValue:   1.0,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho bài viết gốc"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "content_type_multiplier",
			ConfigKey:     "translation",
			ConfigName:    "Bài dịch",
			ConfigValue:   0.7,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho bài dịch"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "content_type_multiplier",
			ConfigKey:     "compilation",
			ConfigName:    "Tổng hợp",
			ConfigValue:   0.5,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho bài tổng hợp"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		// Quality multipliers
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "quality_multiplier",
			ConfigKey:     "excellent",
			ConfigName:    "Xuất sắc",
			ConfigValue:   1.2,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho chất lượng xuất sắc"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "quality_multiplier",
			ConfigKey:     "good",
			ConfigName:    "Tốt",
			ConfigValue:   1.0,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho chất lượng tốt"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
		{
			TenantID:      tenantID,
			WebsiteID:     websiteID,
			ConfigType:    "quality_multiplier",
			ConfigKey:     "average",
			ConfigName:    "Trung bình",
			ConfigValue:   0.8,
			Unit:          &multiplier,
			Description:   strPtr("Hệ số cho chất lượng trung bình"),
			EffectiveFrom: effectiveFrom,
			CreatedBy:     userID,
		},
	}

	// Insert rates using FirstOrCreate to avoid duplicates
	for _, rate := range rates {
		var existing RoyaltyRateConfig
		err := db.Where(
			"tenant_id = ? AND website_id = ? AND config_type = ? AND config_key = ?",
			rate.TenantID, rate.WebsiteID, rate.ConfigType, rate.ConfigKey,
		).First(&existing).Error
		
		if err == gorm.ErrRecordNotFound {
			if err := db.Create(&rate).Error; err != nil {
				return fmt.Errorf("failed to create royalty rate config: %w", err)
			}
		}
	}

	fmt.Printf("✅ Seeded %d royalty rate configurations\n", len(rates))
	return nil
}

// Helper function to get string pointer
func strPtr(s string) *string {
	return &s
}