-- Seeder: 001_setting_schemas.sql
-- Description: Seed default setting schemas for system configuration
-- Author: System
-- Date: 2025-01-18

-- System-level schemas
INSERT INTO setting_schemas (
    schema_name, version, module_name, group_name, setting_key,
    json_schema, default_value, ui_config, ui_component, ui_label, ui_description,
    ui_order, is_public, is_required, is_system, status
) VALUES

-- System General Settings
('system_general_app_name', '1.0.0', 'system', 'general', 'app_name',
 JSON_OBJECT('type', 'string', 'minLength', 1, 'maxLength', 100),
 JSON_OBJECT('value', 'WN API v3'),
 JSON_OBJECT('width', 'full', 'helpText', 'Application name displayed in UI'),
 'input', 'Application Name', 'The name of your application', 1, true, true, true, 'active'),

('system_general_app_description', '1.0.0', 'system', 'general', 'app_description',
 JSON_OBJECT('type', 'string', 'maxLength', 500),
 JSON_OBJECT('value', 'Multi-tenant blog and CMS platform'),
 JSON_OBJECT('width', 'full', 'rows', 3),
 'textarea', 'Application Description', 'Brief description of your application', 2, true, false, true, 'active'),

('system_general_timezone', '1.0.0', 'system', 'general', 'timezone',
 JSON_OBJECT('type', 'string', 'enum', JSON_ARRAY('UTC', 'America/New_York', 'Europe/London', 'Asia/Tokyo')),
 JSON_OBJECT('value', 'UTC'),
 JSON_OBJECT('width', 'half'),
 'select', 'Default Timezone', 'Default timezone for the application', 3, true, true, true, 'active'),

('system_general_locale', '1.0.0', 'system', 'general', 'locale',
 JSON_OBJECT('type', 'string', 'enum', JSON_ARRAY('en', 'vi', 'ja', 'fr', 'de', 'es')),
 JSON_OBJECT('value', 'en'),
 JSON_OBJECT('width', 'half'),
 'select', 'Default Locale', 'Default language for the application', 4, true, true, true, 'active'),

-- System Security Settings
('system_security_jwt_secret', '1.0.0', 'system', 'security', 'jwt_secret',
 JSON_OBJECT('type', 'string', 'minLength', 32),
 JSON_OBJECT('value', ''),
 JSON_OBJECT('width', 'full', 'helpText', 'JWT secret key for token signing'),
 'input', 'JWT Secret', 'Secret key for JWT token signing', 1, false, true, true, 'active'),

('system_security_session_timeout', '1.0.0', 'system', 'security', 'session_timeout',
 JSON_OBJECT('type', 'integer', 'minimum', 300, 'maximum', 86400),
 JSON_OBJECT('value', 3600),
 JSON_OBJECT('width', 'half', 'suffix', 'seconds'),
 'input', 'Session Timeout', 'Session timeout in seconds', 2, false, true, true, 'active'),

('system_security_password_min_length', '1.0.0', 'system', 'security', 'password_min_length',
 JSON_OBJECT('type', 'integer', 'minimum', 6, 'maximum', 50),
 JSON_OBJECT('value', 8),
 JSON_OBJECT('width', 'half'),
 'input', 'Minimum Password Length', 'Minimum required password length', 3, true, true, true, 'active'),

('system_security_require_2fa', '1.0.0', 'system', 'security', 'require_2fa',
 JSON_OBJECT('type', 'boolean'),
 JSON_OBJECT('value', false),
 JSON_OBJECT('width', 'half'),
 'checkbox', 'Require 2FA', 'Require two-factor authentication for all users', 4, true, false, true, 'active'),

-- Auth Module Settings
('auth_login_max_attempts', '1.0.0', 'auth', 'security', 'max_login_attempts',
 JSON_OBJECT('type', 'integer', 'minimum', 1, 'maximum', 20),
 JSON_OBJECT('value', 5),
 JSON_OBJECT('width', 'half'),
 'input', 'Max Login Attempts', 'Maximum login attempts before lockout', 1, false, true, false, 'active'),

('auth_login_lockout_duration', '1.0.0', 'auth', 'security', 'lockout_duration',
 JSON_OBJECT('type', 'integer', 'minimum', 60, 'maximum', 3600),
 JSON_OBJECT('value', 900),
 JSON_OBJECT('width', 'half', 'suffix', 'seconds'),
 'input', 'Lockout Duration', 'Account lockout duration in seconds', 2, false, true, false, 'active'),

('auth_oauth_enable_google', '1.0.0', 'auth', 'oauth', 'enable_google',
 JSON_OBJECT('type', 'boolean'),
 JSON_OBJECT('value', false),
 JSON_OBJECT('width', 'half'),
 'checkbox', 'Enable Google OAuth', 'Allow users to login with Google', 1, true, false, false, 'active'),

('auth_oauth_enable_github', '1.0.0', 'auth', 'oauth', 'enable_github',
 JSON_OBJECT('type', 'boolean'),
 JSON_OBJECT('value', false),
 JSON_OBJECT('width', 'half'),
 'checkbox', 'Enable GitHub OAuth', 'Allow users to login with GitHub', 2, true, false, false, 'active'),

-- Email Settings
('email_smtp_host', '1.0.0', 'email', 'smtp', 'host',
 JSON_OBJECT('type', 'string', 'minLength', 1),
 JSON_OBJECT('value', 'localhost'),
 JSON_OBJECT('width', 'half'),
 'input', 'SMTP Host', 'SMTP server hostname', 1, false, true, false, 'active'),

('email_smtp_port', '1.0.0', 'email', 'smtp', 'port',
 JSON_OBJECT('type', 'integer', 'minimum', 1, 'maximum', 65535),
 JSON_OBJECT('value', 587),
 JSON_OBJECT('width', 'half'),
 'input', 'SMTP Port', 'SMTP server port', 2, false, true, false, 'active'),

('email_smtp_username', '1.0.0', 'email', 'smtp', 'username',
 JSON_OBJECT('type', 'string'),
 JSON_OBJECT('value', ''),
 JSON_OBJECT('width', 'half'),
 'input', 'SMTP Username', 'SMTP authentication username', 3, false, false, false, 'active'),

('email_smtp_password', '1.0.0', 'email', 'smtp', 'password',
 JSON_OBJECT('type', 'string'),
 JSON_OBJECT('value', ''),
 JSON_OBJECT('width', 'half', 'type', 'password'),
 'input', 'SMTP Password', 'SMTP authentication password', 4, false, false, false, 'active'),

('email_general_from_name', '1.0.0', 'email', 'general', 'from_name',
 JSON_OBJECT('type', 'string', 'minLength', 1, 'maxLength', 100),
 JSON_OBJECT('value', 'WN API v3'),
 JSON_OBJECT('width', 'half'),
 'input', 'From Name', 'Default sender name for emails', 1, true, true, false, 'active'),

('email_general_from_address', '1.0.0', 'email', 'general', 'from_address',
 JSON_OBJECT('type', 'string', 'format', 'email'),
 JSON_OBJECT('value', '<EMAIL>'),
 JSON_OBJECT('width', 'half'),
 'input', 'From Address', 'Default sender email address', 2, true, true, false, 'active'),

-- Media Settings
('media_upload_max_size', '1.0.0', 'media', 'upload', 'max_file_size',
 JSON_OBJECT('type', 'integer', 'minimum', 1048576, 'maximum', 104857600),
 JSON_OBJECT('value', 10485760),
 JSON_OBJECT('width', 'half', 'suffix', 'bytes'),
 'input', 'Max Upload Size', 'Maximum file upload size in bytes', 1, true, true, false, 'active'),

('media_upload_allowed_types', '1.0.0', 'media', 'upload', 'allowed_types',
 JSON_OBJECT('type', 'array', 'items', JSON_OBJECT('type', 'string')),
 JSON_ARRAY('image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'),
 JSON_OBJECT('width', 'full', 'helpText', 'Allowed MIME types for uploads'),
 'select', 'Allowed File Types', 'MIME types allowed for file uploads', 2, true, true, false, 'active'),

('media_storage_driver', '1.0.0', 'media', 'storage', 'driver',
 JSON_OBJECT('type', 'string', 'enum', JSON_ARRAY('local', 's3', 'gcs', 'azure')),
 JSON_OBJECT('value', 'local'),
 JSON_OBJECT('width', 'half'),
 'select', 'Storage Driver', 'File storage driver to use', 1, false, true, false, 'active'),

('media_storage_s3_bucket', '1.0.0', 'media', 'storage', 's3_bucket',
 JSON_OBJECT('type', 'string'),
 JSON_OBJECT('value', ''),
 JSON_OBJECT('width', 'half'),
 'input', 'S3 Bucket', 'AWS S3 bucket name', 2, false, false, false, 'active');