package tenant

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	websiteModels "github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AccountTenantSeeder handles seeding of a complete account tenant setup
type AccountTenantSeeder struct{}

// stringPtr returns a pointer to the provided string
func stringPtr(s string) *string {
	return &s
}

// Seed seeds a complete tenant account with user, tenant, and website data
func (s *AccountTenantSeeder) Seed(db *gorm.DB) error {
	log.Println("Seeding account tenants with full setup...")

	// Helper function to convert map to JSON
	toJSON := func(m map[string]interface{}) tenantModels.JSONMap {
		return tenantModels.JSONMap(m)
	}

	// Get the enterprise plan (highest plan)
	var enterprisePlan tenantModels.TenantPlan
	if err := db.Where("slug = ?", "enterprise").First(&enterprisePlan).Error; err != nil {
		return fmt.Errorf("enterprise plan not found: %w", err)
	}

	// Define the two tenants to create
	tenantConfigs := []struct {
		tenant      tenantModels.Tenant
		userEmail   string
		password    string
		websiteName string
		subdomain   string
	}{
		{
			tenant: tenantModels.Tenant{
				Name:         "Admin Organization",
				Slug:         "admin-org",
				Domain:       "admin.localhost.com",
				PlanID:       enterprisePlan.ID,
				OwnerEmail:   "<EMAIL>",
				BillingEmail: "<EMAIL>",
				SupportEmail: "<EMAIL>",
				Settings: toJSON(map[string]interface{}{
					"timezone":           "Asia/Ho_Chi_Minh",
					"dateFormat":         "DD/MM/YYYY",
					"language":           "vi",
					"theme":              "dark",
					"emailNotifications": true,
					"allowRegistration":  true,
					"maintenanceMode":    false,
				}),
				Metadata: toJSON(map[string]interface{}{
					"industry":    "Technology",
					"companySize": "10-50",
					"country":     "VN",
					"foundedYear": 2024,
					"description": "Admin organization for system management",
					"website":     "https://admin.localhost.com",
					"socialMedia": map[string]string{
						"facebook": "https://facebook.com/admin-org",
						"twitter":  "https://twitter.com/admin_org",
						"linkedin": "https://linkedin.com/company/admin-org",
					},
				}),
				Status:      tenantModels.TenantStatusActive,
				TrialEndsAt: nil, // No trial for enterprise
			},
			userEmail:   "<EMAIL>",
			password:    "Admin@123",
			websiteName: "Admin Blog",
			subdomain:   "admin",
		},
		{
			tenant: tenantModels.Tenant{
				Name:         "Tenant Company",
				Slug:         "tenant-company",
				Domain:       "tenant.localhost.com",
				PlanID:       enterprisePlan.ID,
				OwnerEmail:   "<EMAIL>",
				BillingEmail: "<EMAIL>",
				SupportEmail: "<EMAIL>",
				Settings: toJSON(map[string]interface{}{
					"timezone":           "Asia/Ho_Chi_Minh",
					"dateFormat":         "DD/MM/YYYY",
					"language":           "vi",
					"theme":              "light",
					"emailNotifications": true,
					"allowRegistration":  true,
					"maintenanceMode":    false,
				}),
				Metadata: toJSON(map[string]interface{}{
					"industry":    "E-commerce",
					"companySize": "50-100",
					"country":     "VN",
					"foundedYear": 2024,
					"description": "Tenant company for content management",
					"website":     "https://tenant.localhost.com",
					"socialMedia": map[string]string{
						"facebook": "https://facebook.com/tenant-company",
						"twitter":  "https://twitter.com/tenant_company",
						"linkedin": "https://linkedin.com/company/tenant-company",
					},
				}),
				Status:      tenantModels.TenantStatusActive,
				TrialEndsAt: nil, // No trial for enterprise
			},
			userEmail:   "<EMAIL>",
			password:    "tenant@123",
			websiteName: "Tenant Blog",
			subdomain:   "tenant",
		},
	}

	// Process each tenant configuration
	for _, config := range tenantConfigs {
		// 1. Check if the tenant already exists
		var existingTenant tenantModels.Tenant
		result := db.Where("slug = ?", config.tenant.Slug).First(&existingTenant)
		if result.Error == nil {
			log.Printf("Tenant %s already exists, skipping...", config.tenant.Slug)
			continue
		}

		// 2. Create the tenant
		if err := db.Create(&config.tenant).Error; err != nil {
			return fmt.Errorf("failed to create tenant %s: %w", config.tenant.Name, err)
		}

		log.Printf("✅ Created tenant: %s (ID: %d)", config.tenant.Name, config.tenant.ID)

		// 3. Get or create the user
		var user userModels.User
		result = db.Where("email = ?", config.userEmail).First(&user)
		if result.Error != nil {
			// User doesn't exist, create it
			passwordHash, err := bcrypt.GenerateFromPassword([]byte(config.password), bcrypt.DefaultCost)
			if err != nil {
				return fmt.Errorf("failed to hash password: %w", err)
			}

			username := strings.Split(config.userEmail, "@")[0]
			firstName := strings.Title(username)
			lastName := "User"
			displayName := firstName + " " + lastName

			user = userModels.User{
				Email:           config.userEmail,
				Username:        &username,
				FirstName:       &firstName,
				LastName:        &lastName,
				DisplayName:     &displayName,
				PasswordHash:    string(passwordHash),
				EmailVerified:   true,
				EmailVerifiedAt: func() *time.Time { t := time.Now(); return &t }(),
				Status:          userModels.UserStatusActive,
			}

			if err := db.Create(&user).Error; err != nil {
				return fmt.Errorf("failed to create user %s: %w", config.userEmail, err)
			}
			log.Printf("✅ Created user: %s (ID: %d)", user.Email, user.ID)
		} else {
			log.Printf("User %s already exists (ID: %d)", user.Email, user.ID)
		}

		// 4. Create tenant membership
		tenantMembership := userModels.TenantMembership{
			UserID:    user.ID,
			TenantID:  config.tenant.ID,
			IsPrimary: true,
			Status:    userModels.TenantMembershipStatusActive,
			JoinedAt:  time.Now(),
		}

		var existingMembership userModels.TenantMembership
		result = db.Where("user_id = ? AND tenant_id = ?", user.ID, config.tenant.ID).First(&existingMembership)
		if result.Error != nil {
			if err := db.Create(&tenantMembership).Error; err != nil {
				return fmt.Errorf("failed to create tenant membership: %w", err)
			}
			log.Printf("✅ Created tenant membership for user %d in tenant %d", user.ID, config.tenant.ID)
		}

		// 5. Create website
		website := websiteModels.Website{
			TenantID:    config.tenant.ID,
			Name:        config.websiteName,
			Domain:      stringPtr(config.subdomain + ".localhost.com"),
			Subdomain:   config.subdomain,
			Description: fmt.Sprintf("Main blog website for %s", config.tenant.Name),
			SocialMedia: websiteModels.JSONMap{
				"facebook":  fmt.Sprintf("https://facebook.com/%s", config.tenant.Slug),
				"twitter":   fmt.Sprintf("https://twitter.com/%s", strings.ReplaceAll(config.tenant.Slug, "-", "_")),
				"linkedin":  fmt.Sprintf("https://linkedin.com/company/%s", config.tenant.Slug),
				"instagram": fmt.Sprintf("https://instagram.com/%s", strings.ReplaceAll(config.tenant.Slug, "-", "_")),
				"youtube":   fmt.Sprintf("https://youtube.com/c/%s", config.tenant.Slug),
			},
			Status: websiteModels.WebsiteStatusActive,
		}

		if err := db.Create(&website).Error; err != nil {
			return fmt.Errorf("failed to create website: %w", err)
		}

		log.Printf("✅ Created website: %s (ID: %d)", website.Name, website.ID)

		// 6. Create website settings
		websiteSettings := []websiteModels.WebsiteSetting{
			{
				WebsiteID:    website.ID,
				TenantID:     config.tenant.ID,
				SettingKey:   "site_title",
				SettingName:  "Site Title",
				Category:     "general",
				SettingValue: config.websiteName,
				DataType:     "string",
				Description:  "Main title of the website",
			},
			{
				WebsiteID:    website.ID,
				TenantID:     config.tenant.ID,
				SettingKey:   "site_tagline",
				SettingName:  "Site Tagline",
				Category:     "general",
				SettingValue: fmt.Sprintf("Welcome to %s", config.tenant.Name),
				DataType:     "string",
				Description:  "Tagline displayed under the site title",
			},
			{
				WebsiteID:    website.ID,
				TenantID:     config.tenant.ID,
				SettingKey:   "posts_per_page",
				SettingName:  "Posts Per Page",
				Category:     "general",
				SettingValue: "10",
				DataType:     "number",
				Description:  "Number of posts to display per page",
			},
			{
				WebsiteID:    website.ID,
				TenantID:     config.tenant.ID,
				SettingKey:   "theme_color",
				SettingName:  "Theme Color",
				Category:     "theme",
				SettingValue: "#3B82F6",
				DataType:     "string",
				Description:  "Primary theme color",
			},
			{
				WebsiteID:    website.ID,
				TenantID:     config.tenant.ID,
				SettingKey:   "enable_comments",
				SettingName:  "Enable Comments",
				Category:     "comments",
				SettingValue: "true",
				DataType:     "boolean",
				Description:  "Allow comments on blog posts",
			},
		}

		for _, setting := range websiteSettings {
			var existingSetting websiteModels.WebsiteSetting
			result := db.Where("website_id = ? AND category = ? AND setting_key = ?",
				setting.WebsiteID, setting.Category, setting.SettingKey).First(&existingSetting)

			if result.Error != nil {
				if err := db.Create(&setting).Error; err != nil {
					return fmt.Errorf("failed to create website setting %s.%s: %w",
						setting.Category, setting.SettingKey, err)
				}
			}
		}

		// 7. Create tenant settings
		tenantSettings := []tenantModels.TenantSetting{
			{
				TenantID:    config.tenant.ID,
				Category:    "general",
				Key:         "organization_name",
				Value:       json.RawMessage(fmt.Sprintf(`"%s"`, config.tenant.Name)),
				DataType:    "string",
				IsPublic:    true,
				Description: "Organization display name",
			},
			{
				TenantID:    config.tenant.ID,
				Category:    "general",
				Key:         "contact_email",
				Value:       json.RawMessage(fmt.Sprintf(`"%s"`, config.tenant.SupportEmail)),
				DataType:    "string",
				IsPublic:    true,
				Description: "Primary contact email",
			},
			{
				TenantID:    config.tenant.ID,
				Category:    "general",
				Key:         "timezone",
				Value:       json.RawMessage(`"Asia/Ho_Chi_Minh"`),
				DataType:    "string",
				IsPublic:    false,
				Description: "Default timezone for the organization",
			},
		}

		for _, setting := range tenantSettings {
			var existingSetting tenantModels.TenantSetting
			result := db.Where("tenant_id = ? AND category = ? AND `key` = ?",
				setting.TenantID, setting.Category, setting.Key).First(&existingSetting)

			if result.Error != nil {
				if err := db.Create(&setting).Error; err != nil {
					return fmt.Errorf("failed to create tenant setting %s.%s: %w",
						setting.Category, setting.Key, err)
				}
			}
		}

		// 8. Mark onboarding as completed
		now := time.Now()
		onboardingProgress := map[string]interface{}{
			"user_id":      user.ID,
			"status":       "completed",
			"step":         "completed",
			"started_at":   now,
			"completed_at": now,
			"metadata":     `{"seeded": true, "tenant_id": ` + fmt.Sprintf("%d", config.tenant.ID) + `, "website_id": ` + fmt.Sprintf("%d", website.ID) + `}`,
			"created_at":   now,
			"updated_at":   now,
		}

		if err := db.Table("onboarding_progress").Create(onboardingProgress).Error; err != nil {
			return fmt.Errorf("failed to create onboarding progress: %w", err)
		}

		log.Printf("✅ Marked onboarding as completed for user %d", user.ID)

		log.Printf("🎉 Completed setup for %s:", config.tenant.Name)
		log.Printf("   Tenant: %s (slug: %s)", config.tenant.Name, config.tenant.Slug)
		log.Printf("   User: %s (password: %s)", config.userEmail, config.password)
		log.Printf("   Website: %s (domain: %s.localhost.com)", website.Name, config.subdomain)
		log.Printf("   Plan: Enterprise (highest plan)")
	}

	log.Println("🎉 Account tenant seeding completed successfully!")
	return nil
}
