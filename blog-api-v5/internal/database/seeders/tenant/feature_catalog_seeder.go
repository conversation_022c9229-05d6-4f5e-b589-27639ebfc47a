package tenant

import (
	"encoding/json"
	"gorm.io/gorm"
	"time"
)

// FeatureCatalog represents a feature in the catalog
type FeatureCatalog struct {
	ID                   uint   `gorm:"primaryKey"`
	FeatureKey           string `gorm:"uniqueIndex;not null"`
	FeatureName          string `gorm:"not null"`
	Description          string
	Category             string
	IsBeta               bool   `gorm:"default:false"`
	IsExperimental       bool   `gorm:"default:false"`
	IsDeprecated         bool   `gorm:"default:false"`
	RequiredPlans        string `gorm:"type:json"`
	ExcludedPlans        string `gorm:"type:json"`
	DefaultEnabled       bool   `gorm:"default:false"`
	DefaultConfiguration string `gorm:"type:json"`
	DocumentationURL     string
	ChangelogURL         string
	Status               string `gorm:"default:'active'"`
	CreatedAt            time.Time
	UpdatedAt            time.Time
}

// TableName sets the table name for FeatureCatalog
func (FeatureCatalog) TableName() string {
	return "feature_catalog"
}

// FeatureCatalogSeeder handles seeding of feature catalog
type FeatureCatalogSeeder struct{}

// Seed creates default features in the catalog
func (s *FeatureCatalogSeeder) Seed(db *gorm.DB) error {
	// Helper functions
	toJSON := func(m map[string]interface{}) string {
		b, _ := json.Marshal(m)
		return string(b)
	}

	toJSONArray := func(arr []string) string {
		if arr == nil || len(arr) == 0 {
			return "[]"
		}
		b, _ := json.Marshal(arr)
		return string(b)
	}

	features := []FeatureCatalog{
		// Blog features
		{
			FeatureKey:     "blog_comments",
			FeatureName:    "Blog Comments",
			Description:    "Enable commenting system on blog posts",
			Category:       "blog",
			DefaultEnabled: true,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"moderation":     "auto",
				"guest_comments": false,
			}),
			RequiredPlans: toJSONArray([]string{}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:     "blog_reactions",
			FeatureName:    "Blog Reactions",
			Description:    "Enable reactions (like, love, etc.) on blog posts",
			Category:       "blog",
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"types": []string{"like", "love", "insightful"},
			}),
			RequiredPlans: toJSONArray([]string{}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:           "blog_drafts",
			FeatureName:          "Draft Posts",
			Description:          "Allow saving posts as drafts before publishing",
			Category:             "blog",
			DefaultEnabled:       true,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},
		{
			FeatureKey:           "blog_scheduling",
			FeatureName:          "Post Scheduling",
			Description:          "Schedule posts to be published in the future",
			Category:             "blog",
			DefaultEnabled:       true,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},
		{
			FeatureKey:     "blog_revisions",
			FeatureName:    "Post Revisions",
			Description:    "Track and restore previous versions of posts",
			Category:       "blog",
			DefaultEnabled: true,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"max_revisions": 10,
			}),
			RequiredPlans: toJSONArray([]string{}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},

		// Content features
		{
			FeatureKey:     "markdown_editor",
			FeatureName:    "Markdown Editor",
			Description:    "Enable markdown editing for content",
			Category:       "content",
			DefaultEnabled: true,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"toolbar": true,
				"preview": true,
			}),
			RequiredPlans: toJSONArray([]string{}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:     "rich_text_editor",
			FeatureName:    "Rich Text Editor",
			Description:    "Enable WYSIWYG rich text editing",
			Category:       "content",
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"plugins": []string{"image", "link", "table"},
			}),
			RequiredPlans: toJSONArray([]string{}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:     "ai_content_assistant",
			FeatureName:    "AI Content Assistant",
			Description:    "AI-powered content suggestions and improvements",
			Category:       "content",
			IsBeta:         true,
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"provider": "openai",
				"model":    "gpt-3.5-turbo",
			}),
			RequiredPlans: toJSONArray([]string{"professional", "enterprise"}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},

		// Media features
		{
			FeatureKey:     "image_optimization",
			FeatureName:    "Image Optimization",
			Description:    "Automatically optimize uploaded images",
			Category:       "media",
			DefaultEnabled: true,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"quality": 85,
				"formats": []string{"webp", "jpg"},
			}),
			RequiredPlans: toJSONArray([]string{}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:     "video_upload",
			FeatureName:    "Video Upload",
			Description:    "Allow video file uploads",
			Category:       "media",
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"max_size_mb": 500,
				"formats":     []string{"mp4", "webm"},
			}),
			RequiredPlans: toJSONArray([]string{"professional", "enterprise"}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:     "cdn_integration",
			FeatureName:    "CDN Integration",
			Description:    "Serve media files through CDN",
			Category:       "media",
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"provider": "cloudflare",
			}),
			RequiredPlans: toJSONArray([]string{"starter", "professional", "enterprise"}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},

		// SEO features
		{
			FeatureKey:           "auto_sitemap",
			FeatureName:          "Automatic Sitemap",
			Description:          "Generate sitemap.xml automatically",
			Category:             "seo",
			DefaultEnabled:       true,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},
		{
			FeatureKey:     "schema_markup",
			FeatureName:    "Schema Markup",
			Description:    "Add structured data markup to pages",
			Category:       "seo",
			DefaultEnabled: true,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"types": []string{"Article", "BlogPosting"},
			}),
			RequiredPlans: toJSONArray([]string{}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:           "social_previews",
			FeatureName:          "Social Media Previews",
			Description:          "Generate Open Graph and Twitter cards",
			Category:             "seo",
			DefaultEnabled:       true,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},

		// Analytics features
		{
			FeatureKey:           "basic_analytics",
			FeatureName:          "Basic Analytics",
			Description:          "Track page views and visitor statistics",
			Category:             "analytics",
			DefaultEnabled:       true,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},
		{
			FeatureKey:     "advanced_analytics",
			FeatureName:    "Advanced Analytics",
			Description:    "Detailed analytics with user behavior tracking",
			Category:       "analytics",
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"heatmaps":          true,
				"session_recording": false,
			}),
			RequiredPlans: toJSONArray([]string{"professional", "enterprise"}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:           "custom_events",
			FeatureName:          "Custom Event Tracking",
			Description:          "Track custom events and conversions",
			Category:             "analytics",
			DefaultEnabled:       false,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{"professional", "enterprise"}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},

		// Security features
		{
			FeatureKey:     "two_factor_auth",
			FeatureName:    "Two-Factor Authentication",
			Description:    "Enable 2FA for user accounts",
			Category:       "security",
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"methods": []string{"totp", "sms"},
			}),
			RequiredPlans: toJSONArray([]string{}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:     "sso_integration",
			FeatureName:    "Single Sign-On",
			Description:    "Enable SSO integration",
			Category:       "security",
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"providers": []string{"saml", "oauth2"},
			}),
			RequiredPlans: toJSONArray([]string{"enterprise"}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:           "ip_whitelist",
			FeatureName:          "IP Whitelisting",
			Description:          "Restrict access by IP address",
			Category:             "security",
			DefaultEnabled:       false,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{"professional", "enterprise"}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},

		// API features
		{
			FeatureKey:           "graphql_api",
			FeatureName:          "GraphQL API",
			Description:          "Enable GraphQL API endpoint",
			Category:             "api",
			IsBeta:               true,
			DefaultEnabled:       false,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{"professional", "enterprise"}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},
		{
			FeatureKey:     "webhook_integration",
			FeatureName:    "Webhooks",
			Description:    "Send webhooks for events",
			Category:       "api",
			DefaultEnabled: false,
			DefaultConfiguration: toJSON(map[string]interface{}{
				"retry_attempts": 3,
			}),
			RequiredPlans: toJSONArray([]string{"starter", "professional", "enterprise"}),
			ExcludedPlans: toJSONArray([]string{}),
			Status:        "active",
		},
		{
			FeatureKey:           "api_versioning",
			FeatureName:          "API Versioning",
			Description:          "Support multiple API versions",
			Category:             "api",
			DefaultEnabled:       true,
			DefaultConfiguration: toJSON(map[string]interface{}{}),
			RequiredPlans:        toJSONArray([]string{}),
			ExcludedPlans:        toJSONArray([]string{}),
			Status:               "active",
		},
	}

	for _, feature := range features {
		var existing FeatureCatalog
		if err := db.Where("feature_key = ?", feature.FeatureKey).First(&existing).Error; err == nil {
			// Update existing feature
			db.Model(&existing).Updates(feature)
		} else {
			// Create new feature
			if err := db.Create(&feature).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// Rollback removes seeded features
func (s *FeatureCatalogSeeder) Rollback(db *gorm.DB) error {
	// Remove all features from our seed list
	return db.Where("1 = 1").Delete(&FeatureCatalog{}).Error
}

// Name returns the name of this seeder
func (s *FeatureCatalogSeeder) Name() string {
	return "feature_catalog"
}

// Dependencies returns the dependencies for this seeder
func (s *FeatureCatalogSeeder) Dependencies() []string {
	return []string{"tenant_plans"} // Depends on plans for plan-based features
}
