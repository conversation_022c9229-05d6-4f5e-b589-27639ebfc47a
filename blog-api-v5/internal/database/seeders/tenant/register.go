package tenant

import (
	"context"
	"database/sql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// TenantModuleSeeder wraps all tenant seeders
type TenantModuleSeeder struct {
	db             *sql.DB
	planSeeder     *TenantPlanSeeder
	catalogSeeder  *FeatureCatalogSeeder
	tenantSeeder   *TenantSeeder
	settingsSeeder *TenantSettingsSeeder
	featuresSeeder *TenantFeaturesSeeder
	accountSeeder  *AccountTenantSeeder
}

// NewTenantModuleSeeder creates a new tenant module seeder
func NewTenantModuleSeeder(db *sql.DB) *TenantModuleSeeder {
	return &TenantModuleSeeder{
		db:             db,
		planSeeder:     &TenantPlanSeeder{},
		catalogSeeder:  &FeatureCatalogSeeder{},
		tenantSeeder:   &TenantSeeder{},
		settingsSeeder: &TenantSettingsSeeder{},
		featuresSeeder: &TenantFeaturesSeeder{},
		accountSeeder:  &AccountTenantSeeder{},
	}
}

// Name returns the name of this seeder
func (s *TenantModuleSeeder) Name() string {
	return "tenant_module"
}

// Seed runs all tenant seeders in the correct order
func (s *TenantModuleSeeder) Seed(ctx context.Context) error {
	// Convert sql.DB to gorm.DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: s.db,
	}), &gorm.Config{})
	if err != nil {
		return err
	}

	// Run seeders in dependency order
	seeders := []interface {
		Seed(*gorm.DB) error
	}{
		s.planSeeder,     // Plans first
		s.catalogSeeder,  // Feature catalog
		s.tenantSeeder,   // Tenants (depends on plans)
		s.settingsSeeder, // Settings (depends on tenants)
		s.featuresSeeder, // Features (depends on tenants and catalog)
		s.accountSeeder,  // Account tenant (depends on plans and websites)
	}

	for _, seeder := range seeders {
		if err := seeder.Seed(gormDB); err != nil {
			return err
		}
	}

	return nil
}

// GetAllSeeders returns all individual tenant seeders for registration
func GetAllSeeders() map[string]interface{} {
	return map[string]interface{}{
		"tenant_plans":    &TenantPlanSeeder{},
		"feature_catalog": &FeatureCatalogSeeder{},
		"tenants":         &TenantSeeder{},
		"tenant_settings": &TenantSettingsSeeder{},
		"tenant_features": &TenantFeaturesSeeder{},
		"account_tenant":  &AccountTenantSeeder{},
	}
}
