package tenant

import (
	"encoding/json"
	"gorm.io/gorm"
	"time"
)

// TenantFeatures represents enabled features for a tenant
type TenantFeatures struct {
	ID                uint   `gorm:"primaryKey"`
	TenantID          uint   `gorm:"not null;uniqueIndex:idx_tenant_feature"`
	FeatureKey        string `gorm:"not null;uniqueIndex:idx_tenant_feature"`
	FeatureName       string `gorm:"not null"`
	Enabled           bool   `gorm:"default:true"`
	Configuration     string `gorm:"type:json"`
	RolloutPercentage int    `gorm:"default:100"`
	RolloutGroups     string `gorm:"type:json"`
	AvailableFrom     *time.Time
	AvailableUntil    *time.Time
	RequiresFeatures  string `gorm:"type:json"`
	ConflictsWith     string `gorm:"type:json"`
	CreatedAt         time.Time
	UpdatedAt         time.Time
}

// TableName sets the table name for TenantFeatures
func (TenantFeatures) TableName() string {
	return "tenant_features"
}

// TenantFeaturesSeeder handles seeding of tenant features
type TenantFeaturesSeeder struct{}

// Seed creates tenant features based on their plans
func (s *TenantFeaturesSeeder) Seed(db *gorm.DB) error {
	// Helper function to convert value to JSON string
	toJSON := func(v interface{}) string {
		b, _ := json.Marshal(v)
		return string(b)
	}

	// Helper function to get feature name from key
	getFeatureName := func(key string) string {
		nameMap := map[string]string{
			"blog_posts":            "Blog Posts",
			"basic_analytics":       "Basic Analytics",
			"advanced_analytics":    "Advanced Analytics",
			"enterprise_analytics":  "Enterprise Analytics",
			"comments":              "Comments System",
			"custom_domain":         "Custom Domain",
			"email_campaigns":       "Email Campaigns",
			"seo_tools":             "SEO Tools",
			"a_b_testing":           "A/B Testing",
			"multi_language":        "Multi-Language Support",
			"ai_content_generation": "AI Content Generation",
			"priority_support":      "Priority Support",
			"api_access":            "API Access",
			"white_labeling":        "White Labeling",
			"custom_integrations":   "Custom Integrations",
			"content_translation":   "Content Translation",
		}
		if name, exists := nameMap[key]; exists {
			return name
		}
		return key // fallback to key if not found
	}

	// Get all tenants with their plans
	var tenants []struct {
		ID       uint
		Slug     string
		PlanSlug string `gorm:"column:plan_slug"`
	}

	err := db.Table("tenants").
		Select("tenants.id, tenants.slug, tenant_plans.slug as plan_slug").
		Joins("JOIN tenant_plans ON tenants.plan_id = tenant_plans.id").
		Scan(&tenants).Error

	if err != nil {
		return err
	}

	// Define features by plan
	planFeatures := map[string][]struct {
		Key           string
		Configuration map[string]interface{}
		Rollout       int
	}{
		"free": {
			{Key: "blog_posts", Configuration: map[string]interface{}{"max_posts": 50}},
			{Key: "basic_analytics", Configuration: map[string]interface{}{"retention_days": 30}},
			{Key: "comments", Configuration: map[string]interface{}{"moderation": true}},
		},
		"starter": {
			{Key: "blog_posts", Configuration: map[string]interface{}{"max_posts": 500}},
			{Key: "basic_analytics", Configuration: map[string]interface{}{"retention_days": 90}},
			{Key: "comments", Configuration: map[string]interface{}{"moderation": true}},
			{Key: "custom_domain", Configuration: map[string]interface{}{}},
			{Key: "email_campaigns", Configuration: map[string]interface{}{"max_campaigns_per_month": 10}},
			{Key: "seo_tools", Configuration: map[string]interface{}{"basic": true}},
		},
		"professional": {
			{Key: "blog_posts", Configuration: map[string]interface{}{"max_posts": -1}}, // unlimited
			{Key: "advanced_analytics", Configuration: map[string]interface{}{"retention_days": 365}},
			{Key: "comments", Configuration: map[string]interface{}{"moderation": false, "spam_filter": true}},
			{Key: "custom_domain", Configuration: map[string]interface{}{"multiple": true}},
			{Key: "email_campaigns", Configuration: map[string]interface{}{"max_campaigns_per_month": -1}},
			{Key: "seo_tools", Configuration: map[string]interface{}{"advanced": true}},
			{Key: "api_access", Configuration: map[string]interface{}{"rate_limit": 1000}},
			{Key: "team_collaboration", Configuration: map[string]interface{}{"max_members": 20}},
			{Key: "content_scheduling", Configuration: map[string]interface{}{}},
			{Key: "a_b_testing", Configuration: map[string]interface{}{"max_tests": 10}, Rollout: 50}, // Beta feature
		},
		"enterprise": {
			{Key: "blog_posts", Configuration: map[string]interface{}{"max_posts": -1}},
			{Key: "enterprise_analytics", Configuration: map[string]interface{}{"retention_days": -1, "custom_reports": true}},
			{Key: "comments", Configuration: map[string]interface{}{"moderation": false, "spam_filter": true, "ai_moderation": true}},
			{Key: "custom_domain", Configuration: map[string]interface{}{"multiple": true, "wildcard": true}},
			{Key: "email_campaigns", Configuration: map[string]interface{}{"max_campaigns_per_month": -1, "automation": true}},
			{Key: "seo_tools", Configuration: map[string]interface{}{"enterprise": true}},
			{Key: "api_access", Configuration: map[string]interface{}{"rate_limit": -1}},
			{Key: "team_collaboration", Configuration: map[string]interface{}{"max_members": -1}},
			{Key: "content_scheduling", Configuration: map[string]interface{}{"advanced": true}},
			{Key: "a_b_testing", Configuration: map[string]interface{}{"max_tests": -1}},
			{Key: "white_label", Configuration: map[string]interface{}{}},
			{Key: "priority_support", Configuration: map[string]interface{}{"sla": "4h"}},
			{Key: "custom_integrations", Configuration: map[string]interface{}{}},
			{Key: "ai_content_generation", Configuration: map[string]interface{}{"monthly_credits": 10000}, Rollout: 20}, // Experimental
		},
	}

	// Create features for each tenant based on their plan
	for _, tenant := range tenants {
		features, exists := planFeatures[tenant.PlanSlug]
		if !exists {
			continue
		}

		for _, feature := range features {
			tenantFeature := TenantFeatures{
				TenantID:          tenant.ID,
				FeatureKey:        feature.Key,
				FeatureName:       getFeatureName(feature.Key),
				Enabled:           true,
				Configuration:     toJSON(feature.Configuration),
				RolloutPercentage: 100,
				RolloutGroups:     "{}", // Default to empty JSON object
				RequiresFeatures:  "{}", // Default to empty JSON object
				ConflictsWith:     "{}", // Default to empty JSON object
			}

			// Set rollout percentage if specified
			if feature.Rollout > 0 {
				tenantFeature.RolloutPercentage = feature.Rollout
			}

			// Add some feature dependencies
			switch feature.Key {
			case "advanced_analytics":
				tenantFeature.ConflictsWith = toJSON([]string{"basic_analytics"})
			case "enterprise_analytics":
				tenantFeature.ConflictsWith = toJSON([]string{"basic_analytics", "advanced_analytics"})
			case "a_b_testing":
				tenantFeature.RequiresFeatures = toJSON([]string{"advanced_analytics"})
			case "ai_content_generation":
				tenantFeature.RequiresFeatures = toJSON([]string{"enterprise_analytics"})
			}

			// Add availability dates for experimental features
			if feature.Key == "ai_content_generation" {
				availableFrom := time.Now()
				availableUntil := time.Now().AddDate(0, 6, 0) // 6 months trial
				tenantFeature.AvailableFrom = &availableFrom
				tenantFeature.AvailableUntil = &availableUntil
			}

			// Check if feature already exists
			var existing TenantFeatures
			if err := db.Where("tenant_id = ? AND feature_key = ?",
				tenantFeature.TenantID, tenantFeature.FeatureKey).First(&existing).Error; err == nil {
				// Update existing feature
				db.Model(&existing).Updates(tenantFeature)
			} else {
				// Create new feature
				if err := db.Create(&tenantFeature).Error; err != nil {
					return err
				}
			}
		}

		// Add tenant-specific features
		if tenant.Slug == "global-media" {
			// Add special features for Global Media
			specialFeatures := []TenantFeatures{
				{
					TenantID:          tenant.ID,
					FeatureKey:        "multi_language",
					FeatureName:       getFeatureName("multi_language"),
					Enabled:           true,
					Configuration:     toJSON(map[string]interface{}{"languages": []string{"en", "ja", "zh", "ko"}}),
					RolloutPercentage: 100,
					RolloutGroups:     "{}",
					RequiresFeatures:  "{}",
					ConflictsWith:     "{}",
				},
				{
					TenantID:          tenant.ID,
					FeatureKey:        "content_translation",
					FeatureName:       getFeatureName("content_translation"),
					Enabled:           true,
					Configuration:     toJSON(map[string]interface{}{"auto_translate": true}),
					RolloutPercentage: 100,
					RolloutGroups:     "{}",
					RequiresFeatures:  "{}",
					ConflictsWith:     "{}",
				},
			}

			for _, feature := range specialFeatures {
				var existing TenantFeatures
				if err := db.Where("tenant_id = ? AND feature_key = ?",
					feature.TenantID, feature.FeatureKey).First(&existing).Error; err == nil {
					db.Model(&existing).Updates(feature)
				} else {
					if err := db.Create(&feature).Error; err != nil {
						return err
					}
				}
			}
		}
	}

	return nil
}

// Rollback removes seeded tenant features
func (s *TenantFeaturesSeeder) Rollback(db *gorm.DB) error {
	// Get tenant IDs for our test tenants
	var tenantIDs []uint
	slugs := []string{"demo-company", "acme-corp", "techstart", "global-media", "startup-hub"}

	if err := db.Model(&Tenant{}).Where("slug IN ?", slugs).Pluck("id", &tenantIDs).Error; err != nil {
		return err
	}

	// Delete features for these tenants
	return db.Where("tenant_id IN ?", tenantIDs).Delete(&TenantFeatures{}).Error
}

// Name returns the name of this seeder
func (s *TenantFeaturesSeeder) Name() string {
	return "tenant_features"
}

// Dependencies returns the dependencies for this seeder
func (s *TenantFeaturesSeeder) Dependencies() []string {
	return []string{"tenants", "feature_catalog"}
}
