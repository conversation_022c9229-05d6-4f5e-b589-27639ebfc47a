package tenant

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// Tenant represents a tenant/organization
type Tenant struct {
	ID           uint   `gorm:"primaryKey"`
	Name         string `gorm:"not null"`
	Slug         string `gorm:"uniqueIndex;not null"`
	Domain       string `gorm:"uniqueIndex;not null"`
	PlanID       uint   `gorm:"not null"`
	OwnerEmail   string `gorm:"not null"`
	BillingEmail string
	SupportEmail string
	Settings     string `gorm:"type:json"`
	Metadata     string `gorm:"type:json"`
	TrialEndsAt  *time.Time
	Status       string `gorm:"default:'trial'"`
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

// TableName sets the table name for Tenant
func (Tenant) TableName() string {
	return "tenants"
}

// TenantSeeder handles seeding of tenants
type TenantSeeder struct{}

// Seed creates sample tenants
func (s *TenantSeeder) Seed(db *gorm.DB) error {
	// Helper function to convert map to JSON string
	toJSON := func(m map[string]interface{}) string {
		b, _ := json.Marshal(m)
		return string(b)
	}

	// Calculate trial end date (30 days from now)
	trialEndDate := time.Now().AddDate(0, 0, 30)

	// First, get the plan IDs
	var freePlan, starterPlan, proPlan, enterprisePlan TenantPlan
	db.Where("slug = ?", "free").First(&freePlan)
	db.Where("slug = ?", "starter").First(&starterPlan)
	db.Where("slug = ?", "professional").First(&proPlan)
	db.Where("slug = ?", "enterprise").First(&enterprisePlan)

	tenants := []Tenant{
		{
			Name:         "Webnew.vn",
			Slug:         "webnew",
			Domain:       "http://localhost:9078",
			PlanID:       enterprisePlan.ID,
			OwnerEmail:   "<EMAIL>",
			BillingEmail: "<EMAIL>",
			SupportEmail: "<EMAIL>",
			Settings: toJSON(map[string]interface{}{
				"timezone":           "America/New_York",
				"dateFormat":         "MM/DD/YYYY",
				"language":           "en",
				"theme":              "light",
				"emailNotifications": true,
			}),
			Metadata: toJSON(map[string]interface{}{
				"industry":    "Technology",
				"companySize": "1-10",
				"country":     "US",
			}),
			Status: "active",
		},
		{
			Name:         "Acme Corporation",
			Slug:         "acme-corp",
			Domain:       "acme",
			PlanID:       starterPlan.ID,
			OwnerEmail:   "<EMAIL>",
			BillingEmail: "<EMAIL>",
			SupportEmail: "<EMAIL>",
			Settings: toJSON(map[string]interface{}{
				"timezone":           "America/Los_Angeles",
				"dateFormat":         "DD/MM/YYYY",
				"language":           "en",
				"theme":              "dark",
				"emailNotifications": true,
			}),
			Metadata: toJSON(map[string]interface{}{
				"industry":    "E-commerce",
				"companySize": "11-50",
				"country":     "US",
			}),
			Status: "active",
		},
		{
			Name:         "TechStart Inc",
			Slug:         "techstart",
			Domain:       "techstart",
			PlanID:       proPlan.ID,
			OwnerEmail:   "<EMAIL>",
			BillingEmail: "<EMAIL>",
			SupportEmail: "<EMAIL>",
			Settings: toJSON(map[string]interface{}{
				"timezone":           "Europe/London",
				"dateFormat":         "DD/MM/YYYY",
				"language":           "en",
				"theme":              "light",
				"emailNotifications": true,
			}),
			Metadata: toJSON(map[string]interface{}{
				"industry":    "SaaS",
				"companySize": "51-200",
				"country":     "UK",
			}),
			Status: "active",
		},
		{
			Name:         "Global Media Group",
			Slug:         "global-media",
			Domain:       "globalmedia",
			PlanID:       enterprisePlan.ID,
			OwnerEmail:   "<EMAIL>",
			BillingEmail: "<EMAIL>",
			SupportEmail: "<EMAIL>",
			Settings: toJSON(map[string]interface{}{
				"timezone":           "Asia/Tokyo",
				"dateFormat":         "YYYY/MM/DD",
				"language":           "en",
				"theme":              "auto",
				"emailNotifications": true,
			}),
			Metadata: toJSON(map[string]interface{}{
				"industry":    "Media & Publishing",
				"companySize": "1000+",
				"country":     "JP",
			}),
			Status: "active",
		},
		{
			Name:         "Startup Hub",
			Slug:         "startup-hub",
			Domain:       "startuphub",
			PlanID:       freePlan.ID,
			OwnerEmail:   "<EMAIL>",
			BillingEmail: "<EMAIL>",
			SupportEmail: "<EMAIL>",
			Settings: toJSON(map[string]interface{}{
				"timezone":           "America/Chicago",
				"dateFormat":         "MM/DD/YYYY",
				"language":           "en",
				"theme":              "light",
				"emailNotifications": false,
			}),
			Metadata: toJSON(map[string]interface{}{
				"industry":    "Technology",
				"companySize": "1-10",
				"country":     "US",
			}),
			TrialEndsAt: &trialEndDate,
			Status:      "trial",
		},
	}

	for _, tenant := range tenants {
		var existing Tenant
		if err := db.Where("slug = ?", tenant.Slug).First(&existing).Error; err == nil {
			// Update existing tenant
			db.Model(&existing).Updates(tenant)
		} else {
			// Create new tenant
			if err := db.Create(&tenant).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

// Rollback removes seeded tenants
func (s *TenantSeeder) Rollback(db *gorm.DB) error {
	slugs := []string{"webnew", "acme-corp", "techstart", "global-media", "startup-hub"}
	return db.Where("slug IN ?", slugs).Delete(&Tenant{}).Error
}

// Name returns the name of this seeder
func (s *TenantSeeder) Name() string {
	return "tenants"
}

// Dependencies returns the dependencies for this seeder
func (s *TenantSeeder) Dependencies() []string {
	return []string{"tenant_plans"}
}
