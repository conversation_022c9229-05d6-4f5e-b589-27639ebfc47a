package tenant

import (
	"encoding/json"
	"gorm.io/gorm"
	"time"
)

// TenantSettings represents tenant-specific settings
type TenantSettings struct {
	ID              uint   `gorm:"primaryKey"`
	TenantID        uint   `gorm:"not null;index:idx_tenant_key,unique"`
	Category        string `gorm:"not null;index:idx_tenant_key,unique"`
	Key             string `gorm:"not null;index:idx_tenant_key,unique"`
	Value           string `gorm:"type:json"`
	DataType        string `gorm:"default:'string'"`
	Description     string
	ValidationRules string `gorm:"type:json"`
	DefaultValue    string `gorm:"type:json"`
	IsEncrypted     bool   `gorm:"default:false"`
	IsPublic        bool   `gorm:"default:false"`
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

// TableName sets the table name for TenantSettings
func (TenantSettings) TableName() string {
	return "tenant_settings"
}

// TenantSettingsSeeder handles seeding of tenant settings
type TenantSettingsSeeder struct{}

// Seed creates sample tenant settings
func (s *TenantSettingsSeeder) Seed(db *gorm.DB) error {
	// Helper function to convert value to JSON string
	toJSON := func(v interface{}) string {
		b, _ := json.Marshal(v)
		return string(b)
	}

	// Get all tenants
	var tenants []Tenant
	if err := db.Find(&tenants).Error; err != nil {
		return err
	}

	// Define common settings templates
	settingsTemplates := []TenantSettings{
		// General settings
		{
			Category:    "general",
			Key:         "site_name",
			DataType:    "string",
			Description: "The name of the website",
			IsPublic:    true,
		},
		{
			Category:    "general",
			Key:         "site_tagline",
			DataType:    "string",
			Description: "A brief description of the website",
			IsPublic:    true,
		},
		{
			Category:     "general",
			Key:          "maintenance_mode",
			DataType:     "boolean",
			Description:  "Enable maintenance mode",
			DefaultValue: toJSON(false),
			IsPublic:     false,
		},
		// Branding settings
		{
			Category:    "branding",
			Key:         "logo_url",
			DataType:    "string",
			Description: "URL to the company logo",
			IsPublic:    true,
		},
		{
			Category:    "branding",
			Key:         "favicon_url",
			DataType:    "string",
			Description: "URL to the favicon",
			IsPublic:    true,
		},
		{
			Category:     "branding",
			Key:          "primary_color",
			DataType:     "string",
			Description:  "Primary brand color",
			DefaultValue: toJSON("#007bff"),
			IsPublic:     true,
		},
		// Email settings
		{
			Category:    "email",
			Key:         "smtp_host",
			DataType:    "string",
			Description: "SMTP server hostname",
			IsEncrypted: true,
		},
		{
			Category:     "email",
			Key:          "smtp_port",
			DataType:     "number",
			Description:  "SMTP server port",
			DefaultValue: toJSON(587),
		},
		{
			Category:    "email",
			Key:         "smtp_username",
			DataType:    "string",
			Description: "SMTP username",
			IsEncrypted: true,
		},
		// Security settings
		{
			Category:     "security",
			Key:          "enforce_2fa",
			DataType:     "boolean",
			Description:  "Require two-factor authentication for all users",
			DefaultValue: toJSON(false),
		},
		{
			Category:     "security",
			Key:          "password_min_length",
			DataType:     "number",
			Description:  "Minimum password length",
			DefaultValue: toJSON(8),
		},
		{
			Category:     "security",
			Key:          "session_timeout",
			DataType:     "number",
			Description:  "Session timeout in minutes",
			DefaultValue: toJSON(60),
		},
		// API settings
		{
			Category:     "api",
			Key:          "rate_limit_per_minute",
			DataType:     "number",
			Description:  "API rate limit per minute",
			DefaultValue: toJSON(60),
		},
		{
			Category:     "api",
			Key:          "api_key_expiry_days",
			DataType:     "number",
			Description:  "API key expiry in days",
			DefaultValue: toJSON(365),
		},
		// Notification settings
		{
			Category:     "notifications",
			Key:          "email_notifications_enabled",
			DataType:     "boolean",
			Description:  "Enable email notifications",
			DefaultValue: toJSON(true),
		},
		{
			Category:     "notifications",
			Key:          "webhook_notifications_enabled",
			DataType:     "boolean",
			Description:  "Enable webhook notifications",
			DefaultValue: toJSON(false),
		},
	}

	// Create settings for each tenant
	for _, tenant := range tenants {
		// Set tenant-specific values based on tenant slug
		tenantSpecificValues := map[string]map[string]interface{}{
			"demo-company": {
				"site_name":     "Demo Blog",
				"site_tagline":  "Your demo blogging platform",
				"logo_url":      "/assets/demo-logo.png",
				"primary_color": "#28a745",
			},
			"acme-corp": {
				"site_name":     "Acme Blog Network",
				"site_tagline":  "Professional content management",
				"logo_url":      "/assets/acme-logo.png",
				"primary_color": "#dc3545",
			},
			"techstart": {
				"site_name":     "TechStart Blog",
				"site_tagline":  "Innovation starts here",
				"logo_url":      "/assets/techstart-logo.png",
				"primary_color": "#17a2b8",
			},
			"global-media": {
				"site_name":       "Global Media Publishing",
				"site_tagline":    "News and insights from around the world",
				"logo_url":        "/assets/global-media-logo.png",
				"primary_color":   "#6610f2",
				"enforce_2fa":     true,
				"session_timeout": 30,
			},
			"startup-hub": {
				"site_name":     "Startup Hub Blog",
				"site_tagline":  "Share your startup journey",
				"logo_url":      "/assets/startup-hub-logo.png",
				"primary_color": "#fd7e14",
			},
		}

		// Get tenant-specific values
		specificValues := tenantSpecificValues[tenant.Slug]

		// Create settings for this tenant
		for _, template := range settingsTemplates {
			// Ensure ValidationRules is valid JSON
			validationRules := template.ValidationRules
			if validationRules == "" {
				validationRules = "{}"
			}

			// Ensure DefaultValue is valid JSON
			defaultValue := template.DefaultValue
			if defaultValue == "" {
				defaultValue = "{}"
			}

			setting := TenantSettings{
				TenantID:        tenant.ID,
				Category:        template.Category,
				Key:             template.Key,
				DataType:        template.DataType,
				Description:     template.Description,
				ValidationRules: validationRules,
				DefaultValue:    defaultValue,
				IsEncrypted:     template.IsEncrypted,
				IsPublic:        template.IsPublic,
			}

			// Set value from tenant-specific values or use default
			if specificValue, exists := specificValues[template.Key]; exists {
				setting.Value = toJSON(specificValue)
			} else if template.DefaultValue != "" {
				setting.Value = template.DefaultValue
			} else {
				// Set some default values for certain keys
				switch template.Key {
				case "smtp_host":
					setting.Value = toJSON("smtp.example.com")
				case "smtp_username":
					setting.Value = toJSON("noreply@" + tenant.Domain + ".com")
				case "favicon_url":
					setting.Value = toJSON("/favicon.ico")
				default:
					setting.Value = toJSON("")
				}
			}

			// Check if setting already exists
			var existing TenantSettings
			if err := db.Where("tenant_id = ? AND category = ? AND `key` = ?",
				setting.TenantID, setting.Category, setting.Key).First(&existing).Error; err == nil {
				// Update existing setting
				db.Model(&existing).Updates(setting)
			} else {
				// Create new setting
				if err := db.Create(&setting).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// Rollback removes seeded tenant settings
func (s *TenantSettingsSeeder) Rollback(db *gorm.DB) error {
	// Get tenant IDs for our test tenants
	var tenantIDs []uint
	slugs := []string{"demo-company", "acme-corp", "techstart", "global-media", "startup-hub"}

	if err := db.Model(&Tenant{}).Where("slug IN ?", slugs).Pluck("id", &tenantIDs).Error; err != nil {
		return err
	}

	// Delete settings for these tenants
	return db.Where("tenant_id IN ?", tenantIDs).Delete(&TenantSettings{}).Error
}

// Name returns the name of this seeder
func (s *TenantSettingsSeeder) Name() string {
	return "tenant_settings"
}

// Dependencies returns the dependencies for this seeder
func (s *TenantSettingsSeeder) Dependencies() []string {
	return []string{"tenants"}
}
