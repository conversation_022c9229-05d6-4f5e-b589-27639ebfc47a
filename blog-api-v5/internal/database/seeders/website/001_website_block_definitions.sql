-- Seeder: 001_website_block_definitions.sql
-- Description: Seed default block definitions for website theme system
-- Author: System
-- Date: 2025-07-25

-- Content Blocks
INSERT INTO website_block_definitions (
    tenant_id, block_type, block_name, block_description, block_category,
    field_schema, default_props, style_schema, block_icon, block_preview,
    block_version, supports_nesting, supports_global, supports_responsive, supports_animation,
    css_dependencies, js_dependencies, font_dependencies,
    is_active, is_premium, is_deprecated, documentation_url, example_config, created_by
) VALUES

-- Text Block
(NULL, 'text', 'Text Block', 'Rich text content with formatting options', 'content',
 JSON_OBJECT(
   'content', JSON_OBJECT('type', 'richtext', 'label', 'Content', 'required', true),
   'alignment', JSON_OBJECT('type', 'select', 'label', 'Text Alignment', 'options', JSON_ARRAY('left', 'center', 'right', 'justify'), 'default', 'left'),
   'font_size', JSON_OBJECT('type', 'select', 'label', 'Font Size', 'options', JSON_ARRAY('small', 'medium', 'large', 'xl'), 'default', 'medium')
 ),
 JSON_OBJECT('content', '<p>Enter your text here...</p>', 'alignment', 'left', 'font_size', 'medium'),
 JSON_OBJECT(
   'text_color', JSON_OBJECT('type', 'color', 'label', 'Text Color', 'default', '#333333'),
   'background_color', JSON_OBJECT('type', 'color', 'label', 'Background Color', 'default', 'transparent'),
   'padding', JSON_OBJECT('type', 'spacing', 'label', 'Padding')
 ),
 'text', '/assets/blocks/text-preview.jpg', '1.0.0', false, true, true, false,
 JSON_ARRAY(), JSON_ARRAY(), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/text',
 JSON_OBJECT('content', '<p>This is a sample text block with <strong>bold</strong> and <em>italic</em> formatting.</p>'),
 1),

-- Heading Block
(NULL, 'heading', 'Heading Block', 'Heading text with customizable levels', 'content',
 JSON_OBJECT(
   'text', JSON_OBJECT('type', 'text', 'label', 'Heading Text', 'required', true, 'maxLength', 200),
   'level', JSON_OBJECT('type', 'select', 'label', 'Heading Level', 'options', JSON_ARRAY('h1', 'h2', 'h3', 'h4', 'h5', 'h6'), 'default', 'h2'),
   'alignment', JSON_OBJECT('type', 'select', 'label', 'Text Alignment', 'options', JSON_ARRAY('left', 'center', 'right'), 'default', 'left')
 ),
 JSON_OBJECT('text', 'Your Heading Here', 'level', 'h2', 'alignment', 'left'),
 JSON_OBJECT(
   'color', JSON_OBJECT('type', 'color', 'label', 'Text Color', 'default', '#333333'),
   'font_weight', JSON_OBJECT('type', 'select', 'label', 'Font Weight', 'options', JSON_ARRAY('normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900'), 'default', 'bold'),
   'margin', JSON_OBJECT('type', 'spacing', 'label', 'Margin')
 ),
 'heading', '/assets/blocks/heading-preview.jpg', '1.0.0', false, true, true, false,
 JSON_ARRAY(), JSON_ARRAY(), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/heading',
 JSON_OBJECT('text', 'Welcome to Our Website', 'level', 'h1', 'alignment', 'center'),
 1),

-- Image Block
(NULL, 'image', 'Image Block', 'Single image with caption and styling options', 'media',
 JSON_OBJECT(
   'src', JSON_OBJECT('type', 'image', 'label', 'Image Source', 'required', true, 'validation', JSON_OBJECT('maxSize', '5MB', 'allowedTypes', JSON_ARRAY('jpg', 'png', 'webp', 'gif'))),
   'alt', JSON_OBJECT('type', 'text', 'label', 'Alt Text', 'required', true, 'maxLength', 100),
   'caption', JSON_OBJECT('type', 'text', 'label', 'Caption', 'maxLength', 200),
   'alignment', JSON_OBJECT('type', 'select', 'label', 'Image Alignment', 'options', JSON_ARRAY('left', 'center', 'right'), 'default', 'center'),
   'width', JSON_OBJECT('type', 'select', 'label', 'Width', 'options', JSON_ARRAY('auto', '25%', '50%', '75%', '100%'), 'default', '100%')
 ),
 JSON_OBJECT('src', '', 'alt', '', 'caption', '', 'alignment', 'center', 'width', '100%'),
 JSON_OBJECT(
   'border_radius', JSON_OBJECT('type', 'number', 'label', 'Border Radius', 'min', 0, 'max', 50, 'default', 0, 'unit', 'px'),
   'shadow', JSON_OBJECT('type', 'select', 'label', 'Shadow', 'options', JSON_ARRAY('none', 'small', 'medium', 'large'), 'default', 'none'),
   'margin', JSON_OBJECT('type', 'spacing', 'label', 'Margin')
 ),
 'image', '/assets/blocks/image-preview.jpg', '1.0.0', false, true, true, false,
 JSON_ARRAY(), JSON_ARRAY(), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/image',
 JSON_OBJECT('src', '/uploads/sample-image.jpg', 'alt', 'Sample image', 'caption', 'This is a sample image'),
 1),

-- Button Block
(NULL, 'button', 'Button Block', 'Call-to-action button with customizable styling', 'interactive',
 JSON_OBJECT(
   'text', JSON_OBJECT('type', 'text', 'label', 'Button Text', 'required', true, 'maxLength', 50),
   'url', JSON_OBJECT('type', 'url', 'label', 'Button URL', 'required', true),
   'target', JSON_OBJECT('type', 'select', 'label', 'Link Target', 'options', JSON_ARRAY('_self', '_blank'), 'default', '_self'),
   'style', JSON_OBJECT('type', 'select', 'label', 'Button Style', 'options', JSON_ARRAY('primary', 'secondary', 'outline', 'ghost'), 'default', 'primary'),
   'size', JSON_OBJECT('type', 'select', 'label', 'Button Size', 'options', JSON_ARRAY('small', 'medium', 'large'), 'default', 'medium'),
   'alignment', JSON_OBJECT('type', 'select', 'label', 'Button Alignment', 'options', JSON_ARRAY('left', 'center', 'right'), 'default', 'center')
 ),
 JSON_OBJECT('text', 'Click Here', 'url', '#', 'target', '_self', 'style', 'primary', 'size', 'medium', 'alignment', 'center'),
 JSON_OBJECT(
   'background_color', JSON_OBJECT('type', 'color', 'label', 'Background Color', 'default', '#007bff'),
   'text_color', JSON_OBJECT('type', 'color', 'label', 'Text Color', 'default', '#ffffff'),
   'border_radius', JSON_OBJECT('type', 'number', 'label', 'Border Radius', 'min', 0, 'max', 50, 'default', 5, 'unit', 'px'),
   'padding', JSON_OBJECT('type', 'spacing', 'label', 'Padding')
 ),
 'button', '/assets/blocks/button-preview.jpg', '1.0.0', false, true, true, true,
 JSON_ARRAY(), JSON_ARRAY(), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/button',
 JSON_OBJECT('text', 'Get Started', 'url', '/signup', 'style', 'primary', 'size', 'large'),
 1),

-- Hero Block
(NULL, 'hero', 'Hero Section', 'Large hero section with title, subtitle, and call-to-action', 'layout',
 JSON_OBJECT(
   'title', JSON_OBJECT('type', 'text', 'label', 'Hero Title', 'required', true, 'maxLength', 100),
   'subtitle', JSON_OBJECT('type', 'textarea', 'label', 'Hero Subtitle', 'maxLength', 200),
   'background_image', JSON_OBJECT('type', 'image', 'label', 'Background Image', 'validation', JSON_OBJECT('maxSize', '10MB', 'allowedTypes', JSON_ARRAY('jpg', 'png', 'webp'))),
   'background_overlay', JSON_OBJECT('type', 'checkbox', 'label', 'Enable Background Overlay', 'default', false),
   'cta_text', JSON_OBJECT('type', 'text', 'label', 'Call to Action Text', 'maxLength', 30),
   'cta_url', JSON_OBJECT('type', 'url', 'label', 'Call to Action URL'),
   'text_alignment', JSON_OBJECT('type', 'select', 'label', 'Text Alignment', 'options', JSON_ARRAY('left', 'center', 'right'), 'default', 'center'),
   'height', JSON_OBJECT('type', 'select', 'label', 'Section Height', 'options', JSON_ARRAY('small', 'medium', 'large', 'full'), 'default', 'large')
 ),
 JSON_OBJECT('title', 'Welcome to Our Website', 'subtitle', 'Build amazing things with our platform', 'cta_text', 'Get Started', 'cta_url', '#', 'text_alignment', 'center', 'height', 'large'),
 JSON_OBJECT(
   'background_color', JSON_OBJECT('type', 'color', 'label', 'Background Color', 'default', '#f8f9fa'),
   'overlay_color', JSON_OBJECT('type', 'color', 'label', 'Overlay Color', 'default', 'rgba(0,0,0,0.5)'),
   'title_color', JSON_OBJECT('type', 'color', 'label', 'Title Color', 'default', '#333333'),
   'subtitle_color', JSON_OBJECT('type', 'color', 'label', 'Subtitle Color', 'default', '#666666'),
   'padding', JSON_OBJECT('type', 'spacing', 'label', 'Padding')
 ),
 'hero', '/assets/blocks/hero-preview.jpg', '1.0.0', true, false, true, true,
 JSON_ARRAY('/assets/css/hero-block.css'), JSON_ARRAY('/assets/js/hero-block.js'), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/hero',
 JSON_OBJECT('title', 'Transform Your Business', 'subtitle', 'With our innovative solutions and expert team', 'background_image', '/uploads/hero-bg.jpg'),
 1),

-- Container Block
(NULL, 'container', 'Container Block', 'Content container with styling and layout options', 'layout',
 JSON_OBJECT(
   'width', JSON_OBJECT('type', 'select', 'label', 'Container Width', 'options', JSON_ARRAY('fluid', 'sm', 'md', 'lg', 'xl'), 'default', 'lg'),
   'background_type', JSON_OBJECT('type', 'select', 'label', 'Background Type', 'options', JSON_ARRAY('none', 'color', 'image', 'gradient'), 'default', 'none'),
   'background_color', JSON_OBJECT('type', 'color', 'label', 'Background Color', 'default', 'transparent'),
   'background_image', JSON_OBJECT('type', 'image', 'label', 'Background Image'),
   'border', JSON_OBJECT('type', 'checkbox', 'label', 'Enable Border', 'default', false),
   'shadow', JSON_OBJECT('type', 'select', 'label', 'Shadow', 'options', JSON_ARRAY('none', 'small', 'medium', 'large'), 'default', 'none')
 ),
 JSON_OBJECT('width', 'lg', 'background_type', 'none', 'border', false, 'shadow', 'none'),
 JSON_OBJECT(
   'padding', JSON_OBJECT('type', 'spacing', 'label', 'Padding'),
   'margin', JSON_OBJECT('type', 'spacing', 'label', 'Margin'),
   'border_radius', JSON_OBJECT('type', 'number', 'label', 'Border Radius', 'min', 0, 'max', 50, 'default', 0, 'unit', 'px'),
   'border_color', JSON_OBJECT('type', 'color', 'label', 'Border Color', 'default', '#dee2e6'),
   'border_width', JSON_OBJECT('type', 'number', 'label', 'Border Width', 'min', 0, 'max', 10, 'default', 1, 'unit', 'px')
 ),
 'container', '/assets/blocks/container-preview.jpg', '1.0.0', true, true, true, false,
 JSON_ARRAY(), JSON_ARRAY(), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/container',
 JSON_OBJECT('width', 'lg', 'background_type', 'color', 'background_color', '#f8f9fa'),
 1),

-- Columns Block
(NULL, 'columns', 'Columns Block', 'Multi-column layout with responsive options', 'layout',
 JSON_OBJECT(
   'columns_count', JSON_OBJECT('type', 'select', 'label', 'Number of Columns', 'options', JSON_ARRAY('2', '3', '4'), 'default', '2'),
   'gap', JSON_OBJECT('type', 'select', 'label', 'Column Gap', 'options', JSON_ARRAY('none', 'small', 'medium', 'large'), 'default', 'medium'),
   'mobile_stack', JSON_OBJECT('type', 'checkbox', 'label', 'Stack on Mobile', 'default', true),
   'equal_height', JSON_OBJECT('type', 'checkbox', 'label', 'Equal Height Columns', 'default', false)
 ),
 JSON_OBJECT('columns_count', '2', 'gap', 'medium', 'mobile_stack', true, 'equal_height', false),
 JSON_OBJECT(
   'background_color', JSON_OBJECT('type', 'color', 'label', 'Background Color', 'default', 'transparent'),
   'padding', JSON_OBJECT('type', 'spacing', 'label', 'Padding'),
   'margin', JSON_OBJECT('type', 'spacing', 'label', 'Margin')
 ),
 'columns', '/assets/blocks/columns-preview.jpg', '1.0.0', true, false, true, false,
 JSON_ARRAY('/assets/css/columns-block.css'), JSON_ARRAY(), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/columns',
 JSON_OBJECT('columns_count', '3', 'gap', 'large', 'mobile_stack', true),
 1),

-- Gallery Block
(NULL, 'gallery', 'Gallery Block', 'Image gallery with grid or carousel layout', 'media',
 JSON_OBJECT(
   'images', JSON_OBJECT('type', 'array', 'label', 'Gallery Images', 'required', true, 'items', JSON_OBJECT('type', 'image')),
   'layout', JSON_OBJECT('type', 'select', 'label', 'Gallery Layout', 'options', JSON_ARRAY('grid', 'carousel', 'masonry'), 'default', 'grid'),
   'columns', JSON_OBJECT('type', 'select', 'label', 'Columns', 'options', JSON_ARRAY('2', '3', '4', '5'), 'default', '3'),
   'show_captions', JSON_OBJECT('type', 'checkbox', 'label', 'Show Captions', 'default', true),
   'lightbox', JSON_OBJECT('type', 'checkbox', 'label', 'Enable Lightbox', 'default', true)
 ),
 JSON_OBJECT('images', JSON_ARRAY(), 'layout', 'grid', 'columns', '3', 'show_captions', true, 'lightbox', true),
 JSON_OBJECT(
   'gap', JSON_OBJECT('type', 'number', 'label', 'Image Gap', 'min', 0, 'max', 50, 'default', 10, 'unit', 'px'),
   'border_radius', JSON_OBJECT('type', 'number', 'label', 'Border Radius', 'min', 0, 'max', 50, 'default', 0, 'unit', 'px'),
   'shadow', JSON_OBJECT('type', 'select', 'label', 'Image Shadow', 'options', JSON_ARRAY('none', 'small', 'medium', 'large'), 'default', 'small')
 ),
 'gallery', '/assets/blocks/gallery-preview.jpg', '1.0.0', false, false, true, true,
 JSON_ARRAY('/assets/css/gallery-block.css'), JSON_ARRAY('/assets/js/gallery-block.js'), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/gallery',
 JSON_OBJECT('layout', 'carousel', 'columns', '4', 'lightbox', true),
 1),

-- Video Block
(NULL, 'video', 'Video Block', 'Video embed with support for YouTube, Vimeo, and direct uploads', 'media',
 JSON_OBJECT(
   'video_type', JSON_OBJECT('type', 'select', 'label', 'Video Type', 'options', JSON_ARRAY('youtube', 'vimeo', 'upload'), 'default', 'youtube'),
   'video_url', JSON_OBJECT('type', 'url', 'label', 'Video URL', 'required', true),
   'video_file', JSON_OBJECT('type', 'video', 'label', 'Video File', 'validation', JSON_OBJECT('maxSize', '100MB', 'allowedTypes', JSON_ARRAY('mp4', 'webm', 'ogg'))),
   'poster_image', JSON_OBJECT('type', 'image', 'label', 'Poster Image'),
   'autoplay', JSON_OBJECT('type', 'checkbox', 'label', 'Autoplay', 'default', false),
   'controls', JSON_OBJECT('type', 'checkbox', 'label', 'Show Controls', 'default', true),
   'muted', JSON_OBJECT('type', 'checkbox', 'label', 'Muted', 'default', false),
   'aspect_ratio', JSON_OBJECT('type', 'select', 'label', 'Aspect Ratio', 'options', JSON_ARRAY('16:9', '4:3', '1:1', '21:9'), 'default', '16:9')
 ),
 JSON_OBJECT('video_type', 'youtube', 'video_url', '', 'autoplay', false, 'controls', true, 'muted', false, 'aspect_ratio', '16:9'),
 JSON_OBJECT(
   'border_radius', JSON_OBJECT('type', 'number', 'label', 'Border Radius', 'min', 0, 'max', 50, 'default', 0, 'unit', 'px'),
   'shadow', JSON_OBJECT('type', 'select', 'label', 'Shadow', 'options', JSON_ARRAY('none', 'small', 'medium', 'large'), 'default', 'none'),
   'margin', JSON_OBJECT('type', 'spacing', 'label', 'Margin')
 ),
 'video', '/assets/blocks/video-preview.jpg', '1.0.0', false, false, true, false,
 JSON_ARRAY('/assets/css/video-block.css'), JSON_ARRAY('/assets/js/video-block.js'), JSON_ARRAY(),
 true, false, false, 'https://docs.example.com/blocks/video',
 JSON_OBJECT('video_type', 'youtube', 'video_url', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'),
 1);