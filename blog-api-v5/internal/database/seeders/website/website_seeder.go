package website

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"
)

// WebsiteSeeder implements the website data seeding
type WebsiteSeeder struct {
	db *sql.DB
}

// NewWebsiteSeeder creates a new website seeder
func NewWebsiteSeeder(db *sql.DB) *WebsiteSeeder {
	return &WebsiteSeeder{db: db}
}

// Name returns the seeder name
func (s *WebsiteSeeder) Name() string {
	return "website_seeder"
}

// Dependencies returns the seeders that must run before this one
func (s *WebsiteSeeder) Dependencies() []string {
	return []string{"tenant_seeder"}
}

// Seed executes the website seeding process
func (s *WebsiteSeeder) Seed(ctx context.Context) error {
	log.Println("Starting website module seeding...")

	// Check if websites already exist
	count, err := s.getWebsiteCount(ctx)
	if err != nil {
		return fmt.Errorf("failed to check existing websites: %w", err)
	}

	if count > 0 {
		log.Printf("Websites already exist (%d), skipping seeding", count)
		return nil
	}

	// Get available tenants
	tenantIDs, err := s.getAvailableTenants(ctx)
	if err != nil {
		return fmt.Errorf("failed to get available tenants: %w", err)
	}

	if len(tenantIDs) == 0 {
		return fmt.Errorf("no tenants available for seeding websites")
	}

	// Create websites for each tenant
	for _, tenantID := range tenantIDs {
		if err := s.seedWebsitesForTenant(ctx, tenantID); err != nil {
			return fmt.Errorf("failed to seed websites for tenant %d: %w", tenantID, err)
		}
	}

	log.Println("✅ Website module seeding completed successfully")
	return nil
}

// getWebsiteCount returns the number of existing websites
func (s *WebsiteSeeder) getWebsiteCount(ctx context.Context) (int, error) {
	query := `SELECT COUNT(*) FROM websites WHERE status != 'deleted'`
	var count int
	err := s.db.QueryRowContext(ctx, query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count websites: %w", err)
	}
	return count, nil
}

// getAvailableTenants retrieves all available tenant IDs
func (s *WebsiteSeeder) getAvailableTenants(ctx context.Context) ([]int, error) {
	query := `SELECT id FROM tenants WHERE status = 'active' ORDER BY id LIMIT 10`
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query tenants: %w", err)
	}
	defer rows.Close()

	var tenantIDs []int
	for rows.Next() {
		var id int
		if err := rows.Scan(&id); err != nil {
			return nil, fmt.Errorf("failed to scan tenant ID: %w", err)
		}
		tenantIDs = append(tenantIDs, id)
	}

	return tenantIDs, nil
}

// seedWebsitesForTenant creates one sample website for a specific tenant
func (s *WebsiteSeeder) seedWebsitesForTenant(ctx context.Context, tenantID int) error {
	log.Printf("Seeding website for tenant %d...", tenantID)

	// Create one website per tenant with unique domain
	website := struct {
		Name               string
		Domain             string
		Subdomain          string
		Description        string
		ActiveTheme        string
		CustomCSS          string
		CustomJS           string
		SiteLogo           string
		Favicon            string
		Timezone           string
		Language           string
		GoogleAnalyticsID  string
		GoogleTagManagerID string
		FacebookPixelID    string
		SocialMedia        map[string]interface{}
		Status             string
	}{
		Name:               fmt.Sprintf("My Blog Site %d", tenantID),
		Domain:             fmt.Sprintf("http://localhost:9077/tenant%d", tenantID),
		Subdomain:          fmt.Sprintf("blog%d", tenantID),
		Description:        "A professional blog website covering technology, business, and creative content.",
		ActiveTheme:        "Default",
		CustomCSS:          "/* Custom styles */\n.header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\n.post-card { border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }",
		CustomJS:           "// Custom JavaScript\nconsole.log('Blog site loaded');",
		SiteLogo:           fmt.Sprintf("https://picsum.photos/200/80?random=%d", tenantID),
		Favicon:            fmt.Sprintf("https://picsum.photos/32/32?random=%d", tenantID),
		Timezone:           "America/New_York",
		Language:           "en",
		GoogleAnalyticsID:  fmt.Sprintf("GA-%d", 100000000+tenantID),
		GoogleTagManagerID: fmt.Sprintf("GTM-T%d", tenantID),
		FacebookPixelID:    fmt.Sprintf("%d", 100000000000000+int64(tenantID)),
		SocialMedia: map[string]interface{}{
			"twitter": map[string]interface{}{
				"url":          fmt.Sprintf("https://twitter.com/blogsite%d", tenantID),
				"username":     fmt.Sprintf("blogsite%d", tenantID),
				"display_name": fmt.Sprintf("Blog Site %d", tenantID),
				"is_active":    true,
			},
			"facebook": map[string]interface{}{
				"url":          fmt.Sprintf("https://facebook.com/blogsite%d", tenantID),
				"username":     fmt.Sprintf("blogsite%d", tenantID),
				"display_name": fmt.Sprintf("Blog Site %d", tenantID),
				"is_active":    true,
			},
			"linkedin": map[string]interface{}{
				"url":          fmt.Sprintf("https://linkedin.com/company/blogsite%d", tenantID),
				"username":     fmt.Sprintf("blogsite%d", tenantID),
				"display_name": fmt.Sprintf("Blog Site %d", tenantID),
				"is_active":    true,
			},
		},
		Status: "active",
	}

	// Create the website
	websiteID, err := s.createWebsite(ctx, tenantID, website)
	if err != nil {
		return fmt.Errorf("failed to create website %s: %w", website.Name, err)
	}

	log.Printf("Created website: %s (ID: %d)", website.Name, websiteID)

	// Create default settings for the website
	if err := s.createDefaultSettings(ctx, websiteID, tenantID, website); err != nil {
		return fmt.Errorf("failed to create default settings for website %d: %w", websiteID, err)
	}

	// Create default theme for the website
	if err := s.createDefaultTheme(ctx, websiteID, tenantID, website); err != nil {
		return fmt.Errorf("failed to create default theme for website %d: %w", websiteID, err)
	}

	return nil
}

// createWebsite creates a single website record
func (s *WebsiteSeeder) createWebsite(ctx context.Context, tenantID int, website struct {
	Name               string
	Domain             string
	Subdomain          string
	Description        string
	ActiveTheme        string
	CustomCSS          string
	CustomJS           string
	SiteLogo           string
	Favicon            string
	Timezone           string
	Language           string
	GoogleAnalyticsID  string
	GoogleTagManagerID string
	FacebookPixelID    string
	SocialMedia        map[string]interface{}
	Status             string
}) (int, error) {

	// Convert social media to JSON
	socialMediaJSON, err := json.Marshal(website.SocialMedia)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal social media: %w", err)
	}

	var query string
	var args []interface{}

	now := time.Now()

	if website.Domain == "" {
		query = `
			INSERT INTO websites (
				tenant_id, name, subdomain, description, active_theme,
				custom_css, custom_js, site_logo, favicon, timezone, language,
				google_analytics_id, google_tag_manager_id, facebook_pixel_id,
				social_media, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`
		args = []interface{}{
			tenantID, website.Name, website.Subdomain, website.Description,
			website.ActiveTheme, website.CustomCSS, website.CustomJS, website.SiteLogo,
			website.Favicon, website.Timezone, website.Language, website.GoogleAnalyticsID,
			website.GoogleTagManagerID, website.FacebookPixelID, string(socialMediaJSON),
			website.Status, now, now,
		}
	} else {
		query = `
			INSERT INTO websites (
				tenant_id, name, domain, subdomain, description, active_theme,
				custom_css, custom_js, site_logo, favicon, timezone, language,
				google_analytics_id, google_tag_manager_id, facebook_pixel_id,
				social_media, status, created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`
		args = []interface{}{
			tenantID, website.Name, website.Domain, website.Subdomain, website.Description,
			website.ActiveTheme, website.CustomCSS, website.CustomJS, website.SiteLogo,
			website.Favicon, website.Timezone, website.Language, website.GoogleAnalyticsID,
			website.GoogleTagManagerID, website.FacebookPixelID, string(socialMediaJSON),
			website.Status, now, now,
		}
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to insert website: %w", err)
	}

	websiteID, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get website ID: %w", err)
	}

	return int(websiteID), nil
}

// createDefaultSettings creates default settings for a website
func (s *WebsiteSeeder) createDefaultSettings(ctx context.Context, websiteID, tenantID int, website struct {
	Name               string
	Domain             string
	Subdomain          string
	Description        string
	ActiveTheme        string
	CustomCSS          string
	CustomJS           string
	SiteLogo           string
	Favicon            string
	Timezone           string
	Language           string
	GoogleAnalyticsID  string
	GoogleTagManagerID string
	FacebookPixelID    string
	SocialMedia        map[string]interface{}
	Status             string
}) error {

	settings := []struct {
		SettingKey      string
		SettingName     string
		Category        string
		SettingValue    string
		DefaultValue    string
		DataType        string
		IsPublic        bool
		IsRequired      bool
		IsEncrypted     bool
		Description     string
		ValidationRules map[string]interface{}
		Options         []map[string]interface{}
	}{
		{
			SettingKey:      "site_title",
			SettingName:     "Site Title",
			Category:        "general",
			SettingValue:    website.Name,
			DefaultValue:    website.Name,
			DataType:        "string",
			IsPublic:        true,
			IsRequired:      true,
			IsEncrypted:     false,
			Description:     "The main title of the website",
			ValidationRules: map[string]interface{}{"min_length": 1, "max_length": 255},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "site_description",
			SettingName:     "Site Description",
			Category:        "general",
			SettingValue:    website.Description,
			DefaultValue:    website.Description,
			DataType:        "string",
			IsPublic:        true,
			IsRequired:      false,
			IsEncrypted:     false,
			Description:     "A brief description of the website",
			ValidationRules: map[string]interface{}{"max_length": 5000},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "timezone",
			SettingName:     "Timezone",
			Category:        "general",
			SettingValue:    website.Timezone,
			DefaultValue:    "UTC",
			DataType:        "string",
			IsPublic:        true,
			IsRequired:      true,
			IsEncrypted:     false,
			Description:     "The timezone for the website",
			ValidationRules: map[string]interface{}{},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "language",
			SettingName:     "Language",
			Category:        "general",
			SettingValue:    website.Language,
			DefaultValue:    "en",
			DataType:        "string",
			IsPublic:        true,
			IsRequired:      true,
			IsEncrypted:     false,
			Description:     "The default language for the website",
			ValidationRules: map[string]interface{}{},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "comments_enabled",
			SettingName:     "Comments Enabled",
			Category:        "comments",
			SettingValue:    "true",
			DefaultValue:    "true",
			DataType:        "boolean",
			IsPublic:        true,
			IsRequired:      false,
			IsEncrypted:     false,
			Description:     "Enable or disable comments on the website",
			ValidationRules: map[string]interface{}{},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "comments_moderation",
			SettingName:     "Comments Moderation",
			Category:        "comments",
			SettingValue:    "true",
			DefaultValue:    "true",
			DataType:        "boolean",
			IsPublic:        true,
			IsRequired:      false,
			IsEncrypted:     false,
			Description:     "Enable comment moderation before publishing",
			ValidationRules: map[string]interface{}{},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "posts_per_page",
			SettingName:     "Posts Per Page",
			Category:        "general",
			SettingValue:    "10",
			DefaultValue:    "10",
			DataType:        "number",
			IsPublic:        true,
			IsRequired:      false,
			IsEncrypted:     false,
			Description:     "Number of posts to display per page",
			ValidationRules: map[string]interface{}{"min_value": 1, "max_value": 100},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "seo_meta_description",
			SettingName:     "SEO Meta Description",
			Category:        "seo",
			SettingValue:    website.Description,
			DefaultValue:    website.Description,
			DataType:        "string",
			IsPublic:        true,
			IsRequired:      false,
			IsEncrypted:     false,
			Description:     "Default meta description for SEO",
			ValidationRules: map[string]interface{}{"max_length": 160},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "social_sharing_enabled",
			SettingName:     "Social Sharing Enabled",
			Category:        "social",
			SettingValue:    "true",
			DefaultValue:    "true",
			DataType:        "boolean",
			IsPublic:        true,
			IsRequired:      false,
			IsEncrypted:     false,
			Description:     "Enable social sharing buttons on posts",
			ValidationRules: map[string]interface{}{},
			Options:         []map[string]interface{}{},
		},
		{
			SettingKey:      "analytics_tracking",
			SettingName:     "Analytics Tracking",
			Category:        "analytics",
			SettingValue:    "true",
			DefaultValue:    "true",
			DataType:        "boolean",
			IsPublic:        true,
			IsRequired:      false,
			IsEncrypted:     false,
			Description:     "Enable analytics tracking",
			ValidationRules: map[string]interface{}{},
			Options:         []map[string]interface{}{},
		},
	}

	now := time.Now()
	for _, setting := range settings {
		// Convert validation rules to JSON
		validationRulesJSON, err := json.Marshal(setting.ValidationRules)
		if err != nil {
			return fmt.Errorf("failed to marshal validation rules: %w", err)
		}

		// Convert options to JSON
		optionsJSON, err := json.Marshal(setting.Options)
		if err != nil {
			return fmt.Errorf("failed to marshal options: %w", err)
		}

		query := `
			INSERT INTO website_settings (
				website_id, tenant_id, setting_key, setting_name, category,
				setting_value, default_value, data_type, is_public, is_required,
				is_encrypted, validation_rules, description, options,
				created_at, updated_at
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`

		_, err = s.db.ExecContext(ctx, query,
			websiteID, tenantID, setting.SettingKey, setting.SettingName, setting.Category,
			setting.SettingValue, setting.DefaultValue, setting.DataType, setting.IsPublic,
			setting.IsRequired, setting.IsEncrypted, string(validationRulesJSON),
			setting.Description, string(optionsJSON), now, now,
		)
		if err != nil {
			return fmt.Errorf("failed to insert setting %s: %w", setting.SettingKey, err)
		}
	}

	return nil
}

// createDefaultTheme creates a default theme for a website
func (s *WebsiteSeeder) createDefaultTheme(ctx context.Context, websiteID, tenantID int, website struct {
	Name               string
	Domain             string
	Subdomain          string
	Description        string
	ActiveTheme        string
	CustomCSS          string
	CustomJS           string
	SiteLogo           string
	Favicon            string
	Timezone           string
	Language           string
	GoogleAnalyticsID  string
	GoogleTagManagerID string
	FacebookPixelID    string
	SocialMedia        map[string]interface{}
	Status             string
}) error {

	// Theme configuration
	themeConfig := map[string]interface{}{
		"show_header":          true,
		"show_footer":          true,
		"show_sidebar":         true,
		"sidebar_position":     "right",
		"header_style":         "static",
		"footer_style":         "static",
		"navigation":           "horizontal",
		"breadcrumb_enabled":   true,
		"search_enabled":       true,
		"social_links_enabled": true,
		"dark_mode_enabled":    false,
		"rtl_enabled":          false,
		"responsive_enabled":   true,
	}

	// Theme assets - not used in current theme table structure
	// themeAssets := map[string]interface{}{
	// 	"css":        []string{"theme.css", "custom.css"},
	// 	"javascript": []string{"theme.js", "custom.js"},
	// 	"images":     map[string]string{"logo": website.SiteLogo, "favicon": website.Favicon},
	// 	"fonts":      []map[string]interface{}{},
	// 	"icons":      map[string]string{},
	// 	"custom_css": website.CustomCSS,
	// 	"custom_js":  website.CustomJS,
	// }

	// Color palette
	colorPalette := map[string]interface{}{
		"primary":        "#007bff",
		"secondary":      "#6c757d",
		"accent":         "#17a2b8",
		"background":     "#ffffff",
		"surface":        "#f8f9fa",
		"text":           "#212529",
		"text_secondary": "#6c757d",
		"border":         "#dee2e6",
		"success":        "#28a745",
		"warning":        "#ffc107",
		"error":          "#dc3545",
		"info":           "#17a2b8",
	}

	// Typography
	typography := map[string]interface{}{
		"primary_font": map[string]interface{}{
			"family":   "Inter",
			"weight":   "400",
			"style":    "normal",
			"source":   "google",
			"fallback": "sans-serif",
		},
		"secondary_font": map[string]interface{}{
			"family":   "Inter",
			"weight":   "300",
			"style":    "normal",
			"source":   "google",
			"fallback": "sans-serif",
		},
		"heading_font": map[string]interface{}{
			"family":   "Inter",
			"weight":   "600",
			"style":    "normal",
			"source":   "google",
			"fallback": "sans-serif",
		},
		"code_font": map[string]interface{}{
			"family":   "JetBrains Mono",
			"weight":   "400",
			"style":    "normal",
			"source":   "google",
			"fallback": "monospace",
		},
		"base_font_size": "16px",
		"line_height":    "1.5",
		"letter_spacing": "0",
		"font_weight":    "400",
		"heading_sizes": map[string]interface{}{
			"h1": "2.5rem",
			"h2": "2rem",
			"h3": "1.75rem",
			"h4": "1.5rem",
			"h5": "1.25rem",
			"h6": "1rem",
		},
	}

	// Layout settings - not used in current theme table structure
	// layoutSettings := map[string]interface{}{
	// 	"container": map[string]interface{}{
	// 		"max_width": "1200px",
	// 		"padding":   "1rem",
	// 		"margin":    "0 auto",
	// 		"centered":  true,
	// 		"fluid":     false,
	// 	},
	// 	"grid": map[string]interface{}{
	// 		"columns":    12,
	// 		"gutter_size": "1rem",
	// 		"row_gap":     "1rem",
	// 		"column_gap":  "1rem",
	// 	},
	// 	"spacing": map[string]interface{}{
	// 		"base":        "1rem",
	// 		"small":       "0.5rem",
	// 		"medium":      "1.5rem",
	// 		"large":       "2rem",
	// 		"extra_large": "3rem",
	// 	},
	// 	"border_radius": "0.375rem",
	// 	"box_shadow":    "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
	// 	"animation": map[string]interface{}{
	// 		"enabled":       true,
	// 		"duration":      "0.3s",
	// 		"easing":        "ease-in-out",
	// 		"reduce_motion": false,
	// 	},
	// 	"breakpoints": map[string]interface{}{
	// 		"extra_small": "576px",
	// 		"small":       "768px",
	// 		"medium":      "992px",
	// 		"large":       "1200px",
	// 		"extra_large": "1400px",
	// 	},
	// }

	// Convert to JSON
	themeConfigJSON, err := json.Marshal(themeConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal theme config: %w", err)
	}

	colorPaletteJSON, err := json.Marshal(colorPalette)
	if err != nil {
		return fmt.Errorf("failed to marshal color palette: %w", err)
	}

	typographyJSON, err := json.Marshal(typography)
	if err != nil {
		return fmt.Errorf("failed to marshal typography: %w", err)
	}

	query := `
		INSERT INTO website_themes (
			website_id, tenant_id, name, version, description,
			config, custom_colors, custom_fonts, custom_css, custom_js,
			is_active, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	now := time.Now()
	_, err = s.db.ExecContext(ctx, query,
		websiteID, tenantID, "Default", "1.0.0", fmt.Sprintf("Default theme for %s", website.Name),
		string(themeConfigJSON), string(colorPaletteJSON), string(typographyJSON),
		website.CustomCSS, website.CustomJS, true, now, now,
	)
	if err != nil {
		return fmt.Errorf("failed to insert theme: %w", err)
	}

	return nil
}

// Rollback removes the seeded website data
func (s *WebsiteSeeder) Rollback(db *sql.DB) error {
	log.Println("Rolling back website seeder...")

	// Delete in reverse order of creation
	tables := []string{
		"website_themes",
		"website_settings",
		"websites",
	}

	for _, table := range tables {
		query := fmt.Sprintf("DELETE FROM %s WHERE tenant_id IN (SELECT id FROM tenants WHERE status = 'active') AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)", table)
		_, err := db.Exec(query)
		if err != nil {
			return fmt.Errorf("failed to delete from %s: %w", table, err)
		}
	}

	log.Println("✅ Website seeder rollback completed")
	return nil
}
