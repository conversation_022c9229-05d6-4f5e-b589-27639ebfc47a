package website

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"
)

// WebsiteTemplatesSeeder implements the website templates data seeding
type WebsiteTemplatesSeeder struct {
	db *sql.DB
}

// NewWebsiteTemplatesSeeder creates a new website templates seeder
func NewWebsiteTemplatesSeeder(db *sql.DB) *WebsiteTemplatesSeeder {
	return &WebsiteTemplatesSeeder{db: db}
}

// Name returns the seeder name
func (s *WebsiteTemplatesSeeder) Name() string {
	return "website_templates_seeder"
}

// Dependencies returns the seeders that must run before this one
func (s *WebsiteTemplatesSeeder) Dependencies() []string {
	return []string{"website_template_categories_seeder"}
}

// Seed executes the website templates seeding process
func (s *WebsiteTemplatesSeeder) Seed(ctx context.Context) error {
	log.Println("Starting website templates seeding...")

	// Check if templates already exist
	count, err := s.getTemplateCount(ctx)
	if err != nil {
		return fmt.Errorf("failed to check existing templates: %w", err)
	}

	if count > 0 {
		log.Printf("Website templates already exist (%d), skipping seeding", count)
		return nil
	}

	// Get available categories
	categories, err := s.getAvailableCategories(ctx)
	if err != nil {
		return fmt.Errorf("failed to get available categories: %w", err)
	}

	if len(categories) == 0 {
		return fmt.Errorf("no categories available for seeding templates")
	}

	// Create templates for each category
	if err := s.createTemplates(ctx, categories); err != nil {
		return fmt.Errorf("failed to create templates: %w", err)
	}

	log.Println("✅ Website templates seeding completed successfully")
	return nil
}

// getTemplateCount returns the number of existing templates
func (s *WebsiteTemplatesSeeder) getTemplateCount(ctx context.Context) (int, error) {
	query := `SELECT COUNT(*) FROM website_templates WHERE status != 'deleted'`
	var count int
	err := s.db.QueryRowContext(ctx, query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count templates: %w", err)
	}
	return count, nil
}

// getAvailableCategories retrieves all available categories
func (s *WebsiteTemplatesSeeder) getAvailableCategories(ctx context.Context) (map[string]int, error) {
	query := `SELECT id, slug FROM website_template_categories WHERE status = 'active' AND level = 0 ORDER BY sort_order`
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query categories: %w", err)
	}
	defer rows.Close()

	categories := make(map[string]int)
	for rows.Next() {
		var id int
		var slug string
		if err := rows.Scan(&id, &slug); err != nil {
			return nil, fmt.Errorf("failed to scan category: %w", err)
		}
		categories[slug] = id
	}

	return categories, nil
}

// createTemplates creates sample templates for each category
func (s *WebsiteTemplatesSeeder) createTemplates(ctx context.Context, categories map[string]int) error {
	log.Println("Creating website templates...")

	templates := []struct {
		Name            string
		Slug            string
		Description     string
		CategorySlug    string
		PreviewImageURL string
		TemplateType    string
		Industry        string
		IsPremium       bool
		IsFeatured      bool
		IsPublished     bool
		Version         string
		TemplateData    map[string]interface{}
		Sections        []map[string]interface{}
		Styles          map[string]interface{}
		Settings        map[string]interface{}
		Tags            []string
	}{
		// Business Templates
		{
			Name:            "Corporate Pro",
			Slug:            "corporate-pro",
			Description:     "Professional corporate website template with modern design and comprehensive business sections",
			CategorySlug:    "business",
			PreviewImageURL: "https://picsum.photos/800/600?random=1",
			TemplateType:    "business",
			Industry:        "Corporate",
			IsPremium:       true,
			IsFeatured:      true,
			IsPublished:     true,
			Version:         "1.2.0",
			TemplateData: map[string]interface{}{
				"layout":       "full-width",
				"header_style": "transparent",
				"footer_style": "dark",
				"sidebar":      false,
			},
			Sections: []map[string]interface{}{
				{"type": "header", "name": "Navigation Header", "required": true},
				{"type": "hero", "name": "Hero Banner", "required": true},
				{"type": "about", "name": "About Company", "required": false},
				{"type": "services", "name": "Our Services", "required": false},
				{"type": "testimonials", "name": "Client Testimonials", "required": false},
				{"type": "contact", "name": "Contact Form", "required": false},
				{"type": "footer", "name": "Company Footer", "required": true},
			},
			Styles: map[string]interface{}{
				"primary_color":   "#1e40af",
				"secondary_color": "#64748b",
				"font_family":     "Inter",
				"border_radius":   "8px",
			},
			Settings: map[string]interface{}{
				"responsive":    true,
				"seo_optimized": true,
				"dark_mode":     false,
				"animations":    true,
				"lazy_loading":  true,
			},
			Tags: []string{"corporate", "business", "professional", "modern"},
		},
		{
			Name:            "Startup Launch",
			Slug:            "startup-launch",
			Description:     "Dynamic startup website template perfect for launching new products and services",
			CategorySlug:    "business",
			PreviewImageURL: "https://picsum.photos/800/600?random=2",
			TemplateType:    "business",
			Industry:        "Technology",
			IsPremium:       false,
			IsFeatured:      true,
			IsPublished:     true,
			Version:         "1.0.0",
			TemplateData: map[string]interface{}{
				"layout":       "boxed",
				"header_style": "solid",
				"footer_style": "gradient",
				"sidebar":      false,
			},
			Sections: []map[string]interface{}{
				{"type": "header", "name": "Modern Header", "required": true},
				{"type": "hero", "name": "Product Hero", "required": true},
				{"type": "about", "name": "About Startup", "required": false},
				{"type": "services", "name": "Features", "required": false},
				{"type": "portfolio", "name": "Product Gallery", "required": false},
				{"type": "contact", "name": "Get Started", "required": false},
				{"type": "footer", "name": "Startup Footer", "required": true},
			},
			Styles: map[string]interface{}{
				"primary_color":   "#7c3aed",
				"secondary_color": "#f59e0b",
				"font_family":     "Poppins",
				"border_radius":   "12px",
			},
			Settings: map[string]interface{}{
				"responsive":    true,
				"seo_optimized": true,
				"dark_mode":     true,
				"animations":    true,
				"lazy_loading":  true,
			},
			Tags: []string{"startup", "launch", "modern", "tech"},
		},
		// Portfolio Templates
		{
			Name:            "Creative Portfolio",
			Slug:            "creative-portfolio",
			Description:     "Stunning portfolio template for designers, artists, and creative professionals",
			CategorySlug:    "portfolio",
			PreviewImageURL: "https://picsum.photos/800/600?random=3",
			TemplateType:    "portfolio",
			Industry:        "Design",
			IsPremium:       true,
			IsFeatured:      true,
			IsPublished:     true,
			Version:         "2.1.0",
			TemplateData: map[string]interface{}{
				"layout":       "masonry",
				"header_style": "minimal",
				"footer_style": "minimal",
				"sidebar":      false,
			},
			Sections: []map[string]interface{}{
				{"type": "header", "name": "Portfolio Header", "required": true},
				{"type": "hero", "name": "Creative Hero", "required": true},
				{"type": "about", "name": "About Artist", "required": false},
				{"type": "portfolio", "name": "Project Gallery", "required": true},
				{"type": "testimonials", "name": "Client Reviews", "required": false},
				{"type": "contact", "name": "Contact Form", "required": false},
				{"type": "footer", "name": "Portfolio Footer", "required": true},
			},
			Styles: map[string]interface{}{
				"primary_color":   "#ec4899",
				"secondary_color": "#6b7280",
				"font_family":     "Playfair Display",
				"border_radius":   "0px",
			},
			Settings: map[string]interface{}{
				"responsive":    true,
				"seo_optimized": true,
				"dark_mode":     true,
				"animations":    true,
				"lazy_loading":  true,
				"image_gallery": true,
			},
			Tags: []string{"portfolio", "creative", "designer", "gallery"},
		},
		// Blog Templates
		{
			Name:            "Modern Blog",
			Slug:            "modern-blog",
			Description:     "Clean and modern blog template with excellent typography and reading experience",
			CategorySlug:    "blog",
			PreviewImageURL: "https://picsum.photos/800/600?random=4",
			TemplateType:    "blog",
			Industry:        "Publishing",
			IsPremium:       false,
			IsFeatured:      true,
			IsPublished:     true,
			Version:         "1.5.0",
			TemplateData: map[string]interface{}{
				"layout":       "sidebar-right",
				"header_style": "clean",
				"footer_style": "newsletter",
				"sidebar":      true,
			},
			Sections: []map[string]interface{}{
				{"type": "header", "name": "Blog Header", "required": true},
				{"type": "hero", "name": "Featured Posts", "required": false},
				{"type": "about", "name": "About Blog", "required": false},
				{"type": "custom", "name": "Post List", "required": true},
				{"type": "custom", "name": "Sidebar", "required": false},
				{"type": "footer", "name": "Blog Footer", "required": true},
			},
			Styles: map[string]interface{}{
				"primary_color":   "#059669",
				"secondary_color": "#374151",
				"font_family":     "Source Sans Pro",
				"border_radius":   "6px",
			},
			Settings: map[string]interface{}{
				"responsive":       true,
				"seo_optimized":    true,
				"dark_mode":        true,
				"animations":       false,
				"lazy_loading":     true,
				"comments_enabled": true,
				"social_sharing":   true,
			},
			Tags: []string{"blog", "clean", "typography", "reading"},
		},
		// E-commerce Templates
		{
			Name:            "Shop Elite",
			Slug:            "shop-elite",
			Description:     "Premium e-commerce template with advanced product showcase and shopping features",
			CategorySlug:    "ecommerce",
			PreviewImageURL: "https://picsum.photos/800/600?random=5",
			TemplateType:    "ecommerce",
			Industry:        "Retail",
			IsPremium:       true,
			IsFeatured:      true,
			IsPublished:     true,
			Version:         "3.0.0",
			TemplateData: map[string]interface{}{
				"layout":       "full-width",
				"header_style": "sticky",
				"footer_style": "comprehensive",
				"sidebar":      false,
			},
			Sections: []map[string]interface{}{
				{"type": "header", "name": "Shop Header", "required": true},
				{"type": "hero", "name": "Product Hero", "required": true},
				{"type": "custom", "name": "Product Categories", "required": true},
				{"type": "custom", "name": "Featured Products", "required": true},
				{"type": "testimonials", "name": "Customer Reviews", "required": false},
				{"type": "footer", "name": "Shop Footer", "required": true},
			},
			Styles: map[string]interface{}{
				"primary_color":   "#dc2626",
				"secondary_color": "#1f2937",
				"font_family":     "Open Sans",
				"border_radius":   "4px",
			},
			Settings: map[string]interface{}{
				"responsive":      true,
				"seo_optimized":   true,
				"dark_mode":       false,
				"animations":      true,
				"lazy_loading":    true,
				"cart_enabled":    true,
				"wishlist":        true,
				"product_reviews": true,
			},
			Tags: []string{"ecommerce", "shop", "store", "products"},
		},
		// Landing Page Templates
		{
			Name:            "Launch Landing",
			Slug:            "launch-landing",
			Description:     "High-converting landing page template for product launches and marketing campaigns",
			CategorySlug:    "landing",
			PreviewImageURL: "https://picsum.photos/800/600?random=6",
			TemplateType:    "landing",
			Industry:        "Marketing",
			IsPremium:       false,
			IsFeatured:      false,
			IsPublished:     true,
			Version:         "1.0.0",
			TemplateData: map[string]interface{}{
				"layout":       "single-page",
				"header_style": "transparent",
				"footer_style": "minimal",
				"sidebar":      false,
			},
			Sections: []map[string]interface{}{
				{"type": "header", "name": "Landing Header", "required": true},
				{"type": "hero", "name": "Main CTA", "required": true},
				{"type": "about", "name": "Features", "required": true},
				{"type": "testimonials", "name": "Social Proof", "required": false},
				{"type": "contact", "name": "Sign Up Form", "required": true},
				{"type": "footer", "name": "Simple Footer", "required": true},
			},
			Styles: map[string]interface{}{
				"primary_color":   "#ea580c",
				"secondary_color": "#475569",
				"font_family":     "Roboto",
				"border_radius":   "8px",
			},
			Settings: map[string]interface{}{
				"responsive":    true,
				"seo_optimized": true,
				"dark_mode":     false,
				"animations":    true,
				"lazy_loading":  false,
				"form_tracking": true,
				"analytics":     true,
			},
			Tags: []string{"landing", "conversion", "marketing", "cta"},
		},
	}

	now := time.Now()

	for _, template := range templates {
		categoryID, exists := categories[template.CategorySlug]
		if !exists {
			log.Printf("Warning: category %s not found for template %s", template.CategorySlug, template.Name)
			continue
		}

		templateID, err := s.createTemplate(ctx, template, categoryID, now)
		if err != nil {
			return fmt.Errorf("failed to create template %s: %w", template.Name, err)
		}

		log.Printf("Created template: %s (ID: %d)", template.Name, templateID)
	}

	return nil
}

// createTemplate creates a single template record
func (s *WebsiteTemplatesSeeder) createTemplate(ctx context.Context, template struct {
	Name            string
	Slug            string
	Description     string
	CategorySlug    string
	PreviewImageURL string
	TemplateType    string
	Industry        string
	IsPremium       bool
	IsFeatured      bool
	IsPublished     bool
	Version         string
	TemplateData    map[string]interface{}
	Sections        []map[string]interface{}
	Styles          map[string]interface{}
	Settings        map[string]interface{}
	Tags            []string
}, categoryID int, now time.Time) (int, error) {

	// Convert complex fields to JSON
	templateDataJSON, err := json.Marshal(template.TemplateData)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal template data: %w", err)
	}

	sectionsJSON, err := json.Marshal(template.Sections)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal sections: %w", err)
	}

	stylesJSON, err := json.Marshal(template.Styles)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal styles: %w", err)
	}

	settingsJSON, err := json.Marshal(template.Settings)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal settings: %w", err)
	}

	tagsJSON, err := json.Marshal(template.Tags)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal tags: %w", err)
	}

	var publishedAt *time.Time
	if template.IsPublished {
		publishedAt = &now
	}

	query := `
		INSERT INTO website_templates (
			name, slug, description, preview_image_url, template_data, sections,
			styles, settings, category_id, tags, is_premium, is_published,
			is_featured, version, download_count, usage_count, rating_average,
			rating_count, template_type, industry, status, published_at,
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := s.db.ExecContext(ctx, query,
		template.Name, template.Slug, template.Description, template.PreviewImageURL,
		string(templateDataJSON), string(sectionsJSON), string(stylesJSON),
		string(settingsJSON), categoryID, string(tagsJSON), template.IsPremium,
		template.IsPublished, template.IsFeatured, template.Version, 0, 0, 0.00, 0,
		template.TemplateType, template.Industry, "published", publishedAt, now, now,
	)
	if err != nil {
		return 0, fmt.Errorf("failed to insert template: %w", err)
	}

	templateID, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get template ID: %w", err)
	}

	return int(templateID), nil
}

// Rollback removes the seeded website templates data
func (s *WebsiteTemplatesSeeder) Rollback(db *sql.DB) error {
	log.Println("Rolling back website templates seeder...")

	query := "DELETE FROM website_templates WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)"
	_, err := db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to delete templates: %w", err)
	}

	log.Println("✅ Website templates seeder rollback completed")
	return nil
}
