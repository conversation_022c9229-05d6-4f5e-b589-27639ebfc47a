package integration

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
)

// CrossModuleIntegration handles integration between all modules
type CrossModuleIntegration struct {
	// Module services
	authService       *authservices.AuthService
	userService       *userservices.UserService
	tenantService     *tenantservices.TenantService
	websiteService    *websiteservices.WebsiteService
	blogService       *blogservices.BlogPostService
	mediaService      *mediaservices.MediaFileService
	rbacService       *rbacservices.RBACEngine
	seoService        *seoservices.SEOMetaService
	onboardingService *onboardingservices.OnboardingService

	logger *logrus.Logger
}

// NewCrossModuleIntegration creates a new cross-module integration service
func NewCrossModuleIntegration(
	authService *authservices.AuthService,
	userService *userservices.UserService,
	tenantService *tenantservices.TenantService,
	websiteService *websiteservices.WebsiteService,
	blogService *blogservices.BlogPostService,
	mediaService *mediaservices.MediaFileService,
	rbacService *rbacservices.RBACEngine,
	seoService *seoservices.SEOMetaService,
	onboardingService *onboardingservices.OnboardingService,
	logger *logrus.Logger,
) *CrossModuleIntegration {
	return &CrossModuleIntegration{
		authService:       authService,
		userService:       userService,
		tenantService:     tenantService,
		websiteService:    websiteService,
		blogService:       blogService,
		mediaService:      mediaService,
		rbacService:       rbacService,
		seoService:        seoService,
		onboardingService: onboardingService,
		logger:            logger,
	}
}

// UserTenantWebsiteWorkflow handles the complete workflow for user-tenant-website relationships
type UserTenantWebsiteWorkflow struct {
	UserID    uint      `json:"user_id"`
	TenantID  uint      `json:"tenant_id"`
	WebsiteID uint      `json:"website_id"`
	Role      string    `json:"role"`
	IsPrimary bool      `json:"is_primary"`
	CreatedAt time.Time `json:"created_at"`
}

// ContentWorkflow handles blog-media-seo integration
type ContentWorkflow struct {
	BlogPostID   uint       `json:"blog_post_id"`
	MediaFileIDs []uint     `json:"media_file_ids"`
	SEOMetaID    uint       `json:"seo_meta_id"`
	Status       string     `json:"status"`
	PublishedAt  *time.Time `json:"published_at"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// SetupUserTenantWebsiteWorkflow creates complete user-tenant-website relationship
func (cmi *CrossModuleIntegration) SetupUserTenantWebsiteWorkflow(ctx context.Context, userID, tenantID, websiteID uint, role string) (*UserTenantWebsiteWorkflow, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// 1. Verify user exists
	user, err := cmi.userService.GetUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 2. Verify tenant exists
	tenant, err := cmi.tenantService.GetTenant(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tenant: %w", err)
	}

	// 3. Verify website exists and belongs to tenant
	website, err := cmi.websiteService.GetWebsite(ctx, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get website: %w", err)
	}

	if website.TenantID != tenantID {
		return nil, fmt.Errorf("website does not belong to tenant")
	}

	// 4. Check if user already has access to this tenant
	hasTenantAccess, err := cmi.userService.HasTenantAccess(ctx, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to check tenant access: %w", err)
	}

	// 5. Create tenant membership if needed
	if !hasTenantAccess {
		membershipReq := &userservices.TenantMembershipRequest{
			UserID:    userID,
			TenantID:  tenantID,
			Role:      role,
			IsPrimary: false,
		}

		_, err = cmi.userService.CreateTenantMembership(ctx, membershipReq)
		if err != nil {
			return nil, fmt.Errorf("failed to create tenant membership: %w", err)
		}
	}

	// 6. Setup RBAC permissions
	err = cmi.rbacService.AssignUserToRole(ctx, userID, role, tenantID)
	if err != nil {
		cmi.logger.WithError(err).Warn("Failed to assign RBAC role")
	}

	// 7. Grant website access permissions
	err = cmi.setupWebsitePermissions(ctx, userID, websiteID, role)
	if err != nil {
		cmi.logger.WithError(err).Warn("Failed to setup website permissions")
	}

	workflow := &UserTenantWebsiteWorkflow{
		UserID:    userID,
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Role:      role,
		IsPrimary: false,
		CreatedAt: time.Now(),
	}

	cmi.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"tenant_id":  tenantID,
		"website_id": websiteID,
		"role":       role,
	}).Info("User-tenant-website workflow setup completed")

	return workflow, nil
}

// CreateContentWorkflow handles blog post creation with media and SEO integration
func (cmi *CrossModuleIntegration) CreateContentWorkflow(ctx context.Context, req *ContentWorkflowRequest) (*ContentWorkflow, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// 1. Validate permissions
	canCreate, err := cmi.rbacService.CheckPermission(ctx, tenantCtx.UserID, "blog:create", tenantCtx.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}
	if !canCreate {
		return nil, fmt.Errorf("insufficient permissions to create blog content")
	}

	// 2. Create blog post
	blogPost, err := cmi.blogService.CreateBlogPost(ctx, req.BlogPostRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to create blog post: %w", err)
	}

	// 3. Associate media files
	var mediaFileIDs []uint
	for _, mediaID := range req.MediaFileIDs {
		// Verify media file exists and belongs to tenant
		media, err := cmi.mediaService.GetFile(ctx, mediaID)
		if err != nil {
			cmi.logger.WithError(err).WithField("media_id", mediaID).Warn("Failed to get media file")
			continue
		}

		if media.TenantID != tenantCtx.TenantID {
			cmi.logger.WithField("media_id", mediaID).Warn("Media file does not belong to tenant")
			continue
		}

		mediaFileIDs = append(mediaFileIDs, mediaID)
	}

	// 4. Create SEO metadata
	seoMeta, err := cmi.createSEOForBlogPost(ctx, blogPost, req.SEORequest)
	if err != nil {
		cmi.logger.WithError(err).Warn("Failed to create SEO metadata")
	}

	// 5. Update blog post with media and SEO references
	err = cmi.updateBlogPostReferences(ctx, blogPost.ID, mediaFileIDs, seoMeta)
	if err != nil {
		cmi.logger.WithError(err).Warn("Failed to update blog post references")
	}

	workflow := &ContentWorkflow{
		BlogPostID:   blogPost.ID,
		MediaFileIDs: mediaFileIDs,
		SEOMetaID:    seoMeta.ID,
		Status:       "draft",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	cmi.logger.WithFields(logrus.Fields{
		"blog_post_id": blogPost.ID,
		"media_files":  len(mediaFileIDs),
		"seo_meta_id":  seoMeta.ID,
	}).Info("Content workflow created successfully")

	return workflow, nil
}

// ValidateUserAccess validates user access across all modules
func (cmi *CrossModuleIntegration) ValidateUserAccess(ctx context.Context, userID, tenantID, websiteID uint) (*AccessValidation, error) {
	validation := &AccessValidation{
		UserID:      userID,
		TenantID:    tenantID,
		WebsiteID:   websiteID,
		ValidatedAt: time.Now(),
	}

	// 1. Check if user exists and is active
	user, err := cmi.userService.GetUser(ctx, userID)
	if err != nil {
		validation.UserValid = false
		validation.Errors = append(validation.Errors, "User not found")
		return validation, nil
	}

	if user.Status != "active" {
		validation.UserValid = false
		validation.Errors = append(validation.Errors, "User is not active")
		return validation, nil
	}

	validation.UserValid = true

	// 2. Check tenant access
	hasTenantAccess, err := cmi.userService.HasTenantAccess(ctx, userID, tenantID)
	if err != nil {
		validation.TenantValid = false
		validation.Errors = append(validation.Errors, "Failed to check tenant access")
		return validation, nil
	}

	validation.TenantValid = hasTenantAccess
	if !hasTenantAccess {
		validation.Errors = append(validation.Errors, "User does not have access to tenant")
		return validation, nil
	}

	// 3. Check website access
	website, err := cmi.websiteService.GetWebsite(ctx, websiteID)
	if err != nil {
		validation.WebsiteValid = false
		validation.Errors = append(validation.Errors, "Website not found")
		return validation, nil
	}

	if website.TenantID != tenantID {
		validation.WebsiteValid = false
		validation.Errors = append(validation.Errors, "Website does not belong to tenant")
		return validation, nil
	}

	validation.WebsiteValid = true

	// 4. Check RBAC permissions
	permissions, err := cmi.rbacService.GetUserPermissions(ctx, userID, tenantID)
	if err != nil {
		validation.Errors = append(validation.Errors, "Failed to get user permissions")
		return validation, nil
	}

	validation.Permissions = permissions
	validation.IsValid = validation.UserValid && validation.TenantValid && validation.WebsiteValid

	return validation, nil
}

// SyncModuleData synchronizes data across modules
func (cmi *CrossModuleIntegration) SyncModuleData(ctx context.Context, syncType string) (*SyncResult, error) {
	result := &SyncResult{
		SyncType:   syncType,
		StartedAt:  time.Now(),
		Operations: make(map[string]interface{}),
	}

	switch syncType {
	case "user_tenant_cleanup":
		err := cmi.syncUserTenantCleanup(ctx, result)
		if err != nil {
			result.Errors = append(result.Errors, err.Error())
		}
	case "media_references":
		err := cmi.syncMediaReferences(ctx, result)
		if err != nil {
			result.Errors = append(result.Errors, err.Error())
		}
	case "seo_metadata":
		err := cmi.syncSEOMetadata(ctx, result)
		if err != nil {
			result.Errors = append(result.Errors, err.Error())
		}
	case "permissions":
		err := cmi.syncPermissions(ctx, result)
		if err != nil {
			result.Errors = append(result.Errors, err.Error())
		}
	default:
		return nil, fmt.Errorf("unknown sync type: %s", syncType)
	}

	result.CompletedAt = time.Now()
	result.Duration = result.CompletedAt.Sub(result.StartedAt)
	result.Success = len(result.Errors) == 0

	return result, nil
}

// GetIntegrationHealth checks the health of all module integrations
func (cmi *CrossModuleIntegration) GetIntegrationHealth(ctx context.Context) (*IntegrationHealth, error) {
	health := &IntegrationHealth{
		CheckedAt: time.Now(),
		Modules:   make(map[string]ModuleHealth),
	}

	// Check each module health
	modules := []string{"auth", "user", "tenant", "website", "blog", "media", "rbac", "seo", "onboarding"}

	for _, module := range modules {
		moduleHealth := cmi.checkModuleHealth(ctx, module)
		health.Modules[module] = moduleHealth

		if !moduleHealth.Healthy {
			health.OverallHealthy = false
		}
	}

	// If no modules are unhealthy, set overall health to true
	if health.OverallHealthy {
		health.OverallHealthy = true
	}

	return health, nil
}

// Private helper methods

func (cmi *CrossModuleIntegration) setupWebsitePermissions(ctx context.Context, userID, websiteID uint, role string) error {
	// This would setup specific website permissions based on role
	// For now, just log the operation
	cmi.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"website_id": websiteID,
		"role":       role,
	}).Info("Setting up website permissions")
	return nil
}

func (cmi *CrossModuleIntegration) createSEOForBlogPost(ctx context.Context, blogPost interface{}, seoReq *SEORequest) (*SEOMetadata, error) {
	// Create SEO metadata for the blog post
	seoMeta := &SEOMetadata{
		ID:          1, // This would be generated by the SEO service
		Title:       seoReq.Title,
		Description: seoReq.Description,
		Keywords:    seoReq.Keywords,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	cmi.logger.WithFields(logrus.Fields{
		"seo_title":       seoMeta.Title,
		"seo_description": seoMeta.Description,
	}).Info("SEO metadata created for blog post")

	return seoMeta, nil
}

func (cmi *CrossModuleIntegration) updateBlogPostReferences(ctx context.Context, blogPostID uint, mediaFileIDs []uint, seoMeta *SEOMetadata) error {
	// Update blog post with media and SEO references
	cmi.logger.WithFields(logrus.Fields{
		"blog_post_id": blogPostID,
		"media_files":  len(mediaFileIDs),
		"seo_meta_id":  seoMeta.ID,
	}).Info("Updated blog post references")
	return nil
}

func (cmi *CrossModuleIntegration) syncUserTenantCleanup(ctx context.Context, result *SyncResult) error {
	// Cleanup orphaned user-tenant relationships
	result.Operations["user_tenant_cleanup"] = map[string]interface{}{
		"orphaned_relationships": 0,
		"cleaned_up":             0,
	}
	return nil
}

func (cmi *CrossModuleIntegration) syncMediaReferences(ctx context.Context, result *SyncResult) error {
	// Sync media file references across modules
	result.Operations["media_references"] = map[string]interface{}{
		"total_references":  0,
		"broken_references": 0,
		"fixed_references":  0,
	}
	return nil
}

func (cmi *CrossModuleIntegration) syncSEOMetadata(ctx context.Context, result *SyncResult) error {
	// Sync SEO metadata across content
	result.Operations["seo_metadata"] = map[string]interface{}{
		"total_content": 0,
		"missing_seo":   0,
		"updated_seo":   0,
	}
	return nil
}

func (cmi *CrossModuleIntegration) syncPermissions(ctx context.Context, result *SyncResult) error {
	// Sync permissions across modules
	result.Operations["permissions"] = map[string]interface{}{
		"total_users":          0,
		"permission_conflicts": 0,
		"resolved_conflicts":   0,
	}
	return nil
}

func (cmi *CrossModuleIntegration) checkModuleHealth(ctx context.Context, module string) ModuleHealth {
	health := ModuleHealth{
		Module:    module,
		Healthy:   true,
		CheckedAt: time.Now(),
	}

	// Basic health check for each module
	switch module {
	case "auth":
		health.Details = "Authentication service running"
	case "user":
		health.Details = "User service running"
	case "tenant":
		health.Details = "Tenant service running"
	case "website":
		health.Details = "Website service running"
	case "blog":
		health.Details = "Blog service running"
	case "media":
		health.Details = "Media service running"
	case "rbac":
		health.Details = "RBAC service running"
	case "seo":
		health.Details = "SEO service running"
	case "onboarding":
		health.Details = "Onboarding service running"
	default:
		health.Healthy = false
		health.Details = "Unknown module"
	}

	return health
}

// Supporting types and models
type ContentWorkflowRequest struct {
	BlogPostRequest interface{} `json:"blog_post_request"`
	MediaFileIDs    []uint      `json:"media_file_ids"`
	SEORequest      *SEORequest `json:"seo_request"`
}

type SEORequest struct {
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Keywords    []string `json:"keywords"`
}

type SEOMetadata struct {
	ID          uint      `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Keywords    []string  `json:"keywords"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type AccessValidation struct {
	UserID       uint      `json:"user_id"`
	TenantID     uint      `json:"tenant_id"`
	WebsiteID    uint      `json:"website_id"`
	UserValid    bool      `json:"user_valid"`
	TenantValid  bool      `json:"tenant_valid"`
	WebsiteValid bool      `json:"website_valid"`
	IsValid      bool      `json:"is_valid"`
	Permissions  []string  `json:"permissions"`
	Errors       []string  `json:"errors"`
	ValidatedAt  time.Time `json:"validated_at"`
}

type SyncResult struct {
	SyncType    string                 `json:"sync_type"`
	Success     bool                   `json:"success"`
	StartedAt   time.Time              `json:"started_at"`
	CompletedAt time.Time              `json:"completed_at"`
	Duration    time.Duration          `json:"duration"`
	Operations  map[string]interface{} `json:"operations"`
	Errors      []string               `json:"errors"`
}

type IntegrationHealth struct {
	OverallHealthy bool                    `json:"overall_healthy"`
	CheckedAt      time.Time               `json:"checked_at"`
	Modules        map[string]ModuleHealth `json:"modules"`
}

type ModuleHealth struct {
	Module    string    `json:"module"`
	Healthy   bool      `json:"healthy"`
	Details   string    `json:"details"`
	CheckedAt time.Time `json:"checked_at"`
}
