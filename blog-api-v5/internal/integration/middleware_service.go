package integration

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
)

// MiddlewareService handles cross-module middleware operations
type MiddlewareService struct {
	authService *authservices.AuthService
	rbacService *rbacservices.RBACEngine
	userService *userservices.UserService
	validation  *ValidationService
	logger      *logrus.Logger
}

// NewMiddlewareService creates a new middleware service
func NewMiddlewareService(
	authService *authservices.AuthService,
	rbacService *rbacservices.RBACEngine,
	userService *userservices.UserService,
	validation *ValidationService,
	logger *logrus.Logger,
) *MiddlewareService {
	return &MiddlewareService{
		authService: authService,
		rbacService: rbacService,
		userService: userService,
		validation:  validation,
		logger:      logger,
	}
}

// AuthenticationMiddleware provides authentication for all modules
func (ms *MiddlewareService) AuthenticationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Skip authentication for public endpoints
		if ms.isPublicEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Extract token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			ms.logger.Warn("Missing authorization header")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing authorization header"})
			c.Abort()
			return
		}

		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || strings.ToLower(tokenParts[0]) != "bearer" {
			ms.logger.Warn("Invalid authorization header format")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// Validate token
		claims, err := ms.authService.ValidateToken(c.Request.Context(), token)
		if err != nil {
			ms.logger.WithError(err).Warn("Invalid token")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Get user information
		user, err := ms.userService.GetUser(c.Request.Context(), claims.UserID)
		if err != nil {
			ms.logger.WithError(err).Warn("Failed to get user")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
			c.Abort()
			return
		}

		// Check if user is active
		if user.Status != "active" {
			ms.logger.WithField("user_id", claims.UserID).Warn("User is not active")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User account is not active"})
			c.Abort()
			return
		}

		// Set user context
		c.Set("user_id", claims.UserID)
		c.Set("user", user)
		c.Set("token_claims", claims)

		duration := time.Since(startTime)
		ms.logger.WithFields(logrus.Fields{
			"user_id":  claims.UserID,
			"duration": duration,
		}).Debug("Authentication completed")

		c.Next()
	}
}

// TenantContextMiddleware provides tenant context for all modules
func (ms *MiddlewareService) TenantContextMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Skip for public endpoints
		if ms.isPublicEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Get user ID from context
		userID, exists := c.Get("user_id")
		if !exists {
			ms.logger.Warn("User ID not found in context")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "User context not available"})
			c.Abort()
			return
		}

		// Extract tenant ID from header or URL
		tenantID := ms.extractTenantID(c)
		if tenantID == 0 {
			ms.logger.Warn("Tenant ID not found")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
			c.Abort()
			return
		}

		// Validate user has access to tenant
		hasAccess, err := ms.userService.HasTenantAccess(c.Request.Context(), userID.(uint), tenantID)
		if err != nil {
			ms.logger.WithError(err).Error("Failed to check tenant access")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate tenant access"})
			c.Abort()
			return
		}

		if !hasAccess {
			ms.logger.WithFields(logrus.Fields{
				"user_id":   userID,
				"tenant_id": tenantID,
			}).Warn("User does not have access to tenant")
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to tenant"})
			c.Abort()
			return
		}

		// Create tenant context
		tenantCtx := &pkgcontext.TenantContext{
			TenantID: tenantID,
			UserID:   userID.(uint),
		}

		// Set tenant context in request context
		ctx := pkgcontext.WithTenantContext(c.Request.Context(), tenantCtx)
		c.Request = c.Request.WithContext(ctx)

		// Set tenant context in gin context
		c.Set("tenant_id", tenantID)
		c.Set("tenant_context", tenantCtx)

		duration := time.Since(startTime)
		ms.logger.WithFields(logrus.Fields{
			"user_id":   userID,
			"tenant_id": tenantID,
			"duration":  duration,
		}).Debug("Tenant context established")

		c.Next()
	}
}

// RBACMiddleware provides role-based access control
func (ms *MiddlewareService) RBACMiddleware(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Skip for public endpoints
		if ms.isPublicEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Get user and tenant context
		userID, exists := c.Get("user_id")
		if !exists {
			ms.logger.Warn("User ID not found in context for RBAC check")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "User context not available"})
			c.Abort()
			return
		}

		tenantID, exists := c.Get("tenant_id")
		if !exists {
			ms.logger.Warn("Tenant ID not found in context for RBAC check")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Tenant context not available"})
			c.Abort()
			return
		}

		// Check permission
		hasPermission, err := ms.rbacService.CheckPermission(
			c.Request.Context(),
			userID.(uint),
			permission,
			tenantID.(uint),
		)
		if err != nil {
			ms.logger.WithError(err).Error("Failed to check permission")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check permission"})
			c.Abort()
			return
		}

		if !hasPermission {
			ms.logger.WithFields(logrus.Fields{
				"user_id":    userID,
				"tenant_id":  tenantID,
				"permission": permission,
			}).Warn("Permission denied")
			c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
			c.Abort()
			return
		}

		duration := time.Since(startTime)
		ms.logger.WithFields(logrus.Fields{
			"user_id":    userID,
			"tenant_id":  tenantID,
			"permission": permission,
			"duration":   duration,
		}).Debug("RBAC check completed")

		c.Next()
	}
}

// ValidationMiddleware provides request validation
func (ms *MiddlewareService) ValidationMiddleware(validationType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Create validation data from request
		validationData := map[string]interface{}{
			"method":    c.Request.Method,
			"path":      c.Request.URL.Path,
			"user_id":   c.GetUint("user_id"),
			"tenant_id": c.GetUint("tenant_id"),
			"headers":   c.Request.Header,
			"query":     c.Request.URL.Query(),
		}

		// Validate request
		report, err := ms.validation.ValidateData(c.Request.Context(), validationData, []string{validationType})
		if err != nil {
			ms.logger.WithError(err).Error("Validation failed")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Validation failed"})
			c.Abort()
			return
		}

		// Check if validation passed
		if report.FailedRules > 0 {
			errors := make([]string, 0)
			for _, result := range report.Results {
				if !result.Valid {
					errors = append(errors, result.Errors...)
				}
			}

			ms.logger.WithFields(logrus.Fields{
				"validation_type": validationType,
				"failed_rules":    report.FailedRules,
				"errors":          errors,
			}).Warn("Request validation failed")

			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Request validation failed",
				"details": errors,
			})
			c.Abort()
			return
		}

		duration := time.Since(startTime)
		ms.logger.WithFields(logrus.Fields{
			"validation_type": validationType,
			"duration":        duration,
		}).Debug("Request validation completed")

		c.Next()
	}
}

// AuditMiddleware provides audit logging for all requests
func (ms *MiddlewareService) AuditMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Capture request details
		requestID := ms.generateRequestID()
		c.Set("request_id", requestID)

		// Log request start
		ms.logger.WithFields(logrus.Fields{
			"request_id": requestID,
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"user_id":    c.GetUint("user_id"),
			"tenant_id":  c.GetUint("tenant_id"),
			"user_agent": c.Request.UserAgent(),
			"ip":         c.ClientIP(),
		}).Info("Request started")

		// Process request
		c.Next()

		// Log request completion
		duration := time.Since(startTime)
		status := c.Writer.Status()

		logLevel := logrus.InfoLevel
		if status >= 400 {
			logLevel = logrus.WarnLevel
		}
		if status >= 500 {
			logLevel = logrus.ErrorLevel
		}

		ms.logger.WithFields(logrus.Fields{
			"request_id": requestID,
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"status":     status,
			"duration":   duration,
			"user_id":    c.GetUint("user_id"),
			"tenant_id":  c.GetUint("tenant_id"),
		}).Log(logLevel, "Request completed")
	}
}

// RateLimitMiddleware provides rate limiting across modules
func (ms *MiddlewareService) RateLimitMiddleware(limit int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Simple rate limiting implementation
		// In production, this would use Redis or similar

		key := ms.getRateLimitKey(c)

		// Check rate limit
		allowed, err := ms.checkRateLimit(key, limit, window)
		if err != nil {
			ms.logger.WithError(err).Error("Rate limit check failed")
			c.Next()
			return
		}

		if !allowed {
			ms.logger.WithFields(logrus.Fields{
				"key":    key,
				"limit":  limit,
				"window": window,
			}).Warn("Rate limit exceeded")

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CORSMiddleware handles CORS for cross-module requests
func (ms *MiddlewareService) CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// Set CORS headers
		c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, X-Tenant-ID")

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// HealthCheckMiddleware provides health check endpoints
func (ms *MiddlewareService) HealthCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == "/health" {
			health := map[string]interface{}{
				"status":    "healthy",
				"timestamp": time.Now(),
				"modules": map[string]string{
					"auth":   "healthy",
					"user":   "healthy",
					"tenant": "healthy",
					"rbac":   "healthy",
					"blog":   "healthy",
					"media":  "healthy",
					"seo":    "healthy",
				},
			}

			c.JSON(http.StatusOK, health)
			c.Abort()
			return
		}

		c.Next()
	}
}

// Private helper methods

func (ms *MiddlewareService) isPublicEndpoint(path string) bool {
	publicPaths := []string{
		"/health",
		"/auth/login",
		"/auth/register",
		"/auth/forgot-password",
		"/auth/reset-password",
		"/public/",
	}

	for _, publicPath := range publicPaths {
		if strings.HasPrefix(path, publicPath) {
			return true
		}
	}

	return false
}

func (ms *MiddlewareService) extractTenantID(c *gin.Context) uint {
	// Try to get tenant ID from X-Tenant-ID header
	tenantIDHeader := c.GetHeader("X-Tenant-ID")
	if tenantIDHeader != "" {
		if tenantID, err := strconv.ParseUint(tenantIDHeader, 10, 32); err == nil {
			return uint(tenantID)
		}
	}

	// Try to get tenant ID from URL parameter
	tenantIDParam := c.Param("tenant_id")
	if tenantIDParam != "" {
		if tenantID, err := strconv.ParseUint(tenantIDParam, 10, 32); err == nil {
			return uint(tenantID)
		}
	}

	// Try to get tenant ID from query parameter
	tenantIDQuery := c.Query("tenant_id")
	if tenantIDQuery != "" {
		if tenantID, err := strconv.ParseUint(tenantIDQuery, 10, 32); err == nil {
			return uint(tenantID)
		}
	}

	return 0
}

func (ms *MiddlewareService) generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

func (ms *MiddlewareService) getRateLimitKey(c *gin.Context) string {
	userID := c.GetUint("user_id")
	if userID > 0 {
		return fmt.Sprintf("rate_limit:user:%d", userID)
	}

	return fmt.Sprintf("rate_limit:ip:%s", c.ClientIP())
}

func (ms *MiddlewareService) checkRateLimit(key string, limit int, window time.Duration) (bool, error) {
	// Simple in-memory rate limiting
	// In production, this would use Redis with sliding window

	// For now, just return true (allow all requests)
	return true, nil
}

// GetMiddlewareChain returns a complete middleware chain for a specific module
func (ms *MiddlewareService) GetMiddlewareChain(module string, permissions ...string) []gin.HandlerFunc {
	chain := []gin.HandlerFunc{
		ms.CORSMiddleware(),
		ms.HealthCheckMiddleware(),
		ms.AuditMiddleware(),
		ms.RateLimitMiddleware(100, time.Minute), // 100 requests per minute
		ms.AuthenticationMiddleware(),
		ms.TenantContextMiddleware(),
	}

	// Add RBAC middleware if permissions are specified
	for _, permission := range permissions {
		chain = append(chain, ms.RBACMiddleware(permission))
	}

	// Add module-specific validation
	chain = append(chain, ms.ValidationMiddleware("relationship"))

	return chain
}

// GetPublicMiddlewareChain returns middleware chain for public endpoints
func (ms *MiddlewareService) GetPublicMiddlewareChain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		ms.CORSMiddleware(),
		ms.HealthCheckMiddleware(),
		ms.AuditMiddleware(),
		ms.RateLimitMiddleware(50, time.Minute), // Lower rate limit for public endpoints
	}
}

// TokenClaims represents JWT token claims
type TokenClaims struct {
	UserID    uint   `json:"user_id"`
	TenantID  uint   `json:"tenant_id"`
	Role      string `json:"role"`
	ExpiresAt int64  `json:"exp"`
}
