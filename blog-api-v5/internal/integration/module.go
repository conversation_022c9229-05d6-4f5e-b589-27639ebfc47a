package integration

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"gorm.io/gorm"
)

// IntegrationModule represents the complete integration module
type IntegrationModule struct {
	// Core services
	CrossModuleIntegration *CrossModuleIntegration
	ValidationService      *ValidationService
	MiddlewareService      *MiddlewareService
	TestFramework          *IntegrationTestFramework

	// Dependencies
	db     *gorm.DB
	logger *logrus.Logger
}

// NewIntegrationModule creates a new integration module
func NewIntegrationModule(
	db *gorm.DB,
	logger *logrus.Logger,
	// Module services
	authService *authservices.AuthService,
	userService *userservices.UserService,
	tenantService *tenantservices.TenantService,
	websiteService *websiteservices.WebsiteService,
	blogService *blogservices.BlogPostService,
	mediaService *mediaservices.MediaFileService,
	rbacService *rbacservices.RBACEngine,
	seoService *seoservices.SEOMetaService,
	onboardingService *onboardingservices.OnboardingService,
) *IntegrationModule {

	// Create validation service
	validationService := NewValidationService(logger)

	// Create cross-module integration service
	crossModuleIntegration := NewCrossModuleIntegration(
		authService,
		userService,
		tenantService,
		websiteService,
		blogService,
		mediaService,
		rbacService,
		seoService,
		onboardingService,
		logger,
	)

	// Create middleware service
	middlewareService := NewMiddlewareService(
		authService,
		rbacService,
		userService,
		validationService,
		logger,
	)

	// Create test framework
	testFramework := NewIntegrationTestFramework(
		crossModuleIntegration,
		validationService,
		logger,
	)

	return &IntegrationModule{
		CrossModuleIntegration: crossModuleIntegration,
		ValidationService:      validationService,
		MiddlewareService:      middlewareService,
		TestFramework:          testFramework,
		db:                     db,
		logger:                 logger,
	}
}

// Initialize sets up the integration module
func (im *IntegrationModule) Initialize(ctx context.Context) error {
	startTime := time.Now()

	im.logger.Info("Initializing integration module")

	// Initialize validation rules
	err := im.initializeValidationRules(ctx)
	if err != nil {
		return fmt.Errorf("failed to initialize validation rules: %w", err)
	}

	// Initialize integration health monitoring
	err = im.initializeHealthMonitoring(ctx)
	if err != nil {
		return fmt.Errorf("failed to initialize health monitoring: %w", err)
	}

	// Initialize test framework
	err = im.initializeTestFramework(ctx)
	if err != nil {
		return fmt.Errorf("failed to initialize test framework: %w", err)
	}

	// Run initial integration health check
	health, err := im.CrossModuleIntegration.GetIntegrationHealth(ctx)
	if err != nil {
		im.logger.WithError(err).Warn("Failed to get integration health")
	} else {
		im.logger.WithFields(logrus.Fields{
			"overall_healthy": health.OverallHealthy,
			"modules_count":   len(health.Modules),
		}).Info("Integration health check completed")
	}

	duration := time.Since(startTime)
	im.logger.WithField("duration", duration).Info("Integration module initialized successfully")

	return nil
}

// RegisterRoutes registers integration endpoints
func (im *IntegrationModule) RegisterRoutes(router *gin.Engine) {
	// Create integration routes group
	integrationGroup := router.Group("/api/v1/integration")

	// Apply middleware
	integrationGroup.Use(im.MiddlewareService.GetMiddlewareChain("integration", "integration:read")...)

	// Health and monitoring endpoints
	integrationGroup.GET("/health", im.handleGetHealth)
	integrationGroup.GET("/metrics", im.handleGetMetrics)

	// Validation endpoints
	integrationGroup.POST("/validate", im.handleValidateData)
	integrationGroup.GET("/validation/rules", im.handleGetValidationRules)
	integrationGroup.GET("/validation/metrics", im.handleGetValidationMetrics)

	// Workflow endpoints
	integrationGroup.POST("/workflows/user-tenant", im.handleSetupUserTenantWorkflow)
	integrationGroup.POST("/workflows/content", im.handleCreateContentWorkflow)
	integrationGroup.POST("/workflows/validate-access", im.handleValidateUserAccess)

	// Sync endpoints
	integrationGroup.POST("/sync/:type", im.handleSyncModuleData)

	// Test endpoints (only in development)
	if gin.Mode() == gin.DebugMode {
		testGroup := integrationGroup.Group("/test")
		testGroup.Use(im.MiddlewareService.RBACMiddleware("integration:test"))

		testGroup.GET("/suites", im.handleGetTestSuites)
		testGroup.POST("/suites/:suite_name/run", im.handleRunTestSuite)
		testGroup.POST("/tests/:test_name/run", im.handleRunSingleTest)
		testGroup.GET("/metrics", im.handleGetTestMetrics)
	}
}

// HTTP Handlers

func (im *IntegrationModule) handleGetHealth(c *gin.Context) {
	health, err := im.CrossModuleIntegration.GetIntegrationHealth(c.Request.Context())
	if err != nil {
		im.logger.WithError(err).Error("Failed to get integration health")
		c.JSON(500, gin.H{"error": "Failed to get integration health"})
		return
	}

	c.JSON(200, health)
}

func (im *IntegrationModule) handleGetMetrics(c *gin.Context) {
	metrics := map[string]interface{}{
		"integration_module": "active",
		"timestamp":          time.Now(),
		"modules_count":      9,
		"validation_rules":   len(im.ValidationService.GetValidationRules()),
	}

	c.JSON(200, metrics)
}

func (im *IntegrationModule) handleValidateData(c *gin.Context) {
	var request struct {
		Data      interface{} `json:"data"`
		RuleTypes []string    `json:"rule_types"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}

	report, err := im.ValidationService.ValidateData(c.Request.Context(), request.Data, request.RuleTypes)
	if err != nil {
		im.logger.WithError(err).Error("Failed to validate data")
		c.JSON(500, gin.H{"error": "Validation failed"})
		return
	}

	c.JSON(200, report)
}

func (im *IntegrationModule) handleGetValidationRules(c *gin.Context) {
	rules := im.ValidationService.GetValidationRules()
	c.JSON(200, gin.H{"rules": rules})
}

func (im *IntegrationModule) handleGetValidationMetrics(c *gin.Context) {
	metrics, err := im.ValidationService.GetValidationMetrics(c.Request.Context())
	if err != nil {
		im.logger.WithError(err).Error("Failed to get validation metrics")
		c.JSON(500, gin.H{"error": "Failed to get validation metrics"})
		return
	}

	c.JSON(200, metrics)
}

func (im *IntegrationModule) handleSetupUserTenantWorkflow(c *gin.Context) {
	var request struct {
		UserID    uint   `json:"user_id" binding:"required"`
		TenantID  uint   `json:"tenant_id" binding:"required"`
		WebsiteID uint   `json:"website_id" binding:"required"`
		Role      string `json:"role" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}

	workflow, err := im.CrossModuleIntegration.SetupUserTenantWebsiteWorkflow(
		c.Request.Context(),
		request.UserID,
		request.TenantID,
		request.WebsiteID,
		request.Role,
	)
	if err != nil {
		im.logger.WithError(err).Error("Failed to setup user-tenant workflow")
		c.JSON(500, gin.H{"error": "Failed to setup workflow"})
		return
	}

	c.JSON(200, workflow)
}

func (im *IntegrationModule) handleCreateContentWorkflow(c *gin.Context) {
	var request ContentWorkflowRequest

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}

	workflow, err := im.CrossModuleIntegration.CreateContentWorkflow(c.Request.Context(), &request)
	if err != nil {
		im.logger.WithError(err).Error("Failed to create content workflow")
		c.JSON(500, gin.H{"error": "Failed to create content workflow"})
		return
	}

	c.JSON(200, workflow)
}

func (im *IntegrationModule) handleValidateUserAccess(c *gin.Context) {
	var request struct {
		UserID    uint `json:"user_id" binding:"required"`
		TenantID  uint `json:"tenant_id" binding:"required"`
		WebsiteID uint `json:"website_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}

	validation, err := im.CrossModuleIntegration.ValidateUserAccess(
		c.Request.Context(),
		request.UserID,
		request.TenantID,
		request.WebsiteID,
	)
	if err != nil {
		im.logger.WithError(err).Error("Failed to validate user access")
		c.JSON(500, gin.H{"error": "Failed to validate user access"})
		return
	}

	c.JSON(200, validation)
}

func (im *IntegrationModule) handleSyncModuleData(c *gin.Context) {
	syncType := c.Param("type")

	result, err := im.CrossModuleIntegration.SyncModuleData(c.Request.Context(), syncType)
	if err != nil {
		im.logger.WithError(err).Error("Failed to sync module data")
		c.JSON(500, gin.H{"error": "Failed to sync module data"})
		return
	}

	c.JSON(200, result)
}

// Test handlers (development only)

func (im *IntegrationModule) handleGetTestSuites(c *gin.Context) {
	suites := im.TestFramework.GetTestSuites()
	c.JSON(200, gin.H{"suites": suites})
}

func (im *IntegrationModule) handleRunTestSuite(c *gin.Context) {
	suiteName := c.Param("suite_name")

	result, err := im.TestFramework.RunTestSuite(c.Request.Context(), suiteName)
	if err != nil {
		im.logger.WithError(err).Error("Failed to run test suite")
		c.JSON(500, gin.H{"error": "Failed to run test suite"})
		return
	}

	c.JSON(200, result)
}

func (im *IntegrationModule) handleRunSingleTest(c *gin.Context) {
	testName := c.Param("test_name")

	result, err := im.TestFramework.RunSingleTest(c.Request.Context(), testName)
	if err != nil {
		im.logger.WithError(err).Error("Failed to run single test")
		c.JSON(500, gin.H{"error": "Failed to run single test"})
		return
	}

	c.JSON(200, result)
}

func (im *IntegrationModule) handleGetTestMetrics(c *gin.Context) {
	metrics, err := im.TestFramework.GetTestMetrics(c.Request.Context())
	if err != nil {
		im.logger.WithError(err).Error("Failed to get test metrics")
		c.JSON(500, gin.H{"error": "Failed to get test metrics"})
		return
	}

	c.JSON(200, metrics)
}

// Private helper methods

func (im *IntegrationModule) initializeValidationRules(ctx context.Context) error {
	rules := im.ValidationService.GetValidationRules()

	im.logger.WithField("rules_count", len(rules)).Info("Validation rules initialized")

	return nil
}

func (im *IntegrationModule) initializeHealthMonitoring(ctx context.Context) error {
	// Initialize health monitoring
	im.logger.Info("Health monitoring initialized")

	return nil
}

func (im *IntegrationModule) initializeTestFramework(ctx context.Context) error {
	// Initialize test framework
	suites := im.TestFramework.GetTestSuites()

	im.logger.WithField("suites_count", len(suites)).Info("Test framework initialized")

	return nil
}

// Shutdown gracefully shuts down the integration module
func (im *IntegrationModule) Shutdown(ctx context.Context) error {
	im.logger.Info("Shutting down integration module")

	// Perform any cleanup tasks

	im.logger.Info("Integration module shutdown completed")

	return nil
}

// GetModuleInfo returns information about the integration module
func (im *IntegrationModule) GetModuleInfo() map[string]interface{} {
	return map[string]interface{}{
		"name":        "integration",
		"version":     "1.0.0",
		"description": "Cross-module integration and data flow management",
		"features": []string{
			"cross_module_integration",
			"data_validation",
			"workflow_automation",
			"integration_testing",
			"middleware_services",
			"health_monitoring",
		},
		"dependencies": []string{
			"auth",
			"user",
			"tenant",
			"website",
			"blog",
			"media",
			"rbac",
			"seo",
			"onboarding",
		},
	}
}
