package integration

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
)

// ValidationService handles cross-module data validation
type ValidationService struct {
	logger *logrus.Logger
}

// NewValidationService creates a new validation service
func NewValidationService(logger *logrus.Logger) *ValidationService {
	return &ValidationService{
		logger: logger,
	}
}

// ValidationRule represents a validation rule
type ValidationRule struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Module      string `json:"module"`
	Description string `json:"description"`
	Validator   func(ctx context.Context, data interface{}) *ValidationResult
}

// ValidationResult represents the result of a validation
type ValidationResult struct {
	Valid       bool      `json:"valid"`
	RuleName    string    `json:"rule_name"`
	Message     string    `json:"message"`
	Errors      []string  `json:"errors"`
	Warnings    []string  `json:"warnings"`
	ValidatedAt time.Time `json:"validated_at"`
}

// ValidationReport represents a complete validation report
type ValidationReport struct {
	TotalRules   int                 `json:"total_rules"`
	PassedRules  int                 `json:"passed_rules"`
	FailedRules  int                 `json:"failed_rules"`
	WarningRules int                 `json:"warning_rules"`
	Results      []*ValidationResult `json:"results"`
	GeneratedAt  time.Time           `json:"generated_at"`
	Duration     time.Duration       `json:"duration"`
}

// GetValidationRules returns all validation rules
func (vs *ValidationService) GetValidationRules() []*ValidationRule {
	rules := []*ValidationRule{
		// User-Tenant relationship validation
		{
			Name:        "user_tenant_consistency",
			Type:        "relationship",
			Module:      "user",
			Description: "Validates user-tenant relationship consistency",
			Validator:   vs.validateUserTenantConsistency,
		},

		// Tenant-Website relationship validation
		{
			Name:        "tenant_website_consistency",
			Type:        "relationship",
			Module:      "tenant",
			Description: "Validates tenant-website relationship consistency",
			Validator:   vs.validateTenantWebsiteConsistency,
		},

		// Blog-Media relationship validation
		{
			Name:        "blog_media_references",
			Type:        "reference",
			Module:      "blog",
			Description: "Validates blog post media references",
			Validator:   vs.validateBlogMediaReferences,
		},

		// SEO-Content relationship validation
		{
			Name:        "seo_content_consistency",
			Type:        "reference",
			Module:      "seo",
			Description: "Validates SEO metadata consistency with content",
			Validator:   vs.validateSEOContentConsistency,
		},

		// RBAC permission consistency validation
		{
			Name:        "rbac_permission_consistency",
			Type:        "permission",
			Module:      "rbac",
			Description: "Validates RBAC permission consistency",
			Validator:   vs.validateRBACPermissionConsistency,
		},

		// Auth session validation
		{
			Name:        "auth_session_validity",
			Type:        "session",
			Module:      "auth",
			Description: "Validates authentication session validity",
			Validator:   vs.validateAuthSessionValidity,
		},

		// Multi-tenant data isolation validation
		{
			Name:        "tenant_data_isolation",
			Type:        "isolation",
			Module:      "tenant",
			Description: "Validates multi-tenant data isolation",
			Validator:   vs.validateTenantDataIsolation,
		},

		// Cross-module status consistency
		{
			Name:        "cross_module_status",
			Type:        "status",
			Module:      "integration",
			Description: "Validates status consistency across modules",
			Validator:   vs.validateCrossModuleStatus,
		},
	}

	return rules
}

// ValidateData validates data against all applicable rules
func (vs *ValidationService) ValidateData(ctx context.Context, data interface{}, ruleTypes []string) (*ValidationReport, error) {
	startTime := time.Now()

	report := &ValidationReport{
		Results:     make([]*ValidationResult, 0),
		GeneratedAt: startTime,
	}

	rules := vs.GetValidationRules()

	// Filter rules by type if specified
	if len(ruleTypes) > 0 {
		filteredRules := make([]*ValidationRule, 0)
		for _, rule := range rules {
			for _, ruleType := range ruleTypes {
				if rule.Type == ruleType {
					filteredRules = append(filteredRules, rule)
					break
				}
			}
		}
		rules = filteredRules
	}

	report.TotalRules = len(rules)

	// Execute validation rules
	for _, rule := range rules {
		result := rule.Validator(ctx, data)
		result.RuleName = rule.Name
		result.ValidatedAt = time.Now()

		report.Results = append(report.Results, result)

		if result.Valid {
			report.PassedRules++
		} else {
			report.FailedRules++
		}

		if len(result.Warnings) > 0 {
			report.WarningRules++
		}
	}

	report.Duration = time.Since(startTime)

	vs.logger.WithFields(logrus.Fields{
		"total_rules":   report.TotalRules,
		"passed_rules":  report.PassedRules,
		"failed_rules":  report.FailedRules,
		"warning_rules": report.WarningRules,
		"duration":      report.Duration,
	}).Info("Validation completed")

	return report, nil
}

// ValidateSpecificRule validates data against a specific rule
func (vs *ValidationService) ValidateSpecificRule(ctx context.Context, ruleName string, data interface{}) (*ValidationResult, error) {
	rules := vs.GetValidationRules()

	for _, rule := range rules {
		if rule.Name == ruleName {
			result := rule.Validator(ctx, data)
			result.RuleName = ruleName
			result.ValidatedAt = time.Now()
			return result, nil
		}
	}

	return nil, fmt.Errorf("validation rule not found: %s", ruleName)
}

// Validation rule implementations

func (vs *ValidationService) validateUserTenantConsistency(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "User-tenant relationship is consistent",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Check if user has valid tenant memberships
	// This would typically query the database to check relationships

	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		result.Valid = false
		result.Errors = append(result.Errors, "No tenant context available")
		return result
	}

	// Validate tenant membership exists and is active
	if tenantCtx.TenantID == 0 {
		result.Valid = false
		result.Errors = append(result.Errors, "Invalid tenant ID in context")
		return result
	}

	// Additional validation logic would go here

	return result
}

func (vs *ValidationService) validateTenantWebsiteConsistency(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "Tenant-website relationship is consistent",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate that websites belong to the correct tenant
	// This would check the database for orphaned websites

	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		result.Valid = false
		result.Errors = append(result.Errors, "No tenant context available")
		return result
	}

	// Check if website data is provided
	if websiteData, ok := data.(map[string]interface{}); ok {
		if websiteID, exists := websiteData["website_id"]; exists {
			if websiteID == nil || websiteID == 0 {
				result.Valid = false
				result.Errors = append(result.Errors, "Invalid website ID")
				return result
			}
		}
	}

	return result
}

func (vs *ValidationService) validateBlogMediaReferences(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "Blog-media references are valid",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate that blog posts reference existing media files
	// Check for orphaned media references

	if blogData, ok := data.(map[string]interface{}); ok {
		if mediaRefs, exists := blogData["media_references"]; exists {
			if refs, ok := mediaRefs.([]interface{}); ok {
				for _, ref := range refs {
					if ref == nil {
						result.Warnings = append(result.Warnings, "Found null media reference")
					}
				}
			}
		}
	}

	return result
}

func (vs *ValidationService) validateSEOContentConsistency(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "SEO metadata is consistent with content",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate that SEO metadata exists for published content
	// Check for missing meta descriptions, titles, etc.

	if seoData, ok := data.(map[string]interface{}); ok {
		if title, exists := seoData["title"]; exists {
			if title == nil || title == "" {
				result.Warnings = append(result.Warnings, "Missing SEO title")
			}
		}

		if description, exists := seoData["description"]; exists {
			if description == nil || description == "" {
				result.Warnings = append(result.Warnings, "Missing SEO description")
			}
		}
	}

	return result
}

func (vs *ValidationService) validateRBACPermissionConsistency(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "RBAC permissions are consistent",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate that user permissions are consistent with roles
	// Check for conflicting permissions

	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		result.Valid = false
		result.Errors = append(result.Errors, "No tenant context for RBAC validation")
		return result
	}

	// Check if user has valid permissions for tenant
	if tenantCtx.UserID == 0 {
		result.Valid = false
		result.Errors = append(result.Errors, "Invalid user ID in context")
		return result
	}

	return result
}

func (vs *ValidationService) validateAuthSessionValidity(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "Authentication session is valid",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate that sessions are not expired
	// Check for valid JWT tokens

	if authData, ok := data.(map[string]interface{}); ok {
		if expiry, exists := authData["expires_at"]; exists {
			if expiryTime, ok := expiry.(time.Time); ok {
				if expiryTime.Before(time.Now()) {
					result.Valid = false
					result.Errors = append(result.Errors, "Session has expired")
					return result
				}
			}
		}
	}

	return result
}

func (vs *ValidationService) validateTenantDataIsolation(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "Tenant data isolation is maintained",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate that data is properly isolated by tenant
	// Check for cross-tenant data access

	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		result.Valid = false
		result.Errors = append(result.Errors, "No tenant context available for isolation check")
		return result
	}

	// Check if data belongs to the current tenant
	if dataMap, ok := data.(map[string]interface{}); ok {
		if tenantID, exists := dataMap["tenant_id"]; exists {
			if tid, ok := tenantID.(uint); ok {
				if tid != tenantCtx.TenantID {
					result.Valid = false
					result.Errors = append(result.Errors, "Data belongs to different tenant")
					return result
				}
			}
		}
	}

	return result
}

func (vs *ValidationService) validateCrossModuleStatus(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "Cross-module status is consistent",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate that entity statuses are consistent across modules
	// Check for inconsistent states

	if statusData, ok := data.(map[string]interface{}); ok {
		if status, exists := statusData["status"]; exists {
			if status == nil || status == "" {
				result.Warnings = append(result.Warnings, "Missing status field")
			}

			// Check for invalid status values
			if statusStr, ok := status.(string); ok {
				validStatuses := []string{"active", "inactive", "pending", "deleted", "draft", "published"}
				isValid := false
				for _, validStatus := range validStatuses {
					if statusStr == validStatus {
						isValid = true
						break
					}
				}
				if !isValid {
					result.Warnings = append(result.Warnings, fmt.Sprintf("Unknown status: %s", statusStr))
				}
			}
		}
	}

	return result
}

// ValidateComplexWorkflow validates complex multi-module workflows
func (vs *ValidationService) ValidateComplexWorkflow(ctx context.Context, workflowType string, data interface{}) (*ValidationResult, error) {
	result := &ValidationResult{
		Valid:       true,
		Message:     fmt.Sprintf("Workflow validation for %s completed", workflowType),
		Errors:      make([]string, 0),
		Warnings:    make([]string, 0),
		ValidatedAt: time.Now(),
	}

	switch workflowType {
	case "user_onboarding":
		return vs.validateUserOnboardingWorkflow(ctx, data)
	case "content_publishing":
		return vs.validateContentPublishingWorkflow(ctx, data)
	case "tenant_setup":
		return vs.validateTenantSetupWorkflow(ctx, data)
	default:
		result.Valid = false
		result.Errors = append(result.Errors, fmt.Sprintf("Unknown workflow type: %s", workflowType))
	}

	return result, nil
}

func (vs *ValidationService) validateUserOnboardingWorkflow(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "User onboarding workflow is valid",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate complete user onboarding workflow
	// Check user creation, tenant assignment, role assignment, etc.

	return result
}

func (vs *ValidationService) validateContentPublishingWorkflow(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "Content publishing workflow is valid",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate content publishing workflow
	// Check blog post creation, media association, SEO metadata, etc.

	return result
}

func (vs *ValidationService) validateTenantSetupWorkflow(ctx context.Context, data interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Message:  "Tenant setup workflow is valid",
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// Validate tenant setup workflow
	// Check tenant creation, website setup, initial user assignment, etc.

	return result
}

// GetValidationMetrics returns metrics about validation performance
func (vs *ValidationService) GetValidationMetrics(ctx context.Context) (*ValidationMetrics, error) {
	metrics := &ValidationMetrics{
		TotalRules:        len(vs.GetValidationRules()),
		AvgValidationTime: time.Millisecond * 50, // This would be calculated from historical data
		ValidationCount:   1000,                  // This would come from metrics store
		FailureRate:       0.02,                  // 2% failure rate
		GeneratedAt:       time.Now(),
	}

	return metrics, nil
}

// ValidationMetrics represents validation performance metrics
type ValidationMetrics struct {
	TotalRules        int           `json:"total_rules"`
	AvgValidationTime time.Duration `json:"avg_validation_time"`
	ValidationCount   int           `json:"validation_count"`
	FailureRate       float64       `json:"failure_rate"`
	GeneratedAt       time.Time     `json:"generated_at"`
}
