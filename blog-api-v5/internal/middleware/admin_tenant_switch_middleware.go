package middleware

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdminTenantSwitchMiddleware allows superadmins to switch tenant context
// This middleware should be used AFTER RequireSuperAdmin middleware
func AdminTenantSwitchMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if user is superadmin (set by RequireSuperAdmin middleware)
		superadminUserID, exists := c.Get("superadmin_user_id")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Superadmin access required for tenant switching",
			})
			c.Abort()
			return
		}

		// Check for X-Admin-Tenant-ID header
		adminTenantIDHeader := c.<PERSON>eader("X-Admin-Tenant-ID")
		if adminTenantIDHeader != "" {
			// Parse tenant ID
			tenantID, err := strconv.ParseUint(adminTenantIDHeader, 10, 32)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": "Invalid X-Admin-Tenant-ID header value",
				})
				c.Abort()
				return
			}

			// Set the tenant context
			c.Set("tenant_id", uint(tenantID))
			c.Set("admin_switched_tenant", true)
			c.Set("original_user_id", superadminUserID)

			// Log the tenant switch for audit purposes
			c.Set("audit_action", "admin_tenant_switch")
			c.Set("audit_tenant_id", uint(tenantID))
		}

		c.Next()
	}
}

// RequireAdminOrTenantContext ensures either admin access or valid tenant context
// This is useful for endpoints that can be accessed by both admins and tenant users
func RequireAdminOrTenantContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if user is superadmin
		_, isSuperadmin := c.Get("superadmin_user_id")
		if isSuperadmin {
			c.Next()
			return
		}

		// Otherwise, require tenant context
		tenantID, exists := c.Get("tenant_id")
		if !exists || tenantID == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Tenant context required",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}