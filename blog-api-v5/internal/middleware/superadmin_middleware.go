package middleware

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

)

// RequireSuperAdmin middleware ensures the user has superadmin privileges
func RequireSuperAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by JWT middleware)
		userIDInterface, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not found in context",
			})
			c.Abort()
			return
		}

		// Type assert to uint
		userID, ok := userIDInterface.(uint)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid user ID in context",
			})
			c.Abort()
			return
		}

		// Check if user has superadmin permission
		// This assumes there's a permission check mechanism in context
		// You can implement this based on your RBAC system
		
		// Option 1: Check via permission function in context
		hasPermissionFunc, exists := c.Get("has_permission")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Permission system not available",
			})
			c.Abort()
			return
		}

		checkPermission, ok := hasPermissionFunc.(func(string) bool)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid permission checker",
			})
			c.Abort()
			return
		}

		// Check for superadmin permission
		if !checkPermission("superadmin.access") {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Superadmin access required",
			})
			c.Abort()
			return
		}

		// Store user ID for use in handlers
		c.Set("superadmin_user_id", userID)
		c.Next()
	}
}

// RequireSuperAdminOrSelf middleware allows superadmin or the user themselves
func RequireSuperAdminOrSelf() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context
		userIDInterface, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not found in context",
			})
			c.Abort()
			return
		}

		userID, ok := userIDInterface.(uint)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid user ID in context",
			})
			c.Abort()
			return
		}

		// Check if user has superadmin permission
		hasPermissionFunc, exists := c.Get("has_permission")
		if exists {
			if checkPermission, ok := hasPermissionFunc.(func(string) bool); ok {
				if checkPermission("superadmin.access") {
					c.Next()
					return
				}
			}
		}

		// Check if user is accessing their own resource
		targetUserID := c.Param("id")
		if targetUserID == "" {
			targetUserID = c.Param("user_id")
		}
		if targetUserID == "" {
			targetUserID = c.Param("userId")
		}

		// Convert userID to string for comparison
		if targetUserID != "" && targetUserID == fmt.Sprintf("%d", userID) {
			c.Next()
			return
		}

		c.JSON(http.StatusForbidden, gin.H{
			"error": "Access denied. Superadmin or owner access required",
		})
		c.Abort()
	}
}