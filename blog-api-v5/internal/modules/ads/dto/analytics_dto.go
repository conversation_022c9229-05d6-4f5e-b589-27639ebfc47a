package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// AnalyticsCreateRequest represents the request to create analytics
// @Description Request structure for creating analytics data
type AnalyticsCreateRequest struct {
	TenantID         uint                        `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID  uint                        `json:"advertisement_id" validate:"required" example:"1"`
	AnalyticsDate    time.Time                   `json:"analytics_date" validate:"required" example:"2023-07-15T00:00:00Z"`
	ImpressionsCount int                         `json:"impressions_count" validate:"min=0" example:"1000"`
	ClicksCount      int                         `json:"clicks_count" validate:"min=0" example:"25"`
	CostPerClick     float64                     `json:"cost_per_click" validate:"min=0" example:"0.50"`
	Revenue          float64                     `json:"revenue" validate:"min=0" example:"12.50"`
	DeviceBreakdown  *models.DeviceBreakdownData `json:"device_breakdown,omitempty"`
	PageBreakdown    *models.PageBreakdownData   `json:"page_breakdown,omitempty"`
}

// AnalyticsUpdateRequest represents the request to update analytics
// @Description Request structure for updating analytics data
type AnalyticsUpdateRequest struct {
	ImpressionsCount *int                        `json:"impressions_count,omitempty" validate:"omitempty,min=0" example:"1200"`
	ClicksCount      *int                        `json:"clicks_count,omitempty" validate:"omitempty,min=0" example:"30"`
	CostPerClick     *float64                    `json:"cost_per_click,omitempty" validate:"omitempty,min=0" example:"0.45"`
	Revenue          *float64                    `json:"revenue,omitempty" validate:"omitempty,min=0" example:"13.50"`
	DeviceBreakdown  *models.DeviceBreakdownData `json:"device_breakdown,omitempty"`
	PageBreakdown    *models.PageBreakdownData   `json:"page_breakdown,omitempty"`
}

// AnalyticsResponse represents the response structure for analytics
// @Description Response structure for analytics data
type AnalyticsResponse struct {
	ID               uint                        `json:"id" example:"1"`
	TenantID         uint                        `json:"tenant_id" example:"1"`
	AdvertisementID  uint                        `json:"advertisement_id" example:"1"`
	AnalyticsDate    time.Time                   `json:"analytics_date" example:"2023-07-15T00:00:00Z"`
	ImpressionsCount int                         `json:"impressions_count" example:"1000"`
	ClicksCount      int                         `json:"clicks_count" example:"25"`
	CTRRate          float64                     `json:"ctr_rate" example:"0.025"`
	CostPerClick     float64                     `json:"cost_per_click" example:"0.50"`
	Revenue          float64                     `json:"revenue" example:"12.50"`
	DeviceBreakdown  *models.DeviceBreakdownData `json:"device_breakdown,omitempty"`
	PageBreakdown    *models.PageBreakdownData   `json:"page_breakdown,omitempty"`
	CreatedAt        time.Time                   `json:"created_at" example:"2023-07-15T12:00:00Z"`
	UpdatedAt        time.Time                   `json:"updated_at" example:"2023-07-15T12:00:00Z"`
}

// AnalyticsListFilter represents filter options for listing analytics
// @Description Filter options for listing analytics data
type AnalyticsListFilter struct {
	TenantID        uint                         `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID *uint                        `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint                        `json:"campaign_id,omitempty" example:"1"`
	StartDate       *time.Time                   `json:"start_date,omitempty" example:"2023-07-01T00:00:00Z"`
	EndDate         *time.Time                   `json:"end_date,omitempty" example:"2023-07-31T23:59:59Z"`
	Granularity     string                       `json:"granularity,omitempty" validate:"omitempty,oneof=daily weekly monthly" example:"daily"`
	Pagination      *pagination.CursorPagination `json:"pagination,omitempty"`
}

// AnalyticsListResponse represents the response for listing analytics
// @Description Response structure for analytics listing
type AnalyticsListResponse struct {
	Analytics  []AnalyticsResponse        `json:"analytics"`
	Pagination *pagination.CursorResponse `json:"pagination,omitempty"`
}

// AnalyticsSummaryRequest represents a request for analytics summary
// @Description Request structure for retrieving analytics summary
type AnalyticsSummaryRequest struct {
	TenantID        uint       `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID       uint       `json:"website_id" validate:"required" example:"1"`
	AdvertisementID *uint      `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint      `json:"campaign_id,omitempty" example:"1"`
	StartDate       *time.Time `json:"start_date,omitempty" example:"2023-07-01T00:00:00Z"`
	EndDate         *time.Time `json:"end_date,omitempty" example:"2023-07-31T23:59:59Z"`
	Granularity     string     `json:"granularity,omitempty" validate:"omitempty,oneof=daily weekly monthly" example:"daily"`
}

// AnalyticsSummaryResponse represents the response for analytics summary
// @Description Response structure for analytics summary
type AnalyticsSummaryResponse struct {
	TotalImpressions int     `json:"total_impressions" example:"50000"`
	TotalClicks      int     `json:"total_clicks" example:"1250"`
	CTRRate          float64 `json:"ctr_rate" example:"0.025"`
	TotalRevenue     float64 `json:"total_revenue" example:"625.00"`
	AverageCPC       float64 `json:"average_cpc" example:"0.50"`
	DateRange        struct {
		StartDate time.Time `json:"start_date" example:"2023-07-01T00:00:00Z"`
		EndDate   time.Time `json:"end_date" example:"2023-07-31T23:59:59Z"`
	} `json:"date_range"`
}

// CampaignAnalyticsRequest represents the request to get campaign analytics
// @Description Request structure for retrieving campaign analytics
type CampaignAnalyticsRequest struct {
	TenantID    uint       `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID   uint       `json:"website_id" validate:"required" example:"1"`
	CampaignID  *uint      `json:"campaign_id,omitempty" example:"1"`
	StartDate   *time.Time `json:"start_date,omitempty" example:"2023-07-01T00:00:00Z"`
	EndDate     *time.Time `json:"end_date,omitempty" example:"2023-07-31T23:59:59Z"`
	Granularity string     `json:"granularity,omitempty" validate:"omitempty,oneof=daily weekly monthly" example:"daily"`
}

// CampaignAnalyticsResponse represents the response structure for campaign analytics
// @Description Response structure for campaign analytics
type CampaignAnalyticsResponse struct {
	CampaignID       uint                            `json:"campaign_id" example:"1"`
	CampaignName     string                          `json:"campaign_name" example:"Summer Sale Campaign"`
	DateRange        models.DateRange                `json:"date_range"`
	Summary          models.AnalyticsSummary         `json:"summary"`
	DailyData        []models.DailyAnalytics         `json:"daily_data,omitempty"`
	WeeklyData       []models.WeeklyAnalytics        `json:"weekly_data,omitempty"`
	MonthlyData      []models.MonthlyAnalytics       `json:"monthly_data,omitempty"`
	DeviceBreakdown  *models.DeviceBreakdownData     `json:"device_breakdown,omitempty"`
	PageBreakdown    *models.PageBreakdownData       `json:"page_breakdown,omitempty"`
	TopPerformingAds []models.AdvertisementAnalytics `json:"top_performing_ads,omitempty"`
}

// AdvertisementAnalyticsRequest represents a request for advertisement analytics
// @Description Request structure for retrieving advertisement analytics
type AdvertisementAnalyticsRequest struct {
	TenantID         uint       `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID        uint       `json:"website_id" validate:"required" example:"1"`
	AdvertisementID  uint       `json:"advertisement_id" validate:"required" example:"1"`
	StartDate        *time.Time `json:"start_date,omitempty" example:"2023-07-01T00:00:00Z"`
	EndDate          *time.Time `json:"end_date,omitempty" example:"2023-07-31T23:59:59Z"`
	Granularity      string     `json:"granularity,omitempty" validate:"omitempty,oneof=daily weekly monthly" example:"daily"`
	IncludeBreakdown bool       `json:"include_breakdown,omitempty" example:"true"`
}

// AdvertisementAnalyticsResponse represents the response for advertisement analytics
// @Description Response structure for advertisement analytics
type AdvertisementAnalyticsResponse struct {
	AdvertisementID   uint                        `json:"advertisement_id" example:"1"`
	AdvertisementName string                      `json:"advertisement_name" example:"Summer Banner Ad"`
	CampaignID        uint                        `json:"campaign_id" example:"1"`
	CampaignName      string                      `json:"campaign_name" example:"Summer Sale Campaign"`
	DateRange         models.DateRange            `json:"date_range"`
	Summary           models.AnalyticsSummary     `json:"summary"`
	DailyData         []models.DailyAnalytics     `json:"daily_data,omitempty"`
	WeeklyData        []models.WeeklyAnalytics    `json:"weekly_data,omitempty"`
	MonthlyData       []models.MonthlyAnalytics   `json:"monthly_data,omitempty"`
	DeviceBreakdown   *models.DeviceBreakdownData `json:"device_breakdown,omitempty"`
	PageBreakdown     *models.PageBreakdownData   `json:"page_breakdown,omitempty"`
}

// AnalyticsComparisonRequest represents a request for analytics comparison
// @Description Request structure for comparing analytics between periods or entities
type AnalyticsComparisonRequest struct {
	TenantID            uint       `json:"tenant_id" validate:"required" example:"1"`
	EntityType          string     `json:"entity_type" validate:"required,oneof=campaign advertisement" example:"campaign"`
	EntityIDs           []uint     `json:"entity_ids" validate:"required,min=1,max=5" example:"1,2"`
	StartDate           time.Time  `json:"start_date" validate:"required" example:"2023-07-01T00:00:00Z"`
	EndDate             time.Time  `json:"end_date" validate:"required" example:"2023-07-31T23:59:59Z"`
	ComparisonStartDate *time.Time `json:"comparison_start_date,omitempty" example:"2023-06-01T00:00:00Z"`
	ComparisonEndDate   *time.Time `json:"comparison_end_date,omitempty" example:"2023-06-30T23:59:59Z"`
	Granularity         string     `json:"granularity,omitempty" validate:"omitempty,oneof=daily weekly monthly" example:"daily"`
}

// AnalyticsComparisonData represents comparison data for an entity
// @Description Analytics data for comparison
type AnalyticsComparisonData struct {
	EntityID         uint                     `json:"entity_id" example:"1"`
	EntityName       string                   `json:"entity_name" example:"Summer Sale Campaign"`
	CurrentPeriod    models.AnalyticsSummary  `json:"current_period"`
	ComparisonPeriod *models.AnalyticsSummary `json:"comparison_period,omitempty"`
	PercentageChange *struct {
		Impressions float64 `json:"impressions" example:"15.5"`
		Clicks      float64 `json:"clicks" example:"22.3"`
		CTR         float64 `json:"ctr" example:"5.9"`
		Revenue     float64 `json:"revenue" example:"18.7"`
	} `json:"percentage_change,omitempty"`
}

// AnalyticsComparisonResponse represents the response for analytics comparison
// @Description Response structure for analytics comparison
type AnalyticsComparisonResponse struct {
	EntityType      string                    `json:"entity_type" example:"campaign"`
	DateRange       models.DateRange          `json:"date_range"`
	ComparisonRange *models.DateRange         `json:"comparison_range,omitempty"`
	Entities        []AnalyticsComparisonData `json:"entities"`
}

// AnalyticsExportRequest represents a request for exporting analytics data
// @Description Request structure for exporting analytics data
type AnalyticsExportRequest struct {
	TenantID         uint      `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID  *uint     `json:"advertisement_id,omitempty" example:"1"`
	CampaignID       *uint     `json:"campaign_id,omitempty" example:"1"`
	StartDate        time.Time `json:"start_date" validate:"required" example:"2023-07-01T00:00:00Z"`
	EndDate          time.Time `json:"end_date" validate:"required" example:"2023-07-31T23:59:59Z"`
	Granularity      string    `json:"granularity" validate:"required,oneof=daily weekly monthly" example:"daily"`
	Format           string    `json:"format" validate:"required,oneof=csv excel pdf" example:"csv"`
	IncludeBreakdown bool      `json:"include_breakdown,omitempty" example:"true"`
	Email            string    `json:"email,omitempty" validate:"omitempty,email" example:"<EMAIL>"`
}

// AnalyticsExportResponse represents the response for analytics export
// @Description Response structure for analytics export
type AnalyticsExportResponse struct {
	ExportID  string    `json:"export_id" example:"export_123456"`
	Status    string    `json:"status" example:"processing"`
	FileURL   string    `json:"file_url,omitempty" example:"https://example.com/exports/analytics_123456.csv"`
	CreatedAt time.Time `json:"created_at" example:"2023-07-15T12:00:00Z"`
	ExpiresAt time.Time `json:"expires_at" example:"2023-07-22T12:00:00Z"`
	Message   string    `json:"message" example:"Export is being processed. You will receive an email when ready."`
}

// ImpressionCreateRequest represents the request to record an impression
// @Description Request structure for recording an advertisement impression
type ImpressionCreateRequest struct {
	TenantID        uint   `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID       uint   `json:"website_id" validate:"required" example:"1"`
	AdvertisementID uint   `json:"advertisement_id" validate:"required" example:"1"`
	PlacementID     uint   `json:"placement_id" validate:"required" example:"1"`
	UserAgent       string `json:"user_agent,omitempty" example:"Mozilla/5.0 Chrome/91.0.4472.124"`
	IPAddress       string `json:"ip_address,omitempty" example:"***********"`
	Referrer        string `json:"referrer,omitempty" example:"https://example.com/page"`
	DeviceType      string `json:"device_type,omitempty" validate:"omitempty,oneof=web_pc web_mobile" example:"web_pc"`
	PageURL         string `json:"page_url,omitempty" example:"https://example.com/blog/article"`
}

// ClickCreateRequest represents the request to record a click
// @Description Request structure for recording an advertisement click
type ClickCreateRequest struct {
	TenantID        uint   `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID       uint   `json:"website_id" validate:"required" example:"1"`
	AdvertisementID uint   `json:"advertisement_id" validate:"required" example:"1"`
	ImpressionID    *uint  `json:"impression_id,omitempty" example:"1"`
	UserAgent       string `json:"user_agent,omitempty" example:"Mozilla/5.0 Chrome/91.0.4472.124"`
	IPAddress       string `json:"ip_address,omitempty" example:"***********"`
	Referrer        string `json:"referrer,omitempty" example:"https://example.com/page"`
	DeviceType      string `json:"device_type,omitempty" validate:"omitempty,oneof=web_pc web_mobile" example:"web_pc"`
	PageURL         string `json:"page_url,omitempty" example:"https://example.com/blog/article"`
}

// TrackingResponse represents the response for tracking operations
// @Description Response structure for impression/click tracking
type TrackingResponse struct {
	Success    bool      `json:"success" example:"true"`
	Message    string    `json:"message" example:"Impression recorded successfully"`
	Timestamp  time.Time `json:"timestamp" example:"2023-07-15T12:00:00Z"`
	TrackingID string    `json:"tracking_id,omitempty" example:"track_123456"`
}
