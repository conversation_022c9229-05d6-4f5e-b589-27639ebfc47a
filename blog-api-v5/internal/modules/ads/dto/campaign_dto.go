package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CampaignCreateRequest represents the request to create a campaign
// @Description Request structure for creating a new advertising campaign
type CampaignCreateRequest struct {
	TenantID    uint      `json:"tenant_id" validate:"required" example:"1"`
	Name        string    `json:"name" validate:"required,min=1,max=255" example:"Summer Sale Campaign"`
	Description string    `json:"description,omitempty" validate:"max=1000" example:"Comprehensive summer marketing campaign with multiple ad placements"`
	Budget      float64   `json:"budget" validate:"min=0" example:"1000.00"`
	StartDate   time.Time `json:"start_date" validate:"required" example:"2023-06-01T00:00:00Z"`
	EndDate     time.Time `json:"end_date" validate:"required" example:"2023-08-31T23:59:59Z"`
}

// CampaignUpdateRequest represents the request to update a campaign
// @Description Request structure for updating an existing campaign
type CampaignUpdateRequest struct {
	Name        *string                `json:"name,omitempty" validate:"omitempty,min=1,max=255" example:"Updated Summer Sale Campaign"`
	Description *string                `json:"description,omitempty" validate:"omitempty,max=1000" example:"Updated comprehensive summer marketing campaign"`
	Budget      *float64               `json:"budget,omitempty" validate:"omitempty,min=0" example:"1500.00"`
	Status      *models.CampaignStatus `json:"status,omitempty" validate:"omitempty,oneof=draft active paused completed cancelled deleted" example:"active"`
	StartDate   *time.Time             `json:"start_date,omitempty" example:"2023-06-01T00:00:00Z"`
	EndDate     *time.Time             `json:"end_date,omitempty" example:"2023-09-30T23:59:59Z"`
}

// CampaignResponse represents the response structure for campaign
// @Description Response structure for campaign data
type CampaignResponse struct {
	ID          uint                  `json:"id" example:"1"`
	TenantID    uint                  `json:"tenant_id" example:"1"`
	Name        string                `json:"name" example:"Summer Sale Campaign"`
	Description string                `json:"description,omitempty" example:"Comprehensive summer marketing campaign with multiple ad placements"`
	Budget      float64               `json:"budget" example:"1000.00"`
	Status      models.CampaignStatus `json:"status" example:"active"`
	StartDate   time.Time             `json:"start_date" example:"2023-06-01T00:00:00Z"`
	EndDate     time.Time             `json:"end_date" example:"2023-08-31T23:59:59Z"`
	CreatedAt   time.Time             `json:"created_at" example:"2023-05-15T10:30:00Z"`
	UpdatedAt   time.Time             `json:"updated_at" example:"2023-05-15T10:30:00Z"`
	IsActive    bool                  `json:"is_active" example:"true"`
	IsRunning   bool                  `json:"is_running" example:"true"`
}

// CampaignListFilter represents filter options for listing campaigns
// @Description Filter options for listing campaigns
type CampaignListFilter struct {
	TenantID   uint                         `json:"tenant_id" validate:"required" example:"1"`
	Status     *models.CampaignStatus       `json:"status,omitempty" validate:"omitempty,oneof=draft active paused completed cancelled deleted" example:"active"`
	IsActive   *bool                        `json:"is_active,omitempty" example:"true"`
	IsRunning  *bool                        `json:"is_running,omitempty" example:"true"`
	StartDate  *time.Time                   `json:"start_date,omitempty" example:"2023-06-01T00:00:00Z"`
	EndDate    *time.Time                   `json:"end_date,omitempty" example:"2023-08-31T23:59:59Z"`
	Search     string                       `json:"search,omitempty" example:"summer sale"`
	Pagination *pagination.CursorPagination `json:"pagination,omitempty"`
}

// CampaignListResponse represents the response for listing campaigns
// @Description Response structure for campaign listing
type CampaignListResponse struct {
	Campaigns  []CampaignResponse         `json:"campaigns"`
	Pagination *pagination.CursorResponse `json:"pagination,omitempty"`
}

// CampaignStatsRequest represents a request for campaign statistics
// @Description Request structure for retrieving campaign statistics
type CampaignStatsRequest struct {
	TenantID    uint       `json:"tenant_id" validate:"required" example:"1"`
	CampaignID  *uint      `json:"campaign_id,omitempty" example:"1"`
	StartDate   *time.Time `json:"start_date,omitempty" example:"2023-06-01T00:00:00Z"`
	EndDate     *time.Time `json:"end_date,omitempty" example:"2023-08-31T23:59:59Z"`
	Granularity string     `json:"granularity,omitempty" validate:"omitempty,oneof=hour day week month" example:"day"`
}

// CampaignStatsResponse represents the response for campaign statistics
// @Description Response structure for campaign statistics
type CampaignStatsResponse struct {
	CampaignID      uint    `json:"campaign_id" example:"1"`
	Impressions     int64   `json:"impressions" example:"50000"`
	Clicks          int64   `json:"clicks" example:"1250"`
	CTR             float64 `json:"ctr" example:"2.5"`
	Revenue         float64 `json:"revenue" example:"625.00"`
	CPC             float64 `json:"cpc" example:"0.50"`
	CPM             float64 `json:"cpm" example:"12.50"`
	BudgetSpent     float64 `json:"budget_spent" example:"625.00"`
	BudgetRemaining float64 `json:"budget_remaining" example:"375.00"`
	ROI             float64 `json:"roi" example:"1.56"`
}

// CampaignPerformanceRequest represents a request for campaign performance data
// @Description Request structure for retrieving detailed campaign performance
type CampaignPerformanceRequest struct {
	TenantID    uint       `json:"tenant_id" validate:"required" example:"1"`
	CampaignID  uint       `json:"campaign_id" validate:"required" example:"1"`
	StartDate   *time.Time `json:"start_date,omitempty" example:"2023-06-01T00:00:00Z"`
	EndDate     *time.Time `json:"end_date,omitempty" example:"2023-08-31T23:59:59Z"`
	Granularity string     `json:"granularity,omitempty" validate:"omitempty,oneof=hour day week month" example:"day"`
	Metrics     []string   `json:"metrics,omitempty" example:"impressions,clicks,revenue"`
}

// CampaignPerformanceData represents performance data for a specific time period
// @Description Performance metrics for a campaign in a specific time period
type CampaignPerformanceData struct {
	Date        time.Time `json:"date" example:"2023-06-01T00:00:00Z"`
	Impressions int64     `json:"impressions" example:"2500"`
	Clicks      int64     `json:"clicks" example:"62"`
	Revenue     float64   `json:"revenue" example:"31.25"`
	Spend       float64   `json:"spend" example:"25.00"`
}

// CampaignPerformanceResponse represents the response for campaign performance
// @Description Response structure for campaign performance data
type CampaignPerformanceResponse struct {
	CampaignID   uint                      `json:"campaign_id" example:"1"`
	CampaignName string                    `json:"campaign_name" example:"Summer Sale Campaign"`
	StartDate    time.Time                 `json:"start_date" example:"2023-06-01T00:00:00Z"`
	EndDate      time.Time                 `json:"end_date" example:"2023-08-31T23:59:59Z"`
	TotalStats   CampaignStatsResponse     `json:"total_stats"`
	Performance  []CampaignPerformanceData `json:"performance"`
}

// CampaignBulkUpdateRequest represents a request for bulk updating campaigns
// @Description Request structure for bulk updating multiple campaigns
type CampaignBulkUpdateRequest struct {
	TenantID    uint                   `json:"tenant_id" validate:"required" example:"1"`
	CampaignIDs []uint                 `json:"campaign_ids" validate:"required,min=1" example:"1,2,3"`
	Status      *models.CampaignStatus `json:"status,omitempty" validate:"omitempty,oneof=draft active paused completed cancelled deleted" example:"paused"`
	Budget      *float64               `json:"budget,omitempty" validate:"omitempty,min=0" example:"2000.00"`
}

// CampaignBulkUpdateResponse represents the response for bulk updating campaigns
// @Description Response structure for bulk campaign updates
type CampaignBulkUpdateResponse struct {
	UpdatedCount int    `json:"updated_count" example:"3"`
	FailedCount  int    `json:"failed_count" example:"0"`
	FailedIDs    []uint `json:"failed_ids,omitempty" example:""`
	Message      string `json:"message" example:"Successfully updated 3 campaigns"`
}

// CampaignWithAdsResponse represents a campaign with its advertisements
// @Description Response structure for campaign with associated advertisements
type CampaignWithAdsResponse struct {
	Campaign       CampaignResponse        `json:"campaign"`
	Advertisements []AdvertisementResponse `json:"advertisements"`
	TotalAds       int                     `json:"total_ads" example:"5"`
	ActiveAds      int                     `json:"active_ads" example:"3"`
}

// CampaignDuplicateRequest represents a request to duplicate a campaign
// @Description Request structure for duplicating an existing campaign
type CampaignDuplicateRequest struct {
	TenantID         uint      `json:"tenant_id" validate:"required" example:"1"`
	SourceCampaignID uint      `json:"source_campaign_id" validate:"required" example:"1"`
	Name             string    `json:"name" validate:"required,min=1,max=255" example:"Copy of Summer Sale Campaign"`
	StartDate        time.Time `json:"start_date" validate:"required" example:"2023-09-01T00:00:00Z"`
	EndDate          time.Time `json:"end_date" validate:"required" example:"2023-11-30T23:59:59Z"`
	CopyAds          bool      `json:"copy_ads" example:"true"`
}

// CampaignDuplicateResponse represents the response for campaign duplication
// @Description Response structure for campaign duplication
type CampaignDuplicateResponse struct {
	NewCampaign    CampaignResponse `json:"new_campaign"`
	CopiedAdsCount int              `json:"copied_ads_count" example:"5"`
	Message        string           `json:"message" example:"Campaign successfully duplicated with 5 advertisements"`
}
