package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// PlacementCreateRequest represents the request to create a placement
// @Description Request structure for creating a new ad placement
type PlacementCreateRequest struct {
	TenantID       uint                            `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID      uint                            `json:"website_id" validate:"required" example:"1"`
	PageType       string                          `json:"page_type" validate:"required,oneof=homepage category article tag custom" example:"homepage"`
	Position       string                          `json:"position" validate:"required,oneof=header sidebar footer inline popup" example:"sidebar"`
	TargetingRules *models.PlacementTargetingRules `json:"targeting_rules,omitempty"`
	MaxAds         int                             `json:"max_ads,omitempty" validate:"omitempty,min=1,max=10" example:"3"`
}

// PlacementUpdateRequest represents the request to update a placement
// @Description Request structure for updating an existing placement
type PlacementUpdateRequest struct {
	TargetingRules *models.PlacementTargetingRules `json:"targeting_rules,omitempty"`
	MaxAds         *int                            `json:"max_ads,omitempty" validate:"omitempty,min=1,max=10" example:"5"`
	Status         *string                         `json:"status,omitempty" validate:"omitempty,oneof=active inactive deleted" example:"active"`
}

// PlacementResponse represents the response structure for placement
// @Description Response structure for placement data
type PlacementResponse struct {
	ID             uint                            `json:"id" example:"1"`
	TenantID       uint                            `json:"tenant_id" example:"1"`
	PageType       string                          `json:"page_type" example:"homepage"`
	Position       string                          `json:"position" example:"sidebar"`
	TargetingRules *models.PlacementTargetingRules `json:"targeting_rules,omitempty"`
	MaxAds         int                             `json:"max_ads" example:"3"`
	Status         string                          `json:"status" example:"active"`
	CreatedAt      time.Time                       `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt      time.Time                       `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	IsActive       bool                            `json:"is_active" example:"true"`
}

// PlacementListFilter represents filter options for listing placements
// @Description Filter options for listing placements
type PlacementListFilter struct {
	TenantID   uint                         `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID  uint                         `json:"website_id" validate:"required" example:"1"`
	PageType   *string                      `json:"page_type,omitempty" validate:"omitempty,oneof=homepage category article tag custom" example:"homepage"`
	Position   *string                      `json:"position,omitempty" validate:"omitempty,oneof=header sidebar footer inline popup" example:"sidebar"`
	Status     *string                      `json:"status,omitempty" validate:"omitempty,oneof=active inactive deleted" example:"active"`
	IsActive   *bool                        `json:"is_active,omitempty" example:"true"`
	Search     string                       `json:"search,omitempty" example:"header placement"`
	Pagination *pagination.CursorPagination `json:"pagination,omitempty"`
}

// PlacementListResponse represents the response for listing placements
// @Description Response structure for placement listing
type PlacementListResponse struct {
	Placements []PlacementResponse        `json:"placements"`
	Pagination *pagination.CursorResponse `json:"pagination,omitempty"`
}

// PlacementAvailabilityRequest represents a request to check placement availability
// @Description Request structure for checking placement availability
type PlacementAvailabilityRequest struct {
	TenantID    uint   `json:"tenant_id" validate:"required" example:"1"`
	PageType    string `json:"page_type" validate:"required,oneof=homepage category article tag custom" example:"homepage"`
	Position    string `json:"position" validate:"required,oneof=header sidebar footer inline popup" example:"sidebar"`
	DeviceType  string `json:"device_type,omitempty" validate:"omitempty,oneof=web_pc web_mobile" example:"web_pc"`
	ScreenWidth int    `json:"screen_width,omitempty" example:"1920"`
	UserAgent   string `json:"user_agent,omitempty" example:"Mozilla/5.0 Chrome/91.0.4472.124"`
	CountryCode string `json:"country_code,omitempty" example:"US"`
	CurrentTime string `json:"current_time,omitempty" example:"14:30"`
}

// PlacementAvailabilityResponse represents the response for placement availability
// @Description Response structure for placement availability
type PlacementAvailabilityResponse struct {
	PlacementID    uint   `json:"placement_id" example:"1"`
	IsAvailable    bool   `json:"is_available" example:"true"`
	MaxAds         int    `json:"max_ads" example:"3"`
	AvailableSlots int    `json:"available_slots" example:"2"`
	Reason         string `json:"reason,omitempty" example:"Targeting rules matched"`
	TargetingMatch bool   `json:"targeting_match" example:"true"`
}

// PlacementStatsRequest represents a request for placement statistics
// @Description Request structure for retrieving placement statistics
type PlacementStatsRequest struct {
	TenantID    uint       `json:"tenant_id" validate:"required" example:"1"`
	PlacementID *uint      `json:"placement_id,omitempty" example:"1"`
	PageType    *string    `json:"page_type,omitempty" validate:"omitempty,oneof=homepage category article tag custom" example:"homepage"`
	Position    *string    `json:"position,omitempty" validate:"omitempty,oneof=header sidebar footer inline popup" example:"sidebar"`
	StartDate   *time.Time `json:"start_date,omitempty" example:"2023-07-01T00:00:00Z"`
	EndDate     *time.Time `json:"end_date,omitempty" example:"2023-07-31T23:59:59Z"`
	Granularity string     `json:"granularity,omitempty" validate:"omitempty,oneof=hour day week month" example:"day"`
}

// PlacementStatsResponse represents the response for placement statistics
// @Description Response structure for placement statistics
type PlacementStatsResponse struct {
	PlacementID      uint    `json:"placement_id" example:"1"`
	PageType         string  `json:"page_type" example:"homepage"`
	Position         string  `json:"position" example:"sidebar"`
	TotalImpressions int64   `json:"total_impressions" example:"10000"`
	UniqueViews      int64   `json:"unique_views" example:"8500"`
	FillRate         float64 `json:"fill_rate" example:"0.85"`
	AverageViewTime  float64 `json:"average_view_time" example:"3.5"`
	TopAds           []struct {
		AdvertisementID uint    `json:"advertisement_id" example:"1"`
		Title           string  `json:"title" example:"Summer Sale Banner"`
		Impressions     int64   `json:"impressions" example:"2500"`
		Clicks          int64   `json:"clicks" example:"125"`
		CTR             float64 `json:"ctr" example:"0.05"`
	} `json:"top_ads,omitempty"`
}

// PlacementOptimizationRequest represents a request for placement optimization
// @Description Request structure for placement optimization analysis
type PlacementOptimizationRequest struct {
	TenantID    uint      `json:"tenant_id" validate:"required" example:"1"`
	PlacementID *uint     `json:"placement_id,omitempty" example:"1"`
	PageType    *string   `json:"page_type,omitempty" validate:"omitempty,oneof=homepage category article tag custom" example:"homepage"`
	Position    *string   `json:"position,omitempty" validate:"omitempty,oneof=header sidebar footer inline popup" example:"sidebar"`
	StartDate   time.Time `json:"start_date" validate:"required" example:"2023-07-01T00:00:00Z"`
	EndDate     time.Time `json:"end_date" validate:"required" example:"2023-07-31T23:59:59Z"`
	Metrics     []string  `json:"metrics,omitempty" example:"impressions,clicks,revenue"`
}

// PlacementOptimizationSuggestion represents optimization suggestions
// @Description Optimization suggestion for a placement
type PlacementOptimizationSuggestion struct {
	Type        string `json:"type" example:"increase_max_ads"`
	Title       string `json:"title" example:"Increase Maximum Ads"`
	Description string `json:"description" example:"Consider increasing max ads from 3 to 5 to improve fill rate"`
	Impact      string `json:"impact" example:"medium"`
	Priority    int    `json:"priority" example:"3"`
	Metrics     struct {
		CurrentValue   float64 `json:"current_value" example:"0.75"`
		ProjectedValue float64 `json:"projected_value" example:"0.85"`
		Improvement    float64 `json:"improvement" example:"13.3"`
	} `json:"metrics"`
}

// PlacementOptimizationResponse represents the response for placement optimization
// @Description Response structure for placement optimization suggestions
type PlacementOptimizationResponse struct {
	PlacementID    uint   `json:"placement_id" example:"1"`
	PageType       string `json:"page_type" example:"homepage"`
	Position       string `json:"position" example:"sidebar"`
	AnalysisPeriod struct {
		StartDate time.Time `json:"start_date" example:"2023-07-01T00:00:00Z"`
		EndDate   time.Time `json:"end_date" example:"2023-07-31T23:59:59Z"`
	} `json:"analysis_period"`
	CurrentMetrics struct {
		FillRate       float64 `json:"fill_rate" example:"0.75"`
		AverageRevenue float64 `json:"average_revenue" example:"25.50"`
		ViewTime       float64 `json:"view_time" example:"3.2"`
		EngagementRate float64 `json:"engagement_rate" example:"0.12"`
	} `json:"current_metrics"`
	Suggestions    []PlacementOptimizationSuggestion `json:"suggestions"`
	OverallScore   float64                           `json:"overall_score" example:"7.2"`
	Recommendation string                            `json:"recommendation" example:"Good performance with room for improvement in fill rate"`
}

// PlacementBulkUpdateRequest represents a request for bulk updating placements
// @Description Request structure for bulk updating multiple placements
type PlacementBulkUpdateRequest struct {
	TenantID     uint    `json:"tenant_id" validate:"required" example:"1"`
	PlacementIDs []uint  `json:"placement_ids" validate:"required,min=1" example:"1,2,3"`
	Status       *string `json:"status,omitempty" validate:"omitempty,oneof=active inactive deleted" example:"inactive"`
	MaxAds       *int    `json:"max_ads,omitempty" validate:"omitempty,min=1,max=10" example:"5"`
}

// PlacementBulkUpdateResponse represents the response for bulk updating placements
// @Description Response structure for bulk placement updates
type PlacementBulkUpdateResponse struct {
	UpdatedCount int    `json:"updated_count" example:"3"`
	FailedCount  int    `json:"failed_count" example:"0"`
	FailedIDs    []uint `json:"failed_ids,omitempty" example:""`
	Message      string `json:"message" example:"Successfully updated 3 placements"`
}

// PlacementConfigurationRequest represents a request for advanced placement configuration
// @Description Request structure for advanced placement configuration
type PlacementConfigurationRequest struct {
	TenantID            uint                            `json:"tenant_id" validate:"required" example:"1"`
	PlacementID         uint                            `json:"placement_id" validate:"required" example:"1"`
	TargetingRules      *models.PlacementTargetingRules `json:"targeting_rules,omitempty"`
	RefreshInterval     *int                            `json:"refresh_interval,omitempty" validate:"omitempty,min=30,max=3600" example:"300"`
	RotationEnabled     *bool                           `json:"rotation_enabled,omitempty" example:"true"`
	RotationInterval    *int                            `json:"rotation_interval,omitempty" validate:"omitempty,min=10,max=300" example:"60"`
	LazyLoading         *bool                           `json:"lazy_loading,omitempty" example:"true"`
	ViewabilityTracking *bool                           `json:"viewability_tracking,omitempty" example:"true"`
}

// PlacementConfigurationResponse represents the response for placement configuration
// @Description Response structure for placement configuration
type PlacementConfigurationResponse struct {
	PlacementID      uint                          `json:"placement_id" example:"1"`
	Configuration    PlacementConfigurationRequest `json:"configuration"`
	IsOptimized      bool                          `json:"is_optimized" example:"true"`
	LastOptimized    *time.Time                    `json:"last_optimized,omitempty" example:"2023-07-15T10:30:00Z"`
	PerformanceScore float64                       `json:"performance_score" example:"8.5"`
	UpdatedAt        time.Time                     `json:"updated_at" example:"2023-07-15T12:00:00Z"`
}

// PlacementValidationResponse represents the response for placement validation
// @Description Response structure for placement validation
type PlacementValidationResponse struct {
	IsValid     bool     `json:"is_valid" example:"true"`
	Errors      []string `json:"errors,omitempty" example:""`
	Warnings    []string `json:"warnings,omitempty" example:"Consider using a different position for better performance"`
	Suggestions []string `json:"suggestions,omitempty" example:"Position 'header' typically has higher engagement"`
}
