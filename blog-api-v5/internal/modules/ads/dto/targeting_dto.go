package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// TargetingRuleCreateRequest represents the request to create a targeting rule
// @Description Request structure for creating a new targeting rule
type TargetingRuleCreateRequest struct {
	TenantID        uint   `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID       uint   `json:"website_id" validate:"required" example:"1"`
	AdvertisementID uint   `json:"advertisement_id" validate:"required" example:"1"`
	RuleType        string `json:"rule_type" validate:"required,oneof=page_url referrer device time location custom" example:"page_url"`
	RuleKey         string `json:"rule_key" validate:"required,max=255" example:"url_path"`
	RuleValue       string `json:"rule_value" validate:"required" example:"/blog/summer-sale"`
	Operator        string `json:"operator,omitempty" validate:"omitempty,oneof=equals not_equals contains not_contains starts_with ends_with regex" example:"contains"`
	Priority        int    `json:"priority,omitempty" validate:"omitempty,min=1,max=100" example:"5"`
}

// TargetingRuleUpdateRequest represents the request to update a targeting rule
// @Description Request structure for updating an existing targeting rule
type TargetingRuleUpdateRequest struct {
	RuleType  *string `json:"rule_type,omitempty" validate:"omitempty,oneof=page_url referrer device time location custom" example:"device"`
	RuleKey   *string `json:"rule_key,omitempty" validate:"omitempty,max=255" example:"device_type"`
	RuleValue *string `json:"rule_value,omitempty" example:"mobile"`
	Operator  *string `json:"operator,omitempty" validate:"omitempty,oneof=equals not_equals contains not_contains starts_with ends_with regex" example:"equals"`
	Priority  *int    `json:"priority,omitempty" validate:"omitempty,min=1,max=100" example:"10"`
	Status    *string `json:"status,omitempty" validate:"omitempty,oneof=active inactive deleted" example:"active"`
}

// TargetingRuleResponse represents the response structure for targeting rule
// @Description Response structure for targeting rule data
type TargetingRuleResponse struct {
	ID              uint      `json:"id" example:"1"`
	TenantID        uint      `json:"tenant_id" example:"1"`
	AdvertisementID uint      `json:"advertisement_id" example:"1"`
	RuleType        string    `json:"rule_type" example:"page_url"`
	RuleKey         string    `json:"rule_key" example:"url_path"`
	RuleValue       string    `json:"rule_value" example:"/blog/summer-sale"`
	Operator        string    `json:"operator" example:"contains"`
	Priority        int       `json:"priority" example:"5"`
	Status          string    `json:"status" example:"active"`
	CreatedAt       time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt       time.Time `json:"updated_at" example:"2023-01-01T00:00:00Z"`
	IsActive        bool      `json:"is_active" example:"true"`
}

// TargetingRuleListFilter represents filter options for listing targeting rules
// @Description Filter options for listing targeting rules
type TargetingRuleListFilter struct {
	TenantID        uint                         `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID       uint                         `json:"website_id" validate:"required" example:"1"`
	AdvertisementID *uint                        `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint                        `json:"campaign_id,omitempty" example:"1"`
	RuleType        *string                      `json:"rule_type,omitempty" validate:"omitempty,oneof=page_url referrer device time location custom" example:"page_url"`
	Status          *string                      `json:"status,omitempty" validate:"omitempty,oneof=active inactive deleted" example:"active"`
	IsActive        *bool                        `json:"is_active,omitempty" example:"true"`
	Priority        *int                         `json:"priority,omitempty" validate:"omitempty,min=1,max=100" example:"5"`
	Search          string                       `json:"search,omitempty" example:"summer sale"`
	Pagination      *pagination.CursorPagination `json:"pagination,omitempty"`
}

// TargetingRuleListResponse represents the response for listing targeting rules
// @Description Response structure for targeting rule listing
type TargetingRuleListResponse struct {
	TargetingRules []TargetingRuleResponse    `json:"targeting_rules"`
	Pagination     *pagination.CursorResponse `json:"pagination,omitempty"`
}

// TargetingEvaluationRequest represents a request to evaluate targeting rules
// @Description Request structure for evaluating targeting rules against context
type TargetingEvaluationRequest struct {
	TenantID        uint                    `json:"tenant_id" validate:"required" example:"1"`
	WebsiteID       uint                    `json:"website_id" validate:"required" example:"1"`
	AdvertisementID *uint                   `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint                   `json:"campaign_id,omitempty" example:"1"`
	Context         models.TargetingContext `json:"context" validate:"required"`
}

// TargetingEvaluationResult represents the result of a targeting rule evaluation
// @Description Result of targeting rule evaluation
type TargetingEvaluationResult struct {
	RuleID      uint   `json:"rule_id" example:"1"`
	RuleType    string `json:"rule_type" example:"page_url"`
	RuleKey     string `json:"rule_key" example:"url_path"`
	RuleValue   string `json:"rule_value" example:"/blog/summer-sale"`
	Operator    string `json:"operator" example:"contains"`
	Priority    int    `json:"priority" example:"5"`
	Matched     bool   `json:"matched" example:"true"`
	MatchReason string `json:"match_reason,omitempty" example:"URL contains '/blog/summer-sale'"`
}

// TargetingEvaluationResponse represents the response for targeting evaluation
// @Description Response structure for targeting rule evaluation
type TargetingEvaluationResponse struct {
	TotalRules     int                         `json:"total_rules" example:"5"`
	MatchedRules   int                         `json:"matched_rules" example:"3"`
	OverallMatch   bool                        `json:"overall_match" example:"true"`
	MatchScore     float64                     `json:"match_score" example:"0.6"`
	RuleResults    []TargetingEvaluationResult `json:"rule_results"`
	RecommendedAds []uint                      `json:"recommended_ads,omitempty" example:"1,2,3"`
	EvaluationTime time.Time                   `json:"evaluation_time" example:"2023-07-15T12:00:00Z"`
}

// TargetingOptimizationRequest represents a request for targeting optimization
// @Description Request structure for targeting rule optimization analysis
type TargetingOptimizationRequest struct {
	TenantID        uint      `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID *uint     `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint     `json:"campaign_id,omitempty" example:"1"`
	StartDate       time.Time `json:"start_date" validate:"required" example:"2023-07-01T00:00:00Z"`
	EndDate         time.Time `json:"end_date" validate:"required" example:"2023-07-31T23:59:59Z"`
	Metrics         []string  `json:"metrics,omitempty" example:"ctr,conversion_rate,revenue"`
}

// TargetingOptimizationSuggestion represents an optimization suggestion
// @Description Targeting optimization suggestion
type TargetingOptimizationSuggestion struct {
	Type              string `json:"type" example:"add_rule"`
	Title             string `json:"title" example:"Add Device Targeting"`
	Description       string `json:"description" example:"Consider adding mobile device targeting to improve CTR"`
	RuleType          string `json:"rule_type,omitempty" example:"device"`
	SuggestedKey      string `json:"suggested_key,omitempty" example:"device_type"`
	SuggestedValue    string `json:"suggested_value,omitempty" example:"mobile"`
	SuggestedOperator string `json:"suggested_operator,omitempty" example:"equals"`
	Impact            string `json:"impact" example:"medium"`
	Priority          int    `json:"priority" example:"3"`
	Metrics           struct {
		CurrentCTR   float64 `json:"current_ctr" example:"0.02"`
		ProjectedCTR float64 `json:"projected_ctr" example:"0.035"`
		Improvement  float64 `json:"improvement" example:"75.0"`
	} `json:"metrics"`
}

// TargetingOptimizationResponse represents the response for targeting optimization
// @Description Response structure for targeting optimization suggestions
type TargetingOptimizationResponse struct {
	EntityID       uint   `json:"entity_id" example:"1"`
	EntityType     string `json:"entity_type" example:"advertisement"`
	EntityName     string `json:"entity_name" example:"Summer Banner Ad"`
	AnalysisPeriod struct {
		StartDate time.Time `json:"start_date" example:"2023-07-01T00:00:00Z"`
		EndDate   time.Time `json:"end_date" example:"2023-07-31T23:59:59Z"`
	} `json:"analysis_period"`
	CurrentMetrics struct {
		CTR            float64 `json:"ctr" example:"0.025"`
		ConversionRate float64 `json:"conversion_rate" example:"0.05"`
		Revenue        float64 `json:"revenue" example:"250.50"`
		Impressions    int64   `json:"impressions" example:"10000"`
	} `json:"current_metrics"`
	RuleAnalysis struct {
		TotalRules       int     `json:"total_rules" example:"5"`
		EffectiveRules   int     `json:"effective_rules" example:"3"`
		ConflictingRules int     `json:"conflicting_rules" example:"1"`
		OverallScore     float64 `json:"overall_score" example:"7.5"`
	} `json:"rule_analysis"`
	Suggestions    []TargetingOptimizationSuggestion `json:"suggestions"`
	Recommendation string                            `json:"recommendation" example:"Consider simplifying targeting rules and adding mobile device targeting"`
}

// TargetingBulkUpdateRequest represents a request for bulk updating targeting rules
// @Description Request structure for bulk updating multiple targeting rules
type TargetingBulkUpdateRequest struct {
	TenantID uint    `json:"tenant_id" validate:"required" example:"1"`
	RuleIDs  []uint  `json:"rule_ids" validate:"required,min=1" example:"1,2,3"`
	Status   *string `json:"status,omitempty" validate:"omitempty,oneof=active inactive deleted" example:"inactive"`
	Priority *int    `json:"priority,omitempty" validate:"omitempty,min=1,max=100" example:"10"`
	Operator *string `json:"operator,omitempty" validate:"omitempty,oneof=equals not_equals contains not_contains starts_with ends_with regex" example:"contains"`
}

// TargetingBulkUpdateResponse represents the response for bulk updating targeting rules
// @Description Response structure for bulk targeting rule updates
type TargetingBulkUpdateResponse struct {
	UpdatedCount int    `json:"updated_count" example:"3"`
	FailedCount  int    `json:"failed_count" example:"0"`
	FailedIDs    []uint `json:"failed_ids,omitempty" example:""`
	Message      string `json:"message" example:"Successfully updated 3 targeting rules"`
}

// TargetingTemplateRequest represents a request for applying targeting templates
// @Description Request structure for applying predefined targeting templates
type TargetingTemplateRequest struct {
	TenantID          uint     `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID   uint     `json:"advertisement_id" validate:"required" example:"1"`
	TemplateType      string   `json:"template_type" validate:"required,oneof=mobile_users desktop_users blog_readers product_pages social_referrals" example:"mobile_users"`
	CustomValues      []string `json:"custom_values,omitempty" example:"android,ios"`
	OverwriteExisting bool     `json:"overwrite_existing,omitempty" example:"false"`
}

// TargetingTemplate represents a predefined targeting template
// @Description Predefined targeting rule template
type TargetingTemplate struct {
	Type        string `json:"type" example:"mobile_users"`
	Name        string `json:"name" example:"Mobile Users"`
	Description string `json:"description" example:"Target users on mobile devices"`
	Rules       []struct {
		RuleType  string `json:"rule_type" example:"device"`
		RuleKey   string `json:"rule_key" example:"device_type"`
		RuleValue string `json:"rule_value" example:"mobile"`
		Operator  string `json:"operator" example:"equals"`
		Priority  int    `json:"priority" example:"5"`
	} `json:"rules"`
}

// TargetingTemplateResponse represents the response for targeting template application
// @Description Response structure for targeting template application
type TargetingTemplateResponse struct {
	AdvertisementID  uint                    `json:"advertisement_id" example:"1"`
	TemplateType     string                  `json:"template_type" example:"mobile_users"`
	AppliedTemplate  TargetingTemplate       `json:"applied_template"`
	CreatedRules     []TargetingRuleResponse `json:"created_rules"`
	OverwrittenRules []uint                  `json:"overwritten_rules,omitempty" example:"2,3"`
	Message          string                  `json:"message" example:"Successfully applied mobile_users template with 2 rules"`
}

// TargetingAnalyticsRequest represents a request for targeting analytics
// @Description Request structure for targeting rule performance analytics
type TargetingAnalyticsRequest struct {
	TenantID        uint      `json:"tenant_id" validate:"required" example:"1"`
	AdvertisementID *uint     `json:"advertisement_id,omitempty" example:"1"`
	CampaignID      *uint     `json:"campaign_id,omitempty" example:"1"`
	RuleIDs         []uint    `json:"rule_ids,omitempty" example:"1,2,3"`
	StartDate       time.Time `json:"start_date" validate:"required" example:"2023-07-01T00:00:00Z"`
	EndDate         time.Time `json:"end_date" validate:"required" example:"2023-07-31T23:59:59Z"`
	Granularity     string    `json:"granularity,omitempty" validate:"omitempty,oneof=daily weekly monthly" example:"daily"`
}

// TargetingRuleAnalytics represents analytics data for a targeting rule
// @Description Analytics data for targeting rule performance
type TargetingRuleAnalytics struct {
	RuleID             uint    `json:"rule_id" example:"1"`
	RuleType           string  `json:"rule_type" example:"page_url"`
	RuleKey            string  `json:"rule_key" example:"url_path"`
	RuleValue          string  `json:"rule_value" example:"/blog/summer-sale"`
	Impressions        int64   `json:"impressions" example:"5000"`
	Clicks             int64   `json:"clicks" example:"125"`
	CTR                float64 `json:"ctr" example:"0.025"`
	Conversions        int64   `json:"conversions" example:"25"`
	ConversionRate     float64 `json:"conversion_rate" example:"0.2"`
	Revenue            float64 `json:"revenue" example:"125.50"`
	MatchRate          float64 `json:"match_rate" example:"0.75"`
	EffectivenessScore float64 `json:"effectiveness_score" example:"8.2"`
}

// TargetingAnalyticsResponse represents the response for targeting analytics
// @Description Response structure for targeting rule analytics
type TargetingAnalyticsResponse struct {
	AnalysisPeriod struct {
		StartDate time.Time `json:"start_date" example:"2023-07-01T00:00:00Z"`
		EndDate   time.Time `json:"end_date" example:"2023-07-31T23:59:59Z"`
	} `json:"analysis_period"`
	Summary struct {
		TotalRules       int     `json:"total_rules" example:"5"`
		EffectiveRules   int     `json:"effective_rules" example:"4"`
		AverageMatchRate float64 `json:"average_match_rate" example:"0.68"`
		TotalImpressions int64   `json:"total_impressions" example:"25000"`
		TotalClicks      int64   `json:"total_clicks" example:"625"`
		OverallCTR       float64 `json:"overall_ctr" example:"0.025"`
	} `json:"summary"`
	RuleAnalytics   []TargetingRuleAnalytics `json:"rule_analytics"`
	TopPerforming   []uint                   `json:"top_performing_rules" example:"1,3,5"`
	Underperforming []uint                   `json:"underperforming_rules" example:"2,4"`
}

// TargetingValidationResponse represents the response for targeting rule validation
// @Description Response structure for targeting rule validation
type TargetingValidationResponse struct {
	IsValid     bool     `json:"is_valid" example:"true"`
	Errors      []string `json:"errors,omitempty" example:""`
	Warnings    []string `json:"warnings,omitempty" example:"Rule priority conflicts with existing rules"`
	Suggestions []string `json:"suggestions,omitempty" example:"Consider using a more specific rule value"`
}
