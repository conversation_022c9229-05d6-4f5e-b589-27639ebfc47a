package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// AdvertisementHandler handles advertisement-related HTTP requests
type AdvertisementHandler struct {
	serviceManager services.ServiceManager
	validator      validator.Validator
}

// NewAdvertisementHandler creates a new advertisement handler
func NewAdvertisementHandler(serviceManager services.ServiceManager, validatorInstance validator.Validator) *AdvertisementHandler {
	return &AdvertisementHandler{
		serviceManager: serviceManager,
		validator:      validatorInstance,
	}
}

// getWebsiteID extracts and validates website ID from request header
func getWebsiteID(c *gin.Context) (uint, bool) {
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		response.BadRequest(c.Writer, "X-Website-ID header is required")
		return 0, false
	}
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid X-Website-ID header")
		return 0, false
	}
	return uint(websiteID), true
}

// CreateAdvertisement handles POST /advertisements
func (h *AdvertisementHandler) CreateAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteID(c)
	if !ok {
		return
	}

	var req dto.AdvertisementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Create advertisement using service
	advertisement, err := h.serviceManager.AdvertisementService().CreateAdvertisement(c.Request.Context(), tenantID.(uint), websiteID, &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to create advertisement: "+err.Error())
		return
	}

	response.Created(c.Writer, advertisement)
}

// GetAdvertisement handles GET /advertisements/:id
func (h *AdvertisementHandler) GetAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteID(c)
	if !ok {
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Get advertisement using service
	advertisement, err := h.serviceManager.AdvertisementService().GetAdvertisementByID(c.Request.Context(), tenantID.(uint), websiteID, uint(adID))
	if err != nil {
		response.NotFound(c.Writer, "Advertisement not found: "+err.Error())
		return
	}

	response.Success(c.Writer, advertisement)
}

// UpdateAdvertisement handles PUT /advertisements/:id
func (h *AdvertisementHandler) UpdateAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteID(c)
	if !ok {
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	var req dto.AdvertisementUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Update advertisement using service
	advertisement, err := h.serviceManager.AdvertisementService().UpdateAdvertisement(c.Request.Context(), tenantID.(uint), websiteID, uint(adID), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to update advertisement: "+err.Error())
		return
	}

	response.Success(c.Writer, advertisement)
}

// DeleteAdvertisement handles DELETE /advertisements/:id
func (h *AdvertisementHandler) DeleteAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteID(c)
	if !ok {
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Delete advertisement using service
	err = h.serviceManager.AdvertisementService().DeleteAdvertisement(c.Request.Context(), tenantID.(uint), websiteID, uint(adID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to delete advertisement: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Advertisement deleted successfully"})
}

// ListAdvertisements handles GET /advertisements
func (h *AdvertisementHandler) ListAdvertisements(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteID(c)
	if !ok {
		return
	}

	// Parse query parameters into filter DTO
	var filters dto.AdvertisementListFilter
	filters.TenantID = tenantID.(uint)

	// Bind query parameters
	if err := c.ShouldBindQuery(&filters); err != nil {
		response.BadRequest(c.Writer, "Invalid query parameters", err.Error())
		return
	}

	// Validate filters
	if err := h.validator.Validate(c.Request.Context(), &filters); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get advertisements using service
	responseData, err := h.serviceManager.AdvertisementService().ListAdvertisements(c.Request.Context(), tenantID.(uint), websiteID, &filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list advertisements: "+err.Error())
		return
	}

	response.Success(c.Writer, responseData)
}

// ListAdvertisementsByCampaign handles GET /campaigns/:id/advertisements
func (h *AdvertisementHandler) ListAdvertisementsByCampaign(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteID(c)
	if !ok {
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	// Get advertisements for campaign using service
	advertisements, err := h.serviceManager.AdvertisementService().ListAdvertisementsByCampaign(c.Request.Context(), tenantID.(uint), websiteID, uint(campaignID))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list advertisements for campaign: "+err.Error())
		return
	}

	response.Success(c.Writer, advertisements)
}

// ActivateAdvertisement handles POST /advertisements/:id/activate
func (h *AdvertisementHandler) ActivateAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteID(c)
	if !ok {
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Activate advertisement using service
	err = h.serviceManager.AdvertisementService().ActivateAdvertisement(c.Request.Context(), tenantID.(uint), websiteID, uint(adID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to activate advertisement: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Advertisement activated successfully"})
}

// PauseAdvertisement handles POST /advertisements/:id/pause
func (h *AdvertisementHandler) PauseAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteID(c)
	if !ok {
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Pause advertisement using service
	err = h.serviceManager.AdvertisementService().PauseAdvertisement(c.Request.Context(), tenantID.(uint), websiteID, uint(adID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to pause advertisement: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Advertisement paused successfully"})
}

// ServeAd handles GET /serve - public endpoint for serving ads
func (h *AdvertisementHandler) ServeAd(c *gin.Context) {
	// Parse ad serving request
	var req dto.AdServingRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request parameters", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Build targeting context
	var context models.TargetingContext

	// Get targeting parameters from headers and request
	context.DeviceType = req.DeviceType
	context.PageType = req.PageType
	context.PageURL = req.PageURL
	context.UserAgent = c.GetHeader("User-Agent")
	context.Referrer = c.GetHeader("Referer")
	context.IPAddress = c.ClientIP()
	context.Language = c.Query("language")

	// Serve ads using service
	servedAds, err := h.serviceManager.AdvertisementService().ServeAds(c.Request.Context(), &req, &context)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to serve advertisements: "+err.Error())
		return
	}

	response.Success(c.Writer, servedAds)
}
