package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

// AnalyticsHandler handles analytics and tracking related HTTP requests
type AnalyticsHandler struct {
	serviceManager services.ServiceManager
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(serviceManager services.ServiceManager) *AnalyticsHandler {
	return &AnalyticsHandler{
		serviceManager: serviceManager,
	}
}

// RecordImpression handles POST /track/impression - public endpoint
func (h *AnalyticsHandler) RecordImpression(c *gin.Context) {
	var req dto.ImpressionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Get additional context from request
	req.UserAgent = c.<PERSON>("User-Agent")
	req.IPAddress = c.ClientIP()
	req.Referrer = c.GetHeader("Referer")

	// Parse tenant ID from request body or query param
	tenantIDStr := c.Query("tenant_id")
	if tenantIDStr == "" {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	// Set tenant ID in request
	req.TenantID = uint(tenantID)

	// Record impression using service
	err = h.serviceManager.AnalyticsService().RecordImpression(c.Request.Context(), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to record impression: "+err.Error())
		return
	}

	trackingResponse := &dto.TrackingResponse{
		Success:   true,
		Message:   "Impression recorded successfully",
		Timestamp: time.Now(),
	}

	response.Created(c.Writer, trackingResponse)
}

// RecordClick handles POST /track/click - public endpoint
func (h *AnalyticsHandler) RecordClick(c *gin.Context) {
	var req dto.ClickCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Get additional context from request
	req.UserAgent = c.GetHeader("User-Agent")
	req.IPAddress = c.ClientIP()
	req.Referrer = c.GetHeader("Referer")

	// Parse tenant ID from request body or query param
	tenantIDStr := c.Query("tenant_id")
	if tenantIDStr == "" {
		response.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID")
		return
	}

	// Set tenant ID in request
	req.TenantID = uint(tenantID)

	// Record click using service
	err = h.serviceManager.AnalyticsService().RecordClick(c.Request.Context(), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to record click: "+err.Error())
		return
	}

	trackingResponse := &dto.TrackingResponse{
		Success:   true,
		Message:   "Click recorded successfully",
		Timestamp: time.Now(),
	}

	response.Created(c.Writer, trackingResponse)
}

// GetCampaignAnalytics handles GET /campaigns/:id/analytics
func (h *AnalyticsHandler) GetCampaignAnalytics(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	// Parse date range from query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid start_date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to last 30 days
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid end_date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to today
		endDate = time.Now()
	}

	// Create analytics request
	campaignIDUint := uint(campaignID)
	req := &dto.CampaignAnalyticsRequest{
		TenantID:   tenantID.(uint),
		CampaignID: &campaignIDUint,
		StartDate:  &startDate,
		EndDate:    &endDate,
	}

	// Get campaign analytics using service
	analytics, err := h.serviceManager.AnalyticsService().GetCampaignAnalytics(c.Request.Context(), req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get campaign analytics: "+err.Error())
		return
	}

	response.Success(c.Writer, analytics)
}

// GetAdvertisementAnalytics handles GET /advertisements/:id/analytics
func (h *AnalyticsHandler) GetAdvertisementAnalytics(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Parse date range from query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid start_date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to last 30 days
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid end_date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to today
		endDate = time.Now()
	}

	// Create advertisement analytics request
	req := &dto.AdvertisementAnalyticsRequest{
		TenantID:        tenantID.(uint),
		AdvertisementID: uint(adID),
		StartDate:       &startDate,
		EndDate:         &endDate,
	}

	// Get advertisement analytics using service
	analytics, err := h.serviceManager.AnalyticsService().GetAdvertisementAnalytics(c.Request.Context(), req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get advertisement analytics: "+err.Error())
		return
	}

	response.Success(c.Writer, analytics)
}

// GetDashboard handles GET /analytics/dashboard
func (h *AnalyticsHandler) GetDashboard(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse date range from query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		var err error
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid start_date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to last 30 days
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		var err error
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid end_date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to today
		endDate = time.Now()
	}

	// Create dashboard request
	req := &dto.AnalyticsSummaryRequest{
		TenantID:  tenantID.(uint),
		StartDate: &startDate,
		EndDate:   &endDate,
	}

	// Get dashboard data using service
	dashboardData, err := h.serviceManager.AnalyticsService().GenerateDashboardData(c.Request.Context(), req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get dashboard data: "+err.Error())
		return
	}

	response.Success(c.Writer, dashboardData)
}

// ExportAnalytics handles GET /analytics/export
func (h *AnalyticsHandler) ExportAnalytics(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse export format
	format := c.Query("format")
	if format == "" {
		format = "json" // Default format
	}

	// Validate format
	validFormats := []string{"json", "csv", "xlsx"}
	isValidFormat := false
	for _, validFormat := range validFormats {
		if format == validFormat {
			isValidFormat = true
			break
		}
	}
	if !isValidFormat {
		response.BadRequest(c.Writer, "Invalid format. Supported formats: json, csv, xlsx")
		return
	}

	// Parse date range from query parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		var err error
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid start_date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to last 30 days
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		var err error
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid end_date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to today
		endDate = time.Now()
	}

	// Parse campaign IDs (optional)
	if campaignIDsStr := c.Query("campaign_ids"); campaignIDsStr != "" {
		// Parse comma-separated campaign IDs
		// This is a simplified implementation - in production, use proper CSV parsing
		response.InternalServerError(c.Writer, "Campaign IDs filter not implemented yet")
		return
	}

	// Parse advertisement IDs (optional)
	if adIDsStr := c.Query("advertisement_ids"); adIDsStr != "" {
		// Parse comma-separated advertisement IDs
		// This is a simplified implementation - in production, use proper CSV parsing
		response.InternalServerError(c.Writer, "Advertisement IDs filter not implemented yet")
		return
	}

	// Parse include breakdown
	includeBreakdown := c.Query("include_breakdown") == "true"

	// Create export request
	req := &dto.AnalyticsExportRequest{
		TenantID:         tenantID.(uint),
		StartDate:        startDate,
		EndDate:          endDate,
		Format:           format,
		IncludeBreakdown: includeBreakdown,
	}

	// Export analytics data using service
	data, err := h.serviceManager.AnalyticsService().ExportAnalyticsData(c.Request.Context(), req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to export analytics data: "+err.Error())
		return
	}

	// Set appropriate content type based on format
	var contentType string
	var filename string
	switch format {
	case "json":
		contentType = "application/json"
		filename = "analytics_export.json"
	case "csv":
		contentType = "text/csv"
		filename = "analytics_export.csv"
	case "xlsx":
		contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		filename = "analytics_export.xlsx"
	}

	// Set headers for file download
	c.Header("Content-Type", contentType)
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(200, contentType, data)
}

// AggregateAnalytics handles POST /analytics/aggregate - admin endpoint
func (h *AnalyticsHandler) AggregateAnalytics(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getAnalyticsWebsiteID(c)
	if !ok {
		return
	}

	// Parse date from query parameter
	dateStr := c.Query("date")
	var date time.Time
	if dateStr != "" {
		var err error
		date, err = time.Parse("2006-01-02", dateStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid date format. Use YYYY-MM-DD")
			return
		}
	} else {
		// Default to yesterday
		date = time.Now().AddDate(0, 0, -1)
	}

	// Aggregate analytics using service
	err := h.serviceManager.AnalyticsService().AggregateAnalytics(c.Request.Context(), tenantID.(uint), websiteID, date)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to aggregate analytics: "+err.Error())
		return
	}

	responseData := gin.H{
		"date":      date.Format("2006-01-02"),
		"tenant_id": tenantID,
	}

	response.Success(c.Writer, responseData)
}

// getAnalyticsWebsiteID extracts and validates website ID from request header
func getAnalyticsWebsiteID(c *gin.Context) (uint, bool) {
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		response.BadRequest(c.Writer, "X-Website-ID header is required")
		return 0, false
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID format")
		return 0, false
	}

	return uint(websiteID), true
}
