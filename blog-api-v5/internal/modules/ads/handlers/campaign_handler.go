package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// CampaignHandler handles campaign-related HTTP requests
type CampaignHandler struct {
	serviceManager services.ServiceManager
	validator      validator.Validator
}

// NewCampaignHandler creates a new campaign handler
func NewCampaignHandler(serviceManager services.ServiceManager, validatorInstance validator.Validator) *CampaignHandler {
	return &CampaignHandler{
		serviceManager: serviceManager,
		validator:      validatorInstance,
	}
}

// getWebsiteIDFromHeader extracts and validates website ID from request header
func getWebsiteIDFromHeader(c *gin.Context) (uint, bool) {
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		response.BadRequest(c.Writer, "X-Website-ID header is required")
		return 0, false
	}
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid X-Website-ID header")
		return 0, false
	}
	return uint(websiteID), true
}

// CreateCampaign handles POST /campaigns
func (h *CampaignHandler) CreateCampaign(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteIDFromHeader(c)
	if !ok {
		return
	}

	var req dto.CampaignCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Create campaign using service
	campaign, err := h.serviceManager.CampaignService().CreateCampaign(c.Request.Context(), tenantID.(uint), websiteID, &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to create campaign: "+err.Error())
		return
	}

	response.Created(c.Writer, campaign)
}

// GetCampaign handles GET /campaigns/:id
func (h *CampaignHandler) GetCampaign(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteIDFromHeader(c)
	if !ok {
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	// Get campaign using service
	campaign, err := h.serviceManager.CampaignService().GetCampaignByID(c.Request.Context(), tenantID.(uint), websiteID, uint(campaignID))
	if err != nil {
		response.NotFound(c.Writer, "Campaign not found: "+err.Error())
		return
	}

	response.Success(c.Writer, campaign)
}

// UpdateCampaign handles PUT /campaigns/:id
func (h *CampaignHandler) UpdateCampaign(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteIDFromHeader(c)
	if !ok {
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	var req dto.CampaignUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Update campaign using service
	campaign, err := h.serviceManager.CampaignService().UpdateCampaign(c.Request.Context(), tenantID.(uint), websiteID, uint(campaignID), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to update campaign: "+err.Error())
		return
	}

	response.Success(c.Writer, campaign)
}

// DeleteCampaign handles DELETE /campaigns/:id
func (h *CampaignHandler) DeleteCampaign(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteIDFromHeader(c)
	if !ok {
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	// Delete campaign using service
	err = h.serviceManager.CampaignService().DeleteCampaign(c.Request.Context(), tenantID.(uint), websiteID, uint(campaignID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to delete campaign: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Operation successful"})
}

// ListCampaigns handles GET /campaigns
func (h *CampaignHandler) ListCampaigns(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteIDFromHeader(c)
	if !ok {
		return
	}

	// Parse query parameters into filter DTO
	var filters dto.CampaignListFilter
	filters.TenantID = tenantID.(uint)

	// Bind query parameters
	if err := c.ShouldBindQuery(&filters); err != nil {
		response.BadRequest(c.Writer, "Invalid query parameters", err.Error())
		return
	}

	// Validate filters
	if err := h.validator.Validate(c.Request.Context(), &filters); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get campaigns using service
	responseData, err := h.serviceManager.CampaignService().ListCampaigns(c.Request.Context(), tenantID.(uint), websiteID, &filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list campaigns: "+err.Error())
		return
	}

	response.Success(c.Writer, responseData)
}

// ActivateCampaign handles POST /campaigns/:id/activate
func (h *CampaignHandler) ActivateCampaign(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteIDFromHeader(c)
	if !ok {
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	// Activate campaign using service
	err = h.serviceManager.CampaignService().ActivateCampaign(c.Request.Context(), tenantID.(uint), websiteID, uint(campaignID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to activate campaign: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Operation successful"})
}

// PauseCampaign handles POST /campaigns/:id/pause
func (h *CampaignHandler) PauseCampaign(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteIDFromHeader(c)
	if !ok {
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	// Pause campaign using service
	err = h.serviceManager.CampaignService().PauseCampaign(c.Request.Context(), tenantID.(uint), websiteID, uint(campaignID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to pause campaign: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Operation successful"})
}

// GetCampaignStatus handles GET /campaigns/:id/status
func (h *CampaignHandler) GetCampaignStatus(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse campaign ID from URL parameter
	campaignIDStr := c.Param("id")
	campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid campaign ID")
		return
	}

	// Get website ID from header
	websiteID, ok := getWebsiteIDFromHeader(c)
	if !ok {
		return
	}

	// Get campaign first - we need the model for CheckCampaignStatus
	// For now, we'll need to access the repository directly since CheckCampaignStatus expects a model
	// This is a temporary solution until we refactor CheckCampaignStatus to work with DTOs
	campaignDTO, err := h.serviceManager.CampaignService().GetCampaignByID(c.Request.Context(), tenantID.(uint), websiteID, uint(campaignID))
	if err != nil {
		response.NotFound(c.Writer, "Campaign not found: "+err.Error())
		return
	}

	// For now, return the status from the DTO response
	status := string(campaignDTO.Status)

	responseData := gin.H{
		"campaign_id": campaignID,
		"status":      status,
		"is_active":   campaignDTO.IsActive,
		"is_running":  campaignDTO.IsRunning,
	}

	response.Success(c.Writer, responseData)
}
