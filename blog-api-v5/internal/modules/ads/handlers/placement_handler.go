package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// PlacementHandler handles placement-related HTTP requests
type PlacementHandler struct {
	serviceManager services.ServiceManager
}

// NewPlacementHandler creates a new placement handler
func NewPlacementHandler(serviceManager services.ServiceManager) *PlacementHandler {
	return &PlacementHandler{
		serviceManager: serviceManager,
	}
}

// CreatePlacement handles POST /placements
func (h *PlacementHandler) CreatePlacement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.<PERSON>, "Tenant not found in context")
		return
	}

	var req dto.PlacementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Set tenant ID
	req.TenantID = tenantID.(uint)

	// Create placement using service
	placement, err := h.serviceManager.PlacementService().CreatePlacement(c.Request.Context(), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to create placement: "+err.Error())
		return
	}

	response.Created(c.Writer, placement)
}

// GetPlacement handles GET /placements/:id
func (h *PlacementHandler) GetPlacement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse placement ID from URL parameter
	placementIDStr := c.Param("id")
	placementID, err := strconv.ParseUint(placementIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid placement ID")
		return
	}

	// Get website ID from header
	websiteID, ok := getPlacementWebsiteID(c)
	if !ok {
		return
	}

	// Get placement using service
	placement, err := h.serviceManager.PlacementService().GetPlacementByID(c.Request.Context(), tenantID.(uint), websiteID, uint(placementID))
	if err != nil {
		response.NotFound(c.Writer, "Placement not found: "+err.Error())
		return
	}

	response.Success(c.Writer, placement)
}

// UpdatePlacement handles PUT /placements/:id
func (h *PlacementHandler) UpdatePlacement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse placement ID from URL parameter
	placementIDStr := c.Param("id")
	placementID, err := strconv.ParseUint(placementIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid placement ID")
		return
	}

	// Get website ID from header
	websiteID, ok := getPlacementWebsiteID(c)
	if !ok {
		return
	}

	var req dto.PlacementUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Update placement using service
	placement, err := h.serviceManager.PlacementService().UpdatePlacement(c.Request.Context(), tenantID.(uint), websiteID, uint(placementID), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to update placement: "+err.Error())
		return
	}

	response.Success(c.Writer, placement)
}

// DeletePlacement handles DELETE /placements/:id
func (h *PlacementHandler) DeletePlacement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getPlacementWebsiteID(c)
	if !ok {
		return
	}

	// Parse placement ID from URL parameter
	placementIDStr := c.Param("id")
	placementID, err := strconv.ParseUint(placementIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid placement ID")
		return
	}

	// Delete placement using service
	err = h.serviceManager.PlacementService().DeletePlacement(c.Request.Context(), tenantID.(uint), websiteID, uint(placementID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to delete placement: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Operation successful"})
}

// ListPlacements handles GET /placements
func (h *PlacementHandler) ListPlacements(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse query parameters
	filters := &dto.PlacementListFilter{
		TenantID: tenantID.(uint),
	}

	// Parse page type filter
	if pageType := c.Query("page_type"); pageType != "" {
		filters.PageType = &pageType
	}

	// Parse position filter
	if position := c.Query("position"); position != "" {
		filters.Position = &position
	}

	// Parse status filter
	if status := c.Query("status"); status != "" {
		filters.Status = &status
	}

	// Parse is_active filter
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		isActive := isActiveStr == "true"
		filters.IsActive = &isActive
	}

	// Parse search
	if search := c.Query("search"); search != "" {
		filters.Search = search
	}

	// Parse pagination parameters
	if limitStr := c.Query("limit"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err == nil {
			if filters.Pagination == nil {
				filters.Pagination = &pagination.CursorPagination{}
			}
			filters.Pagination.Limit = limit
		}
	}

	if cursor := c.Query("cursor"); cursor != "" {
		if filters.Pagination == nil {
			filters.Pagination = &pagination.CursorPagination{}
		}
		filters.Pagination.Cursor = cursor
	}

	// Get placements using service
	placementList, err := h.serviceManager.PlacementService().ListPlacements(c.Request.Context(), filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list placements: "+err.Error())
		return
	}

	response.Success(c.Writer, placementList)
}

// GetPlacementsByPage handles GET /placements/page/:page_type
func (h *PlacementHandler) GetPlacementsByPage(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getPlacementWebsiteID(c)
	if !ok {
		return
	}

	// Parse page type from URL parameter
	pageType := c.Param("page_type")
	if pageType == "" {
		response.BadRequest(c.Writer, "Page type is required")
		return
	}

	// Get placements for page using service
	placements, err := h.serviceManager.PlacementService().GetPlacementsByPage(c.Request.Context(), tenantID.(uint), websiteID, pageType)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get placements for page: "+err.Error())
		return
	}

	responseData := gin.H{
		"page_type":  pageType,
		"placements": placements,
	}

	response.Success(c.Writer, responseData)
}

// GetOptimalPlacements handles GET /placements/optimal
func (h *PlacementHandler) GetOptimalPlacements(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getPlacementWebsiteID(c)
	if !ok {
		return
	}

	// Build targeting context from query parameters
	var context models.TargetingContext
	context.DeviceType = c.Query("device_type")
	context.Location = c.Query("location")
	context.UserAgent = c.GetHeader("User-Agent")
	context.Referrer = c.GetHeader("Referer")
	context.IPAddress = c.ClientIP()
	context.PageType = c.Query("page_type")
	context.PageURL = c.Query("page_url")
	context.Language = c.Query("language")

	// Get optimal placements using service
	placements, err := h.serviceManager.PlacementService().GetOptimalPlacements(c.Request.Context(), tenantID.(uint), websiteID, &context)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get optimal placements: "+err.Error())
		return
	}

	responseData := gin.H{
		"context":    context,
		"placements": placements,
	}

	response.Success(c.Writer, responseData)
}

// ValidatePlacement handles POST /placements/validate
func (h *PlacementHandler) ValidatePlacement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	var req dto.PlacementCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Set tenant ID
	req.TenantID = tenantID.(uint)

	// Validate placement using service
	validationResult, err := h.serviceManager.PlacementService().ValidatePlacement(c.Request.Context(), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Placement validation failed: "+err.Error())
		return
	}

	response.Success(c.Writer, validationResult)
}

// getPlacementWebsiteID extracts and validates website ID from request header
func getPlacementWebsiteID(c *gin.Context) (uint, bool) {
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		response.BadRequest(c.Writer, "X-Website-ID header is required")
		return 0, false
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID format")
		return 0, false
	}

	return uint(websiteID), true
}
