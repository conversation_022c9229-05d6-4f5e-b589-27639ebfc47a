package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// ScheduleHandler handles schedule-related HTTP requests
type ScheduleHandler struct {
	serviceManager services.ServiceManager
}

// NewScheduleHandler creates a new schedule handler
func NewScheduleHandler(serviceManager services.ServiceManager) *ScheduleHandler {
	return &ScheduleHandler{
		serviceManager: serviceManager,
	}
}

// getScheduleWebsiteID extracts and validates website ID from request header
func getScheduleWebsiteID(c *gin.Context) (uint, bool) {
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		response.BadRequest(c.Writer, "X-Website-ID header is required")
		return 0, false
	}
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid X-Website-ID header")
		return 0, false
	}
	return uint(websiteID), true
}

// CreateSchedule handles POST /schedules
func (h *ScheduleHandler) CreateSchedule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	var req dto.ScheduleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Set tenant ID
	req.TenantID = tenantID.(uint)

	// Create schedule using service
	schedule, err := h.serviceManager.ScheduleService().CreateSchedule(c.Request.Context(), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to create schedule: "+err.Error())
		return
	}

	response.Created(c.Writer, schedule)
}

// GetSchedule handles GET /schedules/:id
func (h *ScheduleHandler) GetSchedule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getScheduleWebsiteID(c)
	if !ok {
		return
	}

	// Parse schedule ID from URL parameter
	scheduleIDStr := c.Param("id")
	scheduleID, err := strconv.ParseUint(scheduleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid schedule ID")
		return
	}

	// Get schedule using service
	schedule, err := h.serviceManager.ScheduleService().GetScheduleByID(c.Request.Context(), tenantID.(uint), websiteID, uint(scheduleID))
	if err != nil {
		response.NotFound(c.Writer, "Schedule not found: "+err.Error())
		return
	}

	response.Success(c.Writer, schedule)
}

// UpdateSchedule handles PUT /schedules/:id
func (h *ScheduleHandler) UpdateSchedule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getScheduleWebsiteID(c)
	if !ok {
		return
	}

	// Parse schedule ID from URL parameter
	scheduleIDStr := c.Param("id")
	scheduleID, err := strconv.ParseUint(scheduleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid schedule ID")
		return
	}

	var req dto.ScheduleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Update schedule using service
	schedule, err := h.serviceManager.ScheduleService().UpdateSchedule(c.Request.Context(), tenantID.(uint), websiteID, uint(scheduleID), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to update schedule: "+err.Error())
		return
	}

	response.Success(c.Writer, schedule)
}

// DeleteSchedule handles DELETE /schedules/:id
func (h *ScheduleHandler) DeleteSchedule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getScheduleWebsiteID(c)
	if !ok {
		return
	}

	// Parse schedule ID from URL parameter
	scheduleIDStr := c.Param("id")
	scheduleID, err := strconv.ParseUint(scheduleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid schedule ID")
		return
	}

	// Delete schedule using service
	err = h.serviceManager.ScheduleService().DeleteSchedule(c.Request.Context(), tenantID.(uint), websiteID, uint(scheduleID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to delete schedule: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Operation successful"})
}

// ListSchedules handles GET /schedules
func (h *ScheduleHandler) ListSchedules(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse query parameters
	filters := &dto.ScheduleListFilter{
		TenantID: tenantID.(uint),
	}

	// Parse advertisement ID filter
	if adIDStr := c.Query("advertisement_id"); adIDStr != "" {
		adID, err := strconv.ParseUint(adIDStr, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid advertisement_id parameter")
			return
		}
		adIDUint := uint(adID)
		filters.AdvertisementID = &adIDUint
	}

	// Parse campaign ID filter
	if campaignIDStr := c.Query("campaign_id"); campaignIDStr != "" {
		campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid campaign_id parameter")
			return
		}
		campaignIDUint := uint(campaignID)
		filters.CampaignID = &campaignIDUint
	}

	// Parse status filter
	if status := c.Query("status"); status != "" {
		filters.Status = &status
	}

	// Parse timezone filter
	if timezone := c.Query("timezone"); timezone != "" {
		filters.Timezone = &timezone
	}

	// Parse date filters
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		startDate, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			filters.StartDate = &startDate
		}
	}

	if endDateStr := c.Query("end_date"); endDateStr != "" {
		endDate, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			filters.EndDate = &endDate
		}
	}

	// Parse is_active filter
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		isActive := isActiveStr == "true"
		filters.IsActive = &isActive
	}

	// Parse search
	if search := c.Query("search"); search != "" {
		filters.Search = search
	}

	// Parse pagination parameters
	if limitStr := c.Query("limit"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err == nil {
			if filters.Pagination == nil {
				filters.Pagination = &pagination.CursorPagination{}
			}
			filters.Pagination.Limit = limit
		}
	}

	if cursor := c.Query("cursor"); cursor != "" {
		if filters.Pagination == nil {
			filters.Pagination = &pagination.CursorPagination{}
		}
		filters.Pagination.Cursor = cursor
	}

	// Get schedules using service
	scheduleList, err := h.serviceManager.ScheduleService().ListSchedules(c.Request.Context(), filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list schedules: "+err.Error())
		return
	}

	response.Success(c.Writer, scheduleList)
}

// GetActiveSchedules handles GET /schedules/active
func (h *ScheduleHandler) GetActiveSchedules(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Use current time or parse from query parameter
	currentTime := time.Now()
	if timeStr := c.Query("time"); timeStr != "" {
		var err error
		currentTime, err = time.Parse(time.RFC3339, timeStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid time format. Use RFC3339 format")
			return
		}
	}

	// Create active schedules request
	req := &dto.ScheduleActiveRequest{
		TenantID:  tenantID.(uint),
		CheckTime: &currentTime,
	}

	// Parse advertisement ID filter if provided
	if adIDStr := c.Query("advertisement_id"); adIDStr != "" {
		adID, err := strconv.ParseUint(adIDStr, 10, 32)
		if err == nil {
			adIDUint := uint(adID)
			req.AdvertisementID = &adIDUint
		}
	}

	// Parse campaign ID filter if provided
	if campaignIDStr := c.Query("campaign_id"); campaignIDStr != "" {
		campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
		if err == nil {
			campaignIDUint := uint(campaignID)
			req.CampaignID = &campaignIDUint
		}
	}

	// Parse timezone if provided
	if timezone := c.Query("timezone"); timezone != "" {
		req.Timezone = &timezone
	}

	// Get active schedules using service
	activeSchedules, err := h.serviceManager.ScheduleService().GetActiveSchedules(c.Request.Context(), req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get active schedules: "+err.Error())
		return
	}

	response.Success(c.Writer, activeSchedules)
}

// CheckScheduleStatus handles GET /schedules/:id/status
func (h *ScheduleHandler) CheckScheduleStatus(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse schedule ID from URL parameter
	scheduleIDStr := c.Param("id")
	scheduleID, err := strconv.ParseUint(scheduleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid schedule ID")
		return
	}

	// Get website ID from header
	websiteID, ok := getScheduleWebsiteID(c)
	if !ok {
		return
	}

	// Verify schedule exists
	_, err = h.serviceManager.ScheduleService().GetScheduleByID(c.Request.Context(), tenantID.(uint), websiteID, uint(scheduleID))
	if err != nil {
		response.NotFound(c.Writer, "Schedule not found: "+err.Error())
		return
	}

	// Use current time or parse from query parameter
	currentTime := time.Now()
	if timeStr := c.Query("time"); timeStr != "" {
		currentTime, err = time.Parse(time.RFC3339, timeStr)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid time format. Use RFC3339 format")
			return
		}
	}

	// Create status request
	req := &dto.ScheduleStatusRequest{
		TenantID:   tenantID.(uint),
		ScheduleID: uint(scheduleID),
		CheckTime:  currentTime,
	}

	// Check schedule status using service
	statusResponse, err := h.serviceManager.ScheduleService().CheckScheduleStatus(c.Request.Context(), req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to check schedule status: "+err.Error())
		return
	}

	response.Success(c.Writer, statusResponse)
}
