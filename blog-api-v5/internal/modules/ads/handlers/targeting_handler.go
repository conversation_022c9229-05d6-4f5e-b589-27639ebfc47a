package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// TargetingHandler handles targeting rule related HTTP requests
type TargetingHandler struct {
	serviceManager services.ServiceManager
}

// NewTargetingHandler creates a new targeting handler
func NewTargetingHandler(serviceManager services.ServiceManager) *TargetingHandler {
	return &TargetingHandler{
		serviceManager: serviceManager,
	}
}

// getTargetingWebsiteID extracts and validates website ID from request header
func getTargetingWebsiteID(c *gin.Context) (uint, bool) {
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		response.BadRequest(c.Writer, "X-Website-ID header is required")
		return 0, false
	}
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid X-Website-ID header")
		return 0, false
	}
	return uint(websiteID), true
}

// CreateTargetingRule handles POST /targeting-rules
func (h *TargetingHandler) CreateTargetingRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	var req dto.TargetingRuleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Set tenant ID
	req.TenantID = tenantID.(uint)

	// Create targeting rule using service
	targetingRule, err := h.serviceManager.TargetingService().CreateTargetingRule(c.Request.Context(), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to create targeting rule: "+err.Error())
		return
	}

	response.Created(c.Writer, targetingRule)
}

// GetTargetingRule handles GET /targeting-rules/:id
func (h *TargetingHandler) GetTargetingRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse targeting rule ID from URL parameter
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid targeting rule ID")
		return
	}

	// Get website ID from header
	websiteID, ok := getTargetingWebsiteID(c)
	if !ok {
		return
	}

	// Get targeting rule using service
	targetingRule, err := h.serviceManager.TargetingService().GetTargetingRuleByID(c.Request.Context(), tenantID.(uint), websiteID, uint(ruleID))
	if err != nil {
		response.NotFound(c.Writer, "Targeting rule not found: "+err.Error())
		return
	}

	response.Success(c.Writer, targetingRule)
}

// UpdateTargetingRule handles PUT /targeting-rules/:id
func (h *TargetingHandler) UpdateTargetingRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse targeting rule ID from URL parameter
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid targeting rule ID")
		return
	}

	// Get website ID from header
	websiteID, ok := getTargetingWebsiteID(c)
	if !ok {
		return
	}

	var req dto.TargetingRuleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Update targeting rule using service
	targetingRule, err := h.serviceManager.TargetingService().UpdateTargetingRule(c.Request.Context(), tenantID.(uint), websiteID, uint(ruleID), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Failed to update targeting rule: "+err.Error())
		return
	}

	response.Success(c.Writer, targetingRule)
}

// DeleteTargetingRule handles DELETE /targeting-rules/:id
func (h *TargetingHandler) DeleteTargetingRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getTargetingWebsiteID(c)
	if !ok {
		return
	}

	// Parse targeting rule ID from URL parameter
	ruleIDStr := c.Param("id")
	ruleID, err := strconv.ParseUint(ruleIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid targeting rule ID")
		return
	}

	// Delete targeting rule using service
	err = h.serviceManager.TargetingService().DeleteTargetingRule(c.Request.Context(), tenantID.(uint), websiteID, uint(ruleID))
	if err != nil {
		response.BadRequest(c.Writer, "Failed to delete targeting rule: "+err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Operation successful"})
}

// ListTargetingRules handles GET /targeting-rules
func (h *TargetingHandler) ListTargetingRules(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse query parameters
	filters := &dto.TargetingRuleListFilter{
		TenantID: tenantID.(uint),
	}

	// Parse advertisement ID filter
	if adIDStr := c.Query("advertisement_id"); adIDStr != "" {
		adID, err := strconv.ParseUint(adIDStr, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid advertisement_id parameter")
			return
		}
		adIDUint := uint(adID)
		filters.AdvertisementID = &adIDUint
	}

	// Parse campaign ID filter
	if campaignIDStr := c.Query("campaign_id"); campaignIDStr != "" {
		campaignID, err := strconv.ParseUint(campaignIDStr, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid campaign_id parameter")
			return
		}
		campaignIDUint := uint(campaignID)
		filters.CampaignID = &campaignIDUint
	}

	// Parse rule type filter
	if ruleType := c.Query("rule_type"); ruleType != "" {
		filters.RuleType = &ruleType
	}

	// Parse status filter
	if status := c.Query("status"); status != "" {
		filters.Status = &status
	}

	// Parse is_active filter
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		isActive := isActiveStr == "true"
		filters.IsActive = &isActive
	}

	// Parse priority filter
	if priorityStr := c.Query("priority"); priorityStr != "" {
		priority, err := strconv.Atoi(priorityStr)
		if err == nil {
			filters.Priority = &priority
		}
	}

	// Parse search
	if search := c.Query("search"); search != "" {
		filters.Search = search
	}

	// Parse pagination parameters
	if limitStr := c.Query("limit"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err == nil {
			if filters.Pagination == nil {
				filters.Pagination = &pagination.CursorPagination{}
			}
			filters.Pagination.Limit = limit
		}
	}

	if cursor := c.Query("cursor"); cursor != "" {
		if filters.Pagination == nil {
			filters.Pagination = &pagination.CursorPagination{}
		}
		filters.Pagination.Cursor = cursor
	}

	// Get targeting rules using service
	targetingRuleList, err := h.serviceManager.TargetingService().ListTargetingRules(c.Request.Context(), filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to list targeting rules: "+err.Error())
		return
	}

	response.Success(c.Writer, targetingRuleList)
}

// GetRulesByAdvertisement handles GET /advertisements/:id/targeting-rules
func (h *TargetingHandler) GetRulesByAdvertisement(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Get website ID from header
	websiteID, ok := getTargetingWebsiteID(c)
	if !ok {
		return
	}

	// Parse advertisement ID from URL parameter
	adIDStr := c.Param("id")
	adID, err := strconv.ParseUint(adIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid advertisement ID")
		return
	}

	// Get targeting rules for advertisement using service
	targetingRules, err := h.serviceManager.TargetingService().GetRulesByAdvertisement(c.Request.Context(), tenantID.(uint), websiteID, uint(adID))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get targeting rules for advertisement: "+err.Error())
		return
	}

	responseData := gin.H{
		"advertisement_id": adID,
		"targeting_rules":  targetingRules,
	}

	response.Success(c.Writer, responseData)
}

// EvaluateRules handles POST /targeting-rules/evaluate
func (h *TargetingHandler) EvaluateRules(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	// Parse request body
	var req dto.TargetingEvaluationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Set tenant ID
	req.TenantID = tenantID.(uint)

	// Evaluate rules using service
	evaluationResult, err := h.serviceManager.TargetingService().EvaluateRules(c.Request.Context(), &req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to evaluate targeting rules: "+err.Error())
		return
	}

	response.Success(c.Writer, evaluationResult)
}

// ValidateRule handles POST /targeting-rules/validate
func (h *TargetingHandler) ValidateRule(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant not found in context")
		return
	}

	var req dto.TargetingRuleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request payload: "+err.Error())
		return
	}

	// Set tenant ID
	req.TenantID = tenantID.(uint)

	// Validate targeting rule using service
	validationResult, err := h.serviceManager.TargetingService().ValidateRule(c.Request.Context(), &req)
	if err != nil {
		response.BadRequest(c.Writer, "Targeting rule validation failed: "+err.Error())
		return
	}

	response.Success(c.Writer, validationResult)
}
