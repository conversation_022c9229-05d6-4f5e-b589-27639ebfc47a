package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// AdvertisementStatus represents the status of an advertisement
// @Enum draft,active,paused,expired,deleted
type AdvertisementStatus string

const (
	AdvertisementStatusDraft   AdvertisementStatus = "draft"
	AdvertisementStatusActive  AdvertisementStatus = "active"
	AdvertisementStatusPaused  AdvertisementStatus = "paused"
	AdvertisementStatusExpired AdvertisementStatus = "expired"
	AdvertisementStatusDeleted AdvertisementStatus = "deleted"
)

// Scan implements sql.Scanner interface
func (s *AdvertisementStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = AdvertisementStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s AdvertisementStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// AdType represents the type of advertisement
// @Enum banner,text,rich_media,native,video
type AdType string

const (
	AdTypeBanner    AdType = "banner"
	AdTypeText      AdType = "text"
	AdTypeRichMedia AdType = "rich_media"
	AdTypeNative    AdType = "native"
	AdTypeVideo     AdType = "video"
)

// Scan implements sql.Scanner interface
func (t *AdType) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*t = AdType(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (t AdType) Value() (driver.Value, error) {
	return string(t), nil
}

// DeviceTargeting represents device targeting options
// @Enum web_pc,web_mobile,both
type DeviceTargeting string

const (
	DeviceTargetingWebPC     DeviceTargeting = "web_pc"
	DeviceTargetingWebMobile DeviceTargeting = "web_mobile"
	DeviceTargetingBoth      DeviceTargeting = "both"
)

// Scan implements sql.Scanner interface
func (d *DeviceTargeting) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*d = DeviceTargeting(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (d DeviceTargeting) Value() (driver.Value, error) {
	return string(d), nil
}

// AdPosition represents advertisement position options
// @Enum header,sidebar,footer,inline,popup
type AdPosition string

const (
	AdPositionHeader  AdPosition = "header"
	AdPositionSidebar AdPosition = "sidebar"
	AdPositionFooter  AdPosition = "footer"
	AdPositionInline  AdPosition = "inline"
	AdPositionPopup   AdPosition = "popup"
)

// Scan implements sql.Scanner interface
func (p *AdPosition) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*p = AdPosition(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (p AdPosition) Value() (driver.Value, error) {
	return string(p), nil
}

// Advertisement represents an individual advertisement
type Advertisement struct {
	ID              uint                `json:"id" gorm:"primaryKey"`
	TenantID        uint                `json:"tenant_id" gorm:"not null;index"`
	WebsiteID       uint                `json:"website_id" gorm:"not null;index"`
	CampaignID      uint                `json:"campaign_id" gorm:"not null;index"`
	Title           string              `json:"title" gorm:"not null;size:255" validate:"required,min=1,max=255"`
	Description     string              `json:"description,omitempty" gorm:"type:text"`
	ImageURL        string              `json:"image_url,omitempty" gorm:"size:500"`
	LinkURL         string              `json:"link_url" gorm:"not null;size:500" validate:"required,url"`
	AdType          AdType              `json:"ad_type" gorm:"type:enum('banner','text','rich_media','native','video');default:'banner'" validate:"oneof=banner text rich_media native video"`
	DeviceTargeting DeviceTargeting     `json:"device_targeting" gorm:"type:enum('web_pc','web_mobile','both');default:'both'" validate:"oneof=web_pc web_mobile both"`
	PageTargeting   datatypes.JSON      `json:"page_targeting" gorm:"type:json"`
	Position        AdPosition          `json:"position" gorm:"type:enum('header','sidebar','footer','inline','popup');default:'sidebar'" validate:"oneof=header sidebar footer inline popup"`
	Priority        int                 `json:"priority" gorm:"not null;default:5" validate:"min=1,max=10"`
	Status          AdvertisementStatus `json:"status" gorm:"type:enum('draft','active','paused','expired','deleted');default:'draft'" validate:"oneof=draft active paused expired deleted"`
	CreatedAt       time.Time           `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt       time.Time           `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`

	// Relationships
	Campaign       Campaign        `json:"campaign,omitempty" gorm:"foreignKey:CampaignID"`
	Schedules      []Schedule      `json:"schedules,omitempty" gorm:"foreignKey:AdvertisementID"`
	Impressions    []Impression    `json:"impressions,omitempty" gorm:"foreignKey:AdvertisementID"`
	Clicks         []Click         `json:"clicks,omitempty" gorm:"foreignKey:AdvertisementID"`
	Analytics      []Analytics     `json:"analytics,omitempty" gorm:"foreignKey:AdvertisementID"`
	TargetingRules []TargetingRule `json:"targeting_rules,omitempty" gorm:"foreignKey:AdvertisementID"`

	// Media integration fields
	MediaFileIDs []uint `json:"media_file_ids,omitempty" gorm:"-"`
}

// TableName returns the table name for the Advertisement model
func (Advertisement) TableName() string {
	return "ads_advertisements"
}

// BeforeCreate is a GORM hook that runs before creating an advertisement
func (a *Advertisement) BeforeCreate(tx *gorm.DB) error {
	if a.Status == "" {
		a.Status = AdvertisementStatusDraft
	}
	if a.AdType == "" {
		a.AdType = AdTypeBanner
	}
	if a.DeviceTargeting == "" {
		a.DeviceTargeting = DeviceTargetingBoth
	}
	if a.Position == "" {
		a.Position = AdPositionSidebar
	}
	if a.Priority == 0 {
		a.Priority = 5
	}
	return nil
}

// IsActive returns true if the advertisement is active
func (a *Advertisement) IsActive() bool {
	return a.Status == AdvertisementStatusActive
}

// SupportsDevice returns true if the advertisement supports the given device type
func (a *Advertisement) SupportsDevice(deviceType string) bool {
	return a.DeviceTargeting == DeviceTargetingBoth || a.DeviceTargeting == DeviceTargeting(deviceType)
}

// SupportsPage returns true if the advertisement supports the given page type
func (a *Advertisement) SupportsPage(pageType string) bool {
	if a.PageTargeting == nil {
		return true // No targeting means show on all pages
	}

	var targets []string
	if err := json.Unmarshal(a.PageTargeting, &targets); err != nil {
		return false
	}

	if len(targets) == 0 {
		return true // Empty targeting means show on all pages
	}

	for _, target := range targets {
		if target == pageType {
			return true
		}
	}
	return false
}
