package models

import (
	"time"

	"gorm.io/datatypes"
)

// Analytics represents aggregated analytics data for advertisements
type Analytics struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	TenantID         uint           `json:"tenant_id" gorm:"not null;index"`
	WebsiteID        uint           `json:"website_id" gorm:"not null;index"`
	AdvertisementID  uint           `json:"advertisement_id" gorm:"not null;index"`
	AnalyticsDate    time.Time      `json:"analytics_date" gorm:"type:date;not null" validate:"required"`
	ImpressionsCount int            `json:"impressions_count" gorm:"default:0" validate:"min=0"`
	ClicksCount      int            `json:"clicks_count" gorm:"default:0" validate:"min=0"`
	CTRRate          float64        `json:"ctr_rate" gorm:"type:decimal(5,4);default:0.0000" validate:"min=0,max=1"`
	CostPerClick     float64        `json:"cost_per_click" gorm:"type:decimal(10,2);default:0.00" validate:"min=0"`
	Revenue          float64        `json:"revenue" gorm:"type:decimal(10,2);default:0.00" validate:"min=0"`
	DeviceBreakdown  datatypes.JSON `json:"device_breakdown" gorm:"type:json"`
	PageBreakdown    datatypes.JSON `json:"page_breakdown" gorm:"type:json"`
	CreatedAt        time.Time      `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt        time.Time      `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`

	// Relationships
	Advertisement Advertisement `json:"advertisement,omitempty" gorm:"foreignKey:AdvertisementID"`
}

// TableName returns the table name for the Analytics model
func (Analytics) TableName() string {
	return "ads_analytics"
}

// CalculateCTR calculates the click-through rate
func (a *Analytics) CalculateCTR() float64 {
	if a.ImpressionsCount == 0 {
		return 0.0
	}
	return float64(a.ClicksCount) / float64(a.ImpressionsCount)
}

// DeviceBreakdownData represents device breakdown data
type DeviceBreakdownData struct {
	WebPC     DeviceStats `json:"web_pc"`
	WebMobile DeviceStats `json:"web_mobile"`
	Unknown   DeviceStats `json:"unknown"`
}

// DeviceStats represents statistics for a device type
type DeviceStats struct {
	Impressions int     `json:"impressions"`
	Clicks      int     `json:"clicks"`
	CTRRate     float64 `json:"ctr_rate"`
}

// PageBreakdownData represents page breakdown data
type PageBreakdownData struct {
	Homepage PageStats `json:"homepage"`
	Category PageStats `json:"category"`
	Article  PageStats `json:"article"`
	Tag      PageStats `json:"tag"`
	Custom   PageStats `json:"custom"`
}

// PageStats represents statistics for a page type
type PageStats struct {
	Impressions int     `json:"impressions"`
	Clicks      int     `json:"clicks"`
	CTRRate     float64 `json:"ctr_rate"`
}

// DateRange represents a date range
type DateRange struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
}

// AnalyticsSummary represents summary analytics data
type AnalyticsSummary struct {
	TotalImpressions int     `json:"total_impressions"`
	TotalClicks      int     `json:"total_clicks"`
	CTRRate          float64 `json:"ctr_rate"`
	TotalRevenue     float64 `json:"total_revenue"`
	CostPerClick     float64 `json:"cost_per_click"`
}

// DailyAnalytics represents daily analytics data
type DailyAnalytics struct {
	Date        time.Time `json:"date"`
	Impressions int       `json:"impressions"`
	Clicks      int       `json:"clicks"`
	CTRRate     float64   `json:"ctr_rate"`
	Revenue     float64   `json:"revenue"`
}

// WeeklyAnalytics represents weekly analytics data
type WeeklyAnalytics struct {
	Week        time.Time `json:"week"`
	Impressions int       `json:"impressions"`
	Clicks      int       `json:"clicks"`
	CTRRate     float64   `json:"ctr_rate"`
	Revenue     float64   `json:"revenue"`
}

// MonthlyAnalytics represents monthly analytics data
type MonthlyAnalytics struct {
	Month       time.Time `json:"month"`
	Impressions int       `json:"impressions"`
	Clicks      int       `json:"clicks"`
	CTRRate     float64   `json:"ctr_rate"`
	Revenue     float64   `json:"revenue"`
}

// AdvertisementAnalytics represents analytics data for an advertisement
type AdvertisementAnalytics struct {
	AdvertisementID   uint    `json:"advertisement_id"`
	AdvertisementName string  `json:"advertisement_name"`
	Impressions       int     `json:"impressions"`
	Clicks            int     `json:"clicks"`
	CTR               float64 `json:"ctr"`
	Revenue           float64 `json:"revenue"`
}
