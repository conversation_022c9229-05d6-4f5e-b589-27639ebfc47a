package models

import (
	"database/sql/driver"
	"time"

	"gorm.io/gorm"
)

// CampaignStatus represents the status of an advertising campaign
// @Enum draft,active,paused,completed,cancelled,deleted
type CampaignStatus string

const (
	CampaignStatusDraft     CampaignStatus = "draft"
	CampaignStatusActive    CampaignStatus = "active"
	CampaignStatusPaused    CampaignStatus = "paused"
	CampaignStatusCompleted CampaignStatus = "completed"
	CampaignStatusCancelled CampaignStatus = "cancelled"
	CampaignStatusDeleted   CampaignStatus = "deleted"
)

// Scan implements sql.Scanner interface
func (s *CampaignStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = CampaignStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s CampaignStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// Campaign represents an advertising campaign
type Campaign struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	TenantID    uint           `json:"tenant_id" gorm:"not null;index"`
	WebsiteID   uint           `json:"website_id" gorm:"not null;index"`
	Name        string         `json:"name" gorm:"not null;size:255" validate:"required,min=1,max=255"`
	Description string         `json:"description,omitempty" gorm:"type:text"`
	Budget      float64        `json:"budget" gorm:"type:decimal(10,2);default:0.00" validate:"min=0"`
	Status      CampaignStatus `json:"status" gorm:"type:enum('draft','active','paused','completed','cancelled','deleted');default:'draft'" validate:"oneof=draft active paused completed cancelled deleted"`
	StartDate   time.Time      `json:"start_date" gorm:"not null" validate:"required"`
	EndDate     time.Time      `json:"end_date" gorm:"not null" validate:"required"`
	CreatedAt   time.Time      `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`

	// Relationships
	Advertisements []Advertisement `json:"advertisements,omitempty" gorm:"foreignKey:CampaignID"`
}

// TableName returns the table name for the Campaign model
func (Campaign) TableName() string {
	return "ads_campaigns"
}

// BeforeCreate is a GORM hook that runs before creating a campaign
func (c *Campaign) BeforeCreate(tx *gorm.DB) error {
	if c.Status == "" {
		c.Status = CampaignStatusDraft
	}
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a campaign
func (c *Campaign) BeforeUpdate(tx *gorm.DB) error {
	// Validate date range
	if c.EndDate.Before(c.StartDate) {
		return gorm.ErrInvalidData
	}
	return nil
}

// IsActive returns true if the campaign is active
func (c *Campaign) IsActive() bool {
	return c.Status == CampaignStatusActive
}

// IsRunning returns true if the campaign is currently running
func (c *Campaign) IsRunning() bool {
	now := time.Now()
	return c.IsActive() && c.StartDate.Before(now) && c.EndDate.After(now)
}
