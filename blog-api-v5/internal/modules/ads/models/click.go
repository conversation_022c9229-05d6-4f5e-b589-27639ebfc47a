package models

import (
	"time"
)

// Click represents an advertisement click event
type Click struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	TenantID        uint      `json:"tenant_id" gorm:"not null;index"`
	WebsiteID       uint      `json:"website_id" gorm:"not null;index"`
	AdvertisementID uint      `json:"advertisement_id" gorm:"not null;index"`
	ImpressionID    *uint     `json:"impression_id,omitempty" gorm:"index"`
	UserAgent       string    `json:"user_agent,omitempty" gorm:"size:500"`
	IPAddress       string    `json:"ip_address,omitempty" gorm:"size:45"`
	Referrer        string    `json:"referrer,omitempty" gorm:"size:500"`
	DeviceType      string    `json:"device_type" gorm:"type:enum('web_pc','web_mobile','unknown');default:'unknown'" validate:"oneof=web_pc web_mobile unknown"`
	PageURL         string    `json:"page_url,omitempty" gorm:"size:500"`
	ClickedAt       time.Time `json:"clicked_at" gorm:"default:CURRENT_TIMESTAMP"`
	DestinationURL  string    `json:"destination_url,omitempty" gorm:"size:500"`
	CreatedAt       time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`

	// Relationships
	Advertisement Advertisement `json:"advertisement,omitempty" gorm:"foreignKey:AdvertisementID"`
	Impression    *Impression   `json:"impression,omitempty" gorm:"foreignKey:ImpressionID"`
}

// TableName returns the table name for the Click model
func (Click) TableName() string {
	return "ads_clicks"
}

// ClickCreateRequest represents the request to create a click
type ClickCreateRequest struct {
	AdvertisementID uint   `json:"advertisement_id" validate:"required"`
	ImpressionID    *uint  `json:"impression_id,omitempty"`
	UserAgent       string `json:"user_agent,omitempty"`
	IPAddress       string `json:"ip_address,omitempty"`
	Referrer        string `json:"referrer,omitempty"`
	DeviceType      string `json:"device_type,omitempty" validate:"omitempty,oneof=web_pc web_mobile unknown"`
	PageURL         string `json:"page_url,omitempty" validate:"omitempty,url"`
	DestinationURL  string `json:"destination_url,omitempty" validate:"omitempty,url"`
}

// ClickResponse represents the response structure for click
type ClickResponse struct {
	ID              uint      `json:"id"`
	TenantID        uint      `json:"tenant_id"`
	WebsiteID       uint      `json:"website_id"`
	AdvertisementID uint      `json:"advertisement_id"`
	ImpressionID    *uint     `json:"impression_id,omitempty"`
	UserAgent       string    `json:"user_agent,omitempty"`
	IPAddress       string    `json:"ip_address,omitempty"`
	Referrer        string    `json:"referrer,omitempty"`
	DeviceType      string    `json:"device_type"`
	PageURL         string    `json:"page_url,omitempty"`
	ClickedAt       time.Time `json:"clicked_at"`
	DestinationURL  string    `json:"destination_url,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
}

// ToClickResponse converts Click to ClickResponse
func (c *Click) ToClickResponse() *ClickResponse {
	return &ClickResponse{
		ID:              c.ID,
		TenantID:        c.TenantID,
		WebsiteID:       c.WebsiteID,
		AdvertisementID: c.AdvertisementID,
		ImpressionID:    c.ImpressionID,
		UserAgent:       c.UserAgent,
		IPAddress:       c.IPAddress,
		Referrer:        c.Referrer,
		DeviceType:      c.DeviceType,
		PageURL:         c.PageURL,
		ClickedAt:       c.ClickedAt,
		DestinationURL:  c.DestinationURL,
		CreatedAt:       c.CreatedAt,
	}
}

// ClickStatsRequest represents the request to get click statistics
type ClickStatsRequest struct {
	StartDate       *time.Time `json:"start_date,omitempty"`
	EndDate         *time.Time `json:"end_date,omitempty"`
	AdvertisementID *uint      `json:"advertisement_id,omitempty"`
	DeviceType      *string    `json:"device_type,omitempty" validate:"omitempty,oneof=web_pc web_mobile unknown"`
	PageType        *string    `json:"page_type,omitempty"`
}

// ClickStatsResponse represents the response structure for click statistics
type ClickStatsResponse struct {
	TotalClicks     int                         `json:"total_clicks"`
	UniqueClicks    int                         `json:"unique_clicks"`
	DeviceBreakdown map[string]int              `json:"device_breakdown"`
	PageBreakdown   map[string]int              `json:"page_breakdown"`
	DailyClicks     []DailyClickStat            `json:"daily_clicks"`
	TopAds          []TopClickAdvertisementStat `json:"top_ads"`
}

// DailyClickStat represents daily click statistics
type DailyClickStat struct {
	Date         time.Time `json:"date"`
	Clicks       int       `json:"clicks"`
	UniqueClicks int       `json:"unique_clicks"`
}

// TopClickAdvertisementStat represents top performing advertisement click statistics
type TopClickAdvertisementStat struct {
	AdvertisementID uint    `json:"advertisement_id"`
	Title           string  `json:"title"`
	Clicks          int     `json:"clicks"`
	UniqueClicks    int     `json:"unique_clicks"`
	CTR             float64 `json:"ctr"`
}
