package models

import (
	"time"
)

// Impression represents an advertisement impression event
type Impression struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	TenantID        uint      `json:"tenant_id" gorm:"not null;index"`
	WebsiteID       uint      `json:"website_id" gorm:"not null;index"`
	AdvertisementID uint      `json:"advertisement_id" gorm:"not null;index"`
	PlacementID     uint      `json:"placement_id" gorm:"not null;index"`
	UserAgent       string    `json:"user_agent,omitempty" gorm:"size:500"`
	IPAddress       string    `json:"ip_address,omitempty" gorm:"size:45"`
	Referrer        string    `json:"referrer,omitempty" gorm:"size:500"`
	DeviceType      string    `json:"device_type" gorm:"type:enum('web_pc','web_mobile','unknown');default:'unknown'" validate:"oneof=web_pc web_mobile unknown"`
	PageURL         string    `json:"page_url,omitempty" gorm:"size:500"`
	ViewedAt        time.Time `json:"viewed_at" gorm:"default:CURRENT_TIMESTAMP"`
	ViewDuration    int       `json:"view_duration" gorm:"default:0" validate:"min=0"`
	CreatedAt       time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`

	// Relationships
	Advertisement Advertisement `json:"advertisement,omitempty" gorm:"foreignKey:AdvertisementID"`
	Placement     Placement     `json:"placement,omitempty" gorm:"foreignKey:PlacementID"`
	Clicks        []Click       `json:"clicks,omitempty" gorm:"foreignKey:ImpressionID"`
}

// TableName returns the table name for the Impression model
func (Impression) TableName() string {
	return "ads_impressions"
}

// ImpressionCreateRequest represents the request to create an impression
type ImpressionCreateRequest struct {
	AdvertisementID uint   `json:"advertisement_id" validate:"required"`
	PlacementID     uint   `json:"placement_id" validate:"required"`
	UserAgent       string `json:"user_agent,omitempty"`
	IPAddress       string `json:"ip_address,omitempty"`
	Referrer        string `json:"referrer,omitempty"`
	DeviceType      string `json:"device_type,omitempty" validate:"omitempty,oneof=web_pc web_mobile unknown"`
	PageURL         string `json:"page_url,omitempty" validate:"omitempty,url"`
	ViewDuration    int    `json:"view_duration,omitempty" validate:"omitempty,min=0"`
}

// ImpressionResponse represents the response structure for impression
type ImpressionResponse struct {
	ID              uint      `json:"id"`
	TenantID        uint      `json:"tenant_id"`
	WebsiteID       uint      `json:"website_id"`
	AdvertisementID uint      `json:"advertisement_id"`
	PlacementID     uint      `json:"placement_id"`
	UserAgent       string    `json:"user_agent,omitempty"`
	IPAddress       string    `json:"ip_address,omitempty"`
	Referrer        string    `json:"referrer,omitempty"`
	DeviceType      string    `json:"device_type"`
	PageURL         string    `json:"page_url,omitempty"`
	ViewedAt        time.Time `json:"viewed_at"`
	ViewDuration    int       `json:"view_duration"`
	CreatedAt       time.Time `json:"created_at"`
}

// ToImpressionResponse converts Impression to ImpressionResponse
func (i *Impression) ToImpressionResponse() *ImpressionResponse {
	return &ImpressionResponse{
		ID:              i.ID,
		TenantID:        i.TenantID,
		WebsiteID:       i.WebsiteID,
		AdvertisementID: i.AdvertisementID,
		PlacementID:     i.PlacementID,
		UserAgent:       i.UserAgent,
		IPAddress:       i.IPAddress,
		Referrer:        i.Referrer,
		DeviceType:      i.DeviceType,
		PageURL:         i.PageURL,
		ViewedAt:        i.ViewedAt,
		ViewDuration:    i.ViewDuration,
		CreatedAt:       i.CreatedAt,
	}
}

// ImpressionStatsRequest represents the request to get impression statistics
type ImpressionStatsRequest struct {
	StartDate       *time.Time `json:"start_date,omitempty"`
	EndDate         *time.Time `json:"end_date,omitempty"`
	AdvertisementID *uint      `json:"advertisement_id,omitempty"`
	DeviceType      *string    `json:"device_type,omitempty" validate:"omitempty,oneof=web_pc web_mobile unknown"`
	PageType        *string    `json:"page_type,omitempty"`
}

// ImpressionStatsResponse represents the response structure for impression statistics
type ImpressionStatsResponse struct {
	TotalImpressions int                    `json:"total_impressions"`
	DeviceBreakdown  map[string]int         `json:"device_breakdown"`
	PageBreakdown    map[string]int         `json:"page_breakdown"`
	DailyImpressions []DailyImpressionStat  `json:"daily_impressions"`
	TopAds           []TopAdvertisementStat `json:"top_ads"`
}

// DailyImpressionStat represents daily impression statistics
type DailyImpressionStat struct {
	Date        time.Time `json:"date"`
	Impressions int       `json:"impressions"`
	UniqueViews int       `json:"unique_views"`
}

// TopAdvertisementStat represents top performing advertisement statistics
type TopAdvertisementStat struct {
	AdvertisementID uint   `json:"advertisement_id"`
	Title           string `json:"title"`
	Impressions     int    `json:"impressions"`
	UniqueViews     int    `json:"unique_views"`
}
