package models

import (
	"time"
)

// MediaIntegration represents the integration layer with the Media module
type MediaIntegration struct {
	// This file provides types and utilities for integrating with the Media module
}

// MediaFileAssociation represents the association between advertisements and media files
type MediaFileAssociation struct {
	AdvertisementID uint      `json:"advertisement_id"`
	MediaFileID     uint      `json:"media_file_id"`
	MediaType       string    `json:"media_type"` // image, video, audio, document
	IsPrimary       bool      `json:"is_primary"` // Is this the primary media file for the ad
	DisplayOrder    int       `json:"display_order"`
	CreatedAt       time.Time `json:"created_at"`
}

// MediaFileInfo represents basic information about a media file
type MediaFileInfo struct {
	ID       uint   `json:"id"`
	FileName string `json:"file_name"`
	FilePath string `json:"file_path"`
	FileSize int64  `json:"file_size"`
	MimeType string `json:"mime_type"`
	Width    int    `json:"width,omitempty"`
	Height   int    `json:"height,omitempty"`
	Duration int    `json:"duration,omitempty"` // For video/audio files
	URL      string `json:"url"`
	CDNUrl   string `json:"cdn_url,omitempty"`
}

// AttachMediaRequest represents the request to attach media files to an advertisement
type AttachMediaRequest struct {
	MediaFileIDs []uint `json:"media_file_ids" validate:"required,min=1"`
	SetPrimary   *uint  `json:"set_primary,omitempty"` // Which media file ID to set as primary
}

// AttachMediaResponse represents the response after attaching media files
type AttachMediaResponse struct {
	AdvertisementID uint            `json:"advertisement_id"`
	AttachedMedia   []MediaFileInfo `json:"attached_media"`
	PrimaryMediaID  *uint           `json:"primary_media_id,omitempty"`
	TotalAttached   int             `json:"total_attached"`
}

// MediaListRequest represents the request to list media files for an advertisement
type MediaListRequest struct {
	MediaType   string `json:"media_type,omitempty" validate:"omitempty,oneof=image video audio document"`
	IncludeURL  bool   `json:"include_url,omitempty"`
	IncludeCDN  bool   `json:"include_cdn,omitempty"`
	PrimaryOnly bool   `json:"primary_only,omitempty"`
}

// MediaListResponse represents the response with media files for an advertisement
type MediaListResponse struct {
	AdvertisementID uint            `json:"advertisement_id"`
	MediaFiles      []MediaFileInfo `json:"media_files"`
	PrimaryMediaID  *uint           `json:"primary_media_id,omitempty"`
	TotalCount      int             `json:"total_count"`
}

// RemoveMediaRequest represents the request to remove media files from an advertisement
type RemoveMediaRequest struct {
	MediaFileIDs []uint `json:"media_file_ids" validate:"required,min=1"`
}

// RemoveMediaResponse represents the response after removing media files
type RemoveMediaResponse struct {
	AdvertisementID uint   `json:"advertisement_id"`
	RemovedMediaIDs []uint `json:"removed_media_ids"`
	TotalRemoved    int    `json:"total_removed"`
}

// MediaUploadRequest represents the request to upload media for an advertisement
type MediaUploadRequest struct {
	AdvertisementID uint   `json:"advertisement_id" validate:"required"`
	MediaType       string `json:"media_type" validate:"required,oneof=image video audio document"`
	FileName        string `json:"file_name" validate:"required"`
	SetAsPrimary    bool   `json:"set_as_primary,omitempty"`
	// File content would be handled separately in multipart form
}

// MediaUploadResponse represents the response after uploading media
type MediaUploadResponse struct {
	AdvertisementID uint          `json:"advertisement_id"`
	MediaFile       MediaFileInfo `json:"media_file"`
	IsPrimary       bool          `json:"is_primary"`
	UploadedAt      time.Time     `json:"uploaded_at"`
}

// MediaServiceInterface defines the interface for media service operations
type MediaServiceInterface interface {
	// AttachMediaToAdvertisement attaches existing media files to an advertisement
	AttachMediaToAdvertisement(tenantID, advertisementID uint, request *AttachMediaRequest) (*AttachMediaResponse, error)

	// GetAdvertisementMedia retrieves all media files associated with an advertisement
	GetAdvertisementMedia(tenantID, advertisementID uint, request *MediaListRequest) (*MediaListResponse, error)

	// RemoveMediaFromAdvertisement removes media files from an advertisement
	RemoveMediaFromAdvertisement(tenantID, advertisementID uint, request *RemoveMediaRequest) (*RemoveMediaResponse, error)

	// UploadMediaForAdvertisement uploads and associates media with an advertisement
	UploadMediaForAdvertisement(tenantID uint, request *MediaUploadRequest, fileData []byte) (*MediaUploadResponse, error)

	// GetMediaFileInfo retrieves information about a specific media file
	GetMediaFileInfo(tenantID, mediaFileID uint) (*MediaFileInfo, error)

	// ValidateMediaAccess validates if a media file can be accessed by the tenant
	ValidateMediaAccess(tenantID, mediaFileID uint) error
}

// MediaIntegrationError represents errors specific to media integration
type MediaIntegrationError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *MediaIntegrationError) Error() string {
	return e.Message
}

// Common media integration error codes
const (
	MediaErrorCodeFileNotFound     = "MEDIA_FILE_NOT_FOUND"
	MediaErrorCodeAccessDenied     = "MEDIA_ACCESS_DENIED"
	MediaErrorCodeUploadFailed     = "MEDIA_UPLOAD_FAILED"
	MediaErrorCodeInvalidFileType  = "MEDIA_INVALID_FILE_TYPE"
	MediaErrorCodeFileSizeExceeded = "MEDIA_FILE_SIZE_EXCEEDED"
	MediaErrorCodeQuotaExceeded    = "MEDIA_QUOTA_EXCEEDED"
)
