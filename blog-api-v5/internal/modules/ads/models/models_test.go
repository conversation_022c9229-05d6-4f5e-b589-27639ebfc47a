package models

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/datatypes"
)

func TestCampaignModel(t *testing.T) {
	campaign := &Campaign{
		TenantID:    1,
		Name:        "Test Campaign",
		Description: "Test Description",
		Budget:      1000.50,
		Status:      "active",
		StartDate:   time.Now(),
		EndDate:     time.Now().Add(24 * time.Hour),
	}

	assert.Equal(t, uint(1), campaign.TenantID)
	assert.Equal(t, "Test Campaign", campaign.Name)
	assert.Equal(t, 1000.50, campaign.Budget)
	assert.True(t, campaign.IsActive())
}

func TestAdvertisementModel(t *testing.T) {
	pageTargeting := []string{"homepage", "category"}
	pageTargetingJSON, _ := json.Marshal(pageTargeting)

	advertisement := &Advertisement{
		TenantID:        1,
		CampaignID:      1,
		Title:           "Test Ad",
		Description:     "Test Ad Description",
		LinkURL:         "https://example.com",
		AdType:          "banner",
		DeviceTargeting: "both",
		PageTargeting:   datatypes.JSON(pageTargetingJSON),
		Position:        "header",
		Priority:        5,
		Status:          "active",
	}

	assert.Equal(t, uint(1), advertisement.TenantID)
	assert.Equal(t, "Test Ad", advertisement.Title)
	assert.True(t, advertisement.IsActive())
	// Debug device targeting function
	t.Logf("Device targeting: %s", advertisement.DeviceTargeting)
	t.Logf("Device check result for 'both': %v", advertisement.SupportsDevice("both"))
	t.Logf("Device check result for 'web_pc': %v", advertisement.SupportsDevice("web_pc"))
	t.Logf("Device check result for 'web_mobile': %v", advertisement.SupportsDevice("web_mobile"))

	// Test actual device types instead of "both"
	assert.True(t, advertisement.SupportsDevice("web_pc"))
	assert.True(t, advertisement.SupportsDevice("web_mobile"))
	// Debug page targeting function
	t.Logf("Page targeting JSON: %v", advertisement.PageTargeting)

	// Let's test the scanning manually first
	var targets []string
	scanErr := advertisement.PageTargeting.Scan(&targets)
	t.Logf("Scan error: %v", scanErr)
	t.Logf("Scanned targets: %v", targets)

	pageResult := advertisement.SupportsPage("homepage")
	t.Logf("Page check result for 'homepage': %v", pageResult)
	assert.True(t, pageResult)
}

func TestScheduleModel(t *testing.T) {
	schedule := &Schedule{
		TenantID:        1,
		AdvertisementID: 1,
		StartTime:       time.Now(),
		EndTime:         time.Now().Add(24 * time.Hour),
		Timezone:        "UTC",
		Status:          "active",
	}

	assert.Equal(t, uint(1), schedule.TenantID)
	assert.True(t, schedule.IsActive())
}

func TestPlacementModel(t *testing.T) {
	placement := &Placement{
		TenantID: 1,
		PageType: "homepage",
		Position: "header",
		MaxAds:   3,
		Status:   "active",
	}

	assert.Equal(t, uint(1), placement.TenantID)
	assert.Equal(t, "homepage", placement.PageType)
	assert.True(t, placement.IsActive())
}

func TestImpressionModel(t *testing.T) {
	impression := &Impression{
		TenantID:        1,
		AdvertisementID: 1,
		PlacementID:     1,
		DeviceType:      "web_pc",
		ViewDuration:    5000,
	}

	assert.Equal(t, uint(1), impression.TenantID)
	assert.Equal(t, "web_pc", impression.DeviceType)
	assert.Equal(t, 5000, impression.ViewDuration)
}

func TestClickModel(t *testing.T) {
	click := &Click{
		TenantID:        1,
		AdvertisementID: 1,
		DeviceType:      "web_mobile",
		DestinationURL:  "https://example.com/product",
	}

	assert.Equal(t, uint(1), click.TenantID)
	assert.Equal(t, "web_mobile", click.DeviceType)
	assert.Equal(t, "https://example.com/product", click.DestinationURL)
}

func TestAnalyticsModel(t *testing.T) {
	analytics := &Analytics{
		TenantID:         1,
		AdvertisementID:  1,
		AnalyticsDate:    time.Now(),
		ImpressionsCount: 1000,
		ClicksCount:      30,
		CTRRate:          0.03,
	}

	assert.Equal(t, uint(1), analytics.TenantID)
	assert.Equal(t, 1000, analytics.ImpressionsCount)
	assert.Equal(t, 30, analytics.ClicksCount)
	assert.Equal(t, 0.03, analytics.CTRRate)
	assert.Equal(t, 0.03, analytics.CalculateCTR())
}

func TestTargetingRuleModel(t *testing.T) {
	rule := &TargetingRule{
		TenantID:        1,
		AdvertisementID: 1,
		RuleType:        "page_url",
		RuleKey:         "url",
		RuleValue:       "/products/",
		Operator:        "starts_with",
		Priority:        1,
		Status:          "active",
	}

	assert.Equal(t, uint(1), rule.TenantID)
	assert.Equal(t, "page_url", rule.RuleType)
	assert.True(t, rule.IsActive())

	// Test rule evaluation
	context := &TargetingContext{
		PageURL: "/products/shoes",
	}
	// Debug the evaluation
	result := rule.EvaluateRule(context)
	t.Logf("Rule evaluation for '/products/shoes' with rule '/products/': %v", result)
	assert.True(t, result)

	context.PageURL = "/about"
	assert.False(t, rule.EvaluateRule(context))
}
