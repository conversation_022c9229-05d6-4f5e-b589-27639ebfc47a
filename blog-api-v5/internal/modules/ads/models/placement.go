package models

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Placement represents ad placement configuration
type Placement struct {
	ID             uint           `json:"id" gorm:"primaryKey"`
	TenantID       uint           `json:"tenant_id" gorm:"not null;index"`
	WebsiteID      uint           `json:"website_id" gorm:"not null;index"`
	PageType       string         `json:"page_type" gorm:"type:enum('homepage','category','article','tag','custom');not null" validate:"required,oneof=homepage category article tag custom"`
	Position       string         `json:"position" gorm:"type:enum('header','sidebar','footer','inline','popup');not null" validate:"required,oneof=header sidebar footer inline popup"`
	TargetingRules datatypes.JSON `json:"targeting_rules" gorm:"type:json"`
	MaxAds         int            `json:"max_ads" gorm:"not null;default:1" validate:"min=1,max=10"`
	Status         string         `json:"status" gorm:"type:enum('active','inactive','deleted');default:'active'" validate:"oneof=active inactive deleted"`
	CreatedAt      time.Time      `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt      time.Time      `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`

	// Relationships
	Impressions []Impression `json:"impressions,omitempty" gorm:"foreignKey:PlacementID"`
}

// TableName returns the table name for the Placement model
func (Placement) TableName() string {
	return "ads_placements"
}

// BeforeCreate is a GORM hook that runs before creating a placement
func (p *Placement) BeforeCreate(tx *gorm.DB) error {
	if p.Status == "" {
		p.Status = "active"
	}
	if p.MaxAds == 0 {
		p.MaxAds = 1
	}
	return nil
}

// IsActive returns true if the placement is active
func (p *Placement) IsActive() bool {
	return p.Status == "active"
}

// PlacementTargetingRules represents targeting rules for placement
type PlacementTargetingRules struct {
	DeviceTypes     []string `json:"device_types,omitempty"`     // web_pc, web_mobile
	MinScreenWidth  int      `json:"min_screen_width,omitempty"` // Minimum screen width in pixels
	MaxScreenWidth  int      `json:"max_screen_width,omitempty"` // Maximum screen width in pixels
	UserAgentRules  []string `json:"user_agent_rules,omitempty"` // User agent patterns
	GeoTargeting    []string `json:"geo_targeting,omitempty"`    // Country codes
	TimeRestriction []string `json:"time_restriction,omitempty"` // Time ranges
}
