package models

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Schedule represents advertisement scheduling configuration
type Schedule struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	TenantID         uint           `json:"tenant_id" gorm:"not null;index"`
	WebsiteID        uint           `json:"website_id" gorm:"not null;index"`
	AdvertisementID  uint           `json:"advertisement_id" gorm:"not null;index"`
	StartTime        time.Time      `json:"start_time" gorm:"not null" validate:"required"`
	EndTime          time.Time      `json:"end_time" gorm:"not null" validate:"required"`
	Timezone         string         `json:"timezone" gorm:"size:50;default:'UTC'" validate:"max=50"`
	RecurringPattern datatypes.JSON `json:"recurring_pattern" gorm:"type:json"`
	Status           string         `json:"status" gorm:"type:enum('active','paused','expired','deleted');default:'active'" validate:"oneof=active paused expired deleted"`
	CreatedAt        time.Time      `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt        time.Time      `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`

	// Relationships
	Advertisement Advertisement `json:"advertisement,omitempty" gorm:"foreignKey:AdvertisementID"`
}

// TableName returns the table name for the Schedule model
func (Schedule) TableName() string {
	return "ads_schedules"
}

// BeforeCreate is a GORM hook that runs before creating a schedule
func (s *Schedule) BeforeCreate(tx *gorm.DB) error {
	if s.Status == "" {
		s.Status = "active"
	}
	if s.Timezone == "" {
		s.Timezone = "UTC"
	}
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a schedule
func (s *Schedule) BeforeUpdate(tx *gorm.DB) error {
	// Validate time range
	if s.EndTime.Before(s.StartTime) {
		return gorm.ErrInvalidData
	}
	return nil
}

// IsActive returns true if the schedule is active
func (s *Schedule) IsActive() bool {
	return s.Status == "active"
}

// IsCurrentlyActive returns true if the schedule is currently active based on time
func (s *Schedule) IsCurrentlyActive() bool {
	if !s.IsActive() {
		return false
	}

	now := time.Now()
	return s.StartTime.Before(now) && s.EndTime.After(now)
}

// RecurringPatternType represents the type of recurring pattern
type RecurringPatternType string

const (
	RecurringPatternDaily   RecurringPatternType = "daily"
	RecurringPatternWeekly  RecurringPatternType = "weekly"
	RecurringPatternMonthly RecurringPatternType = "monthly"
	RecurringPatternCustom  RecurringPatternType = "custom"
)

// RecurringPattern represents the recurring pattern configuration
type RecurringPattern struct {
	Type     RecurringPatternType `json:"type" validate:"required,oneof=daily weekly monthly custom"`
	Days     []string             `json:"days,omitempty"`     // For weekly: monday, tuesday, etc.
	Hours    []int                `json:"hours,omitempty"`    // For daily: 0-23
	Interval int                  `json:"interval,omitempty"` // For custom: every N days/weeks/months
}

// ScheduleCreateRequest represents the request to create a schedule
type ScheduleCreateRequest struct {
	AdvertisementID  uint              `json:"advertisement_id" validate:"required"`
	StartTime        time.Time         `json:"start_time" validate:"required"`
	EndTime          time.Time         `json:"end_time" validate:"required"`
	Timezone         string            `json:"timezone,omitempty" validate:"max=50"`
	RecurringPattern *RecurringPattern `json:"recurring_pattern,omitempty"`
}

// ScheduleUpdateRequest represents the request to update a schedule
type ScheduleUpdateRequest struct {
	StartTime        *time.Time        `json:"start_time,omitempty"`
	EndTime          *time.Time        `json:"end_time,omitempty"`
	Timezone         *string           `json:"timezone,omitempty" validate:"omitempty,max=50"`
	RecurringPattern *RecurringPattern `json:"recurring_pattern,omitempty"`
	Status           *string           `json:"status,omitempty" validate:"omitempty,oneof=active paused expired deleted"`
}

// ScheduleResponse represents the response structure for schedule
type ScheduleResponse struct {
	ID                uint              `json:"id"`
	TenantID          uint              `json:"tenant_id"`
	WebsiteID         uint              `json:"website_id"`
	AdvertisementID   uint              `json:"advertisement_id"`
	StartTime         time.Time         `json:"start_time"`
	EndTime           time.Time         `json:"end_time"`
	Timezone          string            `json:"timezone"`
	RecurringPattern  *RecurringPattern `json:"recurring_pattern,omitempty"`
	Status            string            `json:"status"`
	CreatedAt         time.Time         `json:"created_at"`
	UpdatedAt         time.Time         `json:"updated_at"`
	IsActive          bool              `json:"is_active"`
	IsCurrentlyActive bool              `json:"is_currently_active"`
}

// ToScheduleResponse converts Schedule to ScheduleResponse
func (s *Schedule) ToScheduleResponse() *ScheduleResponse {
	var recurringPattern *RecurringPattern
	if s.RecurringPattern != nil {
		recurringPattern = &RecurringPattern{}
		s.RecurringPattern.Scan(recurringPattern)
	}

	return &ScheduleResponse{
		ID:                s.ID,
		TenantID:          s.TenantID,
		WebsiteID:         s.WebsiteID,
		AdvertisementID:   s.AdvertisementID,
		StartTime:         s.StartTime,
		EndTime:           s.EndTime,
		Timezone:          s.Timezone,
		RecurringPattern:  recurringPattern,
		Status:            s.Status,
		CreatedAt:         s.CreatedAt,
		UpdatedAt:         s.UpdatedAt,
		IsActive:          s.IsActive(),
		IsCurrentlyActive: s.IsCurrentlyActive(),
	}
}
