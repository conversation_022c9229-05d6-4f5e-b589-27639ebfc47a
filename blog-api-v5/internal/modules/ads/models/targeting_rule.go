package models

import (
	"strings"
	"time"

	"gorm.io/gorm"
)

// TargetingRule represents a targeting rule for advertisements
type TargetingRule struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	TenantID        uint      `json:"tenant_id" gorm:"not null;index"`
	WebsiteID       uint      `json:"website_id" gorm:"not null;index"`
	AdvertisementID uint      `json:"advertisement_id" gorm:"not null;index"`
	RuleType        string    `json:"rule_type" gorm:"type:enum('page_url','referrer','device','time','location','custom');not null" validate:"required,oneof=page_url referrer device time location custom"`
	RuleKey         string    `json:"rule_key" gorm:"not null;size:255" validate:"required,max=255"`
	RuleValue       string    `json:"rule_value" gorm:"type:text;not null" validate:"required"`
	Operator        string    `json:"operator" gorm:"type:enum('equals','not_equals','contains','not_contains','starts_with','ends_with','regex');default:'equals'" validate:"oneof=equals not_equals contains not_contains starts_with ends_with regex"`
	Priority        int       `json:"priority" gorm:"not null;default:1" validate:"min=1,max=100"`
	Status          string    `json:"status" gorm:"type:enum('active','inactive','deleted');default:'active'" validate:"oneof=active inactive deleted"`
	CreatedAt       time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`

	// Relationships
	Advertisement Advertisement `json:"advertisement,omitempty" gorm:"foreignKey:AdvertisementID"`
}

// TableName returns the table name for the TargetingRule model
func (TargetingRule) TableName() string {
	return "ads_targeting_rules"
}

// BeforeCreate is a GORM hook that runs before creating a targeting rule
func (t *TargetingRule) BeforeCreate(tx *gorm.DB) error {
	if t.Status == "" {
		t.Status = "active"
	}
	if t.Operator == "" {
		t.Operator = "equals"
	}
	if t.Priority == 0 {
		t.Priority = 1
	}
	return nil
}

// IsActive returns true if the targeting rule is active
func (t *TargetingRule) IsActive() bool {
	return t.Status == "active"
}

// TargetingRuleType represents the type of targeting rule
type TargetingRuleType string

const (
	TargetingRuleTypePageURL  TargetingRuleType = "page_url"
	TargetingRuleTypeReferrer TargetingRuleType = "referrer"
	TargetingRuleTypeDevice   TargetingRuleType = "device"
	TargetingRuleTypeTime     TargetingRuleType = "time"
	TargetingRuleTypeLocation TargetingRuleType = "location"
	TargetingRuleTypeCustom   TargetingRuleType = "custom"
)

// TargetingRuleOperator represents the operator for targeting rules
type TargetingRuleOperator string

const (
	TargetingRuleOperatorEquals      TargetingRuleOperator = "equals"
	TargetingRuleOperatorNotEquals   TargetingRuleOperator = "not_equals"
	TargetingRuleOperatorContains    TargetingRuleOperator = "contains"
	TargetingRuleOperatorNotContains TargetingRuleOperator = "not_contains"
	TargetingRuleOperatorStartsWith  TargetingRuleOperator = "starts_with"
	TargetingRuleOperatorEndsWith    TargetingRuleOperator = "ends_with"
	TargetingRuleOperatorRegex       TargetingRuleOperator = "regex"
)

// TargetingContext represents the context for targeting rule evaluation
type TargetingContext struct {
	PageURL    string            `json:"page_url"`
	Referrer   string            `json:"referrer"`
	DeviceType string            `json:"device_type"`
	UserAgent  string            `json:"user_agent"`
	IPAddress  string            `json:"ip_address"`
	Location   string            `json:"location"`
	PageType   string            `json:"page_type"`
	Language   string            `json:"language"`
	Timestamp  time.Time         `json:"timestamp"`
	CustomData map[string]string `json:"custom_data"`
}

// EvaluateRule evaluates the targeting rule against the given context
func (t *TargetingRule) EvaluateRule(context *TargetingContext) bool {
	if !t.IsActive() {
		return false
	}

	var valueToCheck string

	switch t.RuleType {
	case string(TargetingRuleTypePageURL):
		valueToCheck = context.PageURL
	case string(TargetingRuleTypeReferrer):
		valueToCheck = context.Referrer
	case string(TargetingRuleTypeDevice):
		valueToCheck = context.DeviceType
	case string(TargetingRuleTypeCustom):
		if customValue, exists := context.CustomData[t.RuleKey]; exists {
			valueToCheck = customValue
		}
	default:
		return false
	}

	return t.evaluateOperator(valueToCheck, t.RuleValue)
}

// evaluateOperator evaluates the operator against the values
func (t *TargetingRule) evaluateOperator(actualValue, expectedValue string) bool {
	switch t.Operator {
	case string(TargetingRuleOperatorEquals):
		return actualValue == expectedValue
	case string(TargetingRuleOperatorNotEquals):
		return actualValue != expectedValue
	case string(TargetingRuleOperatorContains):
		return strings.Contains(actualValue, expectedValue)
	case string(TargetingRuleOperatorNotContains):
		return !strings.Contains(actualValue, expectedValue)
	case string(TargetingRuleOperatorStartsWith):
		return strings.HasPrefix(actualValue, expectedValue)
	case string(TargetingRuleOperatorEndsWith):
		return strings.HasSuffix(actualValue, expectedValue)
	case string(TargetingRuleOperatorRegex):
		// Note: Regex evaluation would require regexp package
		// For now, return false as placeholder
		return false
	default:
		return false
	}
}
