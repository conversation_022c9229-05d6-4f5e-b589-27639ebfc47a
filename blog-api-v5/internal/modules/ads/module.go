package ads

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories/mysql"
	contextpkg "github.com/tranthanhloi/wn-api-v3/pkg/context"
)

// Module represents the ads module
type Module struct {
	db          *gorm.DB
	repoManager repositories.RepositoryManager
}

// NewModule creates a new ads module instance
func NewModule(db *gorm.DB) *Module {
	repoManager := mysql.NewRepositoryManager(db)

	return &Module{
		db:          db,
		repoManager: repoManager,
	}
}

// Initialize initializes the ads module
func (m *Module) Initialize(ctx context.Context) error {
	// Auto-migrate models
	if err := m.autoMigrate(); err != nil {
		return fmt.Errorf("failed to auto-migrate ads models: %w", err)
	}

	return nil
}

// RegisterRoutes registers the module routes
func (m *Module) RegisterRoutes(router *gin.RouterGroup) {
	RegisterRoutes(router, m.db)
}

// GetServiceManager returns the service manager (placeholder)
// func (m *Module) GetServiceManager() services.ServiceManager {
//	return m.serviceManager
// }

// GetRepositoryManager returns the repository manager
func (m *Module) GetRepositoryManager() repositories.RepositoryManager {
	return m.repoManager
}

// autoMigrate runs auto migration for all ads models
func (m *Module) autoMigrate() error {
	models := []interface{}{
		&models.Campaign{},
		&models.Advertisement{},
		&models.Schedule{},
		&models.Placement{},
		&models.Impression{},
		&models.Click{},
		&models.Analytics{},
		&models.TargetingRule{},
	}

	for _, model := range models {
		if err := m.db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", model, err)
		}
	}

	return nil
}

// GetTenantContext extracts tenant context from Gin context
func GetTenantContext(c *gin.Context) (*contextpkg.TenantContext, error) {
	tenantCtx, exists := c.Get("tenant_context")
	if !exists {
		return nil, fmt.Errorf("tenant context not found")
	}

	ctx, ok := tenantCtx.(*contextpkg.TenantContext)
	if !ok {
		return nil, fmt.Errorf("invalid tenant context type")
	}

	return ctx, nil
}

// GetCurrentUserID gets the current user ID from context
func GetCurrentUserID(c *gin.Context) (uint, error) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, fmt.Errorf("user ID not found in context")
	}

	id, ok := userID.(uint)
	if !ok {
		return 0, fmt.Errorf("invalid user ID type")
	}

	return id, nil
}

// ValidateTenantAccess validates that the user has access to the tenant
func ValidateTenantAccess(c *gin.Context, tenantID uint) error {
	tenantCtx, err := GetTenantContext(c)
	if err != nil {
		return err
	}

	if tenantCtx.GetScope() != tenantID {
		return fmt.Errorf("access denied: tenant mismatch")
	}

	return nil
}

// GetCurrentTenantID gets the current tenant ID from context
func GetCurrentTenantID(c *gin.Context) (uint, error) {
	tenantCtx, err := GetTenantContext(c)
	if err != nil {
		return 0, err
	}

	return tenantCtx.GetScope(), nil
}
