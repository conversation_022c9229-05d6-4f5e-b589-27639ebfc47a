package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
)

// CampaignRepository defines the interface for campaign data access
type CampaignRepository interface {
	Create(ctx context.Context, campaign *models.Campaign) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Campaign, error)
	GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, status string) ([]*models.Campaign, error)
	Update(ctx context.Context, campaign *models.Campaign) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	Count(ctx context.Context, tenantID, websiteID uint, status string) (int64, error)
	GetActiveCampaigns(ctx context.Context, tenantID, websiteID uint) ([]*models.Campaign, error)
	GetRunningCampaigns(ctx context.Context, tenantID, websiteID uint) ([]*models.Campaign, error)
}

// AdvertisementRepository defines the interface for advertisement data access
type AdvertisementRepository interface {
	Create(ctx context.Context, advertisement *models.Advertisement) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Advertisement, error)
	GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, filters map[string]interface{}) ([]*models.Advertisement, error)
	GetByCampaignID(ctx context.Context, tenantID, websiteID, campaignID uint, limit, offset int) ([]*models.Advertisement, error)
	Update(ctx context.Context, advertisement *models.Advertisement) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error)
	GetActiveAds(ctx context.Context, tenantID, websiteID uint) ([]*models.Advertisement, error)
	GetAdsForServing(ctx context.Context, tenantID, websiteID uint, pageType, position, deviceType string) ([]*models.Advertisement, error)
	GetAdsByPriority(ctx context.Context, tenantID, websiteID uint, pageType, position string, limit int) ([]*models.Advertisement, error)
}

// ScheduleRepository defines the interface for schedule data access
type ScheduleRepository interface {
	Create(ctx context.Context, schedule *models.Schedule) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Schedule, error)
	GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint) ([]*models.Schedule, error)
	GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]*models.Schedule, error)
	Update(ctx context.Context, schedule *models.Schedule) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error)
	GetActiveSchedules(ctx context.Context, tenantID, websiteID uint, currentTime time.Time) ([]*models.Schedule, error)
	GetSchedulesByTimeRange(ctx context.Context, tenantID, websiteID uint, startTime, endTime time.Time) ([]*models.Schedule, error)
}

// PlacementRepository defines the interface for placement data access
type PlacementRepository interface {
	Create(ctx context.Context, placement *models.Placement) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Placement, error)
	GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]*models.Placement, error)
	GetByPageAndPosition(ctx context.Context, tenantID, websiteID uint, pageType, position string) (*models.Placement, error)
	Update(ctx context.Context, placement *models.Placement) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error)
	GetActivePlacements(ctx context.Context, tenantID, websiteID uint) ([]*models.Placement, error)
	GetPlacementsByPage(ctx context.Context, tenantID, websiteID uint, pageType string) ([]*models.Placement, error)
}

// ImpressionRepository defines the interface for impression data access
type ImpressionRepository interface {
	Create(ctx context.Context, impression *models.Impression) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Impression, error)
	GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint, limit, offset int) ([]*models.Impression, error)
	GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, filters map[string]interface{}) ([]*models.Impression, error)
	GetByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, filters map[string]interface{}) ([]*models.Impression, error)
	Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error)
	CountByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, filters map[string]interface{}) (int64, error)
	GetImpressionStats(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) (*models.ImpressionStatsResponse, error)
	GetDailyImpressions(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) ([]models.DailyImpressionStat, error)
	GetTopAds(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, limit int) ([]models.TopAdvertisementStat, error)
}

// ClickRepository defines the interface for click data access
type ClickRepository interface {
	Create(ctx context.Context, click *models.Click) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Click, error)
	GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint, limit, offset int) ([]*models.Click, error)
	GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, filters map[string]interface{}) ([]*models.Click, error)
	GetByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, filters map[string]interface{}) ([]*models.Click, error)
	Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error)
	CountByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, filters map[string]interface{}) (int64, error)
	GetClickStats(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) (*models.ClickStatsResponse, error)
	GetDailyClicks(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) ([]models.DailyClickStat, error)
	GetTopClickAds(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, limit int) ([]models.TopClickAdvertisementStat, error)
}

// AnalyticsRepository defines the interface for analytics data access
type AnalyticsRepository interface {
	Create(ctx context.Context, analytics *models.Analytics) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Analytics, error)
	GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint, limit, offset int) ([]*models.Analytics, error)
	GetByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, advertisementID *uint) ([]*models.Analytics, error)
	Update(ctx context.Context, analytics *models.Analytics) error
	Upsert(ctx context.Context, analytics *models.Analytics) error
	GetCampaignAnalytics(ctx context.Context, tenantID, websiteID, campaignID uint, startDate, endDate time.Time) (*dto.CampaignAnalyticsResponse, error)
	GetAdvertisementAnalytics(ctx context.Context, tenantID, websiteID, advertisementID uint, startDate, endDate time.Time) (*dto.AdvertisementAnalyticsResponse, error)
	GetDailyAnalytics(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, advertisementID *uint) ([]models.DailyAnalytics, error)
	GetWeeklyAnalytics(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, advertisementID *uint) ([]models.WeeklyAnalytics, error)
	GetMonthlyAnalytics(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, advertisementID *uint) ([]models.MonthlyAnalytics, error)
	AggregateAnalytics(ctx context.Context, tenantID, websiteID uint, date time.Time) error
}

// TargetingRuleRepository defines the interface for targeting rule data access
type TargetingRuleRepository interface {
	Create(ctx context.Context, rule *models.TargetingRule) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.TargetingRule, error)
	GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint) ([]*models.TargetingRule, error)
	GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, filters map[string]interface{}) ([]*models.TargetingRule, error)
	Update(ctx context.Context, rule *models.TargetingRule) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error)
	GetActiveRules(ctx context.Context, tenantID, websiteID uint) ([]*models.TargetingRule, error)
	GetRulesByType(ctx context.Context, tenantID, websiteID uint, ruleType string) ([]*models.TargetingRule, error)
	GetRulesByAdvertisement(ctx context.Context, tenantID, websiteID, advertisementID uint) ([]*models.TargetingRule, error)
}

// MediaIntegrationRepository defines the interface for media integration data access
type MediaIntegrationRepository interface {
	AssociateMediaWithAdvertisement(ctx context.Context, tenantID, websiteID, advertisementID uint, mediaFileIDs []uint, primaryMediaID *uint) error
	GetAdvertisementMedia(ctx context.Context, tenantID, websiteID, advertisementID uint) ([]models.MediaFileInfo, error)
	RemoveMediaFromAdvertisement(ctx context.Context, tenantID, websiteID, advertisementID uint, mediaFileIDs []uint) error
	GetPrimaryMedia(ctx context.Context, tenantID, websiteID, advertisementID uint) (*models.MediaFileInfo, error)
	SetPrimaryMedia(ctx context.Context, tenantID, websiteID, advertisementID, mediaFileID uint) error
	ValidateMediaAccess(ctx context.Context, tenantID, websiteID, mediaFileID uint) error
}

// RepositoryManager defines the interface for managing all repositories
type RepositoryManager interface {
	Campaign() CampaignRepository
	Advertisement() AdvertisementRepository
	Schedule() ScheduleRepository
	Placement() PlacementRepository
	Impression() ImpressionRepository
	Click() ClickRepository
	Analytics() AnalyticsRepository
	TargetingRule() TargetingRuleRepository
	MediaIntegration() MediaIntegrationRepository

	// Transaction support
	WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error
	BeginTransaction(ctx context.Context) (context.Context, error)
	CommitTransaction(ctx context.Context) error
	RollbackTransaction(ctx context.Context) error
}
