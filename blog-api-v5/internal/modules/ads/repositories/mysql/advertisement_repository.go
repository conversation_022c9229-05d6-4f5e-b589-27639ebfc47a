package mysql

import (
	"context"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// advertisementRepository implements the AdvertisementRepository interface
type advertisementRepository struct {
	db *gorm.DB
}

// NewAdvertisementRepository creates a new advertisement repository
func NewAdvertisementRepository(db *gorm.DB) repositories.AdvertisementRepository {
	return &advertisementRepository{db: db}
}

// Create creates a new advertisement
func (r *advertisementRepository) Create(ctx context.Context, advertisement *models.Advertisement) error {
	if err := r.db.WithContext(ctx).Create(advertisement).Error; err != nil {
		return fmt.Errorf("failed to create advertisement: %w", err)
	}
	return nil
}

// GetByID retrieves an advertisement by ID and tenant ID
func (r *advertisementRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Advertisement, error) {
	var advertisement models.Advertisement
	err := r.db.WithContext(ctx).
		Preload("Campaign").
		Where("id = ? AND tenant_id = ? AND website_id = ? AND status != ?", id, tenantID, websiteID, "deleted").
		First(&advertisement).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("advertisement not found")
		}
		return nil, fmt.Errorf("failed to get advertisement: %w", err)
	}

	return &advertisement, nil
}

// GetByTenantAndWebsite retrieves advertisements by tenant ID with pagination and filtering
func (r *advertisementRepository) GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, filters map[string]interface{}) ([]*models.Advertisement, error) {
	var advertisements []*models.Advertisement

	query := r.db.WithContext(ctx).
		Preload("Campaign").
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted")

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.
		Order("priority ASC, created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&advertisements).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get advertisements: %w", err)
	}

	return advertisements, nil
}

// GetByCampaignID retrieves advertisements by campaign ID
func (r *advertisementRepository) GetByCampaignID(ctx context.Context, tenantID, websiteID, campaignID uint, limit, offset int) ([]*models.Advertisement, error) {
	var advertisements []*models.Advertisement

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND campaign_id = ? AND status != ?", tenantID, websiteID, campaignID, "deleted").
		Order("priority ASC, created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&advertisements).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get advertisements by campaign: %w", err)
	}

	return advertisements, nil
}

// Update updates an existing advertisement
func (r *advertisementRepository) Update(ctx context.Context, advertisement *models.Advertisement) error {
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", advertisement.ID, advertisement.TenantID).
		Updates(advertisement).Error

	if err != nil {
		return fmt.Errorf("failed to update advertisement: %w", err)
	}

	return nil
}

// Delete soft deletes an advertisement by setting status to 'deleted'
func (r *advertisementRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.Advertisement{}).
		Where("id = ? AND tenant_id = ? AND website_id = ?", id, tenantID, websiteID).
		Update("status", "deleted").Error

	if err != nil {
		return fmt.Errorf("failed to delete advertisement: %w", err)
	}

	return nil
}

// Count returns the total count of advertisements for a tenant
func (r *advertisementRepository) Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).
		Model(&models.Advertisement{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted")

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count advertisements: %w", err)
	}

	return count, nil
}

// GetActiveAds retrieves all active advertisements for a tenant
func (r *advertisementRepository) GetActiveAds(ctx context.Context, tenantID, websiteID uint) ([]*models.Advertisement, error) {
	var advertisements []*models.Advertisement

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, "active").
		Order("priority ASC, created_at DESC").
		Find(&advertisements).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get active advertisements: %w", err)
	}

	return advertisements, nil
}

// GetAdsForServing retrieves advertisements suitable for serving based on criteria
func (r *advertisementRepository) GetAdsForServing(ctx context.Context, tenantID, websiteID uint, pageType, position, deviceType string) ([]*models.Advertisement, error) {
	var advertisements []*models.Advertisement

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ? AND position = ?", tenantID, websiteID, "active", position)

	// Filter by device targeting
	if deviceType != "" {
		query = query.Where("device_targeting IN (?)", []string{"both", deviceType})
	}

	// Filter by page targeting (JSON contains check)
	if pageType != "" {
		query = query.Where("(page_targeting IS NULL OR page_targeting = '[]' OR JSON_CONTAINS(page_targeting, ?))", fmt.Sprintf(`"%s"`, pageType))
	}

	err := query.
		Order("priority ASC, RAND()").
		Find(&advertisements).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get advertisements for serving: %w", err)
	}

	return advertisements, nil
}

// GetAdsByPriority retrieves advertisements by priority with limit
func (r *advertisementRepository) GetAdsByPriority(ctx context.Context, tenantID, websiteID uint, pageType, position string, limit int) ([]*models.Advertisement, error) {
	var advertisements []*models.Advertisement

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ? AND position = ?", tenantID, websiteID, "active", position)

	// Filter by page targeting
	if pageType != "" {
		query = query.Where("(page_targeting IS NULL OR page_targeting = '[]' OR JSON_CONTAINS(page_targeting, ?))", fmt.Sprintf(`"%s"`, pageType))
	}

	err := query.
		Order("priority ASC, created_at ASC").
		Limit(limit).
		Find(&advertisements).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get advertisements by priority: %w", err)
	}

	return advertisements, nil
}

// applyFilters applies filters to the query
func (r *advertisementRepository) applyFilters(query *gorm.DB, filters map[string]interface{}) *gorm.DB {
	if filters == nil {
		return query
	}

	if campaignID, ok := filters["campaign_id"]; ok {
		query = query.Where("campaign_id = ?", campaignID)
	}

	if status, ok := filters["status"]; ok {
		if statusStr, ok := status.(string); ok && statusStr != "" {
			query = query.Where("status = ?", statusStr)
		}
	}

	if adType, ok := filters["ad_type"]; ok {
		if adTypeStr, ok := adType.(string); ok && adTypeStr != "" {
			query = query.Where("ad_type = ?", adTypeStr)
		}
	}

	if deviceTargeting, ok := filters["device_targeting"]; ok {
		if deviceStr, ok := deviceTargeting.(string); ok && deviceStr != "" {
			query = query.Where("device_targeting = ?", deviceStr)
		}
	}

	if position, ok := filters["position"]; ok {
		if positionStr, ok := position.(string); ok && positionStr != "" {
			query = query.Where("position = ?", positionStr)
		}
	}

	if priority, ok := filters["priority"]; ok {
		query = query.Where("priority = ?", priority)
	}

	if search, ok := filters["search"]; ok {
		if searchStr, ok := search.(string); ok && searchStr != "" {
			searchTerm := "%" + strings.ToLower(searchStr) + "%"
			query = query.Where("(LOWER(title) LIKE ? OR LOWER(description) LIKE ?)", searchTerm, searchTerm)
		}
	}

	return query
}
