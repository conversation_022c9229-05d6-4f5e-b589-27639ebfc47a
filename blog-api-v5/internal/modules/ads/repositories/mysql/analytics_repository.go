package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/database/scopes"
)

// analyticsRepository implements the AnalyticsRepository interface
type analyticsRepository struct {
	db *gorm.DB
}

// NewAnalyticsRepository creates a new analytics repository
func NewAnalyticsRepository(db *gorm.DB) repositories.AnalyticsRepository {
	return &analyticsRepository{db: db}
}

// Create creates a new analytics record
func (r *analyticsRepository) Create(ctx context.Context, analytics *models.Analytics) error {
	if err := r.db.WithContext(ctx).Create(analytics).Error; err != nil {
		return fmt.Errorf("failed to create analytics: %w", err)
	}
	return nil
}

// GetByID retrieves analytics by ID, tenant ID, and website ID
func (r *analyticsRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Analytics, error) {
	var analytics models.Analytics
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		First(&analytics).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("analytics not found")
		}
		return nil, fmt.Errorf("failed to get analytics: %w", err)
	}

	return &analytics, nil
}

// GetByAdvertisementID retrieves analytics by advertisement ID
func (r *analyticsRepository) GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint, limit, offset int) ([]*models.Analytics, error) {
	var analytics []*models.Analytics

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("advertisement_id = ?", advertisementID).
		Order("analytics_date DESC").
		Limit(limit).
		Offset(offset).
		Find(&analytics).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get analytics by advertisement: %w", err)
	}

	return analytics, nil
}

// GetByDateRange retrieves analytics within a date range
func (r *analyticsRepository) GetByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, advertisementID *uint) ([]*models.Analytics, error) {
	var analytics []*models.Analytics

	query := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("analytics_date BETWEEN ? AND ?", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	if advertisementID != nil {
		query = query.Where("advertisement_id = ?", *advertisementID)
	}

	err := query.
		Order("analytics_date DESC").
		Find(&analytics).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get analytics by date range: %w", err)
	}

	return analytics, nil
}

// Update updates an existing analytics record
func (r *analyticsRepository) Update(ctx context.Context, analytics *models.Analytics) error {
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND website_id = ?", analytics.ID, analytics.TenantID, analytics.WebsiteID).
		Omit("tenant_id", "website_id", "id", "created_at").
		Updates(analytics).Error

	if err != nil {
		return fmt.Errorf("failed to update analytics: %w", err)
	}

	return nil
}

// Upsert creates or updates an analytics record
func (r *analyticsRepository) Upsert(ctx context.Context, analytics *models.Analytics) error {
	// Check if record exists
	var existing models.Analytics
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND advertisement_id = ? AND analytics_date = ?",
			analytics.TenantID, analytics.WebsiteID, analytics.AdvertisementID, analytics.AnalyticsDate.Format("2006-01-02")).
		First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// Create new record
		return r.Create(ctx, analytics)
	} else if err != nil {
		return fmt.Errorf("failed to check existing analytics: %w", err)
	}

	// Update existing record
	analytics.ID = existing.ID
	return r.Update(ctx, analytics)
}

// GetCampaignAnalytics retrieves campaign analytics
func (r *analyticsRepository) GetCampaignAnalytics(ctx context.Context, tenantID, websiteID, campaignID uint, startDate, endDate time.Time) (*dto.CampaignAnalyticsResponse, error) {
	// TODO: Implement comprehensive campaign analytics aggregation
	return &dto.CampaignAnalyticsResponse{
		CampaignID: campaignID,
		DateRange: models.DateRange{
			StartDate: startDate,
			EndDate:   endDate,
		},
		Summary: models.AnalyticsSummary{},
	}, nil
}

// GetAdvertisementAnalytics retrieves advertisement analytics
func (r *analyticsRepository) GetAdvertisementAnalytics(ctx context.Context, tenantID, websiteID, advertisementID uint, startDate, endDate time.Time) (*dto.AdvertisementAnalyticsResponse, error) {
	analytics, err := r.GetByDateRange(ctx, tenantID, websiteID, startDate, endDate, &advertisementID)
	if err != nil {
		return nil, err
	}

	if len(analytics) == 0 {
		return nil, fmt.Errorf("no analytics data found")
	}

	// Return the most recent analytics record for simplicity
	// Convert models.Analytics to dto.AdvertisementAnalyticsResponse
	a := analytics[0]
	return &dto.AdvertisementAnalyticsResponse{
		AdvertisementID: a.AdvertisementID,
		DateRange: models.DateRange{
			StartDate: startDate,
			EndDate:   endDate,
		},
		Summary: models.AnalyticsSummary{
			TotalImpressions: a.ImpressionsCount,
			TotalClicks:      a.ClicksCount,
			CTRRate:          a.CTRRate,
			TotalRevenue:     a.Revenue,
			CostPerClick:     a.CostPerClick,
		},
	}, nil
}

// GetDailyAnalytics retrieves daily analytics data
func (r *analyticsRepository) GetDailyAnalytics(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, advertisementID *uint) ([]models.DailyAnalytics, error) {
	// TODO: Implement daily analytics aggregation
	return []models.DailyAnalytics{}, nil
}

// GetWeeklyAnalytics retrieves weekly analytics data
func (r *analyticsRepository) GetWeeklyAnalytics(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, advertisementID *uint) ([]models.WeeklyAnalytics, error) {
	// TODO: Implement weekly analytics aggregation
	return []models.WeeklyAnalytics{}, nil
}

// GetMonthlyAnalytics retrieves monthly analytics data
func (r *analyticsRepository) GetMonthlyAnalytics(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, advertisementID *uint) ([]models.MonthlyAnalytics, error) {
	// TODO: Implement monthly analytics aggregation
	return []models.MonthlyAnalytics{}, nil
}

// AggregateAnalytics aggregates analytics data for a specific date
func (r *analyticsRepository) AggregateAnalytics(ctx context.Context, tenantID, websiteID uint, date time.Time) error {
	// TODO: Implement analytics aggregation from impression and click data
	// This would typically:
	// 1. Count impressions for the date
	// 2. Count clicks for the date
	// 3. Calculate CTR
	// 4. Calculate other metrics
	// 5. Upsert the aggregated data

	return nil
}
