package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// campaignRepository implements the CampaignRepository interface
type campaignRepository struct {
	db *gorm.DB
}

// NewCampaignRepository creates a new campaign repository
func NewCampaignRepository(db *gorm.DB) repositories.CampaignRepository {
	return &campaignRepository{db: db}
}

// Create creates a new campaign
func (r *campaignRepository) Create(ctx context.Context, campaign *models.Campaign) error {
	if err := r.db.WithContext(ctx).Create(campaign).Error; err != nil {
		return fmt.Errorf("failed to create campaign: %w", err)
	}
	return nil
}

// GetByID retrieves a campaign by ID and tenant ID
func (r *campaignRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Campaign, error) {
	var campaign models.Campaign
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND website_id = ? AND status != ?", id, tenantID, websiteID, "deleted").
		First(&campaign).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("campaign not found")
		}
		return nil, fmt.Errorf("failed to get campaign: %w", err)
	}

	return &campaign, nil
}

// GetByTenantAndWebsite retrieves campaigns by tenant ID with pagination and filtering
func (r *campaignRepository) GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, status string) ([]*models.Campaign, error) {
	var campaigns []*models.Campaign

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted")

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&campaigns).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get campaigns: %w", err)
	}

	return campaigns, nil
}

// Update updates an existing campaign
func (r *campaignRepository) Update(ctx context.Context, campaign *models.Campaign) error {
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND website_id = ?", campaign.ID, campaign.TenantID, campaign.WebsiteID).
		Omit("tenant_id", "website_id", "id", "created_at").
		Updates(campaign).Error

	if err != nil {
		return fmt.Errorf("failed to update campaign: %w", err)
	}

	return nil
}

// Delete soft deletes a campaign by setting status to 'deleted'
func (r *campaignRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.Campaign{}).
		Where("id = ? AND tenant_id = ? AND website_id = ?", id, tenantID, websiteID).
		Update("status", "deleted").Error

	if err != nil {
		return fmt.Errorf("failed to delete campaign: %w", err)
	}

	return nil
}

// Count returns the total count of campaigns for a tenant
func (r *campaignRepository) Count(ctx context.Context, tenantID, websiteID uint, status string) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).
		Model(&models.Campaign{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted")

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count campaigns: %w", err)
	}

	return count, nil
}

// GetActiveCampaigns retrieves all active campaigns for a tenant
func (r *campaignRepository) GetActiveCampaigns(ctx context.Context, tenantID, websiteID uint) ([]*models.Campaign, error) {
	var campaigns []*models.Campaign

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, "active").
		Order("created_at DESC").
		Find(&campaigns).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get active campaigns: %w", err)
	}

	return campaigns, nil
}

// GetRunningCampaigns retrieves all currently running campaigns for a tenant
func (r *campaignRepository) GetRunningCampaigns(ctx context.Context, tenantID, websiteID uint) ([]*models.Campaign, error) {
	var campaigns []*models.Campaign
	now := time.Now()

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ? AND start_date <= ? AND end_date >= ?",
			tenantID, websiteID, "active", now, now).
		Order("priority DESC, created_at ASC").
		Find(&campaigns).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get running campaigns: %w", err)
	}

	return campaigns, nil
}
