package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/database/scopes"
)

// clickRepository implements the ClickRepository interface
type clickRepository struct {
	db *gorm.DB
}

// NewClickRepository creates a new click repository
func NewClickRepository(db *gorm.DB) repositories.ClickRepository {
	return &clickRepository{db: db}
}

// Create creates a new click
func (r *clickRepository) Create(ctx context.Context, click *models.Click) error {
	if err := r.db.WithContext(ctx).Create(click).Error; err != nil {
		return fmt.Errorf("failed to create click: %w", err)
	}
	return nil
}

// GetByID retrieves a click by ID, tenant ID, and website ID
func (r *clickRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Click, error) {
	var click models.Click
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		First(&click).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("click not found")
		}
		return nil, fmt.Errorf("failed to get click: %w", err)
	}

	return &click, nil
}

// GetByAdvertisementID retrieves clicks by advertisement ID
func (r *clickRepository) GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint, limit, offset int) ([]*models.Click, error) {
	var clicks []*models.Click

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("advertisement_id = ?", advertisementID).
		Order("clicked_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&clicks).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get clicks by advertisement: %w", err)
	}

	return clicks, nil
}

// GetByTenantAndWebsite retrieves clicks by tenant and website ID with pagination and filtering
func (r *clickRepository) GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, filters map[string]interface{}) ([]*models.Click, error) {
	var clicks []*models.Click

	query := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID))

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.
		Order("clicked_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&clicks).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get clicks: %w", err)
	}

	return clicks, nil
}

// GetByDateRange retrieves clicks within a date range
func (r *clickRepository) GetByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, filters map[string]interface{}) ([]*models.Click, error) {
	var clicks []*models.Click

	query := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("clicked_at BETWEEN ? AND ?", startDate, endDate)

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.
		Order("clicked_at DESC").
		Find(&clicks).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get clicks by date range: %w", err)
	}

	return clicks, nil
}

// Count returns the total count of clicks for a tenant and website
func (r *clickRepository) Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).
		Model(&models.Click{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID))

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count clicks: %w", err)
	}

	return count, nil
}

// CountByDateRange returns the count of clicks within a date range
func (r *clickRepository) CountByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, filters map[string]interface{}) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).
		Model(&models.Click{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("clicked_at BETWEEN ? AND ?", startDate, endDate)

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count clicks by date range: %w", err)
	}

	return count, nil
}

// GetClickStats retrieves click statistics
func (r *clickRepository) GetClickStats(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) (*models.ClickStatsResponse, error) {
	// This is a simplified implementation
	totalClicks, err := r.CountByDateRange(ctx, tenantID, websiteID, startDate, endDate, nil)
	if err != nil {
		return nil, err
	}

	// TODO: Implement unique clicks, device breakdown, etc.
	return &models.ClickStatsResponse{
		TotalClicks:     int(totalClicks),
		UniqueClicks:    int(totalClicks), // Simplified
		DeviceBreakdown: make(map[string]int),
		PageBreakdown:   make(map[string]int),
		DailyClicks:     []models.DailyClickStat{},
		TopAds:          []models.TopClickAdvertisementStat{},
	}, nil
}

// GetDailyClicks retrieves daily click statistics
func (r *clickRepository) GetDailyClicks(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) ([]models.DailyClickStat, error) {
	// TODO: Implement daily clicks aggregation
	return []models.DailyClickStat{}, nil
}

// GetTopClickAds retrieves top performing advertisements by clicks
func (r *clickRepository) GetTopClickAds(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, limit int) ([]models.TopClickAdvertisementStat, error) {
	// TODO: Implement top ads by clicks
	return []models.TopClickAdvertisementStat{}, nil
}

// applyFilters applies filters to the query
func (r *clickRepository) applyFilters(query *gorm.DB, filters map[string]interface{}) *gorm.DB {
	if filters == nil {
		return query
	}

	if advertisementID, ok := filters["advertisement_id"]; ok {
		query = query.Where("advertisement_id = ?", advertisementID)
	}

	if impressionID, ok := filters["impression_id"]; ok {
		query = query.Where("impression_id = ?", impressionID)
	}

	if deviceType, ok := filters["device_type"]; ok {
		if deviceTypeStr, ok := deviceType.(string); ok && deviceTypeStr != "" {
			query = query.Where("device_type = ?", deviceTypeStr)
		}
	}

	return query
}
