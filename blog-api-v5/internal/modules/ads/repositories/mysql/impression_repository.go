package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/database/scopes"
)

// impressionRepository implements the ImpressionRepository interface
type impressionRepository struct {
	db *gorm.DB
}

// NewImpressionRepository creates a new impression repository
func NewImpressionRepository(db *gorm.DB) repositories.ImpressionRepository {
	return &impressionRepository{db: db}
}

// Create creates a new impression
func (r *impressionRepository) Create(ctx context.Context, impression *models.Impression) error {
	if err := r.db.WithContext(ctx).Create(impression).Error; err != nil {
		return fmt.Errorf("failed to create impression: %w", err)
	}
	return nil
}

// GetByID retrieves an impression by ID, tenant ID, and website ID
func (r *impressionRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Impression, error) {
	var impression models.Impression
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		First(&impression).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("impression not found")
		}
		return nil, fmt.Errorf("failed to get impression: %w", err)
	}

	return &impression, nil
}

// GetByAdvertisementID retrieves impressions by advertisement ID
func (r *impressionRepository) GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint, limit, offset int) ([]*models.Impression, error) {
	var impressions []*models.Impression

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("advertisement_id = ?", advertisementID).
		Order("viewed_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&impressions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get impressions by advertisement: %w", err)
	}

	return impressions, nil
}

// GetByTenantAndWebsite retrieves impressions by tenant and website ID with pagination and filtering
func (r *impressionRepository) GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, filters map[string]interface{}) ([]*models.Impression, error) {
	var impressions []*models.Impression

	query := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID))

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.
		Order("viewed_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&impressions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get impressions: %w", err)
	}

	return impressions, nil
}

// GetByDateRange retrieves impressions within a date range
func (r *impressionRepository) GetByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, filters map[string]interface{}) ([]*models.Impression, error) {
	var impressions []*models.Impression

	query := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("viewed_at BETWEEN ? AND ?", startDate, endDate)

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.
		Order("viewed_at DESC").
		Find(&impressions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get impressions by date range: %w", err)
	}

	return impressions, nil
}

// Count returns the total count of impressions for a tenant and website
func (r *impressionRepository) Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).
		Model(&models.Impression{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID))

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count impressions: %w", err)
	}

	return count, nil
}

// CountByDateRange returns the count of impressions within a date range
func (r *impressionRepository) CountByDateRange(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, filters map[string]interface{}) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).
		Model(&models.Impression{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("viewed_at BETWEEN ? AND ?", startDate, endDate)

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count impressions by date range: %w", err)
	}

	return count, nil
}

// GetImpressionStats retrieves impression statistics
func (r *impressionRepository) GetImpressionStats(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) (*models.ImpressionStatsResponse, error) {
	// This is a simplified implementation - in production, you'd want more sophisticated queries
	totalImpressions, err := r.CountByDateRange(ctx, tenantID, websiteID, startDate, endDate, nil)
	if err != nil {
		return nil, err
	}

	// TODO: Implement device breakdown, page breakdown, etc.
	return &models.ImpressionStatsResponse{
		TotalImpressions: int(totalImpressions),
		DeviceBreakdown:  make(map[string]int),
		PageBreakdown:    make(map[string]int),
		DailyImpressions: []models.DailyImpressionStat{},
		TopAds:           []models.TopAdvertisementStat{},
	}, nil
}

// GetDailyImpressions retrieves daily impression statistics
func (r *impressionRepository) GetDailyImpressions(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time) ([]models.DailyImpressionStat, error) {
	// TODO: Implement daily impressions aggregation
	return []models.DailyImpressionStat{}, nil
}

// GetTopAds retrieves top performing advertisements by impressions
func (r *impressionRepository) GetTopAds(ctx context.Context, tenantID, websiteID uint, startDate, endDate time.Time, limit int) ([]models.TopAdvertisementStat, error) {
	// TODO: Implement top ads by impressions
	return []models.TopAdvertisementStat{}, nil
}

// applyFilters applies filters to the query
func (r *impressionRepository) applyFilters(query *gorm.DB, filters map[string]interface{}) *gorm.DB {
	if filters == nil {
		return query
	}

	if advertisementID, ok := filters["advertisement_id"]; ok {
		query = query.Where("advertisement_id = ?", advertisementID)
	}

	if placementID, ok := filters["placement_id"]; ok {
		query = query.Where("placement_id = ?", placementID)
	}

	if deviceType, ok := filters["device_type"]; ok {
		if deviceTypeStr, ok := deviceType.(string); ok && deviceTypeStr != "" {
			query = query.Where("device_type = ?", deviceTypeStr)
		}
	}

	return query
}
