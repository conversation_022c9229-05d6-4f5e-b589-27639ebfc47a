package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/database/scopes"
)

// placementRepository implements the PlacementRepository interface
type placementRepository struct {
	db *gorm.DB
}

// NewPlacementRepository creates a new placement repository
func NewPlacementRepository(db *gorm.DB) repositories.PlacementRepository {
	return &placementRepository{db: db}
}

// Create creates a new placement
func (r *placementRepository) Create(ctx context.Context, placement *models.Placement) error {
	if err := r.db.WithContext(ctx).Create(placement).Error; err != nil {
		return fmt.Errorf("failed to create placement: %w", err)
	}
	return nil
}

// GetByID retrieves a placement by ID, tenant ID, and website ID
func (r *placementRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Placement, error) {
	var placement models.Placement
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ? AND status != ?", id, "deleted").
		First(&placement).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("placement not found")
		}
		return nil, fmt.Errorf("failed to get placement: %w", err)
	}

	return &placement, nil
}

// GetByTenantAndWebsite retrieves placements by tenant and website ID with pagination
func (r *placementRepository) GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]*models.Placement, error) {
	var placements []*models.Placement

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("status != ?", "deleted").
		Order("page_type ASC, position ASC").
		Limit(limit).
		Offset(offset).
		Find(&placements).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get placements: %w", err)
	}

	return placements, nil
}

// GetByPageAndPosition retrieves a placement by page type and position
func (r *placementRepository) GetByPageAndPosition(ctx context.Context, tenantID, websiteID uint, pageType, position string) (*models.Placement, error) {
	var placement models.Placement
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("page_type = ? AND position = ? AND status = ?", pageType, position, "active").
		First(&placement).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("placement not found")
		}
		return nil, fmt.Errorf("failed to get placement: %w", err)
	}

	return &placement, nil
}

// Update updates an existing placement
func (r *placementRepository) Update(ctx context.Context, placement *models.Placement) error {
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND website_id = ?", placement.ID, placement.TenantID, placement.WebsiteID).
		Omit("tenant_id", "website_id", "id", "created_at").
		Updates(placement).Error

	if err != nil {
		return fmt.Errorf("failed to update placement: %w", err)
	}

	return nil
}

// Delete soft deletes a placement by setting status to 'deleted'
func (r *placementRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.Placement{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		Update("status", "deleted").Error

	if err != nil {
		return fmt.Errorf("failed to delete placement: %w", err)
	}

	return nil
}

// GetActivePlacements retrieves all active placements for a tenant and website
func (r *placementRepository) GetActivePlacements(ctx context.Context, tenantID, websiteID uint) ([]*models.Placement, error) {
	var placements []*models.Placement

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("status = ?", "active").
		Order("page_type ASC, position ASC").
		Find(&placements).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get active placements: %w", err)
	}

	return placements, nil
}

// GetPlacementsByPage retrieves placements by page type
func (r *placementRepository) GetPlacementsByPage(ctx context.Context, tenantID, websiteID uint, pageType string) ([]*models.Placement, error) {
	var placements []*models.Placement

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("page_type = ? AND status = ?", pageType, "active").
		Order("position ASC").
		Find(&placements).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get placements by page: %w", err)
	}

	return placements, nil
}

// Count counts the number of placements based on filters
func (r *placementRepository) Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.Placement{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("status != ?", "deleted")

	// Apply filters
	if filters != nil {
		if pageType, ok := filters["page_type"].(string); ok && pageType != "" {
			query = query.Where("page_type = ?", pageType)
		}
		if position, ok := filters["position"].(string); ok && position != "" {
			query = query.Where("position = ?", position)
		}
		if status, ok := filters["status"].(string); ok && status != "" {
			query = query.Where("status = ?", status)
		}
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count placements: %w", err)
	}

	return count, nil
}
