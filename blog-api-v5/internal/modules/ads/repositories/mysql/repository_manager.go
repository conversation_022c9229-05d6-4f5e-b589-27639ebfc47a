package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// repositoryManager implements the RepositoryManager interface
type repositoryManager struct {
	db *gorm.DB

	// Repository instances
	campaignRepo         repositories.CampaignRepository
	advertisementRepo    repositories.AdvertisementRepository
	scheduleRepo         repositories.ScheduleRepository
	placementRepo        repositories.PlacementRepository
	impressionRepo       repositories.ImpressionRepository
	clickRepo            repositories.ClickRepository
	analyticsRepo        repositories.AnalyticsRepository
	targetingRuleRepo    repositories.TargetingRuleRepository
	mediaIntegrationRepo repositories.MediaIntegrationRepository
}

// NewRepositoryManager creates a new repository manager
func NewRepositoryManager(db *gorm.DB) repositories.RepositoryManager {
	return &repositoryManager{
		db:                   db,
		campaignRepo:         NewCampaignRepository(db),
		advertisementRepo:    NewAdvertisementRepository(db),
		scheduleRepo:         NewScheduleRepository(db),
		placementRepo:        NewPlacementRepository(db),
		impressionRepo:       NewImpressionRepository(db),
		clickRepo:            NewClickRepository(db),
		analyticsRepo:        NewAnalyticsRepository(db),
		targetingRuleRepo:    NewTargetingRuleRepository(db),
		mediaIntegrationRepo: NewMediaIntegrationRepository(db),
	}
}

// Campaign returns the campaign repository
func (rm *repositoryManager) Campaign() repositories.CampaignRepository {
	return rm.campaignRepo
}

// Advertisement returns the advertisement repository
func (rm *repositoryManager) Advertisement() repositories.AdvertisementRepository {
	return rm.advertisementRepo
}

// Schedule returns the schedule repository
func (rm *repositoryManager) Schedule() repositories.ScheduleRepository {
	return rm.scheduleRepo
}

// Placement returns the placement repository
func (rm *repositoryManager) Placement() repositories.PlacementRepository {
	return rm.placementRepo
}

// Impression returns the impression repository
func (rm *repositoryManager) Impression() repositories.ImpressionRepository {
	return rm.impressionRepo
}

// Click returns the click repository
func (rm *repositoryManager) Click() repositories.ClickRepository {
	return rm.clickRepo
}

// Analytics returns the analytics repository
func (rm *repositoryManager) Analytics() repositories.AnalyticsRepository {
	return rm.analyticsRepo
}

// TargetingRule returns the targeting rule repository
func (rm *repositoryManager) TargetingRule() repositories.TargetingRuleRepository {
	return rm.targetingRuleRepo
}

// MediaIntegration returns the media integration repository
func (rm *repositoryManager) MediaIntegration() repositories.MediaIntegrationRepository {
	return rm.mediaIntegrationRepo
}

// WithTransaction executes a function within a database transaction
func (rm *repositoryManager) WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return rm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Create a new context with the transaction
		txCtx := context.WithValue(ctx, "db_tx", tx)
		return fn(txCtx)
	})
}

// BeginTransaction starts a new database transaction
func (rm *repositoryManager) BeginTransaction(ctx context.Context) (context.Context, error) {
	tx := rm.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return ctx, fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}

	txCtx := context.WithValue(ctx, "db_tx", tx)
	return txCtx, nil
}

// CommitTransaction commits the current transaction
func (rm *repositoryManager) CommitTransaction(ctx context.Context) error {
	tx, ok := ctx.Value("db_tx").(*gorm.DB)
	if !ok {
		return fmt.Errorf("no transaction found in context")
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// RollbackTransaction rolls back the current transaction
func (rm *repositoryManager) RollbackTransaction(ctx context.Context) error {
	tx, ok := ctx.Value("db_tx").(*gorm.DB)
	if !ok {
		return fmt.Errorf("no transaction found in context")
	}

	if err := tx.Rollback().Error; err != nil {
		return fmt.Errorf("failed to rollback transaction: %w", err)
	}

	return nil
}

// getDB returns the appropriate database instance (transaction or regular)
func getDB(ctx context.Context, defaultDB *gorm.DB) *gorm.DB {
	if tx, ok := ctx.Value("db_tx").(*gorm.DB); ok {
		return tx
	}
	return defaultDB
}
