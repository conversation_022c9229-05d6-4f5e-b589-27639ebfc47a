package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// scheduleRepository implements the ScheduleRepository interface
type scheduleRepository struct {
	db *gorm.DB
}

// NewScheduleRepository creates a new schedule repository
func NewScheduleRepository(db *gorm.DB) repositories.ScheduleRepository {
	return &scheduleRepository{db: db}
}

// Create creates a new schedule
func (r *scheduleRepository) Create(ctx context.Context, schedule *models.Schedule) error {
	if err := r.db.WithContext(ctx).Create(schedule).Error; err != nil {
		return fmt.Errorf("failed to create schedule: %w", err)
	}
	return nil
}

// GetByID retrieves a schedule by ID and tenant ID
func (r *scheduleRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.Schedule, error) {
	var schedule models.Schedule
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND website_id = ? AND status != ?", id, tenantID, websiteID, "deleted").
		First(&schedule).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("schedule not found")
		}
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}

	return &schedule, nil
}

// GetByAdvertisementID retrieves schedules by advertisement ID
func (r *scheduleRepository) GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint) ([]*models.Schedule, error) {
	var schedules []*models.Schedule

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND advertisement_id = ? AND status != ?", tenantID, websiteID, advertisementID, "deleted").
		Order("start_time ASC").
		Find(&schedules).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get schedules by advertisement: %w", err)
	}

	return schedules, nil
}

// GetByTenantAndWebsite retrieves schedules by tenant ID with pagination
func (r *scheduleRepository) GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]*models.Schedule, error) {
	var schedules []*models.Schedule

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted").
		Order("start_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&schedules).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get schedules: %w", err)
	}

	return schedules, nil
}

// Update updates an existing schedule
func (r *scheduleRepository) Update(ctx context.Context, schedule *models.Schedule) error {
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", schedule.ID, schedule.TenantID).
		Updates(schedule).Error

	if err != nil {
		return fmt.Errorf("failed to update schedule: %w", err)
	}

	return nil
}

// Delete soft deletes a schedule by setting status to 'deleted'
func (r *scheduleRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.Schedule{}).
		Where("id = ? AND tenant_id = ? AND website_id = ?", id, tenantID, websiteID).
		Update("status", "deleted").Error

	if err != nil {
		return fmt.Errorf("failed to delete schedule: %w", err)
	}

	return nil
}

// GetActiveSchedules retrieves active schedules for the current time
func (r *scheduleRepository) GetActiveSchedules(ctx context.Context, tenantID, websiteID uint, currentTime time.Time) ([]*models.Schedule, error) {
	var schedules []*models.Schedule

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ? AND start_time <= ? AND end_time >= ?",
			tenantID, websiteID, "active", currentTime, currentTime).
		Order("start_time ASC").
		Find(&schedules).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get active schedules: %w", err)
	}

	return schedules, nil
}

// GetSchedulesByTimeRange retrieves schedules within a time range
func (r *scheduleRepository) GetSchedulesByTimeRange(ctx context.Context, tenantID, websiteID uint, startTime, endTime time.Time) ([]*models.Schedule, error) {
	var schedules []*models.Schedule

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ? AND start_time <= ? AND end_time >= ?",
			tenantID, websiteID, "deleted", endTime, startTime).
		Order("start_time ASC").
		Find(&schedules).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get schedules by time range: %w", err)
	}

	return schedules, nil
}

// Count counts the number of schedules based on filters
func (r *scheduleRepository) Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.Schedule{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted")

	// Apply filters
	if filters != nil {
		if advertisementID, ok := filters["advertisement_id"].(uint); ok && advertisementID > 0 {
			query = query.Where("advertisement_id = ?", advertisementID)
		}
		if status, ok := filters["status"].(string); ok && status != "" {
			query = query.Where("status = ?", status)
		}
		if startTime, ok := filters["start_time"].(time.Time); ok && !startTime.IsZero() {
			query = query.Where("start_time >= ?", startTime)
		}
		if endTime, ok := filters["end_time"].(time.Time); ok && !endTime.IsZero() {
			query = query.Where("end_time <= ?", endTime)
		}
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count schedules: %w", err)
	}

	return count, nil
}
