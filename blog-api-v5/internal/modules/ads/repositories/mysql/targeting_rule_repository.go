package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/database/scopes"
)

// targetingRuleRepository implements the TargetingRuleRepository interface
type targetingRuleRepository struct {
	db *gorm.DB
}

// NewTargetingRuleRepository creates a new targeting rule repository
func NewTargetingRuleRepository(db *gorm.DB) repositories.TargetingRuleRepository {
	return &targetingRuleRepository{db: db}
}

// Create creates a new targeting rule
func (r *targetingRuleRepository) Create(ctx context.Context, rule *models.TargetingRule) error {
	if err := r.db.WithContext(ctx).Create(rule).Error; err != nil {
		return fmt.Errorf("failed to create targeting rule: %w", err)
	}
	return nil
}

// GetByID retrieves a targeting rule by ID, tenant ID, and website ID
func (r *targetingRuleRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.TargetingRule, error) {
	var rule models.TargetingRule
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ? AND status != ?", id, "deleted").
		First(&rule).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("targeting rule not found")
		}
		return nil, fmt.Errorf("failed to get targeting rule: %w", err)
	}

	return &rule, nil
}

// GetByAdvertisementID retrieves targeting rules by advertisement ID
func (r *targetingRuleRepository) GetByAdvertisementID(ctx context.Context, tenantID, websiteID, advertisementID uint) ([]*models.TargetingRule, error) {
	var rules []*models.TargetingRule

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("advertisement_id = ? AND status != ?", advertisementID, "deleted").
		Order("priority ASC, created_at ASC").
		Find(&rules).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get targeting rules by advertisement: %w", err)
	}

	return rules, nil
}

// GetByTenantAndWebsite retrieves targeting rules by tenant and website ID with pagination and filtering
func (r *targetingRuleRepository) GetByTenantAndWebsite(ctx context.Context, tenantID, websiteID uint, limit, offset int, filters map[string]interface{}) ([]*models.TargetingRule, error) {
	var rules []*models.TargetingRule

	query := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("status != ?", "deleted")

	// Apply filters
	query = r.applyFilters(query, filters)

	err := query.
		Order("priority ASC, created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&rules).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get targeting rules: %w", err)
	}

	return rules, nil
}

// Update updates an existing targeting rule
func (r *targetingRuleRepository) Update(ctx context.Context, rule *models.TargetingRule) error {
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND website_id = ?", rule.ID, rule.TenantID, rule.WebsiteID).
		Omit("tenant_id", "website_id", "id", "created_at").
		Updates(rule).Error

	if err != nil {
		return fmt.Errorf("failed to update targeting rule: %w", err)
	}

	return nil
}

// Delete soft deletes a targeting rule by setting status to 'deleted'
func (r *targetingRuleRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.TargetingRule{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		Update("status", "deleted").Error

	if err != nil {
		return fmt.Errorf("failed to delete targeting rule: %w", err)
	}

	return nil
}

// GetActiveRules retrieves all active targeting rules for a tenant and website
func (r *targetingRuleRepository) GetActiveRules(ctx context.Context, tenantID, websiteID uint) ([]*models.TargetingRule, error) {
	var rules []*models.TargetingRule

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("status = ?", "active").
		Order("priority ASC, created_at ASC").
		Find(&rules).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get active targeting rules: %w", err)
	}

	return rules, nil
}

// GetRulesByType retrieves targeting rules by rule type
func (r *targetingRuleRepository) GetRulesByType(ctx context.Context, tenantID, websiteID uint, ruleType string) ([]*models.TargetingRule, error) {
	var rules []*models.TargetingRule

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("rule_type = ? AND status = ?", ruleType, "active").
		Order("priority ASC, created_at ASC").
		Find(&rules).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get targeting rules by type: %w", err)
	}

	return rules, nil
}

// GetRulesByAdvertisement retrieves targeting rules by advertisement (alias for GetByAdvertisementID)
func (r *targetingRuleRepository) GetRulesByAdvertisement(ctx context.Context, tenantID, websiteID, advertisementID uint) ([]*models.TargetingRule, error) {
	return r.GetByAdvertisementID(ctx, tenantID, websiteID, advertisementID)
}

// applyFilters applies filters to the query
func (r *targetingRuleRepository) applyFilters(query *gorm.DB, filters map[string]interface{}) *gorm.DB {
	if filters == nil {
		return query
	}

	if advertisementID, ok := filters["advertisement_id"]; ok {
		query = query.Where("advertisement_id = ?", advertisementID)
	}

	if ruleType, ok := filters["rule_type"]; ok {
		if ruleTypeStr, ok := ruleType.(string); ok && ruleTypeStr != "" {
			query = query.Where("rule_type = ?", ruleTypeStr)
		}
	}

	if status, ok := filters["status"]; ok {
		if statusStr, ok := status.(string); ok && statusStr != "" {
			query = query.Where("status = ?", statusStr)
		}
	}

	if priority, ok := filters["priority"]; ok {
		query = query.Where("priority = ?", priority)
	}

	if operator, ok := filters["operator"]; ok {
		if operatorStr, ok := operator.(string); ok && operatorStr != "" {
			query = query.Where("operator = ?", operatorStr)
		}
	}

	return query
}

// Count counts the number of targeting rules based on filters
func (r *targetingRuleRepository) Count(ctx context.Context, tenantID, websiteID uint, filters map[string]interface{}) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.TargetingRule{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("status != ?", "deleted")

	// Apply filters
	query = r.applyFilters(query, filters)

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count targeting rules: %w", err)
	}

	return count, nil
}
