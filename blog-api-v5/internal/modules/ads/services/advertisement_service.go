package services

import (
	"context"
	"fmt"

	"encoding/json"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/datatypes"
)

// advertisementService implements the AdvertisementService interface
type advertisementService struct {
	repoManager repositories.RepositoryManager
}

// NewAdvertisementService creates a new advertisement service instance
func NewAdvertisementService(repoManager repositories.RepositoryManager) AdvertisementService {
	return &advertisementService{
		repoManager: repoManager,
	}
}

// CreateAdvertisement creates a new advertisement
func (s *advertisementService) CreateAdvertisement(ctx context.Context, tenantID, websiteID uint, req *dto.AdvertisementCreateRequest) (*dto.AdvertisementResponse, error) {
	// Validate request
	if err := s.validateAdvertisementCreateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid advertisement request: %w", err)
	}

	// Check if campaign exists and belongs to tenant
	campaign, err := s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, req.CampaignID)
	if err != nil {
		return nil, fmt.Errorf("campaign not found: %w", err)
	}

	// Check if campaign is active or scheduled
	if campaign.Status != "active" && campaign.Status != "scheduled" && campaign.Status != "draft" {
		return nil, fmt.Errorf("cannot create advertisement for campaign with status: %s", campaign.Status)
	}

	// Convert page targeting to JSON
	var pageTargetingJSON datatypes.JSON
	if len(req.PageTargeting) > 0 {
		pageTargetingBytes, err := json.Marshal(req.PageTargeting)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal page targeting: %w", err)
		}
		pageTargetingJSON = pageTargetingBytes
	}

	advertisement := &models.Advertisement{
		TenantID:        tenantID,
		CampaignID:      req.CampaignID,
		Title:           req.Title,
		Description:     req.Description,
		ImageURL:        req.ImageURL,
		LinkURL:         req.LinkURL,
		AdType:          req.AdType,
		Position:        req.Position,
		Priority:        req.Priority,
		DeviceTargeting: req.DeviceTargeting,
		PageTargeting:   pageTargetingJSON,
		Status:          "draft", // Default to draft status
		MediaFileIDs:    req.MediaFileIDs,
	}

	// Create advertisement in database
	err = s.repoManager.Advertisement().Create(ctx, advertisement)
	if err != nil {
		return nil, fmt.Errorf("failed to create advertisement: %w", err)
	}

	// Convert to DTO response
	return s.convertModelToDTO(advertisement), nil
}

// GetAdvertisementByID retrieves an advertisement by ID
func (s *advertisementService) GetAdvertisementByID(ctx context.Context, tenantID, websiteID uint, adID uint) (*dto.AdvertisementResponse, error) {
	advertisement, err := s.repoManager.Advertisement().GetByID(ctx, tenantID, websiteID, adID)
	if err != nil {
		return nil, fmt.Errorf("failed to get advertisement: %w", err)
	}

	return s.convertModelToDTO(advertisement), nil
}

// UpdateAdvertisement updates an existing advertisement
func (s *advertisementService) UpdateAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint, req *dto.AdvertisementUpdateRequest) (*dto.AdvertisementResponse, error) {
	// Get existing advertisement as model
	existingAd, err := s.repoManager.Advertisement().GetByID(ctx, tenantID, websiteID, adID)
	if err != nil {
		return nil, fmt.Errorf("failed to get advertisement: %w", err)
	}

	// Apply updates
	if req.Title != nil {
		existingAd.Title = *req.Title
	}
	if req.Description != nil {
		existingAd.Description = *req.Description
	}
	if req.ImageURL != nil {
		existingAd.ImageURL = *req.ImageURL
	}
	if req.LinkURL != nil {
		existingAd.LinkURL = *req.LinkURL
	}
	if req.AdType != nil {
		existingAd.AdType = *req.AdType
	}
	if req.Position != nil {
		existingAd.Position = *req.Position
	}
	if req.Priority != nil {
		existingAd.Priority = *req.Priority
	}
	if req.DeviceTargeting != nil {
		existingAd.DeviceTargeting = *req.DeviceTargeting
	}
	if req.Status != nil {
		existingAd.Status = *req.Status
	}
	if req.PageTargeting != nil && len(req.PageTargeting) > 0 {
		pageTargetingBytes, err := json.Marshal(req.PageTargeting)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal page targeting: %w", err)
		}
		existingAd.PageTargeting = pageTargetingBytes
	}
	if req.MediaFileIDs != nil {
		existingAd.MediaFileIDs = req.MediaFileIDs
	}

	// Update in database
	err = s.repoManager.Advertisement().Update(ctx, existingAd)
	if err != nil {
		return nil, fmt.Errorf("failed to update advertisement: %w", err)
	}

	return s.convertModelToDTO(existingAd), nil
}

// DeleteAdvertisement deletes an advertisement (soft delete)
func (s *advertisementService) DeleteAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint) error {
	// Check if advertisement exists
	_, err := s.repoManager.Advertisement().GetByID(ctx, tenantID, websiteID, adID)
	if err != nil {
		return fmt.Errorf("advertisement not found: %w", err)
	}

	// Soft delete advertisement
	err = s.repoManager.Advertisement().Delete(ctx, tenantID, websiteID, adID)
	if err != nil {
		return fmt.Errorf("failed to delete advertisement: %w", err)
	}

	return nil
}

// ListAdvertisements lists advertisements with filters
func (s *advertisementService) ListAdvertisements(ctx context.Context, tenantID, websiteID uint, filters *dto.AdvertisementListFilter) (*dto.AdvertisementListResponse, error) {
	if filters == nil {
		filters = &dto.AdvertisementListFilter{
			TenantID: tenantID,
		}
	}

	// Convert DTO filter to legacy filter for repository compatibility
	legacyFilters := s.convertDTOFilterToLegacyFilter(filters)

	// Set default pagination
	if legacyFilters.Limit <= 0 {
		legacyFilters.Limit = 20
	}
	if legacyFilters.Limit > 100 {
		legacyFilters.Limit = 100
	}

	// Convert filters to map
	filterMap := make(map[string]interface{})
	if legacyFilters.CampaignID != nil {
		filterMap["campaign_id"] = *legacyFilters.CampaignID
	}
	if legacyFilters.Status != nil {
		filterMap["status"] = *legacyFilters.Status
	}
	if legacyFilters.AdType != nil {
		filterMap["ad_type"] = *legacyFilters.AdType
	}
	if legacyFilters.Position != nil {
		filterMap["position"] = *legacyFilters.Position
	}
	if legacyFilters.DeviceTargeting != nil {
		filterMap["device_targeting"] = *legacyFilters.DeviceTargeting
	}

	advertisements, err := s.repoManager.Advertisement().GetByTenantAndWebsite(ctx, tenantID, websiteID, legacyFilters.Limit, legacyFilters.Offset, filterMap)
	if err != nil {
		return nil, fmt.Errorf("failed to list advertisements: %w", err)
	}

	// Get total count
	total, err := s.repoManager.Advertisement().Count(ctx, tenantID, websiteID, filterMap)
	if err != nil {
		return nil, fmt.Errorf("failed to count advertisements: %w", err)
	}

	// Convert to DTOs
	dtos := s.convertModelsToDTOs(advertisements)

	// Build pagination response
	var paginationResponse *pagination.CursorResponse
	if filters.Pagination != nil {
		paginationResponse = &pagination.CursorResponse{
			Count: int(total),
			Limit: filters.Pagination.Limit,
		}

		// Calculate if there are more results
		hasNext := int64(legacyFilters.Offset+legacyFilters.Limit) < total
		if hasNext && len(dtos) > 0 {
			paginationResponse.HasNext = true
			paginationResponse.NextCursor = fmt.Sprintf("%d", dtos[len(dtos)-1].ID)
		}
	}

	return &dto.AdvertisementListResponse{
		Advertisements: dtos,
		Pagination:     paginationResponse,
	}, nil
}

// ListAdvertisementsByCampaign lists advertisements for a specific campaign
func (s *advertisementService) ListAdvertisementsByCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint) (*dto.AdvertisementListResponse, error) {
	// Check if campaign exists
	_, err := s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, campaignID)
	if err != nil {
		return nil, fmt.Errorf("campaign not found: %w", err)
	}

	advertisements, err := s.repoManager.Advertisement().GetByCampaignID(ctx, tenantID, websiteID, campaignID, 100, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to list advertisements for campaign: %w", err)
	}

	// Convert to DTOs
	dtos := s.convertModelsToDTOs(advertisements)

	return &dto.AdvertisementListResponse{
		Advertisements: dtos,
		Pagination:     nil, // No pagination for this method
	}, nil
}

// GetAdvertisementsForPlacement retrieves advertisements suitable for a placement
func (s *advertisementService) GetAdvertisementsForPlacement(ctx context.Context, tenantID, websiteID uint, placement *dto.PlacementResponse, context *models.TargetingContext) ([]*dto.AdServingResponse, error) {
	// Convert placement DTO to model for internal use
	placementModel := s.convertPlacementDTOToModel(placement)

	// Get all active advertisements
	advertisements, err := s.repoManager.Advertisement().GetActiveAds(ctx, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active advertisements: %w", err)
	}

	var suitableAds []*models.Advertisement
	for _, ad := range advertisements {
		// Check if ad matches placement position
		if string(ad.Position) != placementModel.Position {
			continue
		}

		// Check if ad passes targeting validation
		isValidTarget, err := s.ValidateTargeting(ctx, ad, websiteID, context)
		if err != nil {
			// Log error but don't fail entire operation
			continue
		}

		if isValidTarget {
			suitableAds = append(suitableAds, ad)
		}
	}

	// Sort by priority (higher priority first)
	for i := 0; i < len(suitableAds); i++ {
		for j := i + 1; j < len(suitableAds); j++ {
			if suitableAds[i].Priority < suitableAds[j].Priority {
				suitableAds[i], suitableAds[j] = suitableAds[j], suitableAds[i]
			}
		}
	}

	// Limit to placement's max ads
	if len(suitableAds) > placementModel.MaxAds {
		suitableAds = suitableAds[:placementModel.MaxAds]
	}

	// Convert to ad serving responses
	return s.convertModelsToAdServingResponses(suitableAds), nil
}

// ValidateTargeting validates if an advertisement matches targeting context
func (s *advertisementService) ValidateTargeting(ctx context.Context, ad *models.Advertisement, websiteID uint, context *models.TargetingContext) (bool, error) {
	// Check device targeting
	if ad.DeviceTargeting != "" && string(ad.DeviceTargeting) != context.DeviceType {
		return false, nil
	}

	// Get targeting rules for this advertisement
	rules, err := s.repoManager.TargetingRule().GetByAdvertisementID(ctx, ad.TenantID, websiteID, ad.ID)
	if err != nil {
		return false, fmt.Errorf("failed to get targeting rules: %w", err)
	}

	// If no rules, consider it valid
	if len(rules) == 0 {
		return true, nil
	}

	// All rules must pass for targeting to be valid
	for _, rule := range rules {
		if !s.evaluateTargetingRule(rule, context) {
			return false, nil
		}
	}

	return true, nil
}

// ActivateAdvertisement activates an advertisement
func (s *advertisementService) ActivateAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint) error {
	advertisement, err := s.repoManager.Advertisement().GetByID(ctx, tenantID, websiteID, adID)
	if err != nil {
		return fmt.Errorf("advertisement not found: %w", err)
	}

	if advertisement.Status == "active" {
		return fmt.Errorf("advertisement is already active")
	}

	// Check if campaign is active
	campaign, err := s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, advertisement.CampaignID)
	if err != nil {
		return fmt.Errorf("failed to get campaign: %w", err)
	}

	if campaign.Status != "active" {
		return fmt.Errorf("cannot activate advertisement for inactive campaign")
	}

	// Update status
	advertisement.Status = "active"
	err = s.repoManager.Advertisement().Update(ctx, advertisement)
	if err != nil {
		return fmt.Errorf("failed to activate advertisement: %w", err)
	}

	return nil
}

// PauseAdvertisement pauses an advertisement
func (s *advertisementService) PauseAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint) error {
	advertisement, err := s.repoManager.Advertisement().GetByID(ctx, tenantID, websiteID, adID)
	if err != nil {
		return fmt.Errorf("advertisement not found: %w", err)
	}

	if advertisement.Status != "active" {
		return fmt.Errorf("can only pause active advertisements")
	}

	// Update status
	advertisement.Status = "paused"
	err = s.repoManager.Advertisement().Update(ctx, advertisement)
	if err != nil {
		return fmt.Errorf("failed to pause advertisement: %w", err)
	}

	return nil
}

// ServeAds serves advertisements based on the request and targeting context
func (s *advertisementService) ServeAds(ctx context.Context, req *dto.AdServingRequest, context *models.TargetingContext) ([]*dto.AdServingResponse, error) {
	// Get placement for the specified position and page type
	placement, err := s.repoManager.Placement().GetByPageAndPosition(ctx, req.TenantID, req.WebsiteID, req.PageType, string(req.Position))
	if err != nil {
		// Return empty response if no placement found
		return []*dto.AdServingResponse{}, nil
	}

	// Convert placement to DTO response
	placementDTO := &dto.PlacementResponse{
		ID:       placement.ID,
		TenantID: placement.TenantID,
		PageType: placement.PageType,
		Position: placement.Position,
		MaxAds:   placement.MaxAds,
		Status:   placement.Status,
	}

	// Get advertisements for this placement
	ads, err := s.GetAdvertisementsForPlacement(ctx, req.TenantID, req.WebsiteID, placementDTO, context)
	if err != nil {
		return nil, fmt.Errorf("failed to get advertisements for placement: %w", err)
	}

	return ads, nil
}

// validateAdvertisementCreateRequest validates the advertisement creation request
func (s *advertisementService) validateAdvertisementCreateRequest(req *dto.AdvertisementCreateRequest) error {
	if req.Title == "" {
		return fmt.Errorf("advertisement title is required")
	}

	if len(req.Title) > 255 {
		return fmt.Errorf("advertisement title cannot exceed 255 characters")
	}

	if req.CampaignID == 0 {
		return fmt.Errorf("campaign ID is required")
	}

	if req.AdType == "" {
		return fmt.Errorf("advertisement type is required")
	}

	if req.Description == "" {
		return fmt.Errorf("advertisement description is required")
	}

	if req.Position == "" {
		return fmt.Errorf("advertisement position is required")
	}

	if req.Priority < 1 || req.Priority > 10 {
		return fmt.Errorf("advertisement priority must be between 1 and 10")
	}

	return nil
}

// applyAdvertisementFilters applies additional filtering to advertisements
// Deprecated: Use DTO filters instead
func (s *advertisementService) applyAdvertisementFilters(advertisements []*models.Advertisement, filters *AdvertisementFilters) []*models.Advertisement {
	var filtered []*models.Advertisement

	for _, ad := range advertisements {
		// Campaign ID filter
		if filters.CampaignID != nil && ad.CampaignID != *filters.CampaignID {
			continue
		}

		// Status filter
		if filters.Status != nil && string(ad.Status) != *filters.Status {
			continue
		}

		// Ad type filter
		if filters.AdType != nil && string(ad.AdType) != *filters.AdType {
			continue
		}

		// Position filter
		if filters.Position != nil && string(ad.Position) != *filters.Position {
			continue
		}

		// Device targeting filter
		if filters.DeviceTargeting != nil && string(ad.DeviceTargeting) != *filters.DeviceTargeting {
			continue
		}

		// Priority filters
		if filters.MinPriority != nil && ad.Priority < *filters.MinPriority {
			continue
		}
		if filters.MaxPriority != nil && ad.Priority > *filters.MaxPriority {
			continue
		}

		filtered = append(filtered, ad)
	}

	return filtered
}

// evaluateTargetingRule evaluates a single targeting rule against context
func (s *advertisementService) evaluateTargetingRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	switch rule.RuleType {
	case "location":
		return s.evaluateLocationRule(rule, context)
	case "time":
		return s.evaluateTimeRule(rule, context)
	case "device":
		return s.evaluateDeviceRule(rule, context)
	case "user_agent":
		return s.evaluateUserAgentRule(rule, context)
	case "referrer":
		return s.evaluateReferrerRule(rule, context)
	default:
		// Unknown rule type, consider it invalid
		return false
	}
}

// evaluateLocationRule evaluates location-based targeting
func (s *advertisementService) evaluateLocationRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	if context.IPAddress == "" {
		return false
	}

	// Simple string matching for now
	// In production, this would use more sophisticated geolocation matching
	return context.IPAddress == rule.RuleValue
}

// evaluateTimeRule evaluates time-based targeting
func (s *advertisementService) evaluateTimeRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	// Parse target value as hour range (e.g., "9-17" for 9 AM to 5 PM)
	// This is a simplified implementation
	return true // For now, always return true
}

// evaluateDeviceRule evaluates device-based targeting
func (s *advertisementService) evaluateDeviceRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	if context.DeviceType == "" {
		return false
	}

	return context.DeviceType == rule.RuleValue
}

// evaluateUserAgentRule evaluates user agent-based targeting
func (s *advertisementService) evaluateUserAgentRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	if context.UserAgent == "" {
		return false
	}

	// Simple substring matching
	return len(context.UserAgent) > 0 && rule.RuleValue != ""
}

// evaluateReferrerRule evaluates referrer-based targeting
func (s *advertisementService) evaluateReferrerRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	if context.Referrer == "" {
		return false
	}

	// Simple substring matching
	return len(context.Referrer) > 0 && rule.RuleValue != ""
}

// Legacy Filter Structure (Deprecated - Use DTOs instead)

// DTO Conversion Helper Functions

// convertModelToDTO converts model Advertisement to DTO response
func (s *advertisementService) convertModelToDTO(model *models.Advertisement) *dto.AdvertisementResponse {
	var pageTargeting []string
	if model.PageTargeting != nil {
		json.Unmarshal(model.PageTargeting, &pageTargeting)
	}

	return &dto.AdvertisementResponse{
		ID:              model.ID,
		TenantID:        model.TenantID,
		CampaignID:      model.CampaignID,
		Title:           model.Title,
		Description:     model.Description,
		ImageURL:        model.ImageURL,
		LinkURL:         model.LinkURL,
		AdType:          model.AdType,
		DeviceTargeting: model.DeviceTargeting,
		PageTargeting:   pageTargeting,
		Position:        model.Position,
		Priority:        model.Priority,
		Status:          model.Status,
		CreatedAt:       model.CreatedAt,
		UpdatedAt:       model.UpdatedAt,
		IsActive:        model.IsActive(),
		MediaFileIDs:    model.MediaFileIDs,
	}
}

// convertModelsToDTOs converts multiple models to DTOs
func (s *advertisementService) convertModelsToDTOs(models []*models.Advertisement) []dto.AdvertisementResponse {
	dtos := make([]dto.AdvertisementResponse, len(models))
	for i, model := range models {
		dtos[i] = *s.convertModelToDTO(model)
	}
	return dtos
}

// convertDTOFilterToLegacyFilter converts DTO filter to legacy filter for compatibility
// Deprecated: Remove when repository layer is updated to use DTOs directly
func (s *advertisementService) convertDTOFilterToLegacyFilter(filter *dto.AdvertisementListFilter) *AdvertisementFilters {
	legacy := &AdvertisementFilters{
		CampaignID: filter.CampaignID,
	}

	// Handle priority filter
	if filter.Priority != nil {
		legacy.MinPriority = filter.Priority
		legacy.MaxPriority = filter.Priority
	}

	if filter.Status != nil {
		status := string(*filter.Status)
		legacy.Status = &status
	}
	if filter.AdType != nil {
		adType := string(*filter.AdType)
		legacy.AdType = &adType
	}
	if filter.Position != nil {
		position := string(*filter.Position)
		legacy.Position = &position
	}
	if filter.DeviceTargeting != nil {
		device := string(*filter.DeviceTargeting)
		legacy.DeviceTargeting = &device
	}

	// Handle pagination
	if filter.Pagination != nil {
		legacy.Limit = filter.Pagination.Limit
		legacy.Offset = 0 // Cursor pagination doesn't use offset
	} else {
		legacy.Limit = 20
		legacy.Offset = 0
	}

	return legacy
}

// convertModelToAdServingResponse converts model to ad serving response
func (s *advertisementService) convertModelToAdServingResponse(model *models.Advertisement) *dto.AdServingResponse {
	return &dto.AdServingResponse{
		ID:          model.ID,
		Title:       model.Title,
		Description: model.Description,
		ImageURL:    model.ImageURL,
		LinkURL:     model.LinkURL,
		AdType:      model.AdType,
		Position:    model.Position,
		TrackingID:  fmt.Sprintf("ad_%d_impression_%d", model.ID, model.TenantID),
	}
}

// convertModelsToAdServingResponses converts multiple models to ad serving responses
func (s *advertisementService) convertModelsToAdServingResponses(models []*models.Advertisement) []*dto.AdServingResponse {
	responses := make([]*dto.AdServingResponse, len(models))
	for i, model := range models {
		responses[i] = s.convertModelToAdServingResponse(model)
	}
	return responses
}

// convertPlacementDTOToModel converts placement DTO to model for internal use
func (s *advertisementService) convertPlacementDTOToModel(placementDTO *dto.PlacementResponse) *models.Placement {
	placement := &models.Placement{
		ID:       placementDTO.ID,
		TenantID: placementDTO.TenantID,
		PageType: placementDTO.PageType,
		Position: placementDTO.Position,
		MaxAds:   placementDTO.MaxAds,
		Status:   placementDTO.Status,
	}

	if placementDTO.TargetingRules != nil {
		// Convert targeting rules if needed
		targetingJSON, _ := json.Marshal(*placementDTO.TargetingRules)
		placement.TargetingRules = targetingJSON
	}

	return placement
}
