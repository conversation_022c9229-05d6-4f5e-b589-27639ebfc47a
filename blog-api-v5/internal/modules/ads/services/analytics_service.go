package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// analyticsService implements the AnalyticsService interface
type analyticsService struct {
	repoManager repositories.RepositoryManager
}

// NewAnalyticsService creates a new analytics service instance
func NewAnalyticsService(repoManager repositories.RepositoryManager) AnalyticsService {
	return &analyticsService{
		repoManager: repoManager,
	}
}

// RecordImpression records an advertisement impression
func (s *analyticsService) RecordImpression(ctx context.Context, req *dto.ImpressionCreateRequest) error {
	// Validate request
	if err := s.validateImpressionCreateRequest(req); err != nil {
		return fmt.Errorf("invalid impression request: %w", err)
	}

	// Verify advertisement exists
	_, err := s.repoManager.Advertisement().GetByID(ctx, req.TenantID, req.WebsiteID, req.AdvertisementID)
	if err != nil {
		return fmt.Errorf("advertisement not found: %w", err)
	}

	impression := &models.Impression{
		TenantID:        req.TenantID,
		AdvertisementID: req.AdvertisementID,
		PlacementID:     req.PlacementID,
		UserAgent:       req.UserAgent,
		Referrer:        req.Referrer,
		DeviceType:      req.DeviceType,
		PageURL:         req.PageURL,
		IPAddress:       req.IPAddress,
		ViewedAt:        time.Now(),
	}

	// Create impression in database
	err = s.repoManager.Impression().Create(ctx, impression)
	if err != nil {
		return fmt.Errorf("failed to record impression: %w", err)
	}

	return nil
}

// RecordClick records an advertisement click
func (s *analyticsService) RecordClick(ctx context.Context, req *dto.ClickCreateRequest) error {
	// Validate request
	if err := s.validateClickCreateRequest(req); err != nil {
		return fmt.Errorf("invalid click request: %w", err)
	}

	// Verify advertisement exists
	_, err := s.repoManager.Advertisement().GetByID(ctx, req.TenantID, req.WebsiteID, req.AdvertisementID)
	if err != nil {
		return fmt.Errorf("advertisement not found: %w", err)
	}

	// Check if impression exists (optional validation)
	if req.ImpressionID != nil {
		_, err := s.repoManager.Impression().GetByID(ctx, req.TenantID, req.WebsiteID, *req.ImpressionID)
		if err != nil {
			return fmt.Errorf("impression not found: %w", err)
		}
	}

	click := &models.Click{
		TenantID:        req.TenantID,
		AdvertisementID: req.AdvertisementID,
		ImpressionID:    req.ImpressionID,
		UserAgent:       req.UserAgent,
		Referrer:        req.Referrer,
		DeviceType:      req.DeviceType,
		PageURL:         req.PageURL,
		IPAddress:       req.IPAddress,
		ClickedAt:       time.Now(),
	}

	// Create click in database
	err = s.repoManager.Click().Create(ctx, click)
	if err != nil {
		return fmt.Errorf("failed to record click: %w", err)
	}

	return nil
}

// GetCampaignAnalytics retrieves analytics for a specific campaign
func (s *analyticsService) GetCampaignAnalytics(ctx context.Context, req *dto.CampaignAnalyticsRequest) (*dto.CampaignAnalyticsResponse, error) {
	// Validate required campaign ID
	if req.CampaignID == nil {
		return nil, fmt.Errorf("campaign ID is required")
	}

	// Verify campaign exists
	campaign, err := s.repoManager.Campaign().GetByID(ctx, req.TenantID, 0, *req.CampaignID)
	if err != nil {
		return nil, fmt.Errorf("campaign not found: %w", err)
	}

	// Get campaign advertisements
	advertisements, err := s.repoManager.Advertisement().GetByCampaignID(ctx, req.TenantID, 0, *req.CampaignID, 100, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get campaign advertisements: %w", err)
	}

	// Set default date range if not provided
	startDate := time.Now().AddDate(0, 0, -30)
	endDate := time.Now()
	if req.StartDate != nil {
		startDate = *req.StartDate
	}
	if req.EndDate != nil {
		endDate = *req.EndDate
	}

	// Calculate overall metrics
	totalImpressions := 0
	totalClicks := 0
	totalRevenue := 0.0

	var topPerformingAds []models.AdvertisementAnalytics
	for _, ad := range advertisements {
		// Get analytics for this advertisement
		adReq := &dto.AdvertisementAnalyticsRequest{
			TenantID:        req.TenantID,
			AdvertisementID: ad.ID,
			StartDate:       &startDate,
			EndDate:         &endDate,
		}
		analytics, err := s.GetAdvertisementAnalytics(ctx, adReq)
		if err != nil {
			continue // Skip on error but don't fail entire operation
		}

		topPerformingAds = append(topPerformingAds, models.AdvertisementAnalytics{
			AdvertisementID:   ad.ID,
			AdvertisementName: ad.Title,
			Impressions:       int(analytics.Summary.TotalImpressions),
			Clicks:            int(analytics.Summary.TotalClicks),
			CTR:               analytics.Summary.CTRRate,
			Revenue:           analytics.Summary.TotalRevenue,
		})

		totalImpressions += int(analytics.Summary.TotalImpressions)
		totalClicks += int(analytics.Summary.TotalClicks)
		totalRevenue += analytics.Summary.TotalRevenue
	}

	// Calculate CTR
	var ctr float64
	if totalImpressions > 0 {
		ctr = (float64(totalClicks) / float64(totalImpressions))
	}

	response := &dto.CampaignAnalyticsResponse{
		CampaignID:   *req.CampaignID,
		CampaignName: campaign.Name,
		DateRange:    models.DateRange{StartDate: startDate, EndDate: endDate},
		Summary: models.AnalyticsSummary{
			TotalImpressions: totalImpressions,
			TotalClicks:      totalClicks,
			CTRRate:          ctr,
			TotalRevenue:     totalRevenue,
		},
		TopPerformingAds: topPerformingAds,
	}

	return response, nil
}

// GetAdvertisementAnalytics retrieves analytics for a specific advertisement
func (s *analyticsService) GetAdvertisementAnalytics(ctx context.Context, req *dto.AdvertisementAnalyticsRequest) (*dto.AdvertisementAnalyticsResponse, error) {
	// Verify advertisement exists
	ad, err := s.repoManager.Advertisement().GetByID(ctx, req.TenantID, 0, req.AdvertisementID)
	if err != nil {
		return nil, fmt.Errorf("advertisement not found: %w", err)
	}

	// Set default date range if not provided
	startDate := time.Now().AddDate(0, 0, -30)
	endDate := time.Now()
	if req.StartDate != nil {
		startDate = *req.StartDate
	}
	if req.EndDate != nil {
		endDate = *req.EndDate
	}

	// Get impressions count
	filters := make(map[string]interface{})
	filters["advertisement_id"] = req.AdvertisementID
	impressions, err := s.repoManager.Impression().CountByDateRange(ctx, req.TenantID, req.WebsiteID, startDate, endDate, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count impressions: %w", err)
	}

	// Get clicks count
	clicks, err := s.repoManager.Click().CountByDateRange(ctx, req.TenantID, req.WebsiteID, startDate, endDate, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count clicks: %w", err)
	}

	// Calculate CTR
	var ctr float64
	if impressions > 0 {
		ctr = (float64(clicks) / float64(impressions))
	}

	// Calculate revenue (simplified - in production this would be more complex)
	revenue := float64(clicks) * 0.10 // Example: $0.10 per click

	// Get campaign info if available
	campaignName := ""
	if ad.CampaignID != 0 {
		campaign, err := s.repoManager.Campaign().GetByID(ctx, req.TenantID, 0, ad.CampaignID)
		if err == nil {
			campaignName = campaign.Name
		}
	}

	response := &dto.AdvertisementAnalyticsResponse{
		AdvertisementID:   req.AdvertisementID,
		AdvertisementName: ad.Title,
		CampaignID:        ad.CampaignID,
		CampaignName:      campaignName,
		DateRange:         models.DateRange{StartDate: startDate, EndDate: endDate},
		Summary: models.AnalyticsSummary{
			TotalImpressions: int(impressions),
			TotalClicks:      int(clicks),
			CTRRate:          ctr,
			TotalRevenue:     revenue,
		},
	}

	return response, nil
}

// AggregateAnalytics aggregates analytics data for a specific date
func (s *analyticsService) AggregateAnalytics(ctx context.Context, tenantID, websiteID uint, date time.Time) error {
	// Get all advertisements for the tenant
	advertisements, err := s.repoManager.Advertisement().GetByTenantAndWebsite(ctx, tenantID, 0, 1000, 0, nil)
	if err != nil {
		return fmt.Errorf("failed to get advertisements: %w", err)
	}

	// Aggregate analytics for each advertisement
	for _, ad := range advertisements {
		analytics, err := s.CalculateMetrics(ctx, tenantID, websiteID, ad.ID, date)
		if err != nil {
			continue // Skip on error but don't fail entire operation
		}

		// Store aggregated analytics
		err = s.repoManager.Analytics().Create(ctx, analytics)
		if err != nil {
			continue // Skip on error but don't fail entire operation
		}
	}

	return nil
}

// CalculateMetrics calculates metrics for a specific advertisement and date
func (s *analyticsService) CalculateMetrics(ctx context.Context, tenantID, websiteID uint, adID uint, date time.Time) (*models.Analytics, error) {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	// Get impressions for the day
	filters := make(map[string]interface{})
	filters["advertisement_id"] = adID
	impressions, err := s.repoManager.Impression().CountByDateRange(ctx, tenantID, websiteID, startOfDay, endOfDay, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count impressions: %w", err)
	}

	// Get clicks for the day
	clicks, err := s.repoManager.Click().CountByDateRange(ctx, tenantID, websiteID, startOfDay, endOfDay, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count clicks: %w", err)
	}

	// Calculate CTR
	var ctr float64
	if impressions > 0 {
		ctr = (float64(clicks) / float64(impressions))
	}

	// Calculate revenue (simplified)
	revenue := float64(clicks) * 0.10

	analytics := &models.Analytics{
		TenantID:         tenantID,
		AdvertisementID:  adID,
		AnalyticsDate:    date,
		ImpressionsCount: int(impressions),
		ClicksCount:      int(clicks),
		CTRRate:          ctr,
		Revenue:          revenue,
	}

	return analytics, nil
}

// GenerateDashboardData generates dashboard data for a tenant
func (s *analyticsService) GenerateDashboardData(ctx context.Context, req *dto.AnalyticsSummaryRequest) (*dto.AnalyticsSummaryResponse, error) {
	// Set default date range if not provided
	startDate := time.Now().AddDate(0, 0, -30)
	endDate := time.Now()
	if req.StartDate != nil {
		startDate = *req.StartDate
	}
	if req.EndDate != nil {
		endDate = *req.EndDate
	}

	dateRange := &models.DateRange{
		StartDate: startDate,
		EndDate:   endDate,
	}

	// Get summary analytics
	summary, err := s.generateAnalyticsSummary(ctx, req.TenantID, req.WebsiteID, dateRange)
	if err != nil {
		return nil, fmt.Errorf("failed to generate summary: %w", err)
	}

	// Calculate average CPC
	averageCPC := 0.0
	if summary.TotalClicks > 0 {
		averageCPC = summary.TotalRevenue / float64(summary.TotalClicks)
	}

	response := &dto.AnalyticsSummaryResponse{
		TotalImpressions: summary.TotalImpressions,
		TotalClicks:      summary.TotalClicks,
		CTRRate:          summary.CTRRate,
		TotalRevenue:     summary.TotalRevenue,
		AverageCPC:       averageCPC,
		DateRange: struct {
			StartDate time.Time `json:"start_date" example:"2023-07-01T00:00:00Z"`
			EndDate   time.Time `json:"end_date" example:"2023-07-31T23:59:59Z"`
		}{
			StartDate: startDate,
			EndDate:   endDate,
		},
	}

	return response, nil
}

// ExportAnalyticsData exports analytics data in various formats
func (s *analyticsService) ExportAnalyticsData(ctx context.Context, req *dto.AnalyticsExportRequest) ([]byte, error) {
	// This is a simplified implementation
	// In production, this would generate actual CSV/JSON/XLSX files

	switch req.Format {
	case "csv":
		return s.exportCSV(ctx, req)
	case "excel":
		return s.exportXLSX(ctx, req)
	case "pdf":
		return s.exportPDF(ctx, req)
	default:
		return nil, fmt.Errorf("unsupported export format: %s", req.Format)
	}
}

// Helper methods

func (s *analyticsService) validateImpressionCreateRequest(req *dto.ImpressionCreateRequest) error {
	if req.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}
	if req.AdvertisementID == 0 {
		return fmt.Errorf("advertisement ID is required")
	}
	if req.PlacementID == 0 {
		return fmt.Errorf("placement ID is required")
	}
	return nil
}

func (s *analyticsService) validateClickCreateRequest(req *dto.ClickCreateRequest) error {
	if req.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}
	if req.AdvertisementID == 0 {
		return fmt.Errorf("advertisement ID is required")
	}
	return nil
}

func (s *analyticsService) generateAnalyticsSummary(ctx context.Context, tenantID, websiteID uint, dateRange *models.DateRange) (*models.AnalyticsSummary, error) {
	// Get all analytics for the date range
	analytics, err := s.repoManager.Analytics().GetByDateRange(ctx, tenantID, websiteID, dateRange.StartDate, dateRange.EndDate, nil)
	if err != nil {
		return nil, err
	}

	var totalImpressions, totalClicks int
	var totalRevenue float64

	for _, analytic := range analytics {
		totalImpressions += analytic.ImpressionsCount
		totalClicks += analytic.ClicksCount
		totalRevenue += analytic.Revenue
	}

	var avgCTR float64
	if totalImpressions > 0 {
		avgCTR = (float64(totalClicks) / float64(totalImpressions))
	}

	return &models.AnalyticsSummary{
		TotalImpressions: totalImpressions,
		TotalClicks:      totalClicks,
		CTRRate:          avgCTR,
		TotalRevenue:     totalRevenue,
	}, nil
}

func (s *analyticsService) generateRecentMetrics(ctx context.Context, tenantID, websiteID uint, dateRange *models.DateRange) ([]models.DailyAnalytics, error) {
	// Get daily analytics for the last 30 days
	endDate := dateRange.EndDate
	startDate := endDate.AddDate(0, 0, -30)

	analytics, err := s.repoManager.Analytics().GetDailyAnalytics(ctx, tenantID, websiteID, startDate, endDate, nil)
	if err != nil {
		return nil, err
	}

	return analytics, nil
}

func (s *analyticsService) generateTopCampaigns(ctx context.Context, tenantID uint, dateRange *models.DateRange) ([]CampaignPerformance, error) {
	// Get campaigns with performance metrics
	campaigns, err := s.repoManager.Campaign().GetByTenantAndWebsite(ctx, tenantID, 0, 10, 0, "")
	if err != nil {
		return nil, err
	}

	var topCampaigns []CampaignPerformance
	for _, campaign := range campaigns {
		// Get campaign analytics
		req := &dto.CampaignAnalyticsRequest{
			TenantID:   tenantID,
			CampaignID: &campaign.ID,
			StartDate:  &dateRange.StartDate,
			EndDate:    &dateRange.EndDate,
		}
		analytics, err := s.GetCampaignAnalytics(ctx, req)
		if err != nil {
			continue
		}

		topCampaigns = append(topCampaigns, CampaignPerformance{
			Campaign:    campaign,
			Impressions: analytics.Summary.TotalImpressions,
			Clicks:      analytics.Summary.TotalClicks,
			CTRRate:     analytics.Summary.CTRRate,
			Revenue:     analytics.Summary.TotalRevenue,
		})
	}

	return topCampaigns, nil
}

func (s *analyticsService) generateTopAdvertisements(ctx context.Context, tenantID uint, dateRange *models.DateRange) ([]AdvertisementPerformance, error) {
	// Get advertisements with performance metrics
	advertisements, err := s.repoManager.Advertisement().GetByTenantAndWebsite(ctx, tenantID, 0, 10, 0, nil)
	if err != nil {
		return nil, err
	}

	var topAds []AdvertisementPerformance
	for _, ad := range advertisements {
		// Get advertisement analytics
		req := &dto.AdvertisementAnalyticsRequest{
			TenantID:        tenantID,
			AdvertisementID: ad.ID,
			StartDate:       &dateRange.StartDate,
			EndDate:         &dateRange.EndDate,
		}
		analytics, err := s.GetAdvertisementAnalytics(ctx, req)
		if err != nil {
			continue
		}

		topAds = append(topAds, AdvertisementPerformance{
			Advertisement: ad,
			Impressions:   int(analytics.Summary.TotalImpressions),
			Clicks:        int(analytics.Summary.TotalClicks),
			CTRRate:       analytics.Summary.CTRRate,
			Revenue:       analytics.Summary.TotalRevenue,
		})
	}

	return topAds, nil
}

func (s *analyticsService) exportCSV(ctx context.Context, req *dto.AnalyticsExportRequest) ([]byte, error) {
	// Simplified implementation
	return []byte("CSV export not implemented yet"), nil
}

func (s *analyticsService) exportXLSX(ctx context.Context, req *dto.AnalyticsExportRequest) ([]byte, error) {
	// Simplified implementation
	return []byte("XLSX export not implemented yet"), nil
}

func (s *analyticsService) exportPDF(ctx context.Context, req *dto.AnalyticsExportRequest) ([]byte, error) {
	// Simplified implementation
	return []byte("PDF export not implemented yet"), nil
}
