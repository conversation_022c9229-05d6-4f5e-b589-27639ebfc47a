package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// campaignService implements the CampaignService interface
type campaignService struct {
	repoManager repositories.RepositoryManager
}

// NewCampaignService creates a new campaign service instance
func NewCampaignService(repoManager repositories.RepositoryManager) CampaignService {
	return &campaignService{
		repoManager: repoManager,
	}
}

// CreateCampaign creates a new campaign
func (s *campaignService) CreateCampaign(ctx context.Context, tenantID, websiteID uint, req *dto.CampaignCreateRequest) (*dto.CampaignResponse, error) {
	// Validate request
	if err := s.validateCampaignCreateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid campaign request: %w", err)
	}

	campaign := &models.Campaign{
		TenantID:    tenantID,
		WebsiteID:   websiteID,
		Name:        req.Name,
		Description: req.Description,
		Budget:      req.Budget,
		StartDate:   req.StartDate,
		EndDate:     req.EndDate,
		Status:      "draft", // Default to draft status
	}

	// Validate business rules
	if err := s.ValidateCampaignBudget(ctx, campaign); err != nil {
		return nil, fmt.Errorf("campaign budget validation failed: %w", err)
	}

	// Create campaign in database
	err := s.repoManager.Campaign().Create(ctx, campaign)
	if err != nil {
		return nil, fmt.Errorf("failed to create campaign: %w", err)
	}

	return convertCampaignModelToResponse(campaign), nil
}

// GetCampaignByID retrieves a campaign by ID
func (s *campaignService) GetCampaignByID(ctx context.Context, tenantID, websiteID uint, campaignID uint) (*dto.CampaignResponse, error) {
	campaign, err := s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, campaignID)
	if err != nil {
		return nil, fmt.Errorf("failed to get campaign: %w", err)
	}

	return convertCampaignModelToResponse(campaign), nil
}

// UpdateCampaign updates an existing campaign
func (s *campaignService) UpdateCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint, req *dto.CampaignUpdateRequest) (*dto.CampaignResponse, error) {
	// Get existing campaign model
	existingCampaign, err := s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, campaignID)
	if err != nil {
		return nil, fmt.Errorf("failed to get campaign: %w", err)
	}

	// Apply updates
	if req.Name != nil {
		existingCampaign.Name = *req.Name
	}
	if req.Description != nil {
		existingCampaign.Description = *req.Description
	}
	if req.Budget != nil {
		existingCampaign.Budget = *req.Budget
		// Re-validate budget if changed
		if err := s.ValidateCampaignBudget(ctx, existingCampaign); err != nil {
			return nil, fmt.Errorf("campaign budget validation failed: %w", err)
		}
	}
	if req.StartDate != nil {
		existingCampaign.StartDate = *req.StartDate
	}
	if req.EndDate != nil {
		existingCampaign.EndDate = *req.EndDate
	}
	if req.Status != nil {
		existingCampaign.Status = *req.Status
	}

	// Validate date range
	if existingCampaign.EndDate.Before(existingCampaign.StartDate) {
		return nil, fmt.Errorf("end date cannot be before start date")
	}

	// Update in database
	err = s.repoManager.Campaign().Update(ctx, existingCampaign)
	if err != nil {
		return nil, fmt.Errorf("failed to update campaign: %w", err)
	}

	return convertCampaignModelToResponse(existingCampaign), nil
}

// DeleteCampaign deletes a campaign (soft delete)
func (s *campaignService) DeleteCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint) error {
	// Check if campaign exists
	_, err := s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, campaignID)
	if err != nil {
		return fmt.Errorf("failed to get campaign: %w", err)
	}

	// Check if campaign has active advertisements
	advertisements, err := s.repoManager.Advertisement().GetByCampaignID(ctx, tenantID, websiteID, campaignID, 100, 0)
	if err != nil {
		return fmt.Errorf("failed to check campaign advertisements: %w", err)
	}

	for _, ad := range advertisements {
		if ad.IsActive() {
			return fmt.Errorf("cannot delete campaign with active advertisements")
		}
	}

	// Soft delete campaign
	err = s.repoManager.Campaign().Delete(ctx, tenantID, websiteID, campaignID)
	if err != nil {
		return fmt.Errorf("failed to delete campaign: %w", err)
	}

	return nil
}

// ListCampaigns lists campaigns with filters
func (s *campaignService) ListCampaigns(ctx context.Context, tenantID, websiteID uint, filters *dto.CampaignListFilter) (*dto.CampaignListResponse, error) {
	// Convert DTO filter to service filter
	serviceFilters := convertCampaignListFilterToServiceFilter(filters)

	// Set default pagination
	if serviceFilters.Limit <= 0 {
		serviceFilters.Limit = 20
	}
	if serviceFilters.Limit > 100 {
		serviceFilters.Limit = 100
	}

	campaigns, err := s.repoManager.Campaign().GetByTenantAndWebsite(ctx, tenantID, websiteID, serviceFilters.Limit, serviceFilters.Offset, "")
	if err != nil {
		return nil, fmt.Errorf("failed to list campaigns: %w", err)
	}

	// Apply additional filters
	filteredCampaigns := s.applyCampaignFilters(campaigns, serviceFilters)

	// Convert to DTO responses
	campaignResponses := convertCampaignModelsToResponses(filteredCampaigns)

	// Build pagination response
	var paginationResponse *pagination.CursorResponse
	if filters != nil && filters.Pagination != nil {
		hasMore := int64(len(filteredCampaigns)) == int64(serviceFilters.Limit)
		paginationResponse = &pagination.CursorResponse{
			HasMore: hasMore,
			HasNext: hasMore,
			Count:   len(filteredCampaigns),
			Limit:   serviceFilters.Limit,
		}
	}

	return &dto.CampaignListResponse{
		Campaigns:  campaignResponses,
		Pagination: paginationResponse,
	}, nil
}

// getCampaignModelByID is a helper method to get campaign model for internal use
func (s *campaignService) getCampaignModelByID(ctx context.Context, tenantID, websiteID uint, campaignID uint) (*models.Campaign, error) {
	return s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, campaignID)
}

// ActivateCampaign activates a campaign
func (s *campaignService) ActivateCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint) error {
	campaign, err := s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, campaignID)
	if err != nil {
		return fmt.Errorf("failed to get campaign: %w", err)
	}

	// Check campaign status and validation
	if campaign.Status == "active" {
		return fmt.Errorf("campaign is already active")
	}

	// Validate campaign can be activated
	if err := s.ValidateCampaignBudget(ctx, campaign); err != nil {
		return fmt.Errorf("cannot activate campaign: %w", err)
	}

	// Check date validity
	now := time.Now()
	if campaign.EndDate.Before(now) {
		return fmt.Errorf("cannot activate expired campaign")
	}

	// Update status
	campaign.Status = "active"
	err = s.repoManager.Campaign().Update(ctx, campaign)
	if err != nil {
		return fmt.Errorf("failed to activate campaign: %w", err)
	}

	return nil
}

// PauseCampaign pauses a campaign
func (s *campaignService) PauseCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint) error {
	campaign, err := s.repoManager.Campaign().GetByID(ctx, tenantID, websiteID, campaignID)
	if err != nil {
		return fmt.Errorf("failed to get campaign: %w", err)
	}

	if campaign.Status != "active" {
		return fmt.Errorf("can only pause active campaigns")
	}

	// Update status
	campaign.Status = "paused"
	err = s.repoManager.Campaign().Update(ctx, campaign)
	if err != nil {
		return fmt.Errorf("failed to pause campaign: %w", err)
	}

	return nil
}

// ValidateCampaignBudget validates campaign budget constraints
func (s *campaignService) ValidateCampaignBudget(ctx context.Context, campaign *models.Campaign) error {
	if campaign.Budget <= 0 {
		return fmt.Errorf("campaign budget must be greater than zero")
	}

	// Minimum budget validation (example: $10)
	if campaign.Budget < 10.0 {
		return fmt.Errorf("campaign budget must be at least $10.00")
	}

	// Maximum budget validation (example: $100,000)
	if campaign.Budget > 100000.0 {
		return fmt.Errorf("campaign budget cannot exceed $100,000.00")
	}

	return nil
}

// CheckCampaignStatus checks and updates campaign status based on dates and budget
func (s *campaignService) CheckCampaignStatus(ctx context.Context, campaign *models.Campaign) (string, error) {
	now := time.Now()

	// Check if campaign is expired
	if campaign.EndDate.Before(now) {
		if campaign.Status != "expired" {
			campaign.Status = "expired"
			err := s.repoManager.Campaign().Update(ctx, campaign)
			if err != nil {
				return "", fmt.Errorf("failed to update campaign status to expired: %w", err)
			}
		}
		return "expired", nil
	}

	// Check if campaign hasn't started yet
	if campaign.StartDate.After(now) {
		if campaign.Status == "active" {
			campaign.Status = "scheduled"
			err := s.repoManager.Campaign().Update(ctx, campaign)
			if err != nil {
				return "", fmt.Errorf("failed to update campaign status to scheduled: %w", err)
			}
		}
		return "scheduled", nil
	}

	// Campaign is within date range
	return string(campaign.Status), nil
}

// validateCampaignCreateRequest validates the campaign creation request
func (s *campaignService) validateCampaignCreateRequest(req *dto.CampaignCreateRequest) error {
	if req.Name == "" {
		return fmt.Errorf("campaign name is required")
	}

	if len(req.Name) > 255 {
		return fmt.Errorf("campaign name cannot exceed 255 characters")
	}

	if req.Budget <= 0 {
		return fmt.Errorf("campaign budget must be greater than zero")
	}

	if req.EndDate.Before(req.StartDate) {
		return fmt.Errorf("end date cannot be before start date")
	}

	if req.StartDate.Before(time.Now().Truncate(24 * time.Hour)) {
		return fmt.Errorf("start date cannot be in the past")
	}

	return nil
}

// applyCampaignFilters applies additional filtering to campaigns
func (s *campaignService) applyCampaignFilters(campaigns []*models.Campaign, filters *CampaignFilters) []*models.Campaign {
	var filtered []*models.Campaign

	for _, campaign := range campaigns {
		// Status filter
		if filters.Status != nil && string(campaign.Status) != *filters.Status {
			continue
		}

		// Date range filters
		if filters.StartDate != nil && campaign.StartDate.Before(*filters.StartDate) {
			continue
		}
		if filters.EndDate != nil && campaign.EndDate.After(*filters.EndDate) {
			continue
		}

		// Budget filters
		if filters.MinBudget != nil && campaign.Budget < *filters.MinBudget {
			continue
		}
		if filters.MaxBudget != nil && campaign.Budget > *filters.MaxBudget {
			continue
		}

		filtered = append(filtered, campaign)
	}

	return filtered
}
