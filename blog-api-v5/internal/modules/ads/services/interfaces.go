package services

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
)

// CampaignService defines the interface for campaign business logic
type CampaignService interface {
	// CRUD Operations
	CreateCampaign(ctx context.Context, tenantID, websiteID uint, req *dto.CampaignCreateRequest) (*dto.CampaignResponse, error)
	GetCampaignByID(ctx context.Context, tenantID, websiteID uint, campaignID uint) (*dto.CampaignResponse, error)
	UpdateCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint, req *dto.CampaignUpdateRequest) (*dto.CampaignResponse, error)
	DeleteCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint) error

	// List and Filter
	ListCampaigns(ctx context.Context, tenantID, websiteID uint, filters *dto.CampaignListFilter) (*dto.CampaignListResponse, error)

	// Status Operations
	ActivateCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint) error
	PauseCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint) error

	// Business Logic
	ValidateCampaignBudget(ctx context.Context, campaign *models.Campaign) error
	CheckCampaignStatus(ctx context.Context, campaign *models.Campaign) (string, error)
}

// AdvertisementService defines the interface for advertisement business logic
type AdvertisementService interface {
	// CRUD Operations
	CreateAdvertisement(ctx context.Context, tenantID, websiteID uint, req *dto.AdvertisementCreateRequest) (*dto.AdvertisementResponse, error)
	GetAdvertisementByID(ctx context.Context, tenantID, websiteID uint, adID uint) (*dto.AdvertisementResponse, error)
	UpdateAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint, req *dto.AdvertisementUpdateRequest) (*dto.AdvertisementResponse, error)
	DeleteAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint) error

	// List and Filter
	ListAdvertisements(ctx context.Context, tenantID, websiteID uint, filters *dto.AdvertisementListFilter) (*dto.AdvertisementListResponse, error)
	ListAdvertisementsByCampaign(ctx context.Context, tenantID, websiteID uint, campaignID uint) (*dto.AdvertisementListResponse, error)

	// Targeting and Display
	GetAdvertisementsForPlacement(ctx context.Context, tenantID, websiteID uint, placement *dto.PlacementResponse, context *models.TargetingContext) ([]*dto.AdServingResponse, error)
	ValidateTargeting(ctx context.Context, ad *models.Advertisement, websiteID uint, context *models.TargetingContext) (bool, error)

	// Status Operations
	ActivateAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint) error
	PauseAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint) error

	// Ad Serving
	ServeAds(ctx context.Context, req *dto.AdServingRequest, context *models.TargetingContext) ([]*dto.AdServingResponse, error)
}

// AnalyticsService defines the interface for analytics business logic
type AnalyticsService interface {
	// Data Recording
	RecordImpression(ctx context.Context, req *dto.ImpressionCreateRequest) error
	RecordClick(ctx context.Context, req *dto.ClickCreateRequest) error

	// Analytics Retrieval
	GetCampaignAnalytics(ctx context.Context, req *dto.CampaignAnalyticsRequest) (*dto.CampaignAnalyticsResponse, error)
	GetAdvertisementAnalytics(ctx context.Context, req *dto.AdvertisementAnalyticsRequest) (*dto.AdvertisementAnalyticsResponse, error)

	// Aggregation
	AggregateAnalytics(ctx context.Context, tenantID, websiteID uint, date time.Time) error
	CalculateMetrics(ctx context.Context, tenantID, websiteID uint, adID uint, date time.Time) (*models.Analytics, error)

	// Reports
	GenerateDashboardData(ctx context.Context, req *dto.AnalyticsSummaryRequest) (*dto.AnalyticsSummaryResponse, error)
	ExportAnalyticsData(ctx context.Context, req *dto.AnalyticsExportRequest) ([]byte, error)
}

// PlacementService defines the interface for placement business logic
type PlacementService interface {
	// CRUD Operations
	CreatePlacement(ctx context.Context, req *dto.PlacementCreateRequest) (*dto.PlacementResponse, error)
	GetPlacementByID(ctx context.Context, tenantID, websiteID uint, placementID uint) (*dto.PlacementResponse, error)
	UpdatePlacement(ctx context.Context, tenantID, websiteID uint, placementID uint, req *dto.PlacementUpdateRequest) (*dto.PlacementResponse, error)
	DeletePlacement(ctx context.Context, tenantID, websiteID uint, placementID uint) error

	// List and Filter
	ListPlacements(ctx context.Context, filters *dto.PlacementListFilter) (*dto.PlacementListResponse, error)
	GetPlacementsByPage(ctx context.Context, tenantID, websiteID uint, pageType string) ([]*dto.PlacementResponse, error)

	// Business Logic
	ValidatePlacement(ctx context.Context, req *dto.PlacementCreateRequest) (*dto.PlacementValidationResponse, error)
	GetOptimalPlacements(ctx context.Context, tenantID, websiteID uint, context *models.TargetingContext) ([]*dto.PlacementResponse, error)
}

// ScheduleService defines the interface for schedule business logic
type ScheduleService interface {
	// CRUD Operations
	CreateSchedule(ctx context.Context, req *dto.ScheduleCreateRequest) (*dto.ScheduleResponse, error)
	GetScheduleByID(ctx context.Context, tenantID, websiteID uint, scheduleID uint) (*dto.ScheduleResponse, error)
	UpdateSchedule(ctx context.Context, tenantID, websiteID uint, scheduleID uint, req *dto.ScheduleUpdateRequest) (*dto.ScheduleResponse, error)
	DeleteSchedule(ctx context.Context, tenantID, websiteID uint, scheduleID uint) error

	// List and Filter
	ListSchedules(ctx context.Context, filters *dto.ScheduleListFilter) (*dto.ScheduleListResponse, error)
	GetActiveSchedules(ctx context.Context, req *dto.ScheduleActiveRequest) (*dto.ScheduleActiveResponse, error)

	// Business Logic
	ValidateSchedule(ctx context.Context, req *dto.ScheduleValidationRequest) (*dto.ScheduleValidationResponse, error)
	CheckScheduleStatus(ctx context.Context, req *dto.ScheduleStatusRequest) (*dto.ScheduleStatusResponse, error)
	GetNextScheduledTime(ctx context.Context, schedule *models.Schedule) (*time.Time, error)
}

// TargetingService defines the interface for targeting rule business logic
type TargetingService interface {
	// CRUD Operations
	CreateTargetingRule(ctx context.Context, req *dto.TargetingRuleCreateRequest) (*dto.TargetingRuleResponse, error)
	GetTargetingRuleByID(ctx context.Context, tenantID, websiteID uint, ruleID uint) (*dto.TargetingRuleResponse, error)
	UpdateTargetingRule(ctx context.Context, tenantID, websiteID uint, ruleID uint, req *dto.TargetingRuleUpdateRequest) (*dto.TargetingRuleResponse, error)
	DeleteTargetingRule(ctx context.Context, tenantID, websiteID uint, ruleID uint) error

	// List and Filter
	ListTargetingRules(ctx context.Context, filters *dto.TargetingRuleListFilter) (*dto.TargetingRuleListResponse, error)
	GetRulesByAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint) ([]*dto.TargetingRuleResponse, error)

	// Business Logic
	EvaluateRules(ctx context.Context, req *dto.TargetingEvaluationRequest) (*dto.TargetingEvaluationResponse, error)
	ValidateRule(ctx context.Context, req *dto.TargetingRuleCreateRequest) (*dto.TargetingValidationResponse, error)
}

// ServiceManager defines the interface for managing all ad services
type ServiceManager interface {
	CampaignService() CampaignService
	AdvertisementService() AdvertisementService
	AnalyticsService() AnalyticsService
	PlacementService() PlacementService
	ScheduleService() ScheduleService
	TargetingService() TargetingService
}

// Filter structures for listing operations
type CampaignFilters struct {
	Status    *string    `json:"status,omitempty"`
	StartDate *time.Time `json:"start_date,omitempty"`
	EndDate   *time.Time `json:"end_date,omitempty"`
	MinBudget *float64   `json:"min_budget,omitempty"`
	MaxBudget *float64   `json:"max_budget,omitempty"`
	Limit     int        `json:"limit,omitempty"`
	Offset    int        `json:"offset,omitempty"`
}

// AdvertisementFilters is deprecated, use dto.AdvertisementListFilter instead
type AdvertisementFilters struct {
	CampaignID      *uint   `json:"campaign_id,omitempty"`
	Status          *string `json:"status,omitempty"`
	AdType          *string `json:"ad_type,omitempty"`
	Position        *string `json:"position,omitempty"`
	DeviceTargeting *string `json:"device_targeting,omitempty"`
	MinPriority     *int    `json:"min_priority,omitempty"`
	MaxPriority     *int    `json:"max_priority,omitempty"`
	Limit           int     `json:"limit,omitempty"`
	Offset          int     `json:"offset,omitempty"`
}

type PlacementFilters struct {
	PageType *string `json:"page_type,omitempty"`
	Position *string `json:"position,omitempty"`
	Status   *string `json:"status,omitempty"`
	MinAds   *int    `json:"min_ads,omitempty"`
	MaxAds   *int    `json:"max_ads,omitempty"`
	Limit    int     `json:"limit,omitempty"`
	Offset   int     `json:"offset,omitempty"`
}

type ScheduleFilters struct {
	AdvertisementID *uint      `json:"advertisement_id,omitempty"`
	Status          *string    `json:"status,omitempty"`
	StartDate       *time.Time `json:"start_date,omitempty"`
	EndDate         *time.Time `json:"end_date,omitempty"`
	Timezone        *string    `json:"timezone,omitempty"`
	Limit           int        `json:"limit,omitempty"`
	Offset          int        `json:"offset,omitempty"`
}

type TargetingRuleFilters struct {
	AdvertisementID *uint   `json:"advertisement_id,omitempty"`
	RuleType        *string `json:"rule_type,omitempty"`
	Status          *string `json:"status,omitempty"`
	MinPriority     *int    `json:"min_priority,omitempty"`
	MaxPriority     *int    `json:"max_priority,omitempty"`
	Limit           int     `json:"limit,omitempty"`
	Offset          int     `json:"offset,omitempty"`
}

// Dashboard and reporting structures
type DashboardData struct {
	Summary       *models.AnalyticsSummary    `json:"summary"`
	RecentMetrics []models.DailyAnalytics     `json:"recent_metrics"`
	TopCampaigns  []CampaignPerformance       `json:"top_campaigns"`
	TopAds        []AdvertisementPerformance  `json:"top_ads"`
	DeviceStats   *models.DeviceBreakdownData `json:"device_stats"`
	PageStats     *models.PageBreakdownData   `json:"page_stats"`
}

type CampaignPerformance struct {
	Campaign    *models.Campaign `json:"campaign"`
	Impressions int              `json:"impressions"`
	Clicks      int              `json:"clicks"`
	CTRRate     float64          `json:"ctr_rate"`
	Revenue     float64          `json:"revenue"`
}

type AdvertisementPerformance struct {
	Advertisement *models.Advertisement `json:"advertisement"`
	Impressions   int                   `json:"impressions"`
	Clicks        int                   `json:"clicks"`
	CTRRate       float64               `json:"ctr_rate"`
	Revenue       float64               `json:"revenue"`
}

type AnalyticsExportRequest struct {
	CampaignIDs      []uint            `json:"campaign_ids,omitempty"`
	AdvertisementIDs []uint            `json:"advertisement_ids,omitempty"`
	DateRange        *models.DateRange `json:"date_range"`
	Format           string            `json:"format"` // csv, json, xlsx
	IncludeBreakdown bool              `json:"include_breakdown"`
}
