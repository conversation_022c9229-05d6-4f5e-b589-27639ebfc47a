package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// placementService implements the PlacementService interface
type placementService struct {
	repoManager repositories.RepositoryManager
}

// NewPlacementService creates a new placement service instance
func NewPlacementService(repoManager repositories.RepositoryManager) PlacementService {
	return &placementService{
		repoManager: repoManager,
	}
}

// CreatePlacement creates a new placement
func (s *placementService) CreatePlacement(ctx context.Context, req *dto.PlacementCreateRequest) (*dto.PlacementResponse, error) {
	// Validate request
	if err := s.validatePlacementCreateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid placement request: %w", err)
	}

	// Check for duplicate placement position within tenant and website
	existingPlacement, err := s.repoManager.Placement().GetByPageAndPosition(ctx, req.TenantID, req.WebsiteID, req.PageType, req.Position)
	if err == nil && existingPlacement != nil {
		return nil, fmt.Errorf("placement with page type '%s' and position '%s' already exists", req.PageType, req.Position)
	}

	placement := &models.Placement{
		TenantID:  req.TenantID,
		WebsiteID: req.WebsiteID,
		PageType:  req.PageType,
		Position:  req.Position,
		MaxAds:    req.MaxAds,
		Status:    "active", // Default to active status
	}

	// Validate placement rules
	if err := s.ValidatePlacementRules(ctx, placement); err != nil {
		return nil, fmt.Errorf("placement validation failed: %w", err)
	}

	// Create placement in database
	err = s.repoManager.Placement().Create(ctx, placement)
	if err != nil {
		return nil, fmt.Errorf("failed to create placement: %w", err)
	}

	// Convert to DTO response
	return s.convertPlacementToDTO(placement), nil
}

// GetPlacementByID retrieves a placement by ID
func (s *placementService) GetPlacementByID(ctx context.Context, tenantID, websiteID uint, placementID uint) (*dto.PlacementResponse, error) {
	placement, err := s.repoManager.Placement().GetByID(ctx, tenantID, websiteID, placementID)
	if err != nil {
		return nil, fmt.Errorf("failed to get placement: %w", err)
	}

	return s.convertPlacementToDTO(placement), nil
}

// UpdatePlacement updates an existing placement
func (s *placementService) UpdatePlacement(ctx context.Context, tenantID, websiteID uint, placementID uint, req *dto.PlacementUpdateRequest) (*dto.PlacementResponse, error) {
	// Get existing placement model directly
	existingPlacement, err := s.repoManager.Placement().GetByID(ctx, tenantID, websiteID, placementID)
	if err != nil {
		return nil, fmt.Errorf("failed to get placement: %w", err)
	}

	// Apply updates
	if req.MaxAds != nil {
		existingPlacement.MaxAds = *req.MaxAds
	}
	if req.Status != nil {
		existingPlacement.Status = *req.Status
	}

	// Validate placement rules
	if err := s.ValidatePlacementRules(ctx, existingPlacement); err != nil {
		return nil, fmt.Errorf("placement validation failed: %w", err)
	}

	// Update in database
	err = s.repoManager.Placement().Update(ctx, existingPlacement)
	if err != nil {
		return nil, fmt.Errorf("failed to update placement: %w", err)
	}

	return s.convertPlacementToDTO(existingPlacement), nil
}

// DeletePlacement deletes a placement (soft delete)
func (s *placementService) DeletePlacement(ctx context.Context, tenantID, websiteID uint, placementID uint) error {
	// Check if placement exists
	_, err := s.GetPlacementByID(ctx, tenantID, websiteID, placementID)
	if err != nil {
		return err
	}

	// Check if placement is being used by any active advertisements
	// TODO: This should be updated to check ads for specific website
	activeAds, err := s.repoManager.Advertisement().GetActiveAds(ctx, tenantID, websiteID)
	if err != nil {
		return fmt.Errorf("failed to check active advertisements: %w", err)
	}

	placement, _ := s.GetPlacementByID(ctx, tenantID, websiteID, placementID)
	for _, ad := range activeAds {
		if string(ad.Position) == placement.Position {
			return fmt.Errorf("cannot delete placement that is being used by active advertisements")
		}
	}

	// Soft delete placement
	err = s.repoManager.Placement().Delete(ctx, tenantID, websiteID, placementID)
	if err != nil {
		return fmt.Errorf("failed to delete placement: %w", err)
	}

	return nil
}

// ListPlacements lists placements with filters
func (s *placementService) ListPlacements(ctx context.Context, filters *dto.PlacementListFilter) (*dto.PlacementListResponse, error) {
	if filters == nil {
		return nil, fmt.Errorf("filters cannot be nil")
	}

	// Set default pagination
	limit := 20
	offset := 0
	if filters.Pagination != nil {
		if filters.Pagination.Limit > 0 {
			limit = int(filters.Pagination.Limit)
		}
		if limit > 100 {
			limit = 100
		}
	}

	placements, err := s.repoManager.Placement().GetByTenantAndWebsite(ctx, filters.TenantID, filters.WebsiteID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list placements: %w", err)
	}

	// Convert to legacy filters for applying additional filters
	legacyFilters := &PlacementFilters{
		PageType: filters.PageType,
		Position: filters.Position,
		Status:   filters.Status,
		Limit:    limit,
		Offset:   offset,
	}

	// Apply additional filters
	filteredPlacements := s.applyPlacementFilters(placements, legacyFilters)

	// Convert to DTO responses
	var placementResponses []dto.PlacementResponse
	for _, placement := range filteredPlacements {
		placementResponses = append(placementResponses, *s.convertPlacementToDTO(placement))
	}

	return &dto.PlacementListResponse{
		Placements: placementResponses,
		Pagination: nil, // TODO: Implement proper pagination response
	}, nil
}

// GetPlacementsByPage retrieves placements for a specific page type
func (s *placementService) GetPlacementsByPage(ctx context.Context, tenantID, websiteID uint, pageType string) ([]*dto.PlacementResponse, error) {
	placements, err := s.repoManager.Placement().GetPlacementsByPage(ctx, tenantID, websiteID, pageType)
	if err != nil {
		return nil, fmt.Errorf("failed to get placements for page type '%s': %w", pageType, err)
	}

	// Filter only active placements and convert to DTO
	var activePlacements []*dto.PlacementResponse
	for _, placement := range placements {
		if placement.Status == "active" {
			activePlacements = append(activePlacements, s.convertPlacementToDTO(placement))
		}
	}

	return activePlacements, nil
}

// ValidatePlacementRules validates placement business rules
func (s *placementService) ValidatePlacementRules(ctx context.Context, placement *models.Placement) error {
	// Validate max ads
	if placement.MaxAds < 1 || placement.MaxAds > 10 {
		return fmt.Errorf("max ads must be between 1 and 10")
	}

	// Validate page type
	validPageTypes := []string{"homepage", "category", "article", "tag", "custom"}
	if !s.contains(validPageTypes, placement.PageType) {
		return fmt.Errorf("invalid page type: %s", placement.PageType)
	}

	// Validate position
	validPositions := []string{"header", "sidebar", "footer", "inline", "popup"}
	if !s.contains(validPositions, placement.Position) {
		return fmt.Errorf("invalid position: %s", placement.Position)
	}

	// Validate status
	validStatuses := []string{"active", "inactive", "deleted"}
	if !s.contains(validStatuses, placement.Status) {
		return fmt.Errorf("invalid status: %s", placement.Status)
	}

	return nil
}

// ValidatePlacement validates a placement request
func (s *placementService) ValidatePlacement(ctx context.Context, req *dto.PlacementCreateRequest) (*dto.PlacementValidationResponse, error) {
	var errors []string
	var warnings []string
	var suggestions []string

	// Validate required fields
	if req.PageType == "" {
		errors = append(errors, "page type is required")
	}
	if req.Position == "" {
		errors = append(errors, "position is required")
	}

	// Validate page type
	validPageTypes := []string{"homepage", "category", "article", "tag", "custom"}
	if req.PageType != "" && !s.contains(validPageTypes, req.PageType) {
		errors = append(errors, fmt.Sprintf("invalid page type: %s", req.PageType))
	}

	// Validate position
	validPositions := []string{"header", "sidebar", "footer", "inline", "popup"}
	if req.Position != "" && !s.contains(validPositions, req.Position) {
		errors = append(errors, fmt.Sprintf("invalid position: %s", req.Position))
	}

	// Validate max ads
	if req.MaxAds < 1 || req.MaxAds > 10 {
		errors = append(errors, "max ads must be between 1 and 10")
	}

	// Check for duplicate placement position within tenant
	if req.PageType != "" && req.Position != "" {
		existingPlacement, err := s.repoManager.Placement().GetByPageAndPosition(ctx, req.TenantID, req.WebsiteID, req.PageType, req.Position)
		if err == nil && existingPlacement != nil {
			errors = append(errors, fmt.Sprintf("placement with page type '%s' and position '%s' already exists", req.PageType, req.Position))
		}
	}

	// Add performance suggestions
	if req.Position == "sidebar" {
		suggestions = append(suggestions, "Sidebar placements typically have lower engagement on mobile devices")
	}
	if req.Position == "header" {
		suggestions = append(suggestions, "Header placements typically have higher engagement rates")
	}
	if req.MaxAds > 5 {
		warnings = append(warnings, "High number of ads may negatively impact user experience")
	}

	return &dto.PlacementValidationResponse{
		IsValid:     len(errors) == 0,
		Errors:      errors,
		Warnings:    warnings,
		Suggestions: suggestions,
	}, nil
}

// GetOptimalPlacements retrieves optimal placements for a given context
func (s *placementService) GetOptimalPlacements(ctx context.Context, tenantID, websiteID uint, context *models.TargetingContext) ([]*dto.PlacementResponse, error) {
	// Get all active placements
	allPlacements, err := s.repoManager.Placement().GetActivePlacements(ctx, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active placements: %w", err)
	}

	// Filter placements based on context
	var optimalPlacements []*models.Placement
	for _, placement := range allPlacements {
		if s.isPlacementOptimalForContext(placement, context) {
			optimalPlacements = append(optimalPlacements, placement)
		}
	}

	// Sort by priority (positions with higher visibility first)
	s.sortPlacementsByPriority(optimalPlacements)

	// Convert to DTO responses
	var placementResponses []*dto.PlacementResponse
	for _, placement := range optimalPlacements {
		placementResponses = append(placementResponses, s.convertPlacementToDTO(placement))
	}

	return placementResponses, nil
}

// Helper methods

func (s *placementService) validatePlacementCreateRequest(req *dto.PlacementCreateRequest) error {
	if req.PageType == "" {
		return fmt.Errorf("page type is required")
	}

	if req.Position == "" {
		return fmt.Errorf("position is required")
	}

	if req.MaxAds < 1 || req.MaxAds > 10 {
		return fmt.Errorf("max ads must be between 1 and 10")
	}

	return nil
}

func (s *placementService) applyPlacementFilters(placements []*models.Placement, filters *PlacementFilters) []*models.Placement {
	var filtered []*models.Placement

	for _, placement := range placements {
		// Page type filter
		if filters.PageType != nil && placement.PageType != *filters.PageType {
			continue
		}

		// Position filter
		if filters.Position != nil && placement.Position != *filters.Position {
			continue
		}

		// Status filter
		if filters.Status != nil && placement.Status != *filters.Status {
			continue
		}

		// Max ads filters
		if filters.MinAds != nil && placement.MaxAds < *filters.MinAds {
			continue
		}
		if filters.MaxAds != nil && placement.MaxAds > *filters.MaxAds {
			continue
		}

		filtered = append(filtered, placement)
	}

	return filtered
}

func (s *placementService) isPlacementOptimalForContext(placement *models.Placement, context *models.TargetingContext) bool {
	// Check device type compatibility
	if context.DeviceType == "web_mobile" {
		// Mobile devices prefer certain positions
		if placement.Position == "sidebar" {
			return false
		}
	}

	// Check page type compatibility
	if context.PageURL != "" && placement.PageType != "custom" {
		return true // Allow all page types for custom placements
	}

	// Additional contextual filtering can be added here
	return true
}

func (s *placementService) sortPlacementsByPriority(placements []*models.Placement) {
	// Define position priorities (higher number = higher priority)
	positionPriorities := map[string]int{
		"header":  5,
		"inline":  4,
		"sidebar": 3,
		"footer":  2,
		"popup":   1,
	}

	// Sort placements by priority
	for i := 0; i < len(placements); i++ {
		for j := i + 1; j < len(placements); j++ {
			priority1 := positionPriorities[placements[i].Position]
			priority2 := positionPriorities[placements[j].Position]

			if priority1 < priority2 {
				placements[i], placements[j] = placements[j], placements[i]
			}
		}
	}
}

func (s *placementService) contains(slice []string, item string) bool {
	for _, s := range slice {
		if strings.EqualFold(s, item) {
			return true
		}
	}
	return false
}

// convertPlacementToDTO converts a placement model to DTO response
func (s *placementService) convertPlacementToDTO(placement *models.Placement) *dto.PlacementResponse {
	var targetingRules *models.PlacementTargetingRules
	if placement.TargetingRules != nil {
		// Parse JSON targeting rules if needed
	}

	return &dto.PlacementResponse{
		ID:             placement.ID,
		TenantID:       placement.TenantID,
		PageType:       placement.PageType,
		Position:       placement.Position,
		TargetingRules: targetingRules,
		MaxAds:         placement.MaxAds,
		Status:         placement.Status,
		CreatedAt:      placement.CreatedAt,
		UpdatedAt:      placement.UpdatedAt,
		IsActive:       placement.IsActive(),
	}
}
