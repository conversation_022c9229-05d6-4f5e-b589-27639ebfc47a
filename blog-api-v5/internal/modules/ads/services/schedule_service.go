package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
	"gorm.io/datatypes"
)

// scheduleService implements the ScheduleService interface
type scheduleService struct {
	repoManager repositories.RepositoryManager
}

// NewScheduleService creates a new schedule service instance
func NewScheduleService(repoManager repositories.RepositoryManager) ScheduleService {
	return &scheduleService{
		repoManager: repoManager,
	}
}

// CreateSchedule creates a new schedule
func (s *scheduleService) CreateSchedule(ctx context.Context, req *dto.ScheduleCreateRequest) (*dto.ScheduleResponse, error) {
	// Validate request
	if err := s.validateScheduleCreateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid schedule request: %w", err)
	}

	// Check if advertisement exists and belongs to tenant
	advertisement, err := s.repoManager.Advertisement().GetByID(ctx, req.TenantID, req.WebsiteID, req.AdvertisementID)
	if err != nil {
		return nil, fmt.Errorf("advertisement not found: %w", err)
	}

	// Check if advertisement's campaign allows scheduling
	campaign, err := s.repoManager.Campaign().GetByID(ctx, req.TenantID, req.WebsiteID, advertisement.CampaignID)
	if err != nil {
		return nil, fmt.Errorf("campaign not found: %w", err)
	}

	if campaign.Status != "active" && campaign.Status != "scheduled" {
		return nil, fmt.Errorf("cannot schedule advertisement for inactive campaign")
	}

	schedule := &models.Schedule{
		TenantID:        req.TenantID,
		WebsiteID:       req.WebsiteID,
		AdvertisementID: req.AdvertisementID,
		StartTime:       req.StartTime,
		EndTime:         req.EndTime,
		Timezone:        req.Timezone,
		Status:          "active", // Default to active status
	}

	// Handle recurring pattern conversion
	if req.RecurringPattern != nil {
		schedule.RecurringPattern = datatypes.JSON{}
		schedule.RecurringPattern.Scan(req.RecurringPattern)
	}

	// Validate schedule
	if err := s.validateScheduleModel(ctx, schedule); err != nil {
		return nil, fmt.Errorf("schedule validation failed: %w", err)
	}

	// Create schedule in database
	err = s.repoManager.Schedule().Create(ctx, schedule)
	if err != nil {
		return nil, fmt.Errorf("failed to create schedule: %w", err)
	}

	return s.convertScheduleToDTO(schedule), nil
}

// GetScheduleByID retrieves a schedule by ID
func (s *scheduleService) GetScheduleByID(ctx context.Context, tenantID, websiteID uint, scheduleID uint) (*dto.ScheduleResponse, error) {
	schedule, err := s.repoManager.Schedule().GetByID(ctx, tenantID, websiteID, scheduleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}

	return s.convertScheduleToDTO(schedule), nil
}

// UpdateSchedule updates an existing schedule
func (s *scheduleService) UpdateSchedule(ctx context.Context, tenantID, websiteID uint, scheduleID uint, req *dto.ScheduleUpdateRequest) (*dto.ScheduleResponse, error) {
	// Get existing schedule (get the model directly from repo)
	existingSchedule, err := s.repoManager.Schedule().GetByID(ctx, tenantID, websiteID, scheduleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}

	// Apply updates
	if req.StartTime != nil {
		existingSchedule.StartTime = *req.StartTime
	}
	if req.EndTime != nil {
		existingSchedule.EndTime = *req.EndTime
	}
	if req.Timezone != nil {
		existingSchedule.Timezone = *req.Timezone
	}
	if req.RecurringPattern != nil {
		existingSchedule.RecurringPattern = datatypes.JSON{}
		existingSchedule.RecurringPattern.Scan(req.RecurringPattern)
	}
	if req.Status != nil {
		existingSchedule.Status = *req.Status
	}

	// Validate schedule
	if err := s.validateScheduleModel(ctx, existingSchedule); err != nil {
		return nil, fmt.Errorf("schedule validation failed: %w", err)
	}

	// Update in database
	err = s.repoManager.Schedule().Update(ctx, existingSchedule)
	if err != nil {
		return nil, fmt.Errorf("failed to update schedule: %w", err)
	}

	return s.convertScheduleToDTO(existingSchedule), nil
}

// DeleteSchedule deletes a schedule (soft delete)
func (s *scheduleService) DeleteSchedule(ctx context.Context, tenantID, websiteID uint, scheduleID uint) error {
	// Check if schedule exists
	_, err := s.GetScheduleByID(ctx, tenantID, websiteID, scheduleID)
	if err != nil {
		return err
	}

	// Soft delete schedule
	err = s.repoManager.Schedule().Delete(ctx, tenantID, websiteID, scheduleID)
	if err != nil {
		return fmt.Errorf("failed to delete schedule: %w", err)
	}

	return nil
}

// ListSchedules lists schedules with filters
func (s *scheduleService) ListSchedules(ctx context.Context, filters *dto.ScheduleListFilter) (*dto.ScheduleListResponse, error) {
	if filters == nil {
		return nil, fmt.Errorf("filters cannot be nil")
	}

	// Set default pagination
	limit := 20
	offset := 0
	if filters.Pagination != nil {
		if filters.Pagination.Limit > 0 {
			limit = int(filters.Pagination.Limit)
		}
		if limit > 100 {
			limit = 100
		}
	}

	schedules, err := s.repoManager.Schedule().GetByTenantAndWebsite(ctx, filters.TenantID, filters.WebsiteID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list schedules: %w", err)
	}

	// Convert to legacy filters for applying additional filters
	legacyFilters := &ScheduleFilters{
		AdvertisementID: filters.AdvertisementID,
		Status:          filters.Status,
		StartDate:       filters.StartDate,
		EndDate:         filters.EndDate,
		Timezone:        filters.Timezone,
		Limit:           limit,
		Offset:          offset,
	}

	// Apply additional filters
	filteredSchedules := s.applyScheduleFilters(schedules, legacyFilters)

	// Convert to DTO responses
	var scheduleResponses []dto.ScheduleResponse
	for _, schedule := range filteredSchedules {
		scheduleResponses = append(scheduleResponses, *s.convertScheduleToDTO(schedule))
	}

	return &dto.ScheduleListResponse{
		Schedules:  scheduleResponses,
		Pagination: nil, // TODO: Implement proper pagination response
	}, nil
}

// GetActiveSchedules retrieves active schedules for a given time
func (s *scheduleService) GetActiveSchedules(ctx context.Context, req *dto.ScheduleActiveRequest) (*dto.ScheduleActiveResponse, error) {
	currentTime := time.Now()
	if req.CheckTime != nil {
		currentTime = *req.CheckTime
	}

	timezone := "UTC"
	if req.Timezone != nil {
		timezone = *req.Timezone
	}
	schedules, err := s.repoManager.Schedule().GetActiveSchedules(ctx, req.TenantID, req.WebsiteID, currentTime)
	if err != nil {
		return nil, fmt.Errorf("failed to get active schedules: %w", err)
	}

	// Filter schedules that are actually active at the current time
	var activeSchedules []*models.Schedule
	var matchedCount int
	for _, schedule := range schedules {
		// Apply filters
		if req.AdvertisementID != nil && schedule.AdvertisementID != *req.AdvertisementID {
			continue
		}
		if req.CampaignID != nil {
			// Get advertisement to check campaign ID
			ad, err := s.repoManager.Advertisement().GetByID(ctx, req.TenantID, req.WebsiteID, schedule.AdvertisementID)
			if err != nil || ad.CampaignID != *req.CampaignID {
				continue
			}
		}

		matchedCount++

		isActive, err := s.IsScheduleActive(ctx, schedule, currentTime)
		if err != nil {
			continue // Skip on error but don't fail entire operation
		}
		if isActive {
			activeSchedules = append(activeSchedules, schedule)
		}
	}

	// Convert to DTO responses
	var scheduleResponses []dto.ScheduleResponse
	for _, schedule := range activeSchedules {
		scheduleResponses = append(scheduleResponses, *s.convertScheduleToDTO(schedule))
	}

	return &dto.ScheduleActiveResponse{
		CurrentTime:  currentTime,
		Timezone:     timezone,
		Schedules:    scheduleResponses,
		TotalActive:  len(activeSchedules),
		TotalMatched: matchedCount,
	}, nil
}

// IsScheduleActive checks if a schedule is active at a given time
func (s *scheduleService) IsScheduleActive(ctx context.Context, schedule *models.Schedule, currentTime time.Time) (bool, error) {
	// Check if schedule is active
	if schedule.Status != "active" {
		return false, nil
	}

	// Load timezone
	location, err := time.LoadLocation(schedule.Timezone)
	if err != nil {
		return false, fmt.Errorf("invalid timezone: %s", schedule.Timezone)
	}

	// Convert current time to schedule's timezone
	localTime := currentTime.In(location)

	// Check recurring pattern if present
	if schedule.RecurringPattern != nil {
		// TODO: Add recurring pattern validation
		return true, nil
	}

	// Check if current time is within schedule time range
	currentTimeOfDay := time.Date(0, 1, 1, localTime.Hour(), localTime.Minute(), localTime.Second(), 0, time.UTC)
	startTimeOfDay := time.Date(0, 1, 1, schedule.StartTime.Hour(), schedule.StartTime.Minute(), schedule.StartTime.Second(), 0, time.UTC)
	endTimeOfDay := time.Date(0, 1, 1, schedule.EndTime.Hour(), schedule.EndTime.Minute(), schedule.EndTime.Second(), 0, time.UTC)

	// Handle overnight schedules (e.g., 23:00 to 02:00)
	if endTimeOfDay.Before(startTimeOfDay) {
		return currentTimeOfDay.After(startTimeOfDay) || currentTimeOfDay.Before(endTimeOfDay), nil
	}

	return currentTimeOfDay.After(startTimeOfDay) && currentTimeOfDay.Before(endTimeOfDay), nil
}

// GetNextScheduledTime gets the next scheduled time for a schedule
func (s *scheduleService) GetNextScheduledTime(ctx context.Context, schedule *models.Schedule) (*time.Time, error) {
	// Load timezone
	location, err := time.LoadLocation(schedule.Timezone)
	if err != nil {
		return nil, fmt.Errorf("invalid timezone: %s", schedule.Timezone)
	}

	now := time.Now().In(location)

	// Simple next occurrence calculation
	nextTime := now.Add(24 * time.Hour)
	scheduledTime := time.Date(
		nextTime.Year(),
		nextTime.Month(),
		nextTime.Day(),
		schedule.StartTime.Hour(),
		schedule.StartTime.Minute(),
		schedule.StartTime.Second(),
		0,
		location,
	)

	return &scheduledTime, nil

	// If no next scheduled time found in the next 7 days, return nil
	return nil, nil
}

// ValidateSchedule validates a schedule request
func (s *scheduleService) ValidateSchedule(ctx context.Context, req *dto.ScheduleValidationRequest) (*dto.ScheduleValidationResponse, error) {
	var issues []dto.ScheduleValidationIssue
	var warnings []dto.ScheduleValidationIssue
	var recommendations []string

	// Validate required fields
	if req.AdvertisementID == 0 {
		issues = append(issues, dto.ScheduleValidationIssue{
			Type:     "missing_field",
			Severity: "error",
			Field:    "advertisement_id",
			Message:  "Advertisement ID is required",
		})
	}

	if req.StartTime.IsZero() {
		issues = append(issues, dto.ScheduleValidationIssue{
			Type:     "missing_field",
			Severity: "error",
			Field:    "start_time",
			Message:  "Start time is required",
		})
	}

	if req.EndTime.IsZero() {
		issues = append(issues, dto.ScheduleValidationIssue{
			Type:     "missing_field",
			Severity: "error",
			Field:    "end_time",
			Message:  "End time is required",
		})
	}

	// Validate time range
	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		if req.EndTime.Before(req.StartTime) {
			issues = append(issues, dto.ScheduleValidationIssue{
				Type:       "time_conflict",
				Severity:   "error",
				Field:      "end_time",
				Message:    "End time must be after start time",
				Suggestion: "Set end time to be at least 1 hour after start time",
			})
		}

		duration := req.EndTime.Sub(req.StartTime)
		if duration < time.Hour {
			warnings = append(warnings, dto.ScheduleValidationIssue{
				Type:       "duration_warning",
				Severity:   "warning",
				Field:      "duration",
				Message:    "Schedule duration is less than 1 hour",
				Suggestion: "Consider extending the schedule for better ad exposure",
			})
		}

		if duration > 24*time.Hour {
			warnings = append(warnings, dto.ScheduleValidationIssue{
				Type:       "duration_warning",
				Severity:   "warning",
				Field:      "duration",
				Message:    "Schedule duration exceeds 24 hours",
				Suggestion: "Consider breaking into smaller time windows",
			})
		}
	}

	// Validate timezone
	if req.Timezone != "" {
		if _, err := time.LoadLocation(req.Timezone); err != nil {
			issues = append(issues, dto.ScheduleValidationIssue{
				Type:     "invalid_timezone",
				Severity: "error",
				Field:    "timezone",
				Message:  fmt.Sprintf("Invalid timezone: %s", req.Timezone),
			})
		}
	}

	// Check for conflicting schedules
	var conflictingSchedules []uint
	if req.AdvertisementID != 0 {
		existingSchedules, err := s.repoManager.Schedule().GetByAdvertisementID(ctx, req.TenantID, req.WebsiteID, req.AdvertisementID)
		if err == nil {
			for _, existing := range existingSchedules {
				if existing.Status == "active" {
					// Check for time overlap
					if req.StartTime.Before(existing.EndTime) && req.EndTime.After(existing.StartTime) {
						conflictingSchedules = append(conflictingSchedules, existing.ID)
					}
				}
			}
		}
	}

	if len(conflictingSchedules) > 0 {
		recommendations = append(recommendations, "Consider adjusting time range to avoid conflicts with existing schedules")
	}

	return &dto.ScheduleValidationResponse{
		IsValid:              len(issues) == 0,
		Issues:               issues,
		Warnings:             warnings,
		ConflictingSchedules: conflictingSchedules,
		Recommendations:      recommendations,
	}, nil
}

// CheckScheduleStatus checks the current status of a schedule
func (s *scheduleService) CheckScheduleStatus(ctx context.Context, req *dto.ScheduleStatusRequest) (*dto.ScheduleStatusResponse, error) {
	schedule, err := s.repoManager.Schedule().GetByID(ctx, req.TenantID, req.WebsiteID, req.ScheduleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}

	checkTime := time.Now()
	if !req.CheckTime.IsZero() {
		checkTime = req.CheckTime
	}

	isActive := schedule.Status == "active"
	isCurrentlyActive, _ := s.IsScheduleActive(ctx, schedule, checkTime)

	var nextScheduledTime *time.Time
	var timeUntilNext *string

	if next, err := s.GetNextScheduledTime(ctx, schedule); err == nil && next != nil {
		nextScheduledTime = next
		duration := next.Sub(checkTime)
		if duration > 0 {
			timeStr := duration.String()
			timeUntilNext = &timeStr
		}
	}

	var statusReason string
	if !isActive {
		statusReason = fmt.Sprintf("Schedule status is %s", schedule.Status)
	} else if isCurrentlyActive {
		statusReason = "Schedule is within active time range"
	} else {
		statusReason = "Schedule is not currently active based on time constraints"
	}

	return &dto.ScheduleStatusResponse{
		ScheduleID:        req.ScheduleID,
		CurrentTime:       checkTime,
		IsActive:          isActive,
		IsCurrentlyActive: isCurrentlyActive,
		NextScheduledTime: nextScheduledTime,
		TimeUntilNext:     timeUntilNext,
		Status:            schedule.Status,
		StatusReason:      statusReason,
	}, nil
}

// Helper methods

func (s *scheduleService) validateScheduleCreateRequest(req *dto.ScheduleCreateRequest) error {
	if req.AdvertisementID == 0 {
		return fmt.Errorf("advertisement ID is required")
	}

	if req.StartTime.IsZero() {
		return fmt.Errorf("start time is required")
	}

	if req.EndTime.IsZero() {
		return fmt.Errorf("end time is required")
	}

	if req.Timezone == "" {
		return fmt.Errorf("timezone is required")
	}

	return nil
}

func (s *scheduleService) applyScheduleFilters(schedules []*models.Schedule, filters *ScheduleFilters) []*models.Schedule {
	var filtered []*models.Schedule

	for _, schedule := range schedules {
		// Advertisement ID filter
		if filters.AdvertisementID != nil && schedule.AdvertisementID != *filters.AdvertisementID {
			continue
		}

		// Status filter
		if filters.Status != nil && schedule.Status != *filters.Status {
			continue
		}

		// Start date filter
		if filters.StartDate != nil && schedule.StartTime.Before(*filters.StartDate) {
			continue
		}

		// End date filter
		if filters.EndDate != nil && schedule.EndTime.After(*filters.EndDate) {
			continue
		}

		// Timezone filter
		if filters.Timezone != nil && schedule.Timezone != *filters.Timezone {
			continue
		}

		filtered = append(filtered, schedule)
	}

	return filtered
}

func (s *scheduleService) getDayName(weekday time.Weekday) string {
	days := []string{"sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"}
	return days[weekday]
}

func (s *scheduleService) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// validateScheduleModel validates schedule business rules for models.Schedule
func (s *scheduleService) validateScheduleModel(ctx context.Context, schedule *models.Schedule) error {
	// Validate time range
	if schedule.EndTime.Before(schedule.StartTime) {
		return fmt.Errorf("end time cannot be before start time")
	}

	// Validate recurring pattern if present
	if schedule.RecurringPattern != nil {
		// TODO: Add validation for recurring pattern
	}

	// Validate timezone
	if schedule.Timezone == "" {
		return fmt.Errorf("timezone is required")
	}

	// Validate timezone format
	_, err := time.LoadLocation(schedule.Timezone)
	if err != nil {
		return fmt.Errorf("invalid timezone: %s", schedule.Timezone)
	}

	// Validate status
	validStatuses := []string{"active", "paused", "expired", "deleted"}
	if !s.contains(validStatuses, schedule.Status) {
		return fmt.Errorf("invalid status: %s", schedule.Status)
	}

	// Validate time duration (minimum 1 hour, maximum 24 hours)
	duration := schedule.EndTime.Sub(schedule.StartTime)
	if duration < time.Hour {
		return fmt.Errorf("schedule duration must be at least 1 hour")
	}
	if duration > 24*time.Hour {
		return fmt.Errorf("schedule duration cannot exceed 24 hours")
	}

	return nil
}

// convertScheduleToDTO converts a schedule model to DTO response
func (s *scheduleService) convertScheduleToDTO(schedule *models.Schedule) *dto.ScheduleResponse {
	isCurrentlyActive := false
	if schedule.Status == "active" {
		isCurrentlyActive, _ = s.IsScheduleActive(context.Background(), schedule, time.Now())
	}

	// Handle recurring pattern conversion
	var recurringPattern *models.RecurringPattern
	if schedule.RecurringPattern != nil {
		recurringPattern = &models.RecurringPattern{}
		schedule.RecurringPattern.Scan(recurringPattern)
	}

	return &dto.ScheduleResponse{
		ID:                schedule.ID,
		TenantID:          schedule.TenantID,
		AdvertisementID:   schedule.AdvertisementID,
		StartTime:         schedule.StartTime,
		EndTime:           schedule.EndTime,
		Timezone:          schedule.Timezone,
		RecurringPattern:  recurringPattern,
		Status:            schedule.Status,
		CreatedAt:         schedule.CreatedAt,
		UpdatedAt:         schedule.UpdatedAt,
		IsActive:          schedule.IsActive(),
		IsCurrentlyActive: isCurrentlyActive,
	}
}
