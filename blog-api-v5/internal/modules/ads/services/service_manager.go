package services

import (
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// serviceManager implements the ServiceManager interface
type serviceManager struct {
	repoManager repositories.RepositoryManager

	// Service instances
	campaignService      CampaignService
	advertisementService AdvertisementService
	analyticsService     AnalyticsService
	placementService     PlacementService
	scheduleService      ScheduleService
	targetingService     TargetingService
}

// NewServiceManager creates a new service manager instance
func NewServiceManager(repoManager repositories.RepositoryManager) ServiceManager {
	return &serviceManager{
		repoManager:          repoManager,
		campaignService:      NewCampaignService(repoManager),
		advertisementService: NewAdvertisementService(repoManager),
		analyticsService:     NewAnalyticsService(repoManager),
		placementService:     NewPlacementService(repoManager),
		scheduleService:      NewScheduleService(repoManager),
		targetingService:     NewTargetingService(repoManager),
	}
}

// CampaignService returns the campaign service instance
func (sm *serviceManager) CampaignService() CampaignService {
	return sm.campaignService
}

// AdvertisementService returns the advertisement service instance
func (sm *serviceManager) AdvertisementService() AdvertisementService {
	return sm.advertisementService
}

// AnalyticsService returns the analytics service instance
func (sm *serviceManager) AnalyticsService() AnalyticsService {
	return sm.analyticsService
}

// PlacementService returns the placement service instance
func (sm *serviceManager) PlacementService() PlacementService {
	return sm.placementService
}

// ScheduleService returns the schedule service instance
func (sm *serviceManager) ScheduleService() ScheduleService {
	return sm.scheduleService
}

// TargetingService returns the targeting service instance
func (sm *serviceManager) TargetingService() TargetingService {
	return sm.targetingService
}
