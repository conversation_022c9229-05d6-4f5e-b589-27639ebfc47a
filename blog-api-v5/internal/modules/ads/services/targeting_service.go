package services

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ads/repositories"
)

// targetingService implements the TargetingService interface
type targetingService struct {
	repoManager repositories.RepositoryManager
}

// NewTargetingService creates a new targeting service instance
func NewTargetingService(repoManager repositories.RepositoryManager) TargetingService {
	return &targetingService{
		repoManager: repoManager,
	}
}

// CreateTargetingRule creates a new targeting rule
func (s *targetingService) CreateTargetingRule(ctx context.Context, req *dto.TargetingRuleCreateRequest) (*dto.TargetingRuleResponse, error) {
	// Validate request
	if err := s.validateTargetingRuleCreateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid targeting rule request: %w", err)
	}

	// Check if advertisement exists and belongs to tenant
	_, err := s.repoManager.Advertisement().GetByID(ctx, req.TenantID, req.WebsiteID, req.AdvertisementID)
	if err != nil {
		return nil, fmt.Errorf("advertisement not found: %w", err)
	}

	targetingRule := &models.TargetingRule{
		TenantID:        req.TenantID,
		AdvertisementID: req.AdvertisementID,
		RuleType:        req.RuleType,
		RuleKey:         req.RuleKey,
		RuleValue:       req.RuleValue,
		Operator:        req.Operator,
		Priority:        req.Priority,
		Status:          "active", // Default to active status
	}

	// Validate targeting rule
	if err := s.validateRuleModel(ctx, targetingRule); err != nil {
		return nil, fmt.Errorf("targeting rule validation failed: %w", err)
	}

	// Create targeting rule in database
	err = s.repoManager.TargetingRule().Create(ctx, targetingRule)
	if err != nil {
		return nil, fmt.Errorf("failed to create targeting rule: %w", err)
	}

	return s.convertTargetingRuleToDTO(targetingRule), nil
}

// GetTargetingRuleByID retrieves a targeting rule by ID
func (s *targetingService) GetTargetingRuleByID(ctx context.Context, tenantID, websiteID uint, ruleID uint) (*dto.TargetingRuleResponse, error) {
	targetingRule, err := s.repoManager.TargetingRule().GetByID(ctx, tenantID, websiteID, ruleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get targeting rule: %w", err)
	}

	return s.convertTargetingRuleToDTO(targetingRule), nil
}

// UpdateTargetingRule updates an existing targeting rule
func (s *targetingService) UpdateTargetingRule(ctx context.Context, tenantID, websiteID uint, ruleID uint, req *dto.TargetingRuleUpdateRequest) (*dto.TargetingRuleResponse, error) {
	// Get existing targeting rule (directly from repo)
	existingRule, err := s.repoManager.TargetingRule().GetByID(ctx, tenantID, websiteID, ruleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get targeting rule: %w", err)
	}

	// Apply updates
	if req.RuleType != nil {
		existingRule.RuleType = *req.RuleType
	}
	if req.RuleKey != nil {
		existingRule.RuleKey = *req.RuleKey
	}
	if req.RuleValue != nil {
		existingRule.RuleValue = *req.RuleValue
	}
	if req.Operator != nil {
		existingRule.Operator = *req.Operator
	}
	if req.Priority != nil {
		existingRule.Priority = *req.Priority
	}
	if req.Status != nil {
		existingRule.Status = *req.Status
	}

	// Validate targeting rule
	if err := s.validateRuleModel(ctx, existingRule); err != nil {
		return nil, fmt.Errorf("targeting rule validation failed: %w", err)
	}

	// Update in database
	err = s.repoManager.TargetingRule().Update(ctx, existingRule)
	if err != nil {
		return nil, fmt.Errorf("failed to update targeting rule: %w", err)
	}

	return s.convertTargetingRuleToDTO(existingRule), nil
}

// DeleteTargetingRule deletes a targeting rule (soft delete)
func (s *targetingService) DeleteTargetingRule(ctx context.Context, tenantID, websiteID uint, ruleID uint) error {
	// Check if targeting rule exists
	_, err := s.GetTargetingRuleByID(ctx, tenantID, websiteID, ruleID)
	if err != nil {
		return err
	}

	// Soft delete targeting rule
	err = s.repoManager.TargetingRule().Delete(ctx, tenantID, websiteID, ruleID)
	if err != nil {
		return fmt.Errorf("failed to delete targeting rule: %w", err)
	}

	return nil
}

// ListTargetingRules lists targeting rules with filters
func (s *targetingService) ListTargetingRules(ctx context.Context, filters *dto.TargetingRuleListFilter) (*dto.TargetingRuleListResponse, error) {
	if filters == nil {
		return nil, fmt.Errorf("filters cannot be nil")
	}

	// Set default pagination
	limit := 20
	offset := 0
	if filters.Pagination != nil {
		if filters.Pagination.Limit > 0 {
			limit = int(filters.Pagination.Limit)
		}
		if limit > 100 {
			limit = 100
		}
	}

	filtersMap := make(map[string]interface{})
	targetingRules, err := s.repoManager.TargetingRule().GetByTenantAndWebsite(ctx, filters.TenantID, filters.WebsiteID, limit, offset, filtersMap)
	if err != nil {
		return nil, fmt.Errorf("failed to list targeting rules: %w", err)
	}

	// Convert to legacy filters for applying additional filters
	legacyFilters := &TargetingRuleFilters{
		AdvertisementID: filters.AdvertisementID,
		RuleType:        filters.RuleType,
		Status:          filters.Status,
		Limit:           limit,
		Offset:          offset,
	}

	// Apply additional filters
	filteredRules := s.applyTargetingRuleFilters(targetingRules, legacyFilters)

	// Convert to DTO responses
	var ruleResponses []dto.TargetingRuleResponse
	for _, rule := range filteredRules {
		ruleResponses = append(ruleResponses, *s.convertTargetingRuleToDTO(rule))
	}

	return &dto.TargetingRuleListResponse{
		TargetingRules: ruleResponses,
		Pagination:     nil, // TODO: Implement proper pagination response
	}, nil
}

// GetRulesByAdvertisement retrieves all targeting rules for a specific advertisement
func (s *targetingService) GetRulesByAdvertisement(ctx context.Context, tenantID, websiteID uint, adID uint) ([]*dto.TargetingRuleResponse, error) {
	// Check if advertisement exists
	_, err := s.repoManager.Advertisement().GetByID(ctx, tenantID, websiteID, adID)
	if err != nil {
		return nil, fmt.Errorf("advertisement not found: %w", err)
	}

	targetingRules, err := s.repoManager.TargetingRule().GetByAdvertisementID(ctx, tenantID, websiteID, adID)
	if err != nil {
		return nil, fmt.Errorf("failed to get targeting rules for advertisement: %w", err)
	}

	// Filter only active rules and convert to DTO
	var activeRules []*dto.TargetingRuleResponse
	for _, rule := range targetingRules {
		if rule.Status == "active" {
			activeRules = append(activeRules, s.convertTargetingRuleToDTO(rule))
		}
	}

	return activeRules, nil
}

// EvaluateRules evaluates targeting rules against a context
func (s *targetingService) EvaluateRules(ctx context.Context, req *dto.TargetingEvaluationRequest) (*dto.TargetingEvaluationResponse, error) {
	var rules []*models.TargetingRule
	var err error

	// Get rules based on request parameters
	if req.AdvertisementID != nil {
		rules, err = s.repoManager.TargetingRule().GetByAdvertisementID(ctx, req.TenantID, req.WebsiteID, *req.AdvertisementID)
		if err != nil {
			return nil, fmt.Errorf("failed to get rules for advertisement: %w", err)
		}
	} else if req.CampaignID != nil {
		// Get all advertisements for the campaign and their rules
		advertisements, err := s.repoManager.Advertisement().GetByCampaignID(ctx, req.TenantID, req.WebsiteID, *req.CampaignID, 100, 0)
		if err != nil {
			return nil, fmt.Errorf("failed to get advertisements for campaign: %w", err)
		}

		for _, ad := range advertisements {
			adRules, err := s.repoManager.TargetingRule().GetByAdvertisementID(ctx, req.TenantID, req.WebsiteID, ad.ID)
			if err != nil {
				continue // Skip on error but don't fail entire operation
			}
			rules = append(rules, adRules...)
		}
	}

	// Filter only active rules
	var activeRules []*models.TargetingRule
	for _, rule := range rules {
		if rule.Status == "active" {
			activeRules = append(activeRules, rule)
		}
	}

	// Evaluate rules
	var ruleResults []dto.TargetingEvaluationResult
	matchedCount := 0
	totalScore := 0.0

	for _, rule := range activeRules {
		matched := s.evaluateSingleRule(rule, &req.Context)
		if matched {
			matchedCount++
			totalScore += float64(rule.Priority)
		}

		matchReason := ""
		if matched {
			matchReason = fmt.Sprintf("Rule matched: %s %s %s", rule.RuleKey, rule.Operator, rule.RuleValue)
		}

		ruleResults = append(ruleResults, dto.TargetingEvaluationResult{
			RuleID:      rule.ID,
			RuleType:    rule.RuleType,
			RuleKey:     rule.RuleKey,
			RuleValue:   rule.RuleValue,
			Operator:    rule.Operator,
			Priority:    rule.Priority,
			Matched:     matched,
			MatchReason: matchReason,
		})
	}

	// Calculate overall match and score
	overallMatch := len(activeRules) == 0 || matchedCount > 0 // No rules or at least one rule matches
	matchScore := 0.0
	if len(activeRules) > 0 {
		matchScore = float64(matchedCount) / float64(len(activeRules))
	}

	// Get recommended ads based on evaluation
	var recommendedAds []uint
	if req.AdvertisementID != nil && overallMatch {
		recommendedAds = append(recommendedAds, *req.AdvertisementID)
	}

	return &dto.TargetingEvaluationResponse{
		TotalRules:     len(activeRules),
		MatchedRules:   matchedCount,
		OverallMatch:   overallMatch,
		MatchScore:     matchScore,
		RuleResults:    ruleResults,
		RecommendedAds: recommendedAds,
		EvaluationTime: time.Now(),
	}, nil
}

// validateRuleModel validates a targeting rule model
func (s *targetingService) validateRuleModel(ctx context.Context, rule *models.TargetingRule) error {
	// Validate rule type
	validRuleTypes := []string{"page_url", "referrer", "device", "time", "location", "custom"}
	if !s.contains(validRuleTypes, rule.RuleType) {
		return fmt.Errorf("invalid rule type: %s", rule.RuleType)
	}

	// Validate operator
	validOperators := []string{"equals", "not_equals", "contains", "not_contains", "starts_with", "ends_with", "regex"}
	if !s.contains(validOperators, rule.Operator) {
		return fmt.Errorf("invalid operator: %s", rule.Operator)
	}

	// Validate priority
	if rule.Priority < 1 || rule.Priority > 100 {
		return fmt.Errorf("priority must be between 1 and 100")
	}

	// Validate status
	validStatuses := []string{"active", "inactive", "deleted"}
	if !s.contains(validStatuses, rule.Status) {
		return fmt.Errorf("invalid status: %s", rule.Status)
	}

	// Validate target value based on rule type
	if err := s.validateTargetValue(rule.RuleType, rule.RuleValue, rule.Operator); err != nil {
		return err
	}

	return nil
}

// ValidateRule validates a targeting rule request
func (s *targetingService) ValidateRule(ctx context.Context, req *dto.TargetingRuleCreateRequest) (*dto.TargetingValidationResponse, error) {
	var errors []string
	var warnings []string
	var suggestions []string

	// Validate required fields
	if req.RuleType == "" {
		errors = append(errors, "rule type is required")
	}
	if req.RuleKey == "" {
		errors = append(errors, "rule key is required")
	}
	if req.RuleValue == "" {
		errors = append(errors, "rule value is required")
	}
	if req.Operator == "" {
		errors = append(errors, "operator is required")
	}

	// Validate rule type
	validRuleTypes := []string{"page_url", "referrer", "device", "time", "location", "custom"}
	if req.RuleType != "" && !s.contains(validRuleTypes, req.RuleType) {
		errors = append(errors, fmt.Sprintf("invalid rule type: %s", req.RuleType))
	}

	// Validate operator
	validOperators := []string{"equals", "not_equals", "contains", "not_contains", "starts_with", "ends_with", "regex"}
	if req.Operator != "" && !s.contains(validOperators, req.Operator) {
		errors = append(errors, fmt.Sprintf("invalid operator: %s", req.Operator))
	}

	// Validate priority
	if req.Priority < 1 || req.Priority > 100 {
		errors = append(errors, "priority must be between 1 and 100")
	}

	// Validate target value based on rule type
	if req.RuleType != "" && req.RuleValue != "" && req.Operator != "" {
		if err := s.validateTargetValue(req.RuleType, req.RuleValue, req.Operator); err != nil {
			errors = append(errors, err.Error())
		}
	}

	// Add suggestions based on rule type
	if req.RuleType == "device" {
		suggestions = append(suggestions, "Consider using device-specific targeting for better performance")
	}
	if req.RuleType == "time" {
		suggestions = append(suggestions, "Time-based rules work best with consistent user behavior patterns")
	}
	if req.Priority > 80 {
		warnings = append(warnings, "High priority rules may override other important targeting")
	}

	return &dto.TargetingValidationResponse{
		IsValid:     len(errors) == 0,
		Errors:      errors,
		Warnings:    warnings,
		Suggestions: suggestions,
	}, nil
}

// Helper methods

func (s *targetingService) validateTargetingRuleCreateRequest(req *dto.TargetingRuleCreateRequest) error {
	if req.AdvertisementID == 0 {
		return fmt.Errorf("advertisement ID is required")
	}

	if req.RuleType == "" {
		return fmt.Errorf("rule type is required")
	}

	if req.RuleKey == "" {
		return fmt.Errorf("rule key is required")
	}

	if req.RuleValue == "" {
		return fmt.Errorf("rule value is required")
	}

	if req.Operator == "" {
		return fmt.Errorf("operator is required")
	}

	if req.Priority < 1 || req.Priority > 100 {
		return fmt.Errorf("priority must be between 1 and 100")
	}

	return nil
}

func (s *targetingService) applyTargetingRuleFilters(rules []*models.TargetingRule, filters *TargetingRuleFilters) []*models.TargetingRule {
	var filtered []*models.TargetingRule

	for _, rule := range rules {
		// Advertisement ID filter
		if filters.AdvertisementID != nil && rule.AdvertisementID != *filters.AdvertisementID {
			continue
		}

		// Rule type filter
		if filters.RuleType != nil && rule.RuleType != *filters.RuleType {
			continue
		}

		// Status filter
		if filters.Status != nil && rule.Status != *filters.Status {
			continue
		}

		// Priority filters
		if filters.MinPriority != nil && rule.Priority < *filters.MinPriority {
			continue
		}
		if filters.MaxPriority != nil && rule.Priority > *filters.MaxPriority {
			continue
		}

		filtered = append(filtered, rule)
	}

	return filtered
}

func (s *targetingService) evaluateRuleType(ruleType string, rules []*models.TargetingRule, context *models.TargetingContext) bool {
	// For rules of the same type, use OR logic (any rule can match)
	for _, rule := range rules {
		if s.evaluateSingleRule(rule, context) {
			return true
		}
	}
	return false
}

func (s *targetingService) evaluateSingleRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	switch rule.RuleType {
	case "location":
		return s.evaluateLocationRule(rule, context)
	case "time":
		return s.evaluateTimeRule(rule, context)
	case "device":
		return s.evaluateDeviceRule(rule, context)
	case "user_agent":
		return s.evaluateUserAgentRule(rule, context)
	case "referrer":
		return s.evaluateReferrerRule(rule, context)
	case "page_url":
		return s.evaluatePageURLRule(rule, context)
	case "language":
		return s.evaluateLanguageRule(rule, context)
	case "ip_range":
		return s.evaluateIPRangeRule(rule, context)
	default:
		return false
	}
}

func (s *targetingService) evaluateLocationRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	return s.evaluateStringRule(rule.Operator, rule.RuleValue, context.IPAddress)
}

func (s *targetingService) evaluateTimeRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	// Parse time range (e.g., "9:00-17:00")
	timeRange := strings.Split(rule.RuleValue, "-")
	if len(timeRange) != 2 {
		return false
	}

	startTime, err1 := time.Parse("15:04", timeRange[0])
	endTime, err2 := time.Parse("15:04", timeRange[1])
	if err1 != nil || err2 != nil {
		return false
	}

	now := time.Now()
	currentTime := time.Date(0, 1, 1, now.Hour(), now.Minute(), 0, 0, time.UTC)

	return currentTime.After(startTime) && currentTime.Before(endTime)
}

func (s *targetingService) evaluateDeviceRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	return s.evaluateStringRule(rule.Operator, rule.RuleValue, context.DeviceType)
}

func (s *targetingService) evaluateUserAgentRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	return s.evaluateStringRule(rule.Operator, rule.RuleValue, context.UserAgent)
}

func (s *targetingService) evaluateReferrerRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	return s.evaluateStringRule(rule.Operator, rule.RuleValue, context.Referrer)
}

func (s *targetingService) evaluatePageURLRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	return s.evaluateStringRule(rule.Operator, rule.RuleValue, context.PageURL)
}

func (s *targetingService) evaluateLanguageRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	return s.evaluateStringRule(rule.Operator, rule.RuleValue, context.CustomData["language"])
}

func (s *targetingService) evaluateIPRangeRule(rule *models.TargetingRule, context *models.TargetingContext) bool {
	// Simple IP range check (in production, this would use proper IP parsing)
	if rule.Operator == "contains" {
		// For now, just check if IP starts with the target value
		return strings.HasPrefix(context.IPAddress, rule.RuleValue)
	}
	return s.evaluateStringRule(rule.Operator, rule.RuleValue, context.IPAddress)
}

func (s *targetingService) evaluateStringRule(operator, targetValue, contextValue string) bool {
	switch operator {
	case "equals":
		return contextValue == targetValue
	case "not_equals":
		return contextValue != targetValue
	case "contains":
		return strings.Contains(contextValue, targetValue)
	case "not_contains":
		return !strings.Contains(contextValue, targetValue)
	case "starts_with":
		return strings.HasPrefix(contextValue, targetValue)
	case "ends_with":
		return strings.HasSuffix(contextValue, targetValue)
	case "regex":
		matched, _ := regexp.MatchString(targetValue, contextValue)
		return matched
	default:
		return false
	}
}

func (s *targetingService) validateTargetValue(ruleType, targetValue, operator string) error {
	switch ruleType {
	case "time":
		if operator == "contains" {
			// Validate time range format (e.g., "9:00-17:00")
			timeRange := strings.Split(targetValue, "-")
			if len(timeRange) != 2 {
				return fmt.Errorf("time range must be in format 'HH:MM-HH:MM'")
			}
			for _, t := range timeRange {
				if _, err := time.Parse("15:04", t); err != nil {
					return fmt.Errorf("invalid time format: %s", t)
				}
			}
		}
	case "device":
		validDevices := []string{"web_pc", "web_mobile", "unknown"}
		if !s.contains(validDevices, targetValue) {
			return fmt.Errorf("invalid device type: %s", targetValue)
		}
	case "regex":
		if operator == "regex" {
			if _, err := regexp.Compile(targetValue); err != nil {
				return fmt.Errorf("invalid regex pattern: %s", targetValue)
			}
		}
	case "location":
		if operator == "contains" {
			// Basic IP validation (in production, use proper IP parsing)
			parts := strings.Split(targetValue, ".")
			if len(parts) != 4 {
				return fmt.Errorf("invalid IP format: %s", targetValue)
			}
			for _, part := range parts {
				if num, err := strconv.Atoi(part); err != nil || num < 0 || num > 255 {
					return fmt.Errorf("invalid IP format: %s", targetValue)
				}
			}
		}
	}

	return nil
}

func (s *targetingService) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// convertTargetingRuleToDTO converts a targeting rule model to DTO response
func (s *targetingService) convertTargetingRuleToDTO(rule *models.TargetingRule) *dto.TargetingRuleResponse {
	return &dto.TargetingRuleResponse{
		ID:              rule.ID,
		TenantID:        rule.TenantID,
		AdvertisementID: rule.AdvertisementID,
		RuleType:        rule.RuleType,
		RuleKey:         rule.RuleKey,
		RuleValue:       rule.RuleValue,
		Operator:        rule.Operator,
		Priority:        rule.Priority,
		Status:          rule.Status,
		CreatedAt:       rule.CreatedAt,
		UpdatedAt:       rule.UpdatedAt,
		IsActive:        rule.IsActive(),
	}
}
