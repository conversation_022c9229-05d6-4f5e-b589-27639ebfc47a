# AI Module

Comprehensive AI integration module for the blog API, providing chat functionality, content generation, and analytics.

## Features

### ✅ Completed Features
- **AI Request Management**: Create, process, and track AI requests
- **Chat Sessions**: Multi-user chat sessions with context management
- **Message Handling**: User and assistant message management
- **Analytics**: Comprehensive usage and performance analytics
- **Cost Tracking**: Token usage and cost calculation
- **Rate Limiting**: Request throttling and quota management
- **PII Detection**: Automatic detection of personally identifiable information
- **Security**: Prompt sanitization and access validation
- **Queue Management**: Request queuing for high-traffic scenarios

### 🔄 Available AI Providers
- OpenAI (GPT-4, GPT-3.5-turbo)
- Anthropic Claude (Opus, Sonnet)
- Google Gemini Pro
- Extensible provider architecture

## API Endpoints

### AI Requests
```
POST   /api/v1/ai/requests              # Create AI request
GET    /api/v1/ai/requests/:id          # Get request details
PUT    /api/v1/ai/requests/:id          # Update request
DELETE /api/v1/ai/requests/:id          # Delete request
GET    /api/v1/ai/requests              # List requests
POST   /api/v1/ai/requests/:id/process  # Process request
POST   /api/v1/ai/requests/:id/retry    # Retry failed request
POST   /api/v1/ai/requests/:id/cancel   # Cancel request
GET    /api/v1/ai/requests/search       # Search requests
POST   /api/v1/ai/requests/batch        # Bulk create requests
```

### Chat Sessions
```
POST   /api/v1/ai/chat/sessions         # Create chat session
GET    /api/v1/ai/chat/sessions/:id     # Get session details
PUT    /api/v1/ai/chat/sessions/:id     # Update session
DELETE /api/v1/ai/chat/sessions/:id     # Delete session
POST   /api/v1/ai/chat/sessions/:id/archive  # Archive session
GET    /api/v1/ai/chat/sessions         # List sessions
GET    /api/v1/ai/chat/sessions/:id/history  # Get conversation history
```

### Chat Messages
```
POST   /api/v1/ai/chat/messages         # Create message
GET    /api/v1/ai/chat/messages/:id     # Get message details
```

### Analytics
```
GET    /api/v1/ai/chat/analytics/overall        # Overall analytics
GET    /api/v1/ai/chat/analytics/sessions/stats # Session statistics
GET    /api/v1/ai/chat/analytics/messages/stats # Message statistics
GET    /api/v1/ai/chat/analytics/tokens/usage   # Token usage stats
GET    /api/v1/ai/chat/analytics/sessions/active # Active sessions count
GET    /api/v1/ai/chat/analytics/users/:id      # User-specific analytics
POST   /api/v1/ai/chat/analytics/export         # Export analytics
```

### System
```
GET    /api/v1/ai/health               # Health check
GET    /api/v1/ai/metrics              # System metrics
GET    /api/v1/ai/rate-limits          # Rate limit info
GET    /api/v1/ai/cost-breakdown       # Cost breakdown
```

## Database Schema

### Tables
- `ai_models` - AI model configurations
- `ai_requests` - AI request tracking
- `ai_chat_sessions` - Chat session management
- `ai_chat_messages` - Individual chat messages
- `ai_content_templates` - Content generation templates
- `ai_generated_content` - Generated content storage
- `ai_web_designs` - AI-generated web designs

## Configuration

### Environment Variables
```env
OPENAI_API_KEY=your_openai_key
CLAUDE_API_KEY=your_claude_key
GEMINI_API_KEY=your_gemini_key
AI_RATE_LIMIT_RPM=100
AI_MAX_TOKENS_PER_REQUEST=4000
AI_ENABLE_PII_DETECTION=true
AI_ENABLE_RATE_LIMITING=true
```

### Service Configuration
```go
aiProvider := services.NewAIProviderService(
    openAIKey,
    claudeKey, 
    geminiKey,
    true,  // Enable rate limiting
    true,  // Enable PII detection
    100,   // Max requests per minute
    4000,  // Max tokens per request
)
```

## Usage Examples

### Creating a Chat Session
```json
POST /api/v1/ai/chat/sessions
{
    "title": "My Chat Session",
    "system_prompt": "You are a helpful assistant",
    "website_id": 1,
    "context": {
        "user_preferences": "technical writing"
    }
}
```

### Sending a Message
```json
POST /api/v1/ai/chat/messages
{
    "session_id": 123,
    "role": "user",
    "content": "Help me write a technical blog post about AI",
    "metadata": {
        "intent": "content_generation"
    }
}
```

### Creating an AI Request
```json
POST /api/v1/ai/requests
{
    "type": "content_generation",
    "model": "gpt-4",
    "prompt": "Write a technical article about microservices architecture",
    "website_id": 1,
    "metadata": {
        "target_audience": "developers",
        "word_count": 1500
    }
}
```

## Architecture

### Service Layer
- `AIRequestService` - Manages AI requests and processing
- `AIChatService` - Handles chat sessions and messages
- `AIProviderService` - Integrates with AI providers
- `AIAnalyticsService` - Provides usage analytics

### Repository Layer
- `AIRequestRepository` - AI request data access
- `AIChatSessionRepository` - Chat session persistence
- `AIChatMessageRepository` - Message storage

### Handler Layer
- `AIRequestHandler` - HTTP request handling for AI requests
- `AIChatHandler` - Chat-related endpoints
- `AIAnalyticsHandler` - Analytics endpoints

## Security Features

### PII Detection
Automatically detects and flags:
- Email addresses
- Phone numbers
- Social Security Numbers
- Credit card numbers
- IP addresses

### Rate Limiting
- Per-tenant request limits
- Per-user quota management
- Configurable time windows
- Queue management for high traffic

### Access Control
- Tenant-based isolation
- User permission validation
- Website-scoped access
- Secure prompt sanitization

## Monitoring & Analytics

### Metrics Tracked
- Request counts by type/model
- Processing times and latency
- Token usage and costs
- Error rates and types
- User engagement metrics
- Session analytics

### Cost Management
- Real-time cost calculation
- Per-model pricing
- Token usage tracking
- Budget alerts and limits

## Testing

Run the test suite:
```bash
go test ./internal/modules/ai/...
```

Run integration tests:
```bash
go test -tags=integration ./internal/modules/ai/...
```

## Development

### Adding New AI Providers
1. Implement the provider interface in `services/`
2. Add provider configuration
3. Update the provider factory
4. Add tests and documentation

### Extending Functionality
1. Define new DTOs in `dto/`
2. Update models in `models/`
3. Implement repository methods
4. Add service layer logic
5. Create handlers and routes
6. Add tests

## Troubleshooting

### Common Issues
1. **Rate Limit Exceeded**: Check rate limiting configuration
2. **PII Detection Failures**: Verify content sanitization
3. **High Costs**: Monitor token usage and model selection
4. **Slow Response Times**: Check provider latency and queue status

### Logs
Check application logs for AI module events:
```bash
grep "ai\|chat\|request" /path/to/logs
```

## Migration

Database migrations are located in `internal/database/migrations/j_ai/`:
- `901_create_ai_models_table.up.sql`
- `902_create_ai_requests_table.up.sql`
- `903_create_ai_chat_sessions_table.up.sql`
- `904_create_ai_chat_messages_table.up.sql`
- `905_create_ai_content_templates_table.up.sql`
- `906_create_ai_generated_content_table.up.sql`
- `907_create_ai_web_designs_table.up.sql`
- `908_add_website_id_to_ai_tables.up.sql`
- `909_add_ai_website_constraints.up.sql`
- `910_add_website_id_to_remaining_ai_tables.up.sql`

Run migrations:
```bash
make migrate-up MODULE=j_ai
```