package handlers

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/services"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// AIAnalyticsHandler handles AI analytics related HTTP requests
type AIAnalyticsHandler struct {
	chatService    services.AIChatService
	requestService services.AIRequestService
	logger         utils.Logger
}

// NewAIAnalyticsHandler creates a new AI analytics handler
func NewAIAnalyticsHandler(
	chatService services.AIChatService,
	requestService services.AIRequestService,
	logger utils.Logger,
) *AIAnalyticsHandler {
	return &AIAnalyticsHandler{
		chatService:    chatService,
		requestService: requestService,
		logger:         logger,
	}
}

// GetOverallAnalytics handles GET /api/v1/ai/analytics/overall
func (h *AIAnalyticsHandler) GetOverallAnalytics(c *gin.Context) {
	var req dto.AnalyticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid query parameters: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// Calculate time range if not provided
	startDate, endDate := calculateTimeRange(req.TimeRange, req.StartDate, req.EndDate)

	// Get request analytics
	analyticsInput := services.AnalyticsInput{
		TenantID:  tenantID.(uint),
		WebsiteID: req.WebsiteID,
		UserID:    req.UserID,
		StartTime: startDate,
		EndTime:   endDate,
		Scope:     "tenant",
	}

	requestAnalytics, err := h.requestService.GetAnalytics(c.Request.Context(), analyticsInput)
	if err != nil {
		h.logger.Errorf("Failed to get request analytics: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get request analytics: %w", err)))
		return
	}

	// Get session analytics
	sessionStats, err := h.chatService.GetSessionStats(c.Request.Context(), tenantID.(uint), req.WebsiteID)
	if err != nil {
		h.logger.Errorf("Failed to get session stats: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get session stats: %w", err)))
		return
	}

	// Create mock analytics response based on available data
	analyticsResponse := &dto.OverallAnalyticsResponse{
		TimeRange: req.TimeRange,
		StartDate: startDate,
		EndDate:   endDate,
		RequestAnalytics: dto.RequestAnalyticsData{
			TotalRequests:     requestAnalytics.TotalRequests,
			CompletedRequests: requestAnalytics.CompletedRequests,
			FailedRequests:    requestAnalytics.FailedRequests,
			TimeoutRequests:   requestAnalytics.TimeoutRequests,
			SuccessRate:       float64(requestAnalytics.CompletedRequests) / float64(requestAnalytics.TotalRequests) * 100,
			RequestsByType:    requestAnalytics.RequestsByType,
			RequestsByStatus:  requestAnalytics.RequestsByStatus,
		},
		SessionAnalytics: dto.SessionAnalyticsData{
			TotalSessions:   sessionStats.TotalMessages, // Using available data
			ActiveSessions:  sessionStats.UserMessages,
			AverageMessages: sessionStats.AverageTokens,
		},
		MessageAnalytics: dto.MessageAnalyticsData{
			TotalMessages:     sessionStats.TotalMessages,
			UserMessages:      sessionStats.UserMessages,
			AssistantMessages: sessionStats.AssistantMessages,
			SystemMessages:    sessionStats.SystemMessages,
			AverageLength:     sessionStats.AverageTokens,
		},
		UsageAnalytics: dto.UsageAnalyticsData{
			TotalTokensUsed:  requestAnalytics.TotalTokensUsed,
			AverageTokens:    float64(requestAnalytics.TotalTokensUsed) / float64(requestAnalytics.TotalRequests),
			TotalCostCents:   requestAnalytics.TotalCostCents,
			TotalCostDollars: requestAnalytics.TotalCostDollars,
			AverageCost:      requestAnalytics.TotalCostDollars / float64(requestAnalytics.TotalRequests),
		},
		PerformanceAnalytics: dto.PerformanceAnalyticsData{
			AverageProcessingTimeMs: requestAnalytics.AverageProcessingTime,
		},
	}

	response.SuccessWithContext(c, analyticsResponse)
}

// GetSessionStats handles GET /api/v1/ai/analytics/sessions/stats
func (h *AIAnalyticsHandler) GetSessionStats(c *gin.Context) {
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// Get website ID from context (set by middleware from header)
	var websiteID *uint
	if websiteIDCtx, exists := c.Get("website_id"); exists {
		if websiteIDUint, ok := websiteIDCtx.(uint); ok {
			websiteID = &websiteIDUint
		}
	}

	stats, err := h.chatService.GetSessionStats(c.Request.Context(), tenantID.(uint), websiteID)
	if err != nil {
		h.logger.Errorf("Failed to get session stats: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get session stats: %w", err)))
		return
	}

	response.SuccessWithContext(c, stats)
}

// GetMessageStats handles GET /api/v1/ai/analytics/messages/stats
func (h *AIAnalyticsHandler) GetMessageStats(c *gin.Context) {
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// Get session ID from query params (required for this endpoint)
	sessionIDStr := c.Query("session_id")
	if sessionIDStr == "" {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Session ID is required"))
		return
	}

	sessionID, err := strconv.ParseUint(sessionIDStr, 10, 32)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid session ID format: "+err.Error()))
		return
	}

	stats, err := h.chatService.GetMessageStats(c.Request.Context(), uint(sessionID), tenantID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to get message stats: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get message stats: %w", err)))
		return
	}

	response.SuccessWithContext(c, stats)
}

// GetTokenUsageStats handles GET /api/v1/ai/analytics/tokens/usage
func (h *AIAnalyticsHandler) GetTokenUsageStats(c *gin.Context) {
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// Get session ID from query params (required for this endpoint)
	sessionIDStr := c.Query("session_id")
	if sessionIDStr == "" {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Session ID is required"))
		return
	}

	sessionID, err := strconv.ParseUint(sessionIDStr, 10, 32)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid session ID format: "+err.Error()))
		return
	}

	tokenCount, err := h.chatService.GetTokenUsageStats(c.Request.Context(), uint(sessionID), tenantID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to get token usage stats: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get token usage stats: %w", err)))
		return
	}

	response.SuccessWithContext(c, gin.H{
		"session_id": sessionID,
		"total_tokens": tokenCount,
		"tenant_id": tenantID,
	})
}

// GetActiveSessionsCount handles GET /api/v1/ai/analytics/sessions/active
func (h *AIAnalyticsHandler) GetActiveSessionsCount(c *gin.Context) {
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// Get website ID from context (set by middleware from header)
	var websiteID *uint
	if websiteIDCtx, exists := c.Get("website_id"); exists {
		if websiteIDUint, ok := websiteIDCtx.(uint); ok {
			websiteID = &websiteIDUint
		}
	}

	count, err := h.chatService.GetActiveSessionsCount(c.Request.Context(), tenantID.(uint), websiteID)
	if err != nil {
		h.logger.Errorf("Failed to get active sessions count: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get active sessions count: %w", err)))
		return
	}

	response.Success(c.Writer, gin.H{
		"active_sessions_count": count,
		"timestamp":             time.Now(),
	})
}

// GetUserAnalytics handles GET /api/v1/ai/analytics/users/:id
func (h *AIAnalyticsHandler) GetUserAnalytics(c *gin.Context) {
	userIDStr := c.Param("id")
	if userIDStr == "" {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("User ID is required"))
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid user ID format: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	var req dto.AnalyticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid query parameters: "+err.Error()))
		return
	}

	// Calculate time range if not provided
	startDate, endDate := calculateTimeRange(req.TimeRange, req.StartDate, req.EndDate)

	// Get user's request analytics
	analyticsInput := services.AnalyticsInput{
		TenantID:  tenantID.(uint),
		WebsiteID: req.WebsiteID,
		UserID:    &[]uint{uint(userID)}[0], // Convert to pointer
		StartTime: startDate,
		EndTime:   endDate,
		Scope:     "user",
	}

	requestAnalytics, err := h.requestService.GetAnalytics(c.Request.Context(), analyticsInput)
	if err != nil {
		h.logger.Errorf("Failed to get user request analytics: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get user request analytics: %w", err)))
		return
	}

	// Get user's session analytics
	sessionStats, err := h.chatService.GetSessionStats(c.Request.Context(), tenantID.(uint), req.WebsiteID)
	if err != nil {
		h.logger.Errorf("Failed to get user session stats: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get user session stats: %w", err)))
		return
	}

	userAnalytics := &dto.UserAnalyticsResponse{
		UserID:       uint(userID),
		TimeRange:    req.TimeRange,
		StartDate:    startDate,
		EndDate:      endDate,
		TotalRequests: requestAnalytics.TotalRequests,
		TotalSessions: sessionStats.TotalMessages, // Using available data
		TotalMessages: sessionStats.TotalMessages,
		TokensUsed:    requestAnalytics.TotalTokensUsed,
		CostCents:     requestAnalytics.TotalCostCents,
		CostDollars:   requestAnalytics.TotalCostDollars,
	}

	response.SuccessWithContext(c, userAnalytics)
}

// ExportAnalytics handles POST /api/v1/ai/analytics/export
func (h *AIAnalyticsHandler) ExportAnalytics(c *gin.Context) {
	var req dto.ExportAnalyticsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid request body: "+err.Error()))
		return
	}

	_, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// TODO: Implement analytics export functionality
	// This would typically generate reports in various formats (CSV, PDF, Excel)
	
	exportResponse := &dto.ExportAnalyticsResponse{
		Format:      req.Format,
		Filename:    "analytics_" + time.Now().Format("2006_01_02") + "." + req.Format,
		DownloadURL: "/api/v1/ai/analytics/downloads/export_" + time.Now().Format("20060102150405"),
		ExpiresAt:   time.Now().Add(24 * time.Hour),
		FileSize:    0, // Would be calculated after export
		RecordCount: 0, // Would be calculated after export
	}

	response.SuccessWithContext(c, exportResponse)
}

// Helper function to calculate time range
func calculateTimeRange(timeRange dto.AnalyticsTimeRange, startDate, endDate *time.Time) (time.Time, time.Time) {
	now := time.Now()
	
	if timeRange == dto.TimeRangeCustom && startDate != nil && endDate != nil {
		return *startDate, *endDate
	}
	
	switch timeRange {
	case dto.TimeRangeLastHour:
		return now.Add(-time.Hour), now
	case dto.TimeRangeToday:
		start := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		return start, now
	case dto.TimeRangeYesterday:
		yesterday := now.AddDate(0, 0, -1)
		start := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
		end := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 23, 59, 59, 999999999, yesterday.Location())
		return start, end
	case dto.TimeRangeLast7Days:
		return now.AddDate(0, 0, -7), now
	case dto.TimeRangeLast30Days:
		return now.AddDate(0, 0, -30), now
	case dto.TimeRangeThisMonth:
		start := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		return start, now
	case dto.TimeRangeLastMonth:
		firstOfThisMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		firstOfLastMonth := firstOfThisMonth.AddDate(0, -1, 0)
		lastOfLastMonth := firstOfThisMonth.Add(-time.Nanosecond)
		return firstOfLastMonth, lastOfLastMonth
	default:
		// Default to last 7 days
		return now.AddDate(0, 0, -7), now
	}
}