package handlers

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/services"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// AIChatHandler handles AI chat related HTTP requests
type AIChatHandler struct {
	chatService services.AIChatService
	logger      utils.Logger
}

// NewAIChatHandler creates a new AI chat handler
func NewAIChatHandler(chatService services.AIChatService, logger utils.Logger) *AIChatHandler {
	return &AIChatHandler{
		chatService: chatService,
		logger:      logger,
	}
}

// CreateSession handles POST /api/v1/ai/chat/sessions
func (h *AIChatHandler) CreateSession(c *gin.Context) {
	var req dto.CreateChatSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid request body: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("user context not found")))
		return
	}

	// Convert DTO to model request
	modelReq := &models.CreateChatSessionRequest{
		TenantID:     tenantID.(uint),
		WebsiteID:    req.WebsiteID,
		UserID:       &[]uint{userID.(uint)}[0],
		Title:        req.Title,
		ModelID:      req.ModelID,
		SystemPrompt: req.SystemPrompt,
		Context:      req.Context,
	}

	session, err := h.chatService.CreateSession(c.Request.Context(), modelReq)
	if err != nil {
		h.logger.Errorf("Failed to create chat session: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to create chat session: %w", err)))
		return
	}

	response.CreatedWithContext(c, session)
}

// GetSession handles GET /api/v1/ai/chat/sessions/:id
func (h *AIChatHandler) GetSession(c *gin.Context) {
	sessionID, err := h.parseSessionID(c)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid session ID: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	session, err := h.chatService.GetSession(c.Request.Context(), sessionID, tenantID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to get chat session: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get chat session: %w", err)))
		return
	}

	response.SuccessWithContext(c, session)
}

// UpdateSession handles PUT /api/v1/ai/chat/sessions/:id
func (h *AIChatHandler) UpdateSession(c *gin.Context) {
	sessionID, err := h.parseSessionID(c)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid session ID: "+err.Error()))
		return
	}

	var req dto.UpdateChatSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid request body: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// Convert DTO to model request
	modelReq := &models.UpdateChatSessionRequest{
		Context: req.Context,
	}

	// Handle optional pointer fields
	if req.Title != nil {
		modelReq.Title = *req.Title
	}
	if req.SystemPrompt != nil {
		modelReq.SystemPrompt = req.SystemPrompt
	}
	if req.Status != nil {
		modelReq.Status = *req.Status
	}

	session, err := h.chatService.UpdateSession(c.Request.Context(), sessionID, modelReq, tenantID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to update chat session: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to update chat session: %w", err)))
		return
	}

	response.SuccessWithContext(c, session)
}

// DeleteSession handles DELETE /api/v1/ai/chat/sessions/:id
func (h *AIChatHandler) DeleteSession(c *gin.Context) {
	sessionID, err := h.parseSessionID(c)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid session ID: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	err = h.chatService.DeleteSession(c.Request.Context(), sessionID, tenantID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to delete chat session: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to delete chat session: %w", err)))
		return
	}

	response.SuccessWithContext(c, gin.H{"message": "Chat session deleted successfully"})
}

// ArchiveSession handles POST /api/v1/ai/chat/sessions/:id/archive
func (h *AIChatHandler) ArchiveSession(c *gin.Context) {
	sessionID, err := h.parseSessionID(c)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid session ID: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	err = h.chatService.ArchiveSession(c.Request.Context(), sessionID, tenantID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to archive chat session: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to archive chat session: %w", err)))
		return
	}

	response.SuccessWithContext(c, gin.H{"message": "Chat session archived successfully"})
}

// ListSessions handles GET /api/v1/ai/chat/sessions
func (h *AIChatHandler) ListSessions(c *gin.Context) {
	var filter dto.ChatSessionFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid query parameters: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("user context not found")))
		return
	}

	// Convert DTO filter to model filter
	modelFilter := models.ChatSessionFilter{
		TenantID:       tenantID.(uint),
		WebsiteID:      filter.WebsiteID,
		UserID:         &[]uint{userID.(uint)}[0],
		IncludeDeleted: filter.IncludeDeleted,
	}

	// Handle optional pointer fields
	if filter.Status != nil {
		modelFilter.Status = *filter.Status
	}
	if filter.ModelID != nil {
		modelFilter.ModelID = *filter.ModelID
	}

	// Map search field (different names in DTO vs model)
	modelFilter.Search = filter.SearchQuery

	sessions, err := h.chatService.ListSessions(c.Request.Context(), modelFilter)
	if err != nil {
		h.logger.Errorf("Failed to list chat sessions: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to list chat sessions: %w", err)))
		return
	}

	response.SuccessWithContext(c, sessions)
}

// CreateMessage handles POST /api/v1/ai/chat/messages
func (h *AIChatHandler) CreateMessage(c *gin.Context) {
	var req dto.CreateChatMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid request body: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// Convert DTO to model request
	modelReq := &models.CreateChatMessageRequest{
		SessionID: req.SessionID,
		Role:      req.Role,
		Content:   req.Content,
		Metadata:  req.Metadata,
	}

	// Handle optional pointer field
	if req.TokensUsed != nil {
		modelReq.TokensUsed = *req.TokensUsed
	}

	message, err := h.chatService.CreateMessage(c.Request.Context(), modelReq, tenantID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to create chat message: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to create chat message: %w", err)))
		return
	}

	response.CreatedWithContext(c, message)
}

// GetMessage handles GET /api/v1/ai/chat/messages/:id
func (h *AIChatHandler) GetMessage(c *gin.Context) {
	messageID, err := h.parseMessageID(c)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid message ID: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	message, err := h.chatService.GetMessage(c.Request.Context(), messageID, tenantID.(uint))
	if err != nil {
		h.logger.Errorf("Failed to get chat message: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get chat message: %w", err)))
		return
	}

	response.SuccessWithContext(c, message)
}

// GetConversationHistory handles GET /api/v1/ai/chat/sessions/:id/history
func (h *AIChatHandler) GetConversationHistory(c *gin.Context) {
	sessionID, err := h.parseSessionID(c)
	if err != nil {
		httpmiddleware.AbortWithError(c, utils.BadRequestError("Invalid session ID: "+err.Error()))
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("tenant context not found")))
		return
	}

	// Get limit from query params (optional, default to 100)
	limit := 100
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	history, err := h.chatService.GetConversationHistory(c.Request.Context(), sessionID, tenantID.(uint), limit)
	if err != nil {
		h.logger.Errorf("Failed to get conversation history: %v", err)
		httpmiddleware.AbortWithError(c, utils.InternalError(fmt.Errorf("failed to get conversation history: %w", err)))
		return
	}

	response.SuccessWithContext(c, history)
}

// Helper methods

func (h *AIChatHandler) parseSessionID(c *gin.Context) (uint, error) {
	idStr := c.Param("id")
	if idStr == "" {
		return 0, fmt.Errorf("session ID is required")
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, fmt.Errorf("invalid session ID format")
	}

	return uint(id), nil
}

func (h *AIChatHandler) parseMessageID(c *gin.Context) (uint, error) {
	idStr := c.Param("id")
	if idStr == "" {
		return 0, fmt.Errorf("message ID is required")
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, fmt.Errorf("invalid message ID format")
	}

	return uint(id), nil
}