package models

import (
	"database/sql/driver"
	"time"

	"gorm.io/gorm"
)

// ChatSessionStatus represents the status of a chat session
// @Enum active,archived,deleted
type ChatSessionStatus string

const (
	ChatSessionStatusActive   ChatSessionStatus = "active"
	ChatSessionStatusArchived ChatSessionStatus = "archived"
	ChatSessionStatusDeleted  ChatSessionStatus = "deleted"
)

// Scan implements sql.Scanner interface
func (s *ChatSessionStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = ChatSessionStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s ChatSessionStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// AIChatSession represents an AI chat session
type AIChatSession struct {
	ID           uint              `json:"id" gorm:"primaryKey"`
	TenantID     uint              `json:"tenant_id" gorm:"not null;index"`
	WebsiteID    *uint             `json:"website_id,omitempty" gorm:"index"`
	UserID       *uint             `json:"user_id,omitempty" gorm:"index"`
	Title        string            `json:"title" gorm:"not null" validate:"required,min=1,max=255"`
	ModelID      uint              `json:"model_id" gorm:"not null"`
	Context      JSONMap           `json:"context,omitempty" gorm:"type:json"`
	SystemPrompt *string           `json:"system_prompt,omitempty" gorm:"type:text"`
	Status       ChatSessionStatus `json:"status" gorm:"type:enum('active','archived','deleted');default:'active';not null"`
	MessageCount int               `json:"message_count" gorm:"default:0"`
	CreatedAt    time.Time         `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time         `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (loaded separately to avoid circular references)
	// Tenant   *Tenant      `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	// Website  *Website     `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User     *User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
	// Model    *AIModel     `json:"model,omitempty" gorm:"foreignKey:ModelID"`
	// Messages *[]AIChatMessage `json:"messages,omitempty" gorm:"foreignKey:SessionID"`
}

// TableName specifies the table name for AIChatSession
func (AIChatSession) TableName() string {
	return "ai_chat_sessions"
}

// IsActive checks if session is active
func (s *AIChatSession) IsActive() bool {
	return s.Status == ChatSessionStatusActive
}

// IsArchived checks if session is archived
func (s *AIChatSession) IsArchived() bool {
	return s.Status == ChatSessionStatusArchived
}

// IsDeleted checks if session is soft deleted
func (s *AIChatSession) IsDeleted() bool {
	return s.Status == ChatSessionStatusDeleted
}

// BeforeCreate hook for AIChatSession
func (s *AIChatSession) BeforeCreate(tx *gorm.DB) error {
	if s.Context == nil {
		s.Context = make(JSONMap)
	}
	return nil
}

// Archive marks the session as archived
func (s *AIChatSession) Archive() {
	s.Status = ChatSessionStatusArchived
}

// Delete marks the session as soft deleted
func (s *AIChatSession) Delete() {
	s.Status = ChatSessionStatusDeleted
}

// IncrementMessageCount increments the message count
func (s *AIChatSession) IncrementMessageCount() {
	s.MessageCount++
}

// UpdateContext updates the session context
func (s *AIChatSession) UpdateContext(context JSONMap) {
	s.Context = context
}

// GetContextValue gets a value from the session context
func (s *AIChatSession) GetContextValue(key string) interface{} {
	if s.Context == nil {
		return nil
	}
	return s.Context[key]
}

// SetContextValue sets a value in the session context
func (s *AIChatSession) SetContextValue(key string, value interface{}) {
	if s.Context == nil {
		s.Context = make(JSONMap)
	}
	s.Context[key] = value
}

// ChatSessionFilter represents filters for querying chat sessions
type ChatSessionFilter struct {
	TenantID       uint              `json:"tenant_id,omitempty"`
	WebsiteID      *uint             `json:"website_id,omitempty"`
	UserID         *uint             `json:"user_id,omitempty"`
	Status         ChatSessionStatus `json:"status,omitempty"`
	ModelID        uint              `json:"model_id,omitempty"`
	Search         string            `json:"search,omitempty"`
	IncludeDeleted bool              `json:"include_deleted,omitempty"`
	Page           int               `json:"page,omitempty"`
	PageSize       int               `json:"page_size,omitempty"`
	SortBy         string            `json:"sort_by,omitempty"`
	SortOrder      string            `json:"sort_order,omitempty"`
}

// CreateChatSessionRequest represents the request to create a chat session
type CreateChatSessionRequest struct {
	TenantID     uint    `json:"tenant_id" validate:"required,min=1"`
	WebsiteID    *uint   `json:"website_id,omitempty" validate:"omitempty,min=1"`
	UserID       *uint   `json:"user_id,omitempty" validate:"omitempty,min=1"`
	Title        string  `json:"title" validate:"required,min=1,max=255"`
	ModelID      uint    `json:"model_id" validate:"required,min=1"`
	SystemPrompt *string `json:"system_prompt,omitempty" validate:"omitempty,max=10000"`
	Context      JSONMap `json:"context,omitempty"`
}

// UpdateChatSessionRequest represents the request to update a chat session
type UpdateChatSessionRequest struct {
	Title        string            `json:"title,omitempty" validate:"omitempty,min=1,max=255"`
	SystemPrompt *string           `json:"system_prompt,omitempty" validate:"omitempty,max=10000"`
	Status       ChatSessionStatus `json:"status,omitempty" validate:"omitempty,oneof=active archived"`
	Context      JSONMap           `json:"context,omitempty"`
}

// ChatSessionResponse represents the chat session response
type ChatSessionResponse struct {
	ID           uint              `json:"id"`
	TenantID     uint              `json:"tenant_id"`
	WebsiteID    *uint             `json:"website_id,omitempty"`
	UserID       *uint             `json:"user_id,omitempty"`
	Title        string            `json:"title"`
	ModelID      uint              `json:"model_id"`
	Context      JSONMap           `json:"context,omitempty"`
	SystemPrompt *string           `json:"system_prompt,omitempty"`
	Status       ChatSessionStatus `json:"status"`
	MessageCount int               `json:"message_count"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// ToResponse converts an AIChatSession to ChatSessionResponse
func (s *AIChatSession) ToResponse() *ChatSessionResponse {
	return &ChatSessionResponse{
		ID:           s.ID,
		TenantID:     s.TenantID,
		WebsiteID:    s.WebsiteID,
		UserID:       s.UserID,
		Title:        s.Title,
		ModelID:      s.ModelID,
		Context:      s.Context,
		SystemPrompt: s.SystemPrompt,
		Status:       s.Status,
		MessageCount: s.MessageCount,
		CreatedAt:    s.CreatedAt,
		UpdatedAt:    s.UpdatedAt,
	}
}

// ListChatSessionsResponse represents the response for listing chat sessions
type ListChatSessionsResponse struct {
	Sessions   []ChatSessionResponse `json:"sessions"`
	TotalCount int64                 `json:"total_count"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
}
