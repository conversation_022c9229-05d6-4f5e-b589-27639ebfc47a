package mysql

import (
	"context"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/database/scopes"
	"gorm.io/gorm"
)

// aiChatMessageRepository implements AIChatMessageRepository for MySQL
type aiChatMessageRepository struct {
	db *gorm.DB
}

// NewAIChatMessageRepository creates a new AIChatMessageRepository
func NewAIChatMessageRepository(db *gorm.DB) repositories.AIChatMessageRepository {
	return &aiChatMessageRepository{db: db}
}

// CreateMessage creates a new chat message
func (r *aiChatMessageRepository) CreateMessage(ctx context.Context, tenantID, websiteID uint, message *models.AIChatMessage) error {
	message.TenantID = tenantID
	message.WebsiteID = websiteID
	return r.db.WithContext(ctx).Create(message).Error
}

// GetMessageByID retrieves a chat message by ID
func (r *aiChatMessageRepository) GetMessageByID(ctx context.Context, tenantID, websiteID, id uint) (*models.AIChatMessage, error) {
	var message models.AIChatMessage
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// UpdateMessage updates an existing chat message
func (r *aiChatMessageRepository) UpdateMessage(ctx context.Context, tenantID, websiteID, id uint, message *models.AIChatMessage) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Omit("tenant_id", "website_id", "id", "created_at").
		Updates(message).Error
}

// DeleteMessage deletes a chat message
func (r *aiChatMessageRepository) DeleteMessage(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		Delete(&models.AIChatMessage{}).Error
}

// ListMessages lists chat messages with filtering
func (r *aiChatMessageRepository) ListMessages(ctx context.Context, tenantID, websiteID uint, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error) {
	var messages []models.AIChatMessage
	var total int64

	query := r.db.WithContext(ctx).
		Model(&models.AIChatMessage{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID))

	// Apply filters
	query = r.applyMessageFilters(query, filter)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	query = r.applyMessagePaginationAndSorting(query, filter)

	err := query.Find(&messages).Error
	return messages, total, err
}

// GetMessagesBySession retrieves messages for a specific session
func (r *aiChatMessageRepository) GetMessagesBySession(ctx context.Context, tenantID, websiteID, sessionID uint, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error) {
	filter.SessionID = sessionID
	return r.ListMessages(ctx, tenantID, websiteID, filter)
}

// GetMessagesByUser retrieves messages for a specific user through sessions
func (r *aiChatMessageRepository) GetMessagesByUser(ctx context.Context, tenantID, websiteID, userID uint, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error) {
	var messages []models.AIChatMessage
	var total int64

	query := r.db.WithContext(ctx).Model(&models.AIChatMessage{}).
		Joins("JOIN ai_chat_sessions ON ai_chat_messages.session_id = ai_chat_sessions.id").
		Where("ai_chat_sessions.tenant_id = ? AND ai_chat_sessions.website_id = ? AND ai_chat_sessions.user_id = ? AND ai_chat_sessions.status != ?", tenantID, websiteID, userID, models.ChatSessionStatusDeleted).
		Where("ai_chat_messages.tenant_id = ? AND ai_chat_messages.website_id = ?", tenantID, websiteID)

	// Apply filters
	query = r.applyMessageFilters(query, filter)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	query = r.applyMessagePaginationAndSorting(query, filter)

	err := query.Find(&messages).Error
	return messages, total, err
}

// GetMessageStats retrieves statistics for messages in a session
func (r *aiChatMessageRepository) GetMessageStats(ctx context.Context, tenantID, websiteID, sessionID uint) (*models.MessageStats, error) {
	var stats models.MessageStats

	err := r.db.WithContext(ctx).Model(&models.AIChatMessage{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Select(`
			COUNT(*) as total_messages,
			SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as user_messages,
			SUM(CASE WHEN role = 'assistant' THEN 1 ELSE 0 END) as assistant_messages,
			SUM(CASE WHEN role = 'system' THEN 1 ELSE 0 END) as system_messages,
			SUM(tokens_used) as total_tokens,
			AVG(tokens_used) as average_tokens
		`).
		Where("session_id = ?", sessionID).
		Scan(&stats).Error

	return &stats, err
}

// GetMessageStatsForTenant retrieves statistics for messages in a tenant
func (r *aiChatMessageRepository) GetMessageStatsForTenant(ctx context.Context, tenantID, websiteID uint) (*models.MessageStats, error) {
	var stats models.MessageStats

	query := r.db.WithContext(ctx).Model(&models.AIChatMessage{}).
		Select(`
			COUNT(*) as total_messages,
			SUM(CASE WHEN ai_chat_messages.role = 'user' THEN 1 ELSE 0 END) as user_messages,
			SUM(CASE WHEN ai_chat_messages.role = 'assistant' THEN 1 ELSE 0 END) as assistant_messages,
			SUM(CASE WHEN ai_chat_messages.role = 'system' THEN 1 ELSE 0 END) as system_messages,
			SUM(ai_chat_messages.tokens_used) as total_tokens,
			AVG(ai_chat_messages.tokens_used) as average_tokens
		`).
		Joins("JOIN ai_chat_sessions ON ai_chat_messages.session_id = ai_chat_sessions.id").
		Where("ai_chat_sessions.tenant_id = ? AND ai_chat_sessions.website_id = ? AND ai_chat_sessions.status != ?", tenantID, websiteID, models.ChatSessionStatusDeleted).
		Where("ai_chat_messages.tenant_id = ? AND ai_chat_messages.website_id = ?", tenantID, websiteID)

	err := query.Scan(&stats).Error
	return &stats, err
}

// GetTokenUsageStats retrieves token usage statistics for a session
func (r *aiChatMessageRepository) GetTokenUsageStats(ctx context.Context, tenantID, websiteID, sessionID uint) (int64, error) {
	var totalTokens int64

	err := r.db.WithContext(ctx).Model(&models.AIChatMessage{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Select("SUM(tokens_used)").
		Where("session_id = ?", sessionID).
		Scan(&totalTokens).Error

	return totalTokens, err
}

// SearchMessages searches for messages based on content
func (r *aiChatMessageRepository) SearchMessages(ctx context.Context, tenantID, websiteID uint, request models.SearchChatMessagesRequest) ([]models.AIChatMessage, int64, error) {
	var messages []models.AIChatMessage
	var total int64

	query := r.db.WithContext(ctx).Model(&models.AIChatMessage{}).
		Joins("JOIN ai_chat_sessions ON ai_chat_messages.session_id = ai_chat_sessions.id").
		Where("ai_chat_sessions.tenant_id = ? AND ai_chat_sessions.website_id = ? AND ai_chat_sessions.status != ?", tenantID, websiteID, models.ChatSessionStatusDeleted).
		Where("ai_chat_messages.tenant_id = ? AND ai_chat_messages.website_id = ?", tenantID, websiteID)

	// Apply search filters
	query = r.applySearchFilters(query, request)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	page := request.Page
	if page <= 0 {
		page = 1
	}
	pageSize := request.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	query = query.Offset(offset).Limit(pageSize).Order("ai_chat_messages.created_at desc")

	err := query.Find(&messages).Error
	return messages, total, err
}

// GetConversationHistory retrieves conversation history for a session
func (r *aiChatMessageRepository) GetConversationHistory(ctx context.Context, tenantID, websiteID, sessionID uint, limit int) ([]models.AIChatMessage, error) {
	var messages []models.AIChatMessage

	if limit <= 0 {
		limit = 50
	}

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("session_id = ?", sessionID).
		Order("created_at asc").
		Limit(limit).
		Find(&messages).Error

	return messages, err
}

// BulkCreateMessages creates multiple messages in a single transaction
func (r *aiChatMessageRepository) BulkCreateMessages(ctx context.Context, tenantID, websiteID uint, messages []models.AIChatMessage) error {
	if len(messages) == 0 {
		return nil
	}

	// Set tenant and website IDs for all messages
	for i := range messages {
		messages[i].TenantID = tenantID
		messages[i].WebsiteID = websiteID
	}

	return r.db.WithContext(ctx).CreateInBatches(messages, 100).Error
}

// BulkDeleteMessages deletes multiple messages
func (r *aiChatMessageRepository) BulkDeleteMessages(ctx context.Context, tenantID, websiteID uint, messageIDs []uint) error {
	if len(messageIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id IN ?", messageIDs).
		Delete(&models.AIChatMessage{}).Error
}

// DeleteMessagesBySession deletes all messages for a session
func (r *aiChatMessageRepository) DeleteMessagesBySession(ctx context.Context, tenantID, websiteID, sessionID uint) error {
	return r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("session_id = ?", sessionID).
		Delete(&models.AIChatMessage{}).Error
}

// ExportMessages exports messages for a session based on filters
func (r *aiChatMessageRepository) ExportMessages(ctx context.Context, tenantID, websiteID uint, request models.ExportChatMessagesRequest) ([]models.AIChatMessage, error) {
	var messages []models.AIChatMessage

	query := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("session_id = ?", request.SessionID)

	// Apply role filter
	if request.Role != "" {
		query = query.Where("role = ?", request.Role)
	}

	if len(request.Roles) > 0 {
		query = query.Where("role IN ?", request.Roles)
	}

	// Apply time range filter
	if request.From != nil {
		query = query.Where("created_at >= ?", request.From)
	}

	if request.To != nil {
		query = query.Where("created_at <= ?", request.To)
	}

	err := query.Order("created_at asc").Find(&messages).Error
	return messages, err
}

// GetContextWindowMessages retrieves messages within a token limit for context window
func (r *aiChatMessageRepository) GetContextWindowMessages(ctx context.Context, tenantID, websiteID, sessionID uint, maxTokens int) ([]models.AIChatMessage, error) {
	var messages []models.AIChatMessage

	// Get messages in reverse order and accumulate tokens
	var allMessages []models.AIChatMessage
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("session_id = ?", sessionID).
		Order("created_at desc").
		Find(&allMessages).Error

	if err != nil {
		return nil, err
	}

	// Accumulate messages until we reach the token limit
	totalTokens := 0
	for _, message := range allMessages {
		if totalTokens+message.TokensUsed > maxTokens && len(messages) > 0 {
			break
		}
		messages = append(messages, message)
		totalTokens += message.TokensUsed
	}

	// Reverse the order to get chronological order
	for i := len(messages)/2 - 1; i >= 0; i-- {
		opp := len(messages) - 1 - i
		messages[i], messages[opp] = messages[opp], messages[i]
	}

	return messages, nil
}

// GetLatestMessages retrieves the latest messages for a session
func (r *aiChatMessageRepository) GetLatestMessages(ctx context.Context, tenantID, websiteID, sessionID uint, limit int) ([]models.AIChatMessage, error) {
	var messages []models.AIChatMessage

	if limit <= 0 {
		limit = 10
	}

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("session_id = ?", sessionID).
		Order("created_at desc").
		Limit(limit).
		Find(&messages).Error

	// Reverse to get chronological order
	for i := len(messages)/2 - 1; i >= 0; i-- {
		opp := len(messages) - 1 - i
		messages[i], messages[opp] = messages[opp], messages[i]
	}

	return messages, err
}

// applyMessageFilters applies filters to the message query
func (r *aiChatMessageRepository) applyMessageFilters(query *gorm.DB, filter models.ChatMessageFilter) *gorm.DB {
	if filter.SessionID != 0 {
		query = query.Where("session_id = ?", filter.SessionID)
	}

	if filter.Role != "" {
		query = query.Where("role = ?", filter.Role)
	}

	if len(filter.Roles) > 0 {
		query = query.Where("role IN ?", filter.Roles)
	}

	if filter.Search != "" {
		query = query.Where("content LIKE ?", "%"+filter.Search+"%")
	}

	if filter.MinTokens > 0 {
		query = query.Where("tokens_used >= ?", filter.MinTokens)
	}

	if filter.MaxTokens > 0 {
		query = query.Where("tokens_used <= ?", filter.MaxTokens)
	}

	if filter.From != nil {
		query = query.Where("created_at >= ?", filter.From)
	}

	if filter.To != nil {
		query = query.Where("created_at <= ?", filter.To)
	}

	return query
}

// applyMessagePaginationAndSorting applies pagination and sorting to the message query
func (r *aiChatMessageRepository) applyMessagePaginationAndSorting(query *gorm.DB, filter models.ChatMessageFilter) *gorm.DB {
	// Apply sorting
	sortBy := "created_at"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}

	sortOrder := "asc"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	return query
}

// applySearchFilters applies search-specific filters
func (r *aiChatMessageRepository) applySearchFilters(query *gorm.DB, request models.SearchChatMessagesRequest) *gorm.DB {
	// Full-text search on content
	if request.Query != "" {
		query = query.Where("ai_chat_messages.content LIKE ?", "%"+request.Query+"%")
	}

	// Website filter
	if request.WebsiteID != nil {
		query = query.Where("ai_chat_sessions.website_id = ?", *request.WebsiteID)
	}

	// User filter
	if request.UserID != nil {
		query = query.Where("ai_chat_sessions.user_id = ?", *request.UserID)
	}

	// Session filter
	if request.SessionID != nil {
		query = query.Where("ai_chat_messages.session_id = ?", *request.SessionID)
	}

	// Role filter
	if request.Role != "" {
		query = query.Where("ai_chat_messages.role = ?", request.Role)
	}

	if len(request.Roles) > 0 {
		query = query.Where("ai_chat_messages.role IN ?", request.Roles)
	}

	// Time range filter
	if request.From != nil {
		query = query.Where("ai_chat_messages.created_at >= ?", request.From)
	}

	if request.To != nil {
		query = query.Where("ai_chat_messages.created_at <= ?", request.To)
	}

	return query
}
