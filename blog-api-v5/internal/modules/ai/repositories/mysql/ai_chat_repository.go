package mysql

import (
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/repositories"
	"gorm.io/gorm"
)

// aiChatRepository combines both session and message repositories
type aiChatRepository struct {
	repositories.AIChatSessionRepository
	repositories.AIChatMessageRepository
}

// NewAIChatRepository creates a new combined AI chat repository
func NewAIChatRepository(db *gorm.DB) repositories.AIChatRepository {
	return &aiChatRepository{
		AIChatSessionRepository: NewAIChatSessionRepository(db),
		AIChatMessageRepository: NewAIChatMessageRepository(db),
	}
}
