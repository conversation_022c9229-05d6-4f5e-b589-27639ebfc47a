package mysql

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/database/scopes"
	"gorm.io/gorm"
)

// aiChatSessionRepository implements AIChatSessionRepository for MySQL
type aiChatSessionRepository struct {
	db *gorm.DB
}

// NewAIChatSessionRepository creates a new AIChatSessionRepository
func NewAIChatSessionRepository(db *gorm.DB) repositories.AIChatSessionRepository {
	return &aiChatSessionRepository{db: db}
}

// CreateSession creates a new chat session
func (r *aiChatSessionRepository) CreateSession(ctx context.Context, tenantID, websiteID uint, session *models.AIChatSession) error {
	session.TenantID = tenantID
	session.WebsiteID = websiteID
	return r.db.WithContext(ctx).Create(session).Error
}

// GetSessionByID retrieves a chat session by ID
func (r *aiChatSessionRepository) GetSessionByID(ctx context.Context, tenantID, websiteID, id uint) (*models.AIChatSession, error) {
	var session models.AIChatSession
	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ? AND status != ?", id, models.ChatSessionStatusDeleted).
		First(&session).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

// UpdateSession updates an existing chat session
func (r *aiChatSessionRepository) UpdateSession(ctx context.Context, tenantID, websiteID, id uint, session *models.AIChatSession) error {
	return r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		Omit("tenant_id", "website_id", "id", "created_at").
		Updates(session).Error
}

// DeleteSession soft deletes a chat session
func (r *aiChatSessionRepository) DeleteSession(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		Update("status", models.ChatSessionStatusDeleted).Error
}

// ArchiveSession archives a chat session
func (r *aiChatSessionRepository) ArchiveSession(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", id).
		Update("status", models.ChatSessionStatusArchived).Error
}

// ListSessions lists chat sessions with filtering
func (r *aiChatSessionRepository) ListSessions(ctx context.Context, tenantID, websiteID uint, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error) {
	var sessions []models.AIChatSession
	var total int64

	query := r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID))

	// Apply filters
	query = r.applySessionFilters(query, filter)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	query = r.applyPaginationAndSorting(query, filter)

	err := query.Find(&sessions).Error
	return sessions, total, err
}

// GetSessionsByUser retrieves chat sessions for a specific user
func (r *aiChatSessionRepository) GetSessionsByUser(ctx context.Context, tenantID, websiteID, userID uint, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error) {
	filter.UserID = &userID
	return r.ListSessions(ctx, tenantID, websiteID, filter)
}

// GetSessionStats retrieves statistics for chat sessions
func (r *aiChatSessionRepository) GetSessionStats(ctx context.Context, tenantID, websiteID uint) (*models.MessageStats, error) {
	var stats models.MessageStats

	query := r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Select("COUNT(*) as total_messages, SUM(message_count) as total_tokens").
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("status != ?", models.ChatSessionStatusDeleted)

	err := query.Scan(&stats).Error
	return &stats, err
}

// GetActiveSessionsCount retrieves the count of active sessions
func (r *aiChatSessionRepository) GetActiveSessionsCount(ctx context.Context, tenantID, websiteID uint) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("status = ?", models.ChatSessionStatusActive)

	err := query.Count(&count).Error
	return count, err
}

// UpdateSessionContext updates the context of a chat session
func (r *aiChatSessionRepository) UpdateSessionContext(ctx context.Context, tenantID, websiteID, sessionID uint, context models.JSONMap) error {
	return r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", sessionID).
		Update("context", context).Error
}

// GetSessionContext retrieves the context of a chat session
func (r *aiChatSessionRepository) GetSessionContext(ctx context.Context, tenantID, websiteID, sessionID uint) (models.JSONMap, error) {
	var session models.AIChatSession
	err := r.db.WithContext(ctx).Select("context").
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id = ?", sessionID).
		First(&session).Error
	if err != nil {
		return nil, err
	}
	return session.Context, nil
}

// GetExpiredSessions retrieves sessions that are older than the specified timestamp
func (r *aiChatSessionRepository) GetExpiredSessions(ctx context.Context, tenantID, websiteID uint, olderThan int64) ([]models.AIChatSession, error) {
	var sessions []models.AIChatSession
	expiredTime := time.Unix(olderThan, 0)

	err := r.db.WithContext(ctx).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("updated_at < ? AND status = ?", expiredTime, models.ChatSessionStatusActive).
		Find(&sessions).Error
	return sessions, err
}

// CleanupExpiredSessions archives sessions that are older than the specified timestamp
func (r *aiChatSessionRepository) CleanupExpiredSessions(ctx context.Context, tenantID, websiteID uint, olderThan int64) error {
	expiredTime := time.Unix(olderThan, 0)

	return r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("updated_at < ? AND status = ?", expiredTime, models.ChatSessionStatusActive).
		Update("status", models.ChatSessionStatusArchived).Error
}

// BulkArchiveSessions archives multiple sessions
func (r *aiChatSessionRepository) BulkArchiveSessions(ctx context.Context, tenantID, websiteID uint, sessionIDs []uint) error {
	if len(sessionIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id IN ?", sessionIDs).
		Update("status", models.ChatSessionStatusArchived).Error
}

// BulkDeleteSessions soft deletes multiple sessions
func (r *aiChatSessionRepository) BulkDeleteSessions(ctx context.Context, tenantID, websiteID uint, sessionIDs []uint) error {
	if len(sessionIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Model(&models.AIChatSession{}).
		Scopes(scopes.TenantWebsiteScope(ctx, tenantID, websiteID)).
		Where("id IN ?", sessionIDs).
		Update("status", models.ChatSessionStatusDeleted).Error
}

// applySessionFilters applies filters to the query
func (r *aiChatSessionRepository) applySessionFilters(query *gorm.DB, filter models.ChatSessionFilter) *gorm.DB {
	// TenantID and WebsiteID are already applied by TenantWebsiteScope

	if filter.UserID != nil {
		query = query.Where("user_id = ?", *filter.UserID)
	}

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	} else if !filter.IncludeDeleted {
		query = query.Where("status != ?", models.ChatSessionStatusDeleted)
	}

	if filter.ModelID != 0 {
		query = query.Where("model_id = ?", filter.ModelID)
	}

	if filter.Search != "" {
		query = query.Where("title LIKE ?", "%"+filter.Search+"%")
	}

	return query
}

// applyPaginationAndSorting applies pagination and sorting to the query
func (r *aiChatSessionRepository) applyPaginationAndSorting(query *gorm.DB, filter models.ChatSessionFilter) *gorm.DB {
	// Apply sorting
	sortBy := "updated_at"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}

	sortOrder := "desc"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	return query
}

// calculateTotalPages calculates the total number of pages
func calculateTotalPages(totalCount int64, pageSize int) int {
	if pageSize <= 0 {
		return 1
	}
	return int(math.Ceil(float64(totalCount) / float64(pageSize)))
}
