package ai

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// RegisterRoutes registers all AI module routes
func RegisterRoutes(router *gin.Engine, db *gorm.DB, logger utils.Logger) {
	// Initialize AI request repositories
	aiRequestRepo := mysql.NewAIRequestRepository(db)

	// Initialize AI Provider Service (real implementation)
	aiProvider := services.NewAIProviderService(
		"", // OpenAI API Key - set from environment
		"", // Claude API Key - set from environment  
		"", // Gemini API Key - set from environment
		true, // Enable rate limiting
		true, // Enable PII detection
		100,  // Max requests per minute
		4000, // Max tokens per request
	)

	// Initialize AI request services with real AI provider
	aiRequestService := services.NewAIRequestService(
		aiRequestRepo,
		logger,
		aiProvider, // Rate limiter
		aiProvider, // PII detector
		aiProvider, // Cost calculator
		aiProvider, // Security service
		aiProvider, // Queue service
	)

	// Initialize AI request handlers
	aiRequestHandler := handlers.NewAIRequestHandler(aiRequestService, logger)

	// Initialize chat repositories and services
	chatRepo := mysql.NewAIChatRepository(db)
	chatService := services.NewAIChatService(
		chatRepo,
		logger,
		&services.AIChatConfig{
			MaxSessionsPerUser:    100,
			MaxMessagesPerSession: 1000,
			MaxMessageLength:      10000,
			MaxTokensPerMessage:   4000,
			MaxTokensPerSession:   50000,
			ContextWindowTokens:   8000,
			SessionTimeoutHours:   24,
			EnableContentFilter:   true,
			EnablePIIDetection:    true,
			EnableRateLimiting:    true,
			DefaultSystemPrompt:   "You are a helpful AI assistant.",
		},
	)

	// Initialize chat handler
	chatHandler := handlers.NewAIChatHandler(chatService, logger)

	// Initialize analytics handler
	analyticsHandler := handlers.NewAIAnalyticsHandler(chatService, aiRequestService, logger)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// AI routes group
		aiRoutes := v1.Group("/ai")
		{
			// Protected routes (require authentication and tenant context)
			protectedRoutes := aiRoutes.Group("")
			protectedRoutes.Use(middleware.JWTAuthMiddleware(nil)) // JWT service will be injected by main router
			protectedRoutes.Use(middleware.RequireAuthentication())
			protectedRoutes.Use(middleware.TenantContextMiddleware())

			// AI Request routes - Full implementation from dev branch
			requestRoutes := protectedRoutes.Group("/requests")
			{
				// Basic CRUD operations
				requestRoutes.POST("", aiRequestHandler.CreateRequest)
				requestRoutes.GET("/:id", aiRequestHandler.GetRequest)
				requestRoutes.PUT("/:id", aiRequestHandler.UpdateRequest)
				requestRoutes.DELETE("/:id", aiRequestHandler.DeleteRequest)
				requestRoutes.GET("", aiRequestHandler.ListRequests)

				// Processing operations
				requestRoutes.POST("/:id/process", aiRequestHandler.ProcessRequest)
				requestRoutes.POST("/:id/retry", aiRequestHandler.RetryRequest)
				requestRoutes.POST("/:id/cancel", aiRequestHandler.CancelRequest)
				requestRoutes.POST("/process-pending", aiRequestHandler.ProcessPendingRequests)

				// Search operations
				requestRoutes.GET("/search", aiRequestHandler.SearchRequests)

				// Batch operations
				requestRoutes.POST("/batch", aiRequestHandler.BulkCreateRequests)
				requestRoutes.PUT("/batch/status", aiRequestHandler.BulkUpdateStatus)
				requestRoutes.POST("/batch/process", aiRequestHandler.BulkProcessRequests)
			}

			// Chat routes group - Full implementation
			chatRoutes := protectedRoutes.Group("/chat")
			{
				// Session routes
				sessionRoutes := chatRoutes.Group("/sessions")
				{
					sessionRoutes.POST("", chatHandler.CreateSession)
					sessionRoutes.GET("/:id", chatHandler.GetSession)
					sessionRoutes.PUT("/:id", chatHandler.UpdateSession)
					sessionRoutes.DELETE("/:id", chatHandler.DeleteSession)
					sessionRoutes.POST("/:id/archive", chatHandler.ArchiveSession)
					sessionRoutes.GET("", chatHandler.ListSessions)
					sessionRoutes.GET("/:id/history", chatHandler.GetConversationHistory)
				}

				// Message routes
				messageRoutes := chatRoutes.Group("/messages")
				{
					messageRoutes.POST("", chatHandler.CreateMessage)
					messageRoutes.GET("/:id", chatHandler.GetMessage)
				}

				// Analytics routes
				analyticsRoutes := chatRoutes.Group("/analytics")
				{
					analyticsRoutes.GET("/overall", analyticsHandler.GetOverallAnalytics)
					analyticsRoutes.GET("/sessions/stats", analyticsHandler.GetSessionStats)
					analyticsRoutes.GET("/messages/stats", analyticsHandler.GetMessageStats)
					analyticsRoutes.GET("/tokens/usage", analyticsHandler.GetTokenUsageStats)
					analyticsRoutes.GET("/sessions/active", analyticsHandler.GetActiveSessionsCount)
					analyticsRoutes.GET("/users/:id", analyticsHandler.GetUserAnalytics)
					analyticsRoutes.POST("/export", analyticsHandler.ExportAnalytics)
				}
			}

			// Analytics and reporting routes
			protectedRoutes.GET("/analytics", aiRequestHandler.GetAnalytics)
			protectedRoutes.GET("/usage-stats", aiRequestHandler.GetUsageStats)
			protectedRoutes.GET("/performance-metrics", aiRequestHandler.GetPerformanceMetrics)

			// Rate limiting information
			protectedRoutes.GET("/rate-limits", aiRequestHandler.GetRateLimitInfo)

			// Queue operations
			protectedRoutes.POST("/queue", aiRequestHandler.QueueRequest)
			protectedRoutes.GET("/queue/status", aiRequestHandler.GetQueueStatus)

			// Cost tracking
			protectedRoutes.GET("/cost-breakdown", aiRequestHandler.GetCostBreakdown)

			// Export operations
			protectedRoutes.POST("/export", aiRequestHandler.ExportRequests)

			// Cleanup operations
			protectedRoutes.POST("/cleanup", aiRequestHandler.CleanupOldRequests)

			// Public routes (no authentication required)
			// Health and monitoring
			aiRoutes.GET("/health", func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{
					"module":  "ai",
					"status":  "healthy",
					"version": "1.0.0",
					"checks": gin.H{
						"database":           db != nil,
						"chat_service":       chatService != nil,
						"chat_repo":          chatRepo != nil,
						"ai_request_service": aiRequestService != nil,
						"ai_request_repo":    aiRequestRepo != nil,
					},
				})
			})
			protectedRoutes.GET("/metrics", aiRequestHandler.GetSystemMetrics)
		}
	}
}

// GetAIChatServiceDependencies returns the AI chat service dependencies for external use
func GetAIChatServiceDependencies(db *gorm.DB, logger utils.Logger) (services.AIChatService, error) {
	// Initialize repository
	chatRepo := mysql.NewAIChatRepository(db)

	// Initialize service
	chatService := services.NewAIChatService(
		chatRepo,
		logger,
		&services.AIChatConfig{
			MaxSessionsPerUser:    100,
			MaxMessagesPerSession: 1000,
			MaxMessageLength:      10000,
			MaxTokensPerMessage:   4000,
			MaxTokensPerSession:   50000,
			ContextWindowTokens:   8000,
			SessionTimeoutHours:   24,
			EnableContentFilter:   true,
			EnablePIIDetection:    true,
			EnableRateLimiting:    true,
			DefaultSystemPrompt:   "You are a helpful AI assistant.",
		},
	)

	return chatService, nil
}

// Mock implementations for external dependencies
// These would be replaced with real implementations in production

type mockRateLimiter struct{}

func (m *mockRateLimiter) CheckLimit(ctx context.Context, input services.RateLimitInput) (*services.RateLimitResult, error) {
	return &services.RateLimitResult{Exceeded: false}, nil
}

func (m *mockRateLimiter) GetLimitInfo(ctx context.Context, input services.RateLimitInput) (*services.RateLimitInfo, error) {
	return &services.RateLimitInfo{
		TenantID:          input.TenantID,
		WebsiteID:         input.WebsiteID,
		UserID:            input.UserID,
		RequestsRemaining: 1000,
		TokensRemaining:   100000,
		ResetTime:         time.Now().Add(time.Hour),
	}, nil
}

type mockPIIDetector struct{}

func (m *mockPIIDetector) DetectPII(ctx context.Context, text string) (services.PIIDetectionResult, error) {
	return services.PIIDetectionResult{
		HasPII:       false,
		Types:        []string{},
		RedactedText: text,
	}, nil
}

type mockCostCalculator struct{}

func (m *mockCostCalculator) CalculateCost(ctx context.Context, modelID uint, tokensUsed int) (int, error) {
	// Mock cost calculation: $0.001 per token
	return tokensUsed / 10, nil
}

type mockSecurityService struct{}

func (m *mockSecurityService) ValidateAccess(ctx context.Context, userID uint, requestID uint) error {
	return nil
}

func (m *mockSecurityService) SanitizePrompt(ctx context.Context, prompt string) (string, error) {
	// Mock sanitization - just return the original prompt
	return prompt, nil
}

type mockQueueService struct{}

func (m *mockQueueService) QueueRequest(ctx context.Context, requestID uint) error {
	return nil
}

func (m *mockQueueService) GetQueueStatus(ctx context.Context, tenantID uint) (*services.QueueStatus, error) {
	return &services.QueueStatus{
		TenantID:        tenantID,
		PendingCount:    10,
		ProcessingCount: 5,
		CompletedCount:  100,
		FailedCount:     2,
	}, nil
}
