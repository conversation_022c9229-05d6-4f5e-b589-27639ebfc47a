package services

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

var (
	// Session errors
	ErrSessionNotFound      = errors.New("chat session not found")
	ErrSessionInactive      = errors.New("chat session is inactive")
	ErrSessionDeleted       = errors.New("chat session is deleted")
	ErrSessionArchived      = errors.New("chat session is archived")
	ErrInvalidSessionData   = errors.New("invalid session data")
	ErrSessionTitleRequired = errors.New("session title is required")
	ErrSessionLimitExceeded = errors.New("session limit exceeded")

	// Message errors
	ErrMessageNotFound        = errors.New("chat message not found")
	ErrInvalidMessageData     = errors.New("invalid message data")
	ErrMessageContentRequired = errors.New("message content is required")
	ErrInvalidMessageRole     = errors.New("invalid message role")
	ErrMessageTooLong         = errors.New("message content is too long")
	ErrTokenLimitExceeded     = errors.New("token limit exceeded")

	// Permission errors
	ErrUnauthorizedAccess = errors.New("unauthorized access to chat session")
	ErrInvalidTenant      = errors.New("invalid tenant")
	ErrInvalidWebsite     = errors.New("invalid website")
	ErrInvalidUser        = errors.New("invalid user")

	// General errors
	ErrInvalidRequest = errors.New("invalid request")
	ErrInternalError  = errors.New("internal server error")
)

// AIChatService handles AI chat business logic
type AIChatService interface {
	// Session management
	CreateSession(ctx context.Context, req *models.CreateChatSessionRequest) (*models.ChatSessionResponse, error)
	GetSession(ctx context.Context, sessionID uint, tenantID uint) (*models.ChatSessionResponse, error)
	UpdateSession(ctx context.Context, sessionID uint, req *models.UpdateChatSessionRequest, tenantID uint) (*models.ChatSessionResponse, error)
	DeleteSession(ctx context.Context, sessionID uint, tenantID uint) error
	ArchiveSession(ctx context.Context, sessionID uint, tenantID uint) error
	ListSessions(ctx context.Context, filter models.ChatSessionFilter) (*models.ListChatSessionsResponse, error)
	GetSessionsByUser(ctx context.Context, userID uint, tenantID uint, filter models.ChatSessionFilter) (*models.ListChatSessionsResponse, error)
	GetSessionsByWebsite(ctx context.Context, websiteID uint, tenantID uint, filter models.ChatSessionFilter) (*models.ListChatSessionsResponse, error)

	// Message management
	CreateMessage(ctx context.Context, req *models.CreateChatMessageRequest, tenantID uint) (*models.ChatMessageResponse, error)
	GetMessage(ctx context.Context, messageID uint, tenantID uint) (*models.ChatMessageResponse, error)
	UpdateMessage(ctx context.Context, messageID uint, req *models.UpdateChatMessageRequest, tenantID uint) (*models.ChatMessageResponse, error)
	DeleteMessage(ctx context.Context, messageID uint, tenantID uint) error
	ListMessages(ctx context.Context, sessionID uint, tenantID uint, filter models.ChatMessageFilter) (*models.ListChatMessagesResponse, error)
	GetConversationHistory(ctx context.Context, sessionID uint, tenantID uint, limit int) (*models.ChatConversationResponse, error)

	// Bulk operations
	BulkCreateMessages(ctx context.Context, req *models.BulkCreateChatMessagesRequest, tenantID uint) ([]models.ChatMessageResponse, error)
	BulkDeleteMessages(ctx context.Context, messageIDs []uint, tenantID uint) error
	BulkArchiveSessions(ctx context.Context, sessionIDs []uint, tenantID uint) error
	BulkDeleteSessions(ctx context.Context, sessionIDs []uint, tenantID uint) error

	// Search and analytics
	SearchMessages(ctx context.Context, req *models.SearchChatMessagesRequest) (*models.ListChatMessagesResponse, error)
	GetSessionStats(ctx context.Context, tenantID uint, websiteID *uint) (*models.MessageStats, error)
	GetMessageStats(ctx context.Context, sessionID uint, tenantID uint) (*models.MessageStats, error)
	GetTokenUsageStats(ctx context.Context, sessionID uint, tenantID uint) (int64, error)

	// Context management
	UpdateSessionContext(ctx context.Context, sessionID uint, context models.JSONMap, tenantID uint) error
	GetSessionContext(ctx context.Context, sessionID uint, tenantID uint) (models.JSONMap, error)
	GetContextWindowMessages(ctx context.Context, sessionID uint, maxTokens int, tenantID uint) ([]models.ChatMessageResponse, error)

	// Export functionality
	ExportMessages(ctx context.Context, req *models.ExportChatMessagesRequest, tenantID uint) ([]models.ChatMessageResponse, error)

	// Cleanup and maintenance
	CleanupExpiredSessions(ctx context.Context, olderThanDays int) error
	GetActiveSessionsCount(ctx context.Context, tenantID uint, websiteID *uint) (int64, error)
}

// aiChatService implements AIChatService
type aiChatService struct {
	chatRepo repositories.AIChatRepository
	logger   utils.Logger
	config   *AIChatConfig
}

// AIChatConfig holds AI chat service configuration
type AIChatConfig struct {
	MaxSessionsPerUser    int
	MaxMessagesPerSession int
	MaxMessageLength      int
	MaxTokensPerMessage   int
	MaxTokensPerSession   int
	ContextWindowTokens   int
	SessionTimeoutHours   int
	EnableContentFilter   bool
	EnablePIIDetection    bool
	EnableRateLimiting    bool
	DefaultSystemPrompt   string
}

// NewAIChatService creates a new AI chat service
func NewAIChatService(
	chatRepo repositories.AIChatRepository,
	logger utils.Logger,
	config *AIChatConfig,
) AIChatService {
	if config == nil {
		config = &AIChatConfig{
			MaxSessionsPerUser:    100,
			MaxMessagesPerSession: 1000,
			MaxMessageLength:      10000,
			MaxTokensPerMessage:   4000,
			MaxTokensPerSession:   50000,
			ContextWindowTokens:   8000,
			SessionTimeoutHours:   24,
			EnableContentFilter:   true,
			EnablePIIDetection:    true,
			EnableRateLimiting:    true,
			DefaultSystemPrompt:   "You are a helpful AI assistant.",
		}
	}

	return &aiChatService{
		chatRepo: chatRepo,
		logger:   logger,
		config:   config,
	}
}

// CreateSession creates a new chat session
func (s *aiChatService) CreateSession(ctx context.Context, req *models.CreateChatSessionRequest) (*models.ChatSessionResponse, error) {
	if err := s.validateCreateSessionRequest(req); err != nil {
		return nil, err
	}

	// Check session limit for user
	if req.UserID != nil {
		if err := s.checkSessionLimit(ctx, *req.UserID, req.TenantID); err != nil {
			return nil, err
		}
	}

	// Create session
	session := &models.AIChatSession{
		TenantID:     req.TenantID,
		WebsiteID:    req.WebsiteID,
		UserID:       req.UserID,
		Title:        req.Title,
		ModelID:      req.ModelID,
		SystemPrompt: req.SystemPrompt,
		Context:      req.Context,
		Status:       models.ChatSessionStatusActive,
		MessageCount: 0,
	}

	if err := s.chatRepo.CreateSession(ctx, session); err != nil {
		s.logger.Error("Failed to create chat session", "error", err)
		return nil, ErrInternalError
	}

	s.logger.Info("Created chat session", "session_id", session.ID, "tenant_id", req.TenantID)
	return session.ToResponse(), nil
}

// GetSession retrieves a chat session by ID
func (s *aiChatService) GetSession(ctx context.Context, sessionID uint, tenantID uint) (*models.ChatSessionResponse, error) {
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		if errors.Is(err, ErrSessionNotFound) {
			return nil, ErrSessionNotFound
		}
		s.logger.Error("Failed to get chat session", "session_id", sessionID, "error", err)
		return nil, ErrInternalError
	}

	// Check tenant access
	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	return session.ToResponse(), nil
}

// UpdateSession updates an existing chat session
func (s *aiChatService) UpdateSession(ctx context.Context, sessionID uint, req *models.UpdateChatSessionRequest, tenantID uint) (*models.ChatSessionResponse, error) {
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	// Check tenant access
	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	// Check if session is active
	if session.IsDeleted() {
		return nil, ErrSessionDeleted
	}

	// Update fields
	if req.Title != "" {
		session.Title = req.Title
	}

	if req.SystemPrompt != nil {
		session.SystemPrompt = req.SystemPrompt
	}

	if req.Status != "" {
		session.Status = req.Status
	}

	if req.Context != nil {
		session.Context = req.Context
	}

	if err := s.chatRepo.UpdateSession(ctx, session); err != nil {
		s.logger.Error("Failed to update chat session", "session_id", sessionID, "error", err)
		return nil, ErrInternalError
	}

	s.logger.Info("Updated chat session", "session_id", sessionID, "tenant_id", tenantID)
	return session.ToResponse(), nil
}

// DeleteSession soft deletes a chat session
func (s *aiChatService) DeleteSession(ctx context.Context, sessionID uint, tenantID uint) error {
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return ErrSessionNotFound
	}

	// Check tenant access
	if session.TenantID != tenantID {
		return ErrUnauthorizedAccess
	}

	if err := s.chatRepo.DeleteSession(ctx, sessionID); err != nil {
		s.logger.Error("Failed to delete chat session", "session_id", sessionID, "error", err)
		return ErrInternalError
	}

	s.logger.Info("Deleted chat session", "session_id", sessionID, "tenant_id", tenantID)
	return nil
}

// ArchiveSession archives a chat session
func (s *aiChatService) ArchiveSession(ctx context.Context, sessionID uint, tenantID uint) error {
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return ErrSessionNotFound
	}

	// Check tenant access
	if session.TenantID != tenantID {
		return ErrUnauthorizedAccess
	}

	if err := s.chatRepo.ArchiveSession(ctx, sessionID); err != nil {
		s.logger.Error("Failed to archive chat session", "session_id", sessionID, "error", err)
		return ErrInternalError
	}

	s.logger.Info("Archived chat session", "session_id", sessionID, "tenant_id", tenantID)
	return nil
}

// ListSessions lists chat sessions with filtering
func (s *aiChatService) ListSessions(ctx context.Context, filter models.ChatSessionFilter) (*models.ListChatSessionsResponse, error) {
	sessions, total, err := s.chatRepo.ListSessions(ctx, filter)
	if err != nil {
		s.logger.Error("Failed to list chat sessions", "error", err)
		return nil, ErrInternalError
	}

	// Convert to response format
	sessionResponses := make([]models.ChatSessionResponse, len(sessions))
	for i, session := range sessions {
		sessionResponses[i] = *session.ToResponse()
	}

	response := &models.ListChatSessionsResponse{
		Sessions:   sessionResponses,
		TotalCount: total,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalPages: s.calculateTotalPages(total, filter.PageSize),
	}

	return response, nil
}

// GetSessionsByUser retrieves sessions for a specific user
func (s *aiChatService) GetSessionsByUser(ctx context.Context, userID uint, tenantID uint, filter models.ChatSessionFilter) (*models.ListChatSessionsResponse, error) {
	filter.TenantID = tenantID
	filter.UserID = &userID
	return s.ListSessions(ctx, filter)
}

// GetSessionsByWebsite retrieves sessions for a specific website
func (s *aiChatService) GetSessionsByWebsite(ctx context.Context, websiteID uint, tenantID uint, filter models.ChatSessionFilter) (*models.ListChatSessionsResponse, error) {
	filter.TenantID = tenantID
	filter.WebsiteID = &websiteID
	return s.ListSessions(ctx, filter)
}

// CreateMessage creates a new chat message
func (s *aiChatService) CreateMessage(ctx context.Context, req *models.CreateChatMessageRequest, tenantID uint) (*models.ChatMessageResponse, error) {
	if err := s.validateCreateMessageRequest(req); err != nil {
		return nil, err
	}

	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, req.SessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	if session.IsDeleted() {
		return nil, ErrSessionDeleted
	}

	// Check message limit
	if err := s.checkMessageLimit(ctx, req.SessionID); err != nil {
		return nil, err
	}

	// Content filtering
	if s.config.EnableContentFilter {
		if err := s.filterContent(req.Content); err != nil {
			return nil, err
		}
	}

	// PII detection
	if s.config.EnablePIIDetection {
		if err := s.detectPII(req.Content); err != nil {
			return nil, err
		}
	}

	// Create message
	message := &models.AIChatMessage{
		SessionID:  req.SessionID,
		Role:       req.Role,
		Content:    req.Content,
		Metadata:   req.Metadata,
		TokensUsed: req.TokensUsed,
	}

	if err := s.chatRepo.CreateMessage(ctx, message); err != nil {
		s.logger.Error("Failed to create chat message", "error", err)
		return nil, ErrInternalError
	}

	// Update session message count
	session.IncrementMessageCount()
	if err := s.chatRepo.UpdateSession(ctx, session); err != nil {
		s.logger.Error("Failed to update session message count", "session_id", req.SessionID, "error", err)
	}

	s.logger.Info("Created chat message", "message_id", message.ID, "session_id", req.SessionID)
	return message.ToResponse(), nil
}

// GetMessage retrieves a chat message by ID
func (s *aiChatService) GetMessage(ctx context.Context, messageID uint, tenantID uint) (*models.ChatMessageResponse, error) {
	message, err := s.chatRepo.GetMessageByID(ctx, messageID)
	if err != nil {
		return nil, ErrMessageNotFound
	}

	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, message.SessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	return message.ToResponse(), nil
}

// UpdateMessage updates an existing chat message
func (s *aiChatService) UpdateMessage(ctx context.Context, messageID uint, req *models.UpdateChatMessageRequest, tenantID uint) (*models.ChatMessageResponse, error) {
	message, err := s.chatRepo.GetMessageByID(ctx, messageID)
	if err != nil {
		return nil, ErrMessageNotFound
	}

	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, message.SessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	// Update fields
	if req.Content != "" {
		if s.config.EnableContentFilter {
			if err := s.filterContent(req.Content); err != nil {
				return nil, err
			}
		}
		message.Content = req.Content
	}

	if req.Metadata != nil {
		message.Metadata = req.Metadata
	}

	if req.TokensUsed > 0 {
		message.TokensUsed = req.TokensUsed
	}

	if err := s.chatRepo.UpdateMessage(ctx, message); err != nil {
		s.logger.Error("Failed to update chat message", "message_id", messageID, "error", err)
		return nil, ErrInternalError
	}

	s.logger.Info("Updated chat message", "message_id", messageID)
	return message.ToResponse(), nil
}

// DeleteMessage deletes a chat message
func (s *aiChatService) DeleteMessage(ctx context.Context, messageID uint, tenantID uint) error {
	message, err := s.chatRepo.GetMessageByID(ctx, messageID)
	if err != nil {
		return ErrMessageNotFound
	}

	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, message.SessionID)
	if err != nil {
		return ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return ErrUnauthorizedAccess
	}

	if err := s.chatRepo.DeleteMessage(ctx, messageID); err != nil {
		s.logger.Error("Failed to delete chat message", "message_id", messageID, "error", err)
		return ErrInternalError
	}

	// Update session message count
	session.MessageCount--
	if err := s.chatRepo.UpdateSession(ctx, session); err != nil {
		s.logger.Error("Failed to update session message count", "session_id", message.SessionID, "error", err)
	}

	s.logger.Info("Deleted chat message", "message_id", messageID)
	return nil
}

// ListMessages lists messages for a session
func (s *aiChatService) ListMessages(ctx context.Context, sessionID uint, tenantID uint, filter models.ChatMessageFilter) (*models.ListChatMessagesResponse, error) {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	filter.SessionID = sessionID
	messages, total, err := s.chatRepo.ListMessages(ctx, filter)
	if err != nil {
		s.logger.Error("Failed to list chat messages", "session_id", sessionID, "error", err)
		return nil, ErrInternalError
	}

	// Convert to response format
	messageResponses := make([]models.ChatMessageResponse, len(messages))
	for i, message := range messages {
		messageResponses[i] = *message.ToResponse()
	}

	response := &models.ListChatMessagesResponse{
		Messages:   messageResponses,
		TotalCount: total,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalPages: s.calculateTotalPages(total, filter.PageSize),
	}

	return response, nil
}

// GetConversationHistory retrieves conversation history for a session
func (s *aiChatService) GetConversationHistory(ctx context.Context, sessionID uint, tenantID uint, limit int) (*models.ChatConversationResponse, error) {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	messages, err := s.chatRepo.GetConversationHistory(ctx, sessionID, limit)
	if err != nil {
		s.logger.Error("Failed to get conversation history", "session_id", sessionID, "error", err)
		return nil, ErrInternalError
	}

	// Convert to response format
	messageResponses := make([]models.ChatMessageResponse, len(messages))
	totalTokens := 0
	for i, message := range messages {
		messageResponses[i] = *message.ToResponse()
		totalTokens += message.TokensUsed
	}

	response := &models.ChatConversationResponse{
		SessionID:    sessionID,
		SessionTitle: session.Title,
		Messages:     messageResponses,
		TotalTokens:  totalTokens,
		CreatedAt:    session.CreatedAt,
		UpdatedAt:    session.UpdatedAt,
	}

	return response, nil
}

// Helper methods for validation and business logic

// validateCreateSessionRequest validates session creation request
func (s *aiChatService) validateCreateSessionRequest(req *models.CreateChatSessionRequest) error {
	if req.TenantID == 0 {
		return ErrInvalidTenant
	}

	if strings.TrimSpace(req.Title) == "" {
		return ErrSessionTitleRequired
	}

	if req.ModelID == 0 {
		return ErrInvalidRequest
	}

	return nil
}

// validateCreateMessageRequest validates message creation request
func (s *aiChatService) validateCreateMessageRequest(req *models.CreateChatMessageRequest) error {
	if req.SessionID == 0 {
		return ErrInvalidRequest
	}

	if strings.TrimSpace(req.Content) == "" {
		return ErrMessageContentRequired
	}

	if len(req.Content) > s.config.MaxMessageLength {
		return ErrMessageTooLong
	}

	if req.Role != models.ChatMessageRoleUser && req.Role != models.ChatMessageRoleAssistant && req.Role != models.ChatMessageRoleSystem {
		return ErrInvalidMessageRole
	}

	if req.TokensUsed > s.config.MaxTokensPerMessage {
		return ErrTokenLimitExceeded
	}

	return nil
}

// checkSessionLimit checks if user has exceeded session limit
func (s *aiChatService) checkSessionLimit(ctx context.Context, userID uint, tenantID uint) error {
	filter := models.ChatSessionFilter{
		TenantID: tenantID,
		UserID:   &userID,
		Status:   models.ChatSessionStatusActive,
	}

	_, total, err := s.chatRepo.ListSessions(ctx, filter)
	if err != nil {
		return ErrInternalError
	}

	if int(total) >= s.config.MaxSessionsPerUser {
		return ErrSessionLimitExceeded
	}

	return nil
}

// checkMessageLimit checks if session has exceeded message limit
func (s *aiChatService) checkMessageLimit(ctx context.Context, sessionID uint) error {
	filter := models.ChatMessageFilter{
		SessionID: sessionID,
	}

	_, total, err := s.chatRepo.ListMessages(ctx, filter)
	if err != nil {
		return ErrInternalError
	}

	if int(total) >= s.config.MaxMessagesPerSession {
		return fmt.Errorf("session message limit exceeded: %d/%d", total, s.config.MaxMessagesPerSession)
	}

	return nil
}

// filterContent performs content filtering
func (s *aiChatService) filterContent(content string) error {
	// Basic content filtering - can be extended with more sophisticated filtering
	if strings.Contains(strings.ToLower(content), "spam") {
		return errors.New("content contains prohibited words")
	}

	return nil
}

// detectPII performs PII detection
func (s *aiChatService) detectPII(content string) error {
	// Basic PII detection - can be extended with more sophisticated detection
	if strings.Contains(content, "@") && strings.Contains(content, ".") {
		return errors.New("content may contain email addresses")
	}

	return nil
}

// calculateTotalPages calculates total pages
func (s *aiChatService) calculateTotalPages(totalCount int64, pageSize int) int {
	if pageSize <= 0 {
		return 1
	}
	return int((totalCount + int64(pageSize) - 1) / int64(pageSize))
}

// Additional methods will be implemented in the next part due to length constraints...
