package services

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
)

// Additional methods for AIChatService

// BulkCreateMessages creates multiple messages in a single transaction
func (s *aiChatService) BulkCreateMessages(ctx context.Context, req *models.BulkCreateChatMessagesRequest, tenantID uint) ([]models.ChatMessageResponse, error) {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, req.SessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	if session.IsDeleted() {
		return nil, ErrSessionDeleted
	}

	// Validate all messages
	messages := make([]models.AIChatMessage, len(req.Messages))
	for i, msgReq := range req.Messages {
		if err := s.validateCreateMessageRequest(&msgReq); err != nil {
			return nil, err
		}

		// Content filtering
		if s.config.EnableContentFilter {
			if err := s.filterContent(msgReq.Content); err != nil {
				return nil, err
			}
		}

		// PII detection
		if s.config.EnablePIIDetection {
			if err := s.detectPII(msgReq.Content); err != nil {
				return nil, err
			}
		}

		messages[i] = models.AIChatMessage{
			SessionID:  req.SessionID,
			Role:       msgReq.Role,
			Content:    msgReq.Content,
			Metadata:   msgReq.Metadata,
			TokensUsed: msgReq.TokensUsed,
		}
	}

	// Create messages
	if err := s.chatRepo.BulkCreateMessages(ctx, messages); err != nil {
		s.logger.Error("Failed to bulk create chat messages", "error", err)
		return nil, ErrInternalError
	}

	// Update session message count
	session.MessageCount += len(messages)
	if err := s.chatRepo.UpdateSession(ctx, session); err != nil {
		s.logger.Error("Failed to update session message count", "session_id", req.SessionID, "error", err)
	}

	// Convert to response format
	responses := make([]models.ChatMessageResponse, len(messages))
	for i, message := range messages {
		responses[i] = *message.ToResponse()
	}

	s.logger.Info("Bulk created chat messages", "count", len(messages), "session_id", req.SessionID)
	return responses, nil
}

// BulkDeleteMessages deletes multiple messages
func (s *aiChatService) BulkDeleteMessages(ctx context.Context, messageIDs []uint, tenantID uint) error {
	if len(messageIDs) == 0 {
		return nil
	}

	// Verify all messages belong to tenant
	for _, messageID := range messageIDs {
		message, err := s.chatRepo.GetMessageByID(ctx, messageID)
		if err != nil {
			return ErrMessageNotFound
		}

		session, err := s.chatRepo.GetSessionByID(ctx, message.SessionID)
		if err != nil {
			return ErrSessionNotFound
		}

		if session.TenantID != tenantID {
			return ErrUnauthorizedAccess
		}
	}

	if err := s.chatRepo.BulkDeleteMessages(ctx, messageIDs); err != nil {
		s.logger.Error("Failed to bulk delete chat messages", "error", err)
		return ErrInternalError
	}

	s.logger.Info("Bulk deleted chat messages", "count", len(messageIDs))
	return nil
}

// BulkArchiveSessions archives multiple sessions
func (s *aiChatService) BulkArchiveSessions(ctx context.Context, sessionIDs []uint, tenantID uint) error {
	if len(sessionIDs) == 0 {
		return nil
	}

	// Verify all sessions belong to tenant
	for _, sessionID := range sessionIDs {
		session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
		if err != nil {
			return ErrSessionNotFound
		}

		if session.TenantID != tenantID {
			return ErrUnauthorizedAccess
		}
	}

	if err := s.chatRepo.BulkArchiveSessions(ctx, sessionIDs); err != nil {
		s.logger.Error("Failed to bulk archive chat sessions", "error", err)
		return ErrInternalError
	}

	s.logger.Info("Bulk archived chat sessions", "count", len(sessionIDs))
	return nil
}

// BulkDeleteSessions soft deletes multiple sessions
func (s *aiChatService) BulkDeleteSessions(ctx context.Context, sessionIDs []uint, tenantID uint) error {
	if len(sessionIDs) == 0 {
		return nil
	}

	// Verify all sessions belong to tenant
	for _, sessionID := range sessionIDs {
		session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
		if err != nil {
			return ErrSessionNotFound
		}

		if session.TenantID != tenantID {
			return ErrUnauthorizedAccess
		}
	}

	if err := s.chatRepo.BulkDeleteSessions(ctx, sessionIDs); err != nil {
		s.logger.Error("Failed to bulk delete chat sessions", "error", err)
		return ErrInternalError
	}

	s.logger.Info("Bulk deleted chat sessions", "count", len(sessionIDs))
	return nil
}

// SearchMessages searches for messages across sessions
func (s *aiChatService) SearchMessages(ctx context.Context, req *models.SearchChatMessagesRequest) (*models.ListChatMessagesResponse, error) {
	messages, total, err := s.chatRepo.SearchMessages(ctx, *req)
	if err != nil {
		s.logger.Error("Failed to search chat messages", "error", err)
		return nil, ErrInternalError
	}

	// Convert to response format
	messageResponses := make([]models.ChatMessageResponse, len(messages))
	for i, message := range messages {
		messageResponses[i] = *message.ToResponse()
	}

	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}

	response := &models.ListChatMessagesResponse{
		Messages:   messageResponses,
		TotalCount: total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: s.calculateTotalPages(total, pageSize),
	}

	return response, nil
}

// GetSessionStats retrieves statistics for sessions
func (s *aiChatService) GetSessionStats(ctx context.Context, tenantID uint, websiteID *uint) (*models.MessageStats, error) {
	stats, err := s.chatRepo.GetSessionStats(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.Error("Failed to get session stats", "tenant_id", tenantID, "error", err)
		return nil, ErrInternalError
	}

	return stats, nil
}

// GetMessageStats retrieves statistics for messages in a session
func (s *aiChatService) GetMessageStats(ctx context.Context, sessionID uint, tenantID uint) (*models.MessageStats, error) {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	stats, err := s.chatRepo.GetMessageStats(ctx, sessionID)
	if err != nil {
		s.logger.Error("Failed to get message stats", "session_id", sessionID, "error", err)
		return nil, ErrInternalError
	}

	return stats, nil
}

// GetTokenUsageStats retrieves token usage statistics for a session
func (s *aiChatService) GetTokenUsageStats(ctx context.Context, sessionID uint, tenantID uint) (int64, error) {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return 0, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return 0, ErrUnauthorizedAccess
	}

	tokens, err := s.chatRepo.GetTokenUsageStats(ctx, sessionID)
	if err != nil {
		s.logger.Error("Failed to get token usage stats", "session_id", sessionID, "error", err)
		return 0, ErrInternalError
	}

	return tokens, nil
}

// UpdateSessionContext updates the context of a session
func (s *aiChatService) UpdateSessionContext(ctx context.Context, sessionID uint, context models.JSONMap, tenantID uint) error {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return ErrUnauthorizedAccess
	}

	if session.IsDeleted() {
		return ErrSessionDeleted
	}

	if err := s.chatRepo.UpdateSessionContext(ctx, sessionID, context); err != nil {
		s.logger.Error("Failed to update session context", "session_id", sessionID, "error", err)
		return ErrInternalError
	}

	s.logger.Info("Updated session context", "session_id", sessionID)
	return nil
}

// GetSessionContext retrieves the context of a session
func (s *aiChatService) GetSessionContext(ctx context.Context, sessionID uint, tenantID uint) (models.JSONMap, error) {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	context, err := s.chatRepo.GetSessionContext(ctx, sessionID)
	if err != nil {
		s.logger.Error("Failed to get session context", "session_id", sessionID, "error", err)
		return nil, ErrInternalError
	}

	return context, nil
}

// GetContextWindowMessages retrieves messages within a token limit for context window
func (s *aiChatService) GetContextWindowMessages(ctx context.Context, sessionID uint, maxTokens int, tenantID uint) ([]models.ChatMessageResponse, error) {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	if maxTokens <= 0 {
		maxTokens = s.config.ContextWindowTokens
	}

	messages, err := s.chatRepo.GetContextWindowMessages(ctx, sessionID, maxTokens)
	if err != nil {
		s.logger.Error("Failed to get context window messages", "session_id", sessionID, "error", err)
		return nil, ErrInternalError
	}

	// Convert to response format
	responses := make([]models.ChatMessageResponse, len(messages))
	for i, message := range messages {
		responses[i] = *message.ToResponse()
	}

	return responses, nil
}

// ExportMessages exports messages for a session
func (s *aiChatService) ExportMessages(ctx context.Context, req *models.ExportChatMessagesRequest, tenantID uint) ([]models.ChatMessageResponse, error) {
	// Verify session belongs to tenant
	session, err := s.chatRepo.GetSessionByID(ctx, req.SessionID)
	if err != nil {
		return nil, ErrSessionNotFound
	}

	if session.TenantID != tenantID {
		return nil, ErrUnauthorizedAccess
	}

	messages, err := s.chatRepo.ExportMessages(ctx, *req)
	if err != nil {
		s.logger.Error("Failed to export messages", "session_id", req.SessionID, "error", err)
		return nil, ErrInternalError
	}

	// Convert to response format
	responses := make([]models.ChatMessageResponse, len(messages))
	for i, message := range messages {
		responses[i] = *message.ToResponse()
	}

	return responses, nil
}

// CleanupExpiredSessions archives sessions that are older than specified days
func (s *aiChatService) CleanupExpiredSessions(ctx context.Context, olderThanDays int) error {
	if olderThanDays <= 0 {
		olderThanDays = s.config.SessionTimeoutHours / 24
	}

	expiredTime := time.Now().AddDate(0, 0, -olderThanDays).Unix()

	if err := s.chatRepo.CleanupExpiredSessions(ctx, expiredTime); err != nil {
		s.logger.Error("Failed to cleanup expired sessions", "error", err)
		return ErrInternalError
	}

	s.logger.Info("Cleaned up expired sessions", "older_than_days", olderThanDays)
	return nil
}

// GetActiveSessionsCount retrieves the count of active sessions
func (s *aiChatService) GetActiveSessionsCount(ctx context.Context, tenantID uint, websiteID *uint) (int64, error) {
	count, err := s.chatRepo.GetActiveSessionsCount(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.Error("Failed to get active sessions count", "tenant_id", tenantID, "error", err)
		return 0, ErrInternalError
	}

	return count, nil
}
