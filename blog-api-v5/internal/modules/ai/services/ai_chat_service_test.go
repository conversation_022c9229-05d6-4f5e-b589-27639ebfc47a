package services

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MockAIChatRepository is a mock implementation of AIChatRepository
type MockAIChatRepository struct {
	mock.Mock
}

func (m *MockAIChatRepository) CreateSession(ctx context.Context, session *models.AIChatSession) error {
	args := m.Called(ctx, session)
	return args.Error(0)
}

func (m *MockAIChatRepository) GetSessionByID(ctx context.Context, id uint) (*models.AIChatSession, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.AIChatSession), args.Error(1)
}

func (m *MockAIChatRepository) UpdateSession(ctx context.Context, session *models.AIChatSession) error {
	args := m.Called(ctx, session)
	return args.Error(0)
}

func (m *MockAIChatRepository) DeleteSession(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockAIChatRepository) ArchiveSession(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockAIChatRepository) ListSessions(ctx context.Context, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]models.AIChatSession), args.Get(1).(int64), args.Error(2)
}

func (m *MockAIChatRepository) GetSessionsByTenant(ctx context.Context, tenantID uint, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error) {
	args := m.Called(ctx, tenantID, filter)
	return args.Get(0).([]models.AIChatSession), args.Get(1).(int64), args.Error(2)
}

func (m *MockAIChatRepository) GetSessionsByUser(ctx context.Context, userID uint, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error) {
	args := m.Called(ctx, userID, filter)
	return args.Get(0).([]models.AIChatSession), args.Get(1).(int64), args.Error(2)
}

func (m *MockAIChatRepository) GetSessionsByWebsite(ctx context.Context, websiteID uint, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error) {
	args := m.Called(ctx, websiteID, filter)
	return args.Get(0).([]models.AIChatSession), args.Get(1).(int64), args.Error(2)
}

func (m *MockAIChatRepository) GetSessionStats(ctx context.Context, tenantID uint, websiteID *uint) (*models.MessageStats, error) {
	args := m.Called(ctx, tenantID, websiteID)
	return args.Get(0).(*models.MessageStats), args.Error(1)
}

func (m *MockAIChatRepository) GetActiveSessionsCount(ctx context.Context, tenantID uint, websiteID *uint) (int64, error) {
	args := m.Called(ctx, tenantID, websiteID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockAIChatRepository) UpdateSessionContext(ctx context.Context, sessionID uint, context models.JSONMap) error {
	args := m.Called(ctx, sessionID, context)
	return args.Error(0)
}

func (m *MockAIChatRepository) GetSessionContext(ctx context.Context, sessionID uint) (models.JSONMap, error) {
	args := m.Called(ctx, sessionID)
	return args.Get(0).(models.JSONMap), args.Error(1)
}

func (m *MockAIChatRepository) GetExpiredSessions(ctx context.Context, olderThan int64) ([]models.AIChatSession, error) {
	args := m.Called(ctx, olderThan)
	return args.Get(0).([]models.AIChatSession), args.Error(1)
}

func (m *MockAIChatRepository) CleanupExpiredSessions(ctx context.Context, olderThan int64) error {
	args := m.Called(ctx, olderThan)
	return args.Error(0)
}

func (m *MockAIChatRepository) BulkArchiveSessions(ctx context.Context, sessionIDs []uint) error {
	args := m.Called(ctx, sessionIDs)
	return args.Error(0)
}

func (m *MockAIChatRepository) BulkDeleteSessions(ctx context.Context, sessionIDs []uint) error {
	args := m.Called(ctx, sessionIDs)
	return args.Error(0)
}

func (m *MockAIChatRepository) CreateMessage(ctx context.Context, message *models.AIChatMessage) error {
	args := m.Called(ctx, message)
	return args.Error(0)
}

func (m *MockAIChatRepository) GetMessageByID(ctx context.Context, id uint) (*models.AIChatMessage, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.AIChatMessage), args.Error(1)
}

func (m *MockAIChatRepository) UpdateMessage(ctx context.Context, message *models.AIChatMessage) error {
	args := m.Called(ctx, message)
	return args.Error(0)
}

func (m *MockAIChatRepository) DeleteMessage(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockAIChatRepository) ListMessages(ctx context.Context, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]models.AIChatMessage), args.Get(1).(int64), args.Error(2)
}

func (m *MockAIChatRepository) GetMessagesBySession(ctx context.Context, sessionID uint, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error) {
	args := m.Called(ctx, sessionID, filter)
	return args.Get(0).([]models.AIChatMessage), args.Get(1).(int64), args.Error(2)
}

func (m *MockAIChatRepository) GetMessagesByUser(ctx context.Context, userID uint, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error) {
	args := m.Called(ctx, userID, filter)
	return args.Get(0).([]models.AIChatMessage), args.Get(1).(int64), args.Error(2)
}

func (m *MockAIChatRepository) GetMessageStats(ctx context.Context, sessionID uint) (*models.MessageStats, error) {
	args := m.Called(ctx, sessionID)
	return args.Get(0).(*models.MessageStats), args.Error(1)
}

func (m *MockAIChatRepository) GetMessageStatsForTenant(ctx context.Context, tenantID uint, websiteID *uint) (*models.MessageStats, error) {
	args := m.Called(ctx, tenantID, websiteID)
	return args.Get(0).(*models.MessageStats), args.Error(1)
}

func (m *MockAIChatRepository) GetTokenUsageStats(ctx context.Context, sessionID uint) (int64, error) {
	args := m.Called(ctx, sessionID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockAIChatRepository) SearchMessages(ctx context.Context, request models.SearchChatMessagesRequest) ([]models.AIChatMessage, int64, error) {
	args := m.Called(ctx, request)
	return args.Get(0).([]models.AIChatMessage), args.Get(1).(int64), args.Error(2)
}

func (m *MockAIChatRepository) GetConversationHistory(ctx context.Context, sessionID uint, limit int) ([]models.AIChatMessage, error) {
	args := m.Called(ctx, sessionID, limit)
	return args.Get(0).([]models.AIChatMessage), args.Error(1)
}

func (m *MockAIChatRepository) BulkCreateMessages(ctx context.Context, messages []models.AIChatMessage) error {
	args := m.Called(ctx, messages)
	return args.Error(0)
}

func (m *MockAIChatRepository) BulkDeleteMessages(ctx context.Context, messageIDs []uint) error {
	args := m.Called(ctx, messageIDs)
	return args.Error(0)
}

func (m *MockAIChatRepository) DeleteMessagesBySession(ctx context.Context, sessionID uint) error {
	args := m.Called(ctx, sessionID)
	return args.Error(0)
}

func (m *MockAIChatRepository) ExportMessages(ctx context.Context, request models.ExportChatMessagesRequest) ([]models.AIChatMessage, error) {
	args := m.Called(ctx, request)
	return args.Get(0).([]models.AIChatMessage), args.Error(1)
}

func (m *MockAIChatRepository) GetContextWindowMessages(ctx context.Context, sessionID uint, maxTokens int) ([]models.AIChatMessage, error) {
	args := m.Called(ctx, sessionID, maxTokens)
	return args.Get(0).([]models.AIChatMessage), args.Error(1)
}

func (m *MockAIChatRepository) GetLatestMessages(ctx context.Context, sessionID uint, limit int) ([]models.AIChatMessage, error) {
	args := m.Called(ctx, sessionID, limit)
	return args.Get(0).([]models.AIChatMessage), args.Error(1)
}

// Test helpers
func createTestSession(id uint, tenantID uint, userID *uint) *models.AIChatSession {
	return &models.AIChatSession{
		ID:           id,
		TenantID:     tenantID,
		UserID:       userID,
		Title:        "Test Session",
		ModelID:      1,
		Status:       models.ChatSessionStatusActive,
		MessageCount: 0,
		Context:      make(models.JSONMap),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
}

func createTestMessage(id uint, sessionID uint, role models.ChatMessageRole) *models.AIChatMessage {
	return &models.AIChatMessage{
		ID:         id,
		SessionID:  sessionID,
		Role:       role,
		Content:    "Test message content",
		Metadata:   make(models.JSONMap),
		TokensUsed: 10,
		CreatedAt:  time.Now(),
	}
}

func createTestService() (AIChatService, *MockAIChatRepository, *MockLogger) {
	mockRepo := new(MockAIChatRepository)
	mockLogger := NewMockLogger()

	config := &AIChatConfig{
		MaxSessionsPerUser:    10,
		MaxMessagesPerSession: 100,
		MaxMessageLength:      1000,
		MaxTokensPerMessage:   100,
		MaxTokensPerSession:   1000,
		ContextWindowTokens:   500,
		SessionTimeoutHours:   24,
		EnableContentFilter:   true,
		EnablePIIDetection:    true,
		EnableRateLimiting:    true,
		DefaultSystemPrompt:   "You are a helpful assistant.",
	}

	service := NewAIChatService(mockRepo, mockLogger, config)
	return service, mockRepo, mockLogger
}

func TestAIChatService_CreateSession(t *testing.T) {
	service, mockRepo, mockLogger := createTestService()
	ctx := context.Background()

	t.Run("Success", func(t *testing.T) {
		userID := uint(1)
		req := &models.CreateChatSessionRequest{
			TenantID:     1,
			UserID:       &userID,
			Title:        "Test Session",
			ModelID:      1,
			SystemPrompt: stringPtr("System prompt"),
			Context:      make(models.JSONMap),
		}

		// Mock session limit check
		mockRepo.On("ListSessions", ctx, mock.AnythingOfType("models.ChatSessionFilter")).Return(
			[]models.AIChatSession{}, int64(0), nil,
		)

		// Mock session creation
		mockRepo.On("CreateSession", ctx, mock.AnythingOfType("*models.AIChatSession")).Return(nil)

		// Mock logger
		mockLogger.On("Info", "Created chat session", mock.Anything).Return()

		result, err := service.CreateSession(ctx, req)

		require.NoError(t, err)
		assert.Equal(t, req.TenantID, response.TenantID)
		assert.Equal(t, req.Title, response.Title)
		assert.Equal(t, req.ModelID, response.ModelID)
		assert.Equal(t, models.ChatSessionStatusActive, response.Status)
		mockRepo.AssertExpectations(t)
	})

	t.Run("InvalidTenantID", func(t *testing.T) {
		req := &models.CreateChatSessionRequest{
			TenantID: 0,
			Title:    "Test Session",
			ModelID:  1,
		}

		result, err := service.CreateSession(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrInvalidTenant, err)
	})

	t.Run("EmptyTitle", func(t *testing.T) {
		req := &models.CreateChatSessionRequest{
			TenantID: 1,
			Title:    "",
			ModelID:  1,
		}

		result, err := service.CreateSession(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrSessionTitleRequired, err)
	})

	t.Run("InvalidModelID", func(t *testing.T) {
		req := &models.CreateChatSessionRequest{
			TenantID: 1,
			Title:    "Test Session",
			ModelID:  0,
		}

		result, err := service.CreateSession(ctx, req)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrInvalidRequest, err)
	})
}

func TestAIChatService_GetSession(t *testing.T) {
	service, mockRepo, _ := createTestService()
	ctx := context.Background()

	t.Run("Success", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		session := createTestSession(sessionID, tenantID, nil)

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		result, err := service.GetSession(ctx, sessionID, tenantID)

		require.NoError(t, err)
		assert.Equal(t, sessionID, response.ID)
		assert.Equal(t, tenantID, response.TenantID)
		mockRepo.AssertExpectations(t)
	})

	t.Run("SessionNotFound", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)

		mockRepo.On("GetSessionByID", ctx, sessionID).Return((*models.AIChatSession)(nil), ErrSessionNotFound)

		result, err := service.GetSession(ctx, sessionID, tenantID)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrSessionNotFound, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("UnauthorizedAccess", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		differentTenantID := uint(2)
		session := createTestSession(sessionID, differentTenantID, nil)

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		result, err := service.GetSession(ctx, sessionID, tenantID)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrUnauthorizedAccess, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestAIChatService_CreateMessage(t *testing.T) {
	service, mockRepo, mockLogger := createTestService()
	ctx := context.Background()

	t.Run("Success", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		session := createTestSession(sessionID, tenantID, nil)

		req := &models.CreateChatMessageRequest{
			SessionID:  sessionID,
			Role:       models.ChatMessageRoleUser,
			Content:    "Hello, world!",
			Metadata:   make(models.JSONMap),
			TokensUsed: 5,
		}

		// Mock session retrieval
		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		// Mock message limit check
		mockRepo.On("ListMessages", ctx, mock.AnythingOfType("models.ChatMessageFilter")).Return(
			[]models.AIChatMessage{}, int64(0), nil,
		)

		// Mock message creation
		mockRepo.On("CreateMessage", ctx, mock.AnythingOfType("*models.AIChatMessage")).Return(nil)

		// Mock session update
		mockRepo.On("UpdateSession", ctx, mock.AnythingOfType("*models.AIChatSession")).Return(nil)

		// Mock logger
		mockLogger.On("Info", "Created chat message", mock.Anything).Return()

		result, err := service.CreateMessage(ctx, req, tenantID)

		require.NoError(t, err)
		assert.Equal(t, req.SessionID, response.SessionID)
		assert.Equal(t, req.Role, response.Role)
		assert.Equal(t, req.Content, response.Content)
		assert.Equal(t, req.TokensUsed, response.TokensUsed)
		mockRepo.AssertExpectations(t)
	})

	t.Run("InvalidSessionID", func(t *testing.T) {
		req := &models.CreateChatMessageRequest{
			SessionID: 0,
			Role:      models.ChatMessageRoleUser,
			Content:   "Hello, world!",
		}

		result, err := service.CreateMessage(ctx, req, 1)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrInvalidRequest, err)
	})

	t.Run("EmptyContent", func(t *testing.T) {
		req := &models.CreateChatMessageRequest{
			SessionID: 1,
			Role:      models.ChatMessageRoleUser,
			Content:   "",
		}

		result, err := service.CreateMessage(ctx, req, 1)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrMessageContentRequired, err)
	})

	t.Run("InvalidRole", func(t *testing.T) {
		req := &models.CreateChatMessageRequest{
			SessionID: 1,
			Role:      models.ChatMessageRole("invalid"),
			Content:   "Hello, world!",
		}

		result, err := service.CreateMessage(ctx, req, 1)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrInvalidMessageRole, err)
	})

	t.Run("SessionNotFound", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)

		req := &models.CreateChatMessageRequest{
			SessionID: sessionID,
			Role:      models.ChatMessageRoleUser,
			Content:   "Hello, world!",
		}

		mockRepo.On("GetSessionByID", ctx, sessionID).Return((*models.AIChatSession)(nil), ErrSessionNotFound)

		result, err := service.CreateMessage(ctx, req, tenantID)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrSessionNotFound, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("UnauthorizedAccess", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		differentTenantID := uint(2)
		session := createTestSession(sessionID, differentTenantID, nil)

		req := &models.CreateChatMessageRequest{
			SessionID: sessionID,
			Role:      models.ChatMessageRoleUser,
			Content:   "Hello, world!",
		}

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		result, err := service.CreateMessage(ctx, req, tenantID)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrUnauthorizedAccess, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("SessionDeleted", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		session := createTestSession(sessionID, tenantID, nil)
		session.Status = models.ChatSessionStatusDeleted

		req := &models.CreateChatMessageRequest{
			SessionID: sessionID,
			Role:      models.ChatMessageRoleUser,
			Content:   "Hello, world!",
		}

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		result, err := service.CreateMessage(ctx, req, tenantID)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrSessionDeleted, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestAIChatService_UpdateSession(t *testing.T) {
	service, mockRepo, mockLogger := createTestService()
	ctx := context.Background()

	t.Run("Success", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		session := createTestSession(sessionID, tenantID, nil)

		req := &models.UpdateChatSessionRequest{
			Title:        "Updated Title",
			SystemPrompt: stringPtr("Updated system prompt"),
			Status:       models.ChatSessionStatusArchived,
			Context:      models.JSONMap{"key": "value"},
		}

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)
		mockRepo.On("UpdateSession", ctx, mock.AnythingOfType("*models.AIChatSession")).Return(nil)
		mockLogger.On("Info", "Updated chat session", mock.Anything).Return()

		result, err := service.UpdateSession(ctx, sessionID, req, tenantID)

		require.NoError(t, err)
		assert.Equal(t, req.Title, response.Title)
		assert.Equal(t, req.Status, response.Status)
		mockRepo.AssertExpectations(t)
	})

	t.Run("SessionNotFound", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)

		req := &models.UpdateChatSessionRequest{
			Title: "Updated Title",
		}

		mockRepo.On("GetSessionByID", ctx, sessionID).Return((*models.AIChatSession)(nil), ErrSessionNotFound)

		result, err := service.UpdateSession(ctx, sessionID, req, tenantID)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrSessionNotFound, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("UnauthorizedAccess", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		differentTenantID := uint(2)
		session := createTestSession(sessionID, differentTenantID, nil)

		req := &models.UpdateChatSessionRequest{
			Title: "Updated Title",
		}

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		result, err := service.UpdateSession(ctx, sessionID, req, tenantID)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrUnauthorizedAccess, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("SessionDeleted", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		session := createTestSession(sessionID, tenantID, nil)
		session.Status = models.ChatSessionStatusDeleted

		req := &models.UpdateChatSessionRequest{
			Title: "Updated Title",
		}

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		result, err := service.UpdateSession(ctx, sessionID, req, tenantID)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrSessionDeleted, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestAIChatService_DeleteSession(t *testing.T) {
	service, mockRepo, mockLogger := createTestService()
	ctx := context.Background()

	t.Run("Success", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		session := createTestSession(sessionID, tenantID, nil)

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)
		mockRepo.On("DeleteSession", ctx, sessionID).Return(nil)
		mockLogger.On("Info", "Deleted chat session", mock.Anything).Return()

		err := service.DeleteSession(ctx, sessionID, tenantID)

		require.NoError(t, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("SessionNotFound", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)

		mockRepo.On("GetSessionByID", ctx, sessionID).Return((*models.AIChatSession)(nil), ErrSessionNotFound)

		err := service.DeleteSession(ctx, sessionID, tenantID)

		assert.Error(t, err)
		assert.Equal(t, ErrSessionNotFound, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("UnauthorizedAccess", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		differentTenantID := uint(2)
		session := createTestSession(sessionID, differentTenantID, nil)

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		err := service.DeleteSession(ctx, sessionID, tenantID)

		assert.Error(t, err)
		assert.Equal(t, ErrUnauthorizedAccess, err)
		mockRepo.AssertExpectations(t)
	})
}

func TestAIChatService_GetConversationHistory(t *testing.T) {
	service, mockRepo, _ := createTestService()
	ctx := context.Background()

	t.Run("Success", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		limit := 10
		session := createTestSession(sessionID, tenantID, nil)
		messages := []models.AIChatMessage{
			*createTestMessage(1, sessionID, models.ChatMessageRoleUser),
			*createTestMessage(2, sessionID, models.ChatMessageRoleAssistant),
		}

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)
		mockRepo.On("GetConversationHistory", ctx, sessionID, limit).Return(messages, nil)

		result, err := service.GetConversationHistory(ctx, sessionID, tenantID, limit)

		require.NoError(t, err)
		assert.Equal(t, sessionID, response.SessionID)
		assert.Equal(t, session.Title, response.SessionTitle)
		assert.Len(t, response.Messages, 2)
		assert.Equal(t, 20, response.TotalTokens) // 2 messages * 10 tokens each
		mockRepo.AssertExpectations(t)
	})

	t.Run("SessionNotFound", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		limit := 10

		mockRepo.On("GetSessionByID", ctx, sessionID).Return((*models.AIChatSession)(nil), ErrSessionNotFound)

		result, err := service.GetConversationHistory(ctx, sessionID, tenantID, limit)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrSessionNotFound, err)
		mockRepo.AssertExpectations(t)
	})

	t.Run("UnauthorizedAccess", func(t *testing.T) {
		sessionID := uint(1)
		tenantID := uint(1)
		differentTenantID := uint(2)
		limit := 10
		session := createTestSession(sessionID, differentTenantID, nil)

		mockRepo.On("GetSessionByID", ctx, sessionID).Return(session, nil)

		result, err := service.GetConversationHistory(ctx, sessionID, tenantID, limit)

		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Equal(t, ErrUnauthorizedAccess, err)
		mockRepo.AssertExpectations(t)
	})
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
