package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// AIRequestService defines the interface for AI request business logic operations
type AIRequestService interface {
	// Basic operations
	CreateRequest(ctx context.Context, input dto.CreateAIRequestRequest) (*models.AIRequest, error)
	GetRequest(ctx context.Context, tenantID uint, id uint) (*models.AIRequest, error)
	GetRequestWithRelations(ctx context.Context, tenantID uint, id uint) (*models.AIRequest, error)
	UpdateRequest(ctx context.Context, tenantID uint, id uint, input dto.UpdateAIRequestRequest) (*models.AIRequest, error)
	DeleteRequest(ctx context.Context, tenantID uint, id uint) error

	// Processing operations
	ProcessRequest(ctx context.Context, requestID uint) (*models.AIRequest, error)
	ProcessPendingRequests(ctx context.Context, limit int) ([]ProcessingResult, error)
	RetryFailedRequest(ctx context.Context, requestID uint) (*models.AIRequest, error)
	CancelRequest(ctx context.Context, requestID uint) error

	// List operations
	ListRequests(ctx context.Context, input ListAIRequestsInput) ([]models.AIRequest, *pagination.CursorResponse, error)
	ListRequestsWithRelations(ctx context.Context, input ListAIRequestsInput) ([]models.AIRequest, *pagination.CursorResponse, error)

	// Analytics operations
	GetAnalytics(ctx context.Context, input AnalyticsInput) (*models.AIRequestAnalytics, error)
	GetUsageStats(ctx context.Context, input UsageStatsInput) (*UsageStats, error)
	GetPerformanceMetrics(ctx context.Context, input PerformanceMetricsInput) (*PerformanceMetrics, error)

	// Rate limiting operations
	CheckRateLimit(ctx context.Context, input RateLimitInput) (*RateLimitResult, error)
	GetRateLimitInfo(ctx context.Context, input RateLimitInput) (*RateLimitInfo, error)

	// Security operations
	ValidateAccess(ctx context.Context, userID uint, requestID uint) error
	SanitizePrompt(ctx context.Context, prompt string) (string, error)
	DetectPII(ctx context.Context, text string) (PIIDetectionResult, error)

	// Batch operations
	BulkCreateRequests(ctx context.Context, inputs []CreateAIRequestInput) ([]models.AIRequest, []error)
	BulkUpdateStatus(ctx context.Context, tenantID uint, requestIDs []uint, status models.AIRequestStatus) error
	BulkProcessRequests(ctx context.Context, requestIDs []uint) ([]ProcessingResult, error)

	// Search operations
	SearchRequests(ctx context.Context, input SearchAIRequestsInput) ([]models.AIRequest, *pagination.CursorResponse, error)

	// Cleanup operations
	CleanupOldRequests(ctx context.Context, input CleanupInput) (*CleanupResult, error)

	// Export operations
	ExportRequests(ctx context.Context, input ExportInput) (*ExportResult, error)

	// Queue operations
	QueueRequest(ctx context.Context, input QueueRequestInput) (*models.AIRequest, error)
	GetQueueStatus(ctx context.Context, tenantID uint) (*QueueStatus, error)

	// Cost tracking
	CalculateCost(ctx context.Context, modelID uint, tokensUsed int) (int, error)
	GetCostBreakdown(ctx context.Context, input CostBreakdownInput) (*CostBreakdown, error)

	// Monitoring operations
	GetHealthStatus(ctx context.Context) (*HealthStatus, error)
	GetSystemMetrics(ctx context.Context) (*SystemMetrics, error)
}

// aiRequestService implements the AIRequestService interface
type aiRequestService struct {
	requestRepo     repositories.AIRequestRepository
	logger          utils.Logger
	rateLimiter     RateLimiter
	piIDetector     PIIDetector
	costCalculator  CostCalculator
	securityService SecurityService
	queueService    QueueService
}

// NewAIRequestService creates a new AI request service
func NewAIRequestService(
	requestRepo repositories.AIRequestRepository,
	logger utils.Logger,
	rateLimiter RateLimiter,
	piIDetector PIIDetector,
	costCalculator CostCalculator,
	securityService SecurityService,
	queueService QueueService,
) AIRequestService {
	return &aiRequestService{
		requestRepo:     requestRepo,
		logger:          logger,
		rateLimiter:     rateLimiter,
		piIDetector:     piIDetector,
		costCalculator:  costCalculator,
		securityService: securityService,
		queueService:    queueService,
	}
}

// CreateRequest creates a new AI request from DTO
func (s *aiRequestService) CreateRequest(ctx context.Context, input dto.CreateAIRequestRequest) (*models.AIRequest, error) {
	// Convert DTO to internal input
	internalInput := CreateAIRequestInput{
		TenantID:    input.TenantID,
		WebsiteID:   input.WebsiteID,
		UserID:      input.UserID,
		ModelID:     input.ModelID,
		RequestType: input.RequestType,
		PromptText:  input.PromptText,
		Metadata:    input.Metadata,
	}

	return s.createRequestInternal(ctx, internalInput)
}

// createRequestInternal creates a new AI request (internal method)
func (s *aiRequestService) createRequestInternal(ctx context.Context, input CreateAIRequestInput) (*models.AIRequest, error) {
	// Validate input
	if err := s.validateCreateInput(input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check rate limits
	rateLimitResult, err := s.CheckRateLimit(ctx, RateLimitInput{
		TenantID:  input.TenantID,
		WebsiteID: input.WebsiteID,
		UserID:    input.UserID,
	})
	if err != nil {
		return nil, fmt.Errorf("rate limit check failed: %w", err)
	}

	if rateLimitResult.Exceeded {
		return nil, fmt.Errorf("rate limit exceeded: %s", rateLimitResult.Message)
	}

	// Sanitize prompt
	sanitizedPrompt, err := s.SanitizePrompt(ctx, input.PromptText)
	if err != nil {
		return nil, fmt.Errorf("prompt sanitization failed: %w", err)
	}

	// Detect PII
	piiResult, err := s.DetectPII(ctx, sanitizedPrompt)
	if err != nil {
		s.logger.WithError(err).Warn("PII detection failed")
	}

	// Create request model
	request := &models.AIRequest{
		TenantID:    input.TenantID,
		WebsiteID:   input.WebsiteID,
		UserID:      input.UserID,
		ModelID:     input.ModelID,
		RequestType: input.RequestType,
		PromptText:  sanitizedPrompt,
		Status:      models.AIRequestStatusPending,
		Metadata:    make(models.JSONMap),
	}

	// Add metadata
	if input.Metadata != nil {
		request.Metadata = input.Metadata
	}

	// Add PII detection results to metadata
	if piiResult.HasPII {
		request.Metadata["pii_detected"] = true
		request.Metadata["pii_types"] = piiResult.Types
		request.Metadata["pii_redacted"] = piiResult.RedactedText
	}

	// Add request ID for tracking
	requestID := s.generateRequestID()
	request.Metadata["request_id"] = requestID

	// Create request in database
	if err := s.requestRepo.Create(ctx, request); err != nil {
		s.logger.WithError(err).Error("Failed to create AI request")
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Note: Async processing would be handled separately if needed

	s.logger.WithContext(ctx).Info("AI request created successfully",
		"request_id", request.ID,
		"tenant_id", request.TenantID,
		"request_type", request.RequestType,
	)

	return request, nil
}

// GetRequest retrieves an AI request by ID
func (s *aiRequestService) GetRequest(ctx context.Context, tenantID uint, id uint) (*models.AIRequest, error) {
	// Validate access
	exists, err := s.requestRepo.TenantHasAccess(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("access validation failed: %w", err)
	}

	if !exists {
		return nil, fmt.Errorf("request not found or access denied")
	}

	// Get request
	request, err := s.requestRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get request: %w", err)
	}

	return request, nil
}

// GetRequestWithRelations retrieves an AI request with related data
func (s *aiRequestService) GetRequestWithRelations(ctx context.Context, tenantID uint, id uint) (*models.AIRequest, error) {
	// Validate access
	exists, err := s.requestRepo.TenantHasAccess(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("access validation failed: %w", err)
	}

	if !exists {
		return nil, fmt.Errorf("request not found or access denied")
	}

	// Get request with relations
	request, err := s.requestRepo.GetByIDWithRelations(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get request with relations: %w", err)
	}

	return request, nil
}

// UpdateRequest updates an AI request
func (s *aiRequestService) UpdateRequest(ctx context.Context, tenantID uint, id uint, input dto.UpdateAIRequestRequest) (*models.AIRequest, error) {
	// Convert DTO to internal input
	internalInput := UpdateAIRequestInput{
		Status:           input.Status,
		ResponseText:     input.ResponseText,
		TokensUsed:       input.TokensUsed,
		ProcessingTimeMs: input.ProcessingTimeMs,
		CostCents:        input.CostCents,
		ErrorMessage:     input.ErrorMessage,
		Metadata:         input.Metadata,
	}
	// Get existing request
	request, err := s.GetRequest(ctx, tenantID, id)
	if err != nil {
		return nil, err
	}

	// Validate update permissions
	if !s.canUpdateRequest(request, internalInput) {
		return nil, fmt.Errorf("update not allowed for request in status: %s", request.Status)
	}

	// Update fields
	if input.Status != nil {
		request.Status = *input.Status
	}

	if input.ResponseText != nil {
		request.ResponseText = input.ResponseText
	}

	if input.TokensUsed != nil {
		request.TokensUsed = *input.TokensUsed
	}

	if input.ProcessingTimeMs != nil {
		request.ProcessingTimeMs = *input.ProcessingTimeMs
	}

	if input.CostCents != nil {
		request.CostCents = *input.CostCents
	}

	if input.ErrorMessage != nil {
		request.ErrorMessage = input.ErrorMessage
	}

	if input.Metadata != nil {
		if request.Metadata == nil {
			request.Metadata = make(models.JSONMap)
		}
		for k, v := range input.Metadata {
			request.Metadata[k] = v
		}
	}

	// Update request in database
	if err := s.requestRepo.Update(ctx, request); err != nil {
		return nil, fmt.Errorf("failed to update request: %w", err)
	}

	s.logger.WithContext(ctx).Info("AI request updated successfully",
		"request_id", request.ID,
		"tenant_id", request.TenantID,
		"status", request.Status,
	)

	return request, nil
}

// DeleteRequest deletes an AI request
func (s *aiRequestService) DeleteRequest(ctx context.Context, tenantID uint, id uint) error {
	// Validate access
	exists, err := s.requestRepo.TenantHasAccess(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("access validation failed: %w", err)
	}

	if !exists {
		return fmt.Errorf("request not found or access denied")
	}

	// Delete request
	if err := s.requestRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete request: %w", err)
	}

	s.logger.WithContext(ctx).Info("AI request deleted successfully",
		"request_id", id,
		"tenant_id", tenantID,
	)

	return nil
}

// ProcessRequest processes an AI request
func (s *aiRequestService) ProcessRequest(ctx context.Context, requestID uint) (*models.AIRequest, error) {
	// Get request
	request, err := s.requestRepo.GetByID(ctx, requestID)
	if err != nil {
		return nil, fmt.Errorf("failed to get request: %w", err)
	}

	// Check if request can be processed
	if !request.IsPending() {
		return nil, fmt.Errorf("request cannot be processed in status: %s", request.Status)
	}

	// Process request (this would integrate with actual AI providers)
	startTime := time.Now()

	// Simulate AI processing (in real implementation, this would call AI providers)
	response, tokensUsed, err := s.processWithAIProvider(ctx, request)
	processingTime := time.Since(startTime)

	if err != nil {
		// Mark as failed
		request.MarkAsFailed(err.Error())
		request.AddMetadata("processing_time_ms", processingTime.Milliseconds())

		if updateErr := s.requestRepo.Update(ctx, request); updateErr != nil {
			s.logger.WithError(updateErr).Error("Failed to update failed request")
		}

		return request, fmt.Errorf("processing failed: %w", err)
	}

	// Calculate cost
	cost, err := s.CalculateCost(ctx, request.ModelID, tokensUsed)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to calculate cost")
		cost = 0
	}

	// Mark as completed
	request.MarkAsCompleted(response, tokensUsed, int(processingTime.Milliseconds()), cost)

	// Update request in database
	if err := s.requestRepo.Update(ctx, request); err != nil {
		return nil, fmt.Errorf("failed to update processed request: %w", err)
	}

	s.logger.WithContext(ctx).Info("AI request processed successfully",
		"request_id", request.ID,
		"tenant_id", request.TenantID,
		"tokens_used", tokensUsed,
		"processing_time_ms", processingTime.Milliseconds(),
		"cost_cents", cost,
	)

	return request, nil
}

// ProcessPendingRequests processes pending AI requests
func (s *aiRequestService) ProcessPendingRequests(ctx context.Context, limit int) ([]ProcessingResult, error) {
	// Get pending requests
	requests, err := s.requestRepo.GetPendingRequests(ctx, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending requests: %w", err)
	}

	results := make([]ProcessingResult, len(requests))

	// Process each request
	for i, request := range requests {
		result := ProcessingResult{
			RequestID: request.ID,
			TenantID:  request.TenantID,
			Status:    "processing",
		}

		processedRequest, err := s.ProcessRequest(ctx, request.ID)
		if err != nil {
			result.Status = "failed"
			result.Error = err.Error()
		} else {
			result.Status = "completed"
			result.Request = processedRequest
		}

		results[i] = result
	}

	return results, nil
}

// RetryFailedRequest retries a failed AI request
func (s *aiRequestService) RetryFailedRequest(ctx context.Context, requestID uint) (*models.AIRequest, error) {
	// Get request
	request, err := s.requestRepo.GetByID(ctx, requestID)
	if err != nil {
		return nil, fmt.Errorf("failed to get request: %w", err)
	}

	// Check if request can be retried
	if !request.IsFailed() {
		return nil, fmt.Errorf("request cannot be retried in status: %s", request.Status)
	}

	// Check retry limit
	retryCount, _ := request.GetMetadata("retry_count")
	if retryCount != nil {
		if count, ok := retryCount.(float64); ok && count >= 3 {
			return nil, fmt.Errorf("maximum retry attempts reached")
		}
	}

	// Reset status to pending
	request.Status = models.AIRequestStatusPending
	request.ErrorMessage = nil

	// Update retry count
	if request.Metadata == nil {
		request.Metadata = make(models.JSONMap)
	}

	currentRetryCount := 0
	if retryCount != nil {
		if count, ok := retryCount.(float64); ok {
			currentRetryCount = int(count)
		}
	}

	request.Metadata["retry_count"] = currentRetryCount + 1
	request.Metadata["retried_at"] = time.Now().UTC()

	// Update request in database
	if err := s.requestRepo.Update(ctx, request); err != nil {
		return nil, fmt.Errorf("failed to update request for retry: %w", err)
	}

	// Process the request
	return s.ProcessRequest(ctx, requestID)
}

// CancelRequest cancels an AI request
func (s *aiRequestService) CancelRequest(ctx context.Context, requestID uint) error {
	// Get request
	request, err := s.requestRepo.GetByID(ctx, requestID)
	if err != nil {
		return fmt.Errorf("failed to get request: %w", err)
	}

	// Check if request can be cancelled
	if !request.IsPending() {
		return fmt.Errorf("request cannot be cancelled in status: %s", request.Status)
	}

	// Mark as failed with cancellation message
	request.MarkAsFailed("Request cancelled by user")
	request.AddMetadata("cancelled_at", time.Now().UTC())
	request.AddMetadata("cancelled", true)

	// Update request in database
	if err := s.requestRepo.Update(ctx, request); err != nil {
		return fmt.Errorf("failed to update cancelled request: %w", err)
	}

	s.logger.WithContext(ctx).Info("AI request cancelled successfully",
		"request_id", request.ID,
		"tenant_id", request.TenantID,
	)

	return nil
}

// ListRequests lists AI requests with filtering and pagination
func (s *aiRequestService) ListRequests(ctx context.Context, input ListAIRequestsInput) ([]models.AIRequest, *pagination.CursorResponse, error) {
	// Validate input
	if err := s.validateListInput(input); err != nil {
		return nil, nil, fmt.Errorf("validation failed: %w", err)
	}

	// Create filter
	filter := models.AIRequestFilter{
		TenantID:          &input.TenantID,
		WebsiteID:         input.WebsiteID,
		UserID:            input.UserID,
		ModelID:           input.ModelID,
		RequestType:       input.RequestType,
		Status:            input.Status,
		CreatedAfter:      input.CreatedAfter,
		CreatedBefore:     input.CreatedBefore,
		MinTokens:         input.MinTokens,
		MaxTokens:         input.MaxTokens,
		MinCost:           input.MinCost,
		MaxCost:           input.MaxCost,
		MinProcessingTime: input.MinProcessingTime,
		MaxProcessingTime: input.MaxProcessingTime,
	}

	// List requests
	requests, result, err := s.requestRepo.List(ctx, filter, input.Pagination)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list requests: %w", err)
	}

	return requests, result, nil
}

// ListRequestsWithRelations lists AI requests with related data
func (s *aiRequestService) ListRequestsWithRelations(ctx context.Context, input ListAIRequestsInput) ([]models.AIRequest, *pagination.CursorResponse, error) {
	// Validate input
	if err := s.validateListInput(input); err != nil {
		return nil, nil, fmt.Errorf("validation failed: %w", err)
	}

	// Create filter
	filter := models.AIRequestFilter{
		TenantID:          &input.TenantID,
		WebsiteID:         input.WebsiteID,
		UserID:            input.UserID,
		ModelID:           input.ModelID,
		RequestType:       input.RequestType,
		Status:            input.Status,
		CreatedAfter:      input.CreatedAfter,
		CreatedBefore:     input.CreatedBefore,
		MinTokens:         input.MinTokens,
		MaxTokens:         input.MaxTokens,
		MinCost:           input.MinCost,
		MaxCost:           input.MaxCost,
		MinProcessingTime: input.MinProcessingTime,
		MaxProcessingTime: input.MaxProcessingTime,
	}

	// List requests with relations
	requests, result, err := s.requestRepo.ListWithRelations(ctx, filter, input.Pagination)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list requests with relations: %w", err)
	}

	return requests, result, nil
}

// GetAnalytics generates analytics for AI requests
func (s *aiRequestService) GetAnalytics(ctx context.Context, input AnalyticsInput) (*models.AIRequestAnalytics, error) {
	// Validate input
	if err := s.validateAnalyticsInput(input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Get analytics based on scope
	var analytics *models.AIRequestAnalytics
	var err error

	switch input.Scope {
	case "tenant":
		analytics, err = s.requestRepo.GetAnalytics(ctx, input.TenantID, input.StartTime, input.EndTime)
	case "website":
		if input.WebsiteID == nil {
			return nil, fmt.Errorf("website_id required for website scope")
		}
		analytics, err = s.requestRepo.GetAnalyticsByWebsite(ctx, *input.WebsiteID, input.StartTime, input.EndTime)
	case "user":
		if input.UserID == nil {
			return nil, fmt.Errorf("user_id required for user scope")
		}
		analytics, err = s.requestRepo.GetAnalyticsByUser(ctx, *input.UserID, input.StartTime, input.EndTime)
	case "model":
		if input.ModelID == nil {
			return nil, fmt.Errorf("model_id required for model scope")
		}
		analytics, err = s.requestRepo.GetAnalyticsByModel(ctx, *input.ModelID, input.StartTime, input.EndTime)
	default:
		return nil, fmt.Errorf("invalid scope: %s", input.Scope)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get analytics: %w", err)
	}

	return analytics, nil
}

// CheckRateLimit checks if a request would exceed rate limits
func (s *aiRequestService) CheckRateLimit(ctx context.Context, input RateLimitInput) (*RateLimitResult, error) {
	// Use rate limiter service
	return s.rateLimiter.CheckLimit(ctx, input)
}

// GetRateLimitInfo gets current rate limit information
func (s *aiRequestService) GetRateLimitInfo(ctx context.Context, input RateLimitInput) (*RateLimitInfo, error) {
	// Use rate limiter service
	return s.rateLimiter.GetLimitInfo(ctx, input)
}

// ValidateAccess validates that a user has access to a request
func (s *aiRequestService) ValidateAccess(ctx context.Context, userID uint, requestID uint) error {
	// Use security service
	return s.securityService.ValidateAccess(ctx, userID, requestID)
}

// SanitizePrompt sanitizes a prompt for security
func (s *aiRequestService) SanitizePrompt(ctx context.Context, prompt string) (string, error) {
	// Use security service
	return s.securityService.SanitizePrompt(ctx, prompt)
}

// DetectPII detects personally identifiable information in text
func (s *aiRequestService) DetectPII(ctx context.Context, text string) (PIIDetectionResult, error) {
	// Use PII detector service
	return s.piIDetector.DetectPII(ctx, text)
}

// CalculateCost calculates the cost of an AI request
func (s *aiRequestService) CalculateCost(ctx context.Context, modelID uint, tokensUsed int) (int, error) {
	// Use cost calculator service
	return s.costCalculator.CalculateCost(ctx, modelID, tokensUsed)
}

// BulkCreateRequests creates multiple AI requests
func (s *aiRequestService) BulkCreateRequests(ctx context.Context, inputs []CreateAIRequestInput) ([]models.AIRequest, []error) {
	requests := make([]models.AIRequest, len(inputs))
	errors := make([]error, len(inputs))

	for i, input := range inputs {
		request, err := s.createRequestInternal(ctx, input)
		if err != nil {
			errors[i] = err
		} else {
			requests[i] = *request
		}
	}

	return requests, errors
}

// BulkUpdateStatus updates the status of multiple AI requests
func (s *aiRequestService) BulkUpdateStatus(ctx context.Context, tenantID uint, requestIDs []uint, status models.AIRequestStatus) error {
	// Validate all requests belong to tenant
	for _, id := range requestIDs {
		exists, err := s.requestRepo.TenantHasAccess(ctx, tenantID, id)
		if err != nil {
			return fmt.Errorf("access validation failed for request %d: %w", id, err)
		}
		if !exists {
			return fmt.Errorf("request %d not found or access denied", id)
		}
	}

	// Bulk update status
	return s.requestRepo.BulkUpdateStatus(ctx, requestIDs, status)
}

// SearchRequests searches AI requests
func (s *aiRequestService) SearchRequests(ctx context.Context, input SearchAIRequestsInput) ([]models.AIRequest, *pagination.CursorResponse, error) {
	// Validate input
	if err := s.validateSearchInput(input); err != nil {
		return nil, nil, fmt.Errorf("validation failed: %w", err)
	}

	// Perform search
	return s.requestRepo.Search(ctx, input.TenantID, input.Query, input.Pagination)
}

// CleanupOldRequests cleans up old AI requests
func (s *aiRequestService) CleanupOldRequests(ctx context.Context, input CleanupInput) (*CleanupResult, error) {
	// Validate input
	if err := s.validateCleanupInput(input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Perform cleanup
	deleted, err := s.requestRepo.DeleteOldRequests(ctx, input.TenantID, input.OlderThan)
	if err != nil {
		return nil, fmt.Errorf("cleanup failed: %w", err)
	}

	result := &CleanupResult{
		TenantID:     input.TenantID,
		DeletedCount: deleted,
		CleanupTime:  time.Now(),
	}

	s.logger.WithContext(ctx).Info("AI requests cleanup completed",
		"tenant_id", input.TenantID,
		"deleted_count", deleted,
		"older_than", input.OlderThan,
	)

	return result, nil
}

// Helper methods

// validateCreateInput validates create request input
func (s *aiRequestService) validateCreateInput(input CreateAIRequestInput) error {
	if input.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	if input.ModelID == 0 {
		return fmt.Errorf("model_id is required")
	}

	if input.RequestType == "" {
		return fmt.Errorf("request_type is required")
	}

	if input.PromptText == "" {
		return fmt.Errorf("prompt_text is required")
	}

	if len(input.PromptText) > 10000 {
		return fmt.Errorf("prompt_text must be less than 10000 characters")
	}

	return nil
}

// validateListInput validates list request input
func (s *aiRequestService) validateListInput(input ListAIRequestsInput) error {
	if input.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	return nil
}

// validateAnalyticsInput validates analytics input
func (s *aiRequestService) validateAnalyticsInput(input AnalyticsInput) error {
	if input.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	if input.StartTime.IsZero() {
		return fmt.Errorf("start_time is required")
	}

	if input.EndTime.IsZero() {
		return fmt.Errorf("end_time is required")
	}

	if input.EndTime.Before(input.StartTime) {
		return fmt.Errorf("end_time must be after start_time")
	}

	return nil
}

// validateSearchInput validates search input
func (s *aiRequestService) validateSearchInput(input SearchAIRequestsInput) error {
	if input.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	if input.Query == "" {
		return fmt.Errorf("query is required")
	}

	return nil
}

// validateCleanupInput validates cleanup input
func (s *aiRequestService) validateCleanupInput(input CleanupInput) error {
	if input.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}

	if input.OlderThan.IsZero() {
		return fmt.Errorf("older_than is required")
	}

	return nil
}

// canUpdateRequest checks if a request can be updated
func (s *aiRequestService) canUpdateRequest(request *models.AIRequest, input UpdateAIRequestInput) bool {
	// Allow updates to pending requests
	if request.IsPending() {
		return true
	}

	// Allow metadata updates to completed requests
	if request.IsCompleted() && input.Metadata != nil {
		return true
	}

	// Allow error message updates to failed requests
	if request.IsFailed() && input.ErrorMessage != nil {
		return true
	}

	return false
}

// generateRequestID generates a unique request ID
func (s *aiRequestService) generateRequestID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// processWithAIProvider processes a request with an AI provider (mock implementation)
func (s *aiRequestService) processWithAIProvider(ctx context.Context, request *models.AIRequest) (string, int, error) {
	// This is a mock implementation
	// In a real implementation, this would integrate with actual AI providers

	// Simulate processing time
	time.Sleep(100 * time.Millisecond)

	// Simulate response based on request type
	var response string
	var tokens int

	switch request.RequestType {
	case models.AIRequestTypeContentGeneration:
		response = "Generated content: " + request.PromptText
		tokens = len(strings.Split(request.PromptText, " ")) * 2
	case models.AIRequestTypeChat:
		response = "Chat response: " + request.PromptText
		tokens = len(strings.Split(request.PromptText, " ")) * 3
	case models.AIRequestTypeDesign:
		response = "Design generated for: " + request.PromptText
		tokens = len(strings.Split(request.PromptText, " ")) * 4
	case models.AIRequestTypeOptimization:
		response = "Optimization suggestions: " + request.PromptText
		tokens = len(strings.Split(request.PromptText, " ")) * 2
	case models.AIRequestTypeAnalysis:
		response = "Analysis results: " + request.PromptText
		tokens = len(strings.Split(request.PromptText, " ")) * 3
	default:
		return "", 0, fmt.Errorf("unsupported request type: %s", request.RequestType)
	}

	return response, tokens, nil
}

// Input types

// CreateAIRequestInput represents input for creating an AI request
type CreateAIRequestInput struct {
	TenantID        uint                   `json:"tenant_id" validate:"required"`
	WebsiteID       *uint                  `json:"website_id,omitempty"`
	UserID          *uint                  `json:"user_id,omitempty"`
	ModelID         uint                   `json:"model_id" validate:"required"`
	RequestType     models.AIRequestType   `json:"request_type" validate:"required"`
	PromptText      string                 `json:"prompt_text" validate:"required,max=10000"`
	Metadata        models.JSONMap         `json:"metadata,omitempty"`
	AsyncProcessing bool                   `json:"async_processing,omitempty"`
	ClientInfo      map[string]interface{} `json:"client_info,omitempty"`
}

// UpdateAIRequestInput represents input for updating an AI request
type UpdateAIRequestInput struct {
	Status           *models.AIRequestStatus `json:"status,omitempty"`
	ResponseText     *string                 `json:"response_text,omitempty"`
	TokensUsed       *int                    `json:"tokens_used,omitempty"`
	ProcessingTimeMs *int                    `json:"processing_time_ms,omitempty"`
	CostCents        *int                    `json:"cost_cents,omitempty"`
	ErrorMessage     *string                 `json:"error_message,omitempty"`
	Metadata         models.JSONMap          `json:"metadata,omitempty"`
}

// ListAIRequestsInput represents input for listing AI requests
type ListAIRequestsInput struct {
	TenantID          uint                         `json:"tenant_id" validate:"required"`
	WebsiteID         *uint                        `json:"website_id,omitempty"`
	UserID            *uint                        `json:"user_id,omitempty"`
	ModelID           *uint                        `json:"model_id,omitempty"`
	RequestType       *models.AIRequestType        `json:"request_type,omitempty"`
	Status            *models.AIRequestStatus      `json:"status,omitempty"`
	CreatedAfter      *time.Time                   `json:"created_after,omitempty"`
	CreatedBefore     *time.Time                   `json:"created_before,omitempty"`
	MinTokens         *int                         `json:"min_tokens,omitempty"`
	MaxTokens         *int                         `json:"max_tokens,omitempty"`
	MinCost           *int                         `json:"min_cost,omitempty"`
	MaxCost           *int                         `json:"max_cost,omitempty"`
	MinProcessingTime *int                         `json:"min_processing_time,omitempty"`
	MaxProcessingTime *int                         `json:"max_processing_time,omitempty"`
	Pagination        *pagination.CursorPagination `json:"pagination,omitempty"`
}

// AnalyticsInput represents input for analytics
type AnalyticsInput struct {
	TenantID  uint      `json:"tenant_id" validate:"required"`
	WebsiteID *uint     `json:"website_id,omitempty"`
	UserID    *uint     `json:"user_id,omitempty"`
	ModelID   *uint     `json:"model_id,omitempty"`
	StartTime time.Time `json:"start_time" validate:"required"`
	EndTime   time.Time `json:"end_time" validate:"required"`
	Scope     string    `json:"scope" validate:"required,oneof=tenant website user model"`
}

// SearchAIRequestsInput represents input for searching AI requests
type SearchAIRequestsInput struct {
	TenantID   uint                         `json:"tenant_id" validate:"required"`
	Query      string                       `json:"query" validate:"required"`
	Pagination *pagination.CursorPagination `json:"pagination,omitempty"`
}

// CleanupInput represents input for cleanup operations
type CleanupInput struct {
	TenantID  uint      `json:"tenant_id" validate:"required"`
	OlderThan time.Time `json:"older_than" validate:"required"`
}

// RateLimitInput represents input for rate limiting
type RateLimitInput struct {
	TenantID  uint  `json:"tenant_id" validate:"required"`
	WebsiteID *uint `json:"website_id,omitempty"`
	UserID    *uint `json:"user_id,omitempty"`
}

// Result types

// ProcessingResult represents the result of processing an AI request
type ProcessingResult struct {
	RequestID uint              `json:"request_id"`
	TenantID  uint              `json:"tenant_id"`
	Status    string            `json:"status"`
	Error     string            `json:"error,omitempty"`
	Request   *models.AIRequest `json:"request,omitempty"`
}

// CleanupResult represents the result of a cleanup operation
type CleanupResult struct {
	TenantID     uint      `json:"tenant_id"`
	DeletedCount int64     `json:"deleted_count"`
	CleanupTime  time.Time `json:"cleanup_time"`
}

// RateLimitResult represents the result of a rate limit check
type RateLimitResult struct {
	Exceeded bool   `json:"exceeded"`
	Message  string `json:"message"`
}

// RateLimitInfo represents rate limit information
type RateLimitInfo struct {
	TenantID          uint      `json:"tenant_id"`
	WebsiteID         *uint     `json:"website_id,omitempty"`
	UserID            *uint     `json:"user_id,omitempty"`
	RequestsRemaining int64     `json:"requests_remaining"`
	TokensRemaining   int64     `json:"tokens_remaining"`
	ResetTime         time.Time `json:"reset_time"`
}

// PIIDetectionResult represents the result of PII detection
type PIIDetectionResult struct {
	HasPII       bool     `json:"has_pii"`
	Types        []string `json:"types"`
	RedactedText string   `json:"redacted_text"`
}

// UsageStats represents usage statistics
type UsageStats struct {
	TenantID      uint    `json:"tenant_id"`
	TotalRequests int64   `json:"total_requests"`
	TotalTokens   int64   `json:"total_tokens"`
	TotalCost     int64   `json:"total_cost"`
	AverageTime   float64 `json:"average_time"`
}

// PerformanceMetrics represents performance metrics
type PerformanceMetrics struct {
	TenantID       uint    `json:"tenant_id"`
	SuccessRate    float64 `json:"success_rate"`
	ErrorRate      float64 `json:"error_rate"`
	AverageTime    float64 `json:"average_time"`
	RequestsPerSec float64 `json:"requests_per_sec"`
}

// Additional input types for missing methods
type UsageStatsInput struct {
	TenantID  uint      `json:"tenant_id" validate:"required"`
	WebsiteID *uint     `json:"website_id,omitempty"`
	UserID    *uint     `json:"user_id,omitempty"`
	StartTime time.Time `json:"start_time" validate:"required"`
	EndTime   time.Time `json:"end_time" validate:"required"`
}

type PerformanceMetricsInput struct {
	TenantID  uint      `json:"tenant_id" validate:"required"`
	WebsiteID *uint     `json:"website_id,omitempty"`
	StartTime time.Time `json:"start_time" validate:"required"`
	EndTime   time.Time `json:"end_time" validate:"required"`
}

type QueueRequestInput struct {
	RequestID uint `json:"request_id" validate:"required"`
	Priority  int  `json:"priority,omitempty"`
}

type QueueStatus struct {
	TenantID        uint `json:"tenant_id"`
	PendingCount    int  `json:"pending_count"`
	ProcessingCount int  `json:"processing_count"`
	CompletedCount  int  `json:"completed_count"`
	FailedCount     int  `json:"failed_count"`
}

type CostBreakdownInput struct {
	TenantID  uint      `json:"tenant_id" validate:"required"`
	WebsiteID *uint     `json:"website_id,omitempty"`
	StartTime time.Time `json:"start_time" validate:"required"`
	EndTime   time.Time `json:"end_time" validate:"required"`
}

type CostBreakdown struct {
	TenantID   uint             `json:"tenant_id"`
	TotalCost  int64            `json:"total_cost"`
	ByModel    map[string]int64 `json:"by_model"`
	ByType     map[string]int64 `json:"by_type"`
	ByTimeUnit map[string]int64 `json:"by_time_unit"`
}

type ExportInput struct {
	TenantID  uint      `json:"tenant_id" validate:"required"`
	WebsiteID *uint     `json:"website_id,omitempty"`
	StartTime time.Time `json:"start_time" validate:"required"`
	EndTime   time.Time `json:"end_time" validate:"required"`
	Format    string    `json:"format" validate:"required,oneof=csv json xlsx"`
}

type ExportResult struct {
	TenantID    uint      `json:"tenant_id"`
	Format      string    `json:"format"`
	FilePath    string    `json:"file_path"`
	RecordCount int64     `json:"record_count"`
	ExportTime  time.Time `json:"export_time"`
}

type HealthStatus struct {
	Status     string    `json:"status"`
	Timestamp  time.Time `json:"timestamp"`
	Database   string    `json:"database"`
	Queue      string    `json:"queue"`
	AIProvider string    `json:"ai_provider"`
}

type SystemMetrics struct {
	Timestamp      time.Time `json:"timestamp"`
	ActiveRequests int       `json:"active_requests"`
	QueuedRequests int       `json:"queued_requests"`
	ProcessingRate float64   `json:"processing_rate"`
	ErrorRate      float64   `json:"error_rate"`
	AverageLatency float64   `json:"average_latency"`
	MemoryUsage    float64   `json:"memory_usage"`
	CPUUsage       float64   `json:"cpu_usage"`
}

// External service interfaces (these would be implemented separately)

// RateLimiter interface for rate limiting functionality
type RateLimiter interface {
	CheckLimit(ctx context.Context, input RateLimitInput) (*RateLimitResult, error)
	GetLimitInfo(ctx context.Context, input RateLimitInput) (*RateLimitInfo, error)
}

// PIIDetector interface for PII detection functionality
type PIIDetector interface {
	DetectPII(ctx context.Context, text string) (PIIDetectionResult, error)
}

// CostCalculator interface for cost calculation functionality
type CostCalculator interface {
	CalculateCost(ctx context.Context, modelID uint, tokensUsed int) (int, error)
}

// SecurityService interface for security functionality
type SecurityService interface {
	ValidateAccess(ctx context.Context, userID uint, requestID uint) error
	SanitizePrompt(ctx context.Context, prompt string) (string, error)
}

// QueueService interface for queue management functionality
type QueueService interface {
	QueueRequest(ctx context.Context, requestID uint) error
	GetQueueStatus(ctx context.Context, tenantID uint) (*QueueStatus, error)
}

// Stub implementations for missing methods
func (s *aiRequestService) GetUsageStats(ctx context.Context, input UsageStatsInput) (*UsageStats, error) {
	// Implementation would go here
	return &UsageStats{
		TenantID: input.TenantID,
	}, nil
}

func (s *aiRequestService) GetPerformanceMetrics(ctx context.Context, input PerformanceMetricsInput) (*PerformanceMetrics, error) {
	// Implementation would go here
	return &PerformanceMetrics{
		TenantID: input.TenantID,
	}, nil
}

func (s *aiRequestService) BulkProcessRequests(ctx context.Context, requestIDs []uint) ([]ProcessingResult, error) {
	// Implementation would go here
	results := make([]ProcessingResult, len(requestIDs))
	for i, id := range requestIDs {
		results[i] = ProcessingResult{RequestID: id}
	}
	return results, nil
}

func (s *aiRequestService) QueueRequest(ctx context.Context, input QueueRequestInput) (*models.AIRequest, error) {
	// Implementation would go here
	return nil, nil
}

func (s *aiRequestService) GetQueueStatus(ctx context.Context, tenantID uint) (*QueueStatus, error) {
	// Implementation would go here
	return &QueueStatus{
		TenantID: tenantID,
	}, nil
}

func (s *aiRequestService) GetCostBreakdown(ctx context.Context, input CostBreakdownInput) (*CostBreakdown, error) {
	// Implementation would go here
	return &CostBreakdown{
		TenantID: input.TenantID,
	}, nil
}

func (s *aiRequestService) ExportRequests(ctx context.Context, input ExportInput) (*ExportResult, error) {
	// Implementation would go here
	return &ExportResult{
		TenantID: input.TenantID,
		Format:   input.Format,
	}, nil
}

func (s *aiRequestService) GetHealthStatus(ctx context.Context) (*HealthStatus, error) {
	// Implementation would go here
	return &HealthStatus{
		Status:    "healthy",
		Timestamp: time.Now(),
	}, nil
}

func (s *aiRequestService) GetSystemMetrics(ctx context.Context) (*SystemMetrics, error) {
	// Implementation would go here
	return &SystemMetrics{
		Timestamp: time.Now(),
	}, nil
}
