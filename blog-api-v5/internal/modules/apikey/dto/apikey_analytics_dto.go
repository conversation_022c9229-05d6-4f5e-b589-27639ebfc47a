package dto

import "time"

// APIKeyAnalyticsRequest represents the request for API key analytics
type APIKeyAnalyticsRequest struct {
	APIKeyIDs []uint   `form:"api_key_ids"`
	Period    string   `form:"period" validate:"omitempty,oneof=1h 24h 7d 30d 90d" example:"7d"`
	StartDate string   `form:"start_date" example:"2023-01-01"`
	EndDate   string   `form:"end_date" example:"2023-01-31"`
	Metrics   []string `form:"metrics" example:"requests,errors,response_time"`
}

// APIKeyAnalyticsResponse represents the response for API key analytics
type APIKeyAnalyticsResponse struct {
	Summary     map[string]interface{} `json:"summary"`
	KeyMetrics  []KeyMetric            `json:"key_metrics"`
	Timeline    []AnalyticsDataPoint   `json:"timeline"`
	Comparisons []KeyComparison        `json:"comparisons"`
}

// KeyMetric represents metrics for a specific API key
type KeyMetric struct {
	APIKeyID        uint       `json:"api_key_id" example:"1"`
	APIKeyName      string     `json:"api_key_name" example:"Production API Key"`
	TotalRequests   int64      `json:"total_requests" example:"10000"`
	SuccessRate     float64    `json:"success_rate" example:"0.95"`
	ErrorRate       float64    `json:"error_rate" example:"0.05"`
	AvgResponseTime int64      `json:"avg_response_time" example:"150"`
	LastUsed        *time.Time `json:"last_used,omitempty"`
}

// AnalyticsDataPoint represents a single analytics data point
type AnalyticsDataPoint struct {
	Timestamp       time.Time `json:"timestamp"`
	TotalRequests   int64     `json:"total_requests" example:"500"`
	SuccessRequests int64     `json:"success_requests" example:"475"`
	ErrorRequests   int64     `json:"error_requests" example:"25"`
	AvgResponseTime int64     `json:"avg_response_time" example:"145"`
}

// KeyComparison represents comparison between API keys
type KeyComparison struct {
	APIKeyID1        uint    `json:"api_key_id_1" example:"1"`
	APIKeyName1      string  `json:"api_key_name_1" example:"Production API Key"`
	APIKeyID2        uint    `json:"api_key_id_2" example:"2"`
	APIKeyName2      string  `json:"api_key_name_2" example:"Staging API Key"`
	RequestsDiff     int64   `json:"requests_diff" example:"5000"`
	ErrorRateDiff    float64 `json:"error_rate_diff" example:"0.02"`
	ResponseTimeDiff int64   `json:"response_time_diff" example:"25"`
}
