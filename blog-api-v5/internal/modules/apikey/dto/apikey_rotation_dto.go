package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/models"
)

// RotateAPIKeyRequest represents the request to rotate an API key
type RotateAPIKeyRequest struct {
	Reason           string `json:"reason" validate:"max=255" example:"Security rotation"`
	GracePeriodHours int    `json:"grace_period_hours" validate:"min=0,max=168" example:"24"`
}

// RotateAPIKeyResponse represents the response when rotating an API key
type RotateAPIKeyResponse struct {
	ID             uint                  `json:"id" example:"1"`
	NewKey         string                `json:"new_key" example:"ak_new1234567890abcdef"`
	NewKeyPrefix   string                `json:"new_key_prefix" example:"ak_new1"`
	GracePeriod    int                   `json:"grace_period_hours" example:"24"`
	GraceExpiresAt *time.Time            `json:"grace_expires_at"`
	RotationType   models.RotationType   `json:"rotation_type" example:"manual"`
	RotationStatus models.RotationStatus `json:"rotation_status" example:"pending"`
	RotatedAt      time.Time             `json:"rotated_at"`
}

// RevokeAPIKeyRequest represents the request to revoke an API key
type RevokeAPIKeyRequest struct {
	Reason string `json:"reason" validate:"max=255" example:"Security breach"`
}

// APIKeyRotation represents rotation history entry
type APIKeyRotation struct {
	ID               uint                  `json:"id"`
	APIKeyID         uint                  `json:"api_key_id"`
	OldKeyPrefix     string                `json:"old_key_prefix"`
	NewKeyPrefix     string                `json:"new_key_prefix"`
	RotationType     models.RotationType   `json:"rotation_type"`
	RotationStatus   models.RotationStatus `json:"rotation_status"`
	Reason           string                `json:"reason"`
	GracePeriodHours int                   `json:"grace_period_hours"`
	GraceExpiresAt   *time.Time            `json:"grace_expires_at"`
	RotatedAt        time.Time             `json:"rotated_at"`
	CompletedAt      *time.Time            `json:"completed_at,omitempty"`
}
