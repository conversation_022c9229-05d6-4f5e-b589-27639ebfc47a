package handlers

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// APIKeyHandler handles API key HTTP requests
type APIKeyHandler struct {
	apiKeyService services.APIKeyService
}

// NewAPIKeyHandler creates a new API key handler
func NewAPIKeyHandler(apiKeyService services.APIKeyService) *APIKeyHandler {
	return &APIKeyHandler{
		apiKeyService: apiKeyService,
	}
}

// CreateAPIKey creates a new API key
// @Summary      Create a new API key
// @Description  Creates a new API key with the provided configuration
// @Tags         API Keys
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.CreateAPIKeyRequest true "API key creation data"
// @Success      201 {object} response.Response{data=dto.APIKeyResponse} "API key created successfully"
// @Failure      400 {object} response.Response "Invalid request body or missing tenant/website ID"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to create API key"
// @Router       /api/cms/v1/apikeys [post]
func (h *APIKeyHandler) CreateAPIKey(c *gin.Context) {
	var req dto.CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	// Get tenant and website ID from context
	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)
	createdBy := getUserIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	// Convert DTO to models
	serviceReq := &models.CreateAPIKeyRequest{
		Name:        req.Name,
		Description: req.Description,
		Permissions: req.Permissions,
		Scopes:      req.Scopes,
		IPWhitelist: req.IPWhitelist,
		RateLimit:   req.RateLimit,
		RateWindow:  req.RateWindow,
		ExpiresIn:   req.ExpiresIn,
	}

	// Create API key
	apiKey, err := h.apiKeyService.CreateAPIKey(c.Request.Context(), tenantID, websiteID, createdBy, serviceReq)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to create API key", err.Error())
		return
	}

	// Convert models response to DTO response
	result := h.convertToAPIKeyResponse(apiKey)

	response.CreatedWithContext(c, result)
}

// GetAPIKey retrieves an API key by ID
func (h *APIKeyHandler) GetAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	apiKey, err := h.apiKeyService.GetAPIKey(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		response.NotFoundWithContext(c, "API key not found")
		return
	}

	// Convert to DTO response
	apiKeyResponse := h.convertToAPIKeyResponse(&apiKey.APIKeyResponse)
	response.SuccessWithContext(c, apiKeyResponse)
}

// GetAPIKeyDetail retrieves detailed API key information
func (h *APIKeyHandler) GetAPIKeyDetail(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	detail, err := h.apiKeyService.GetAPIKey(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		response.NotFoundWithContext(c, "API key not found")
		return
	}

	// Convert to DTO response
	detailResponse := h.convertToAPIKeyDetailResponse(detail)
	response.SuccessWithContext(c, detailResponse)
}

// ListAPIKeys retrieves a list of API keys with cursor-based pagination
func (h *APIKeyHandler) ListAPIKeys(c *gin.Context) {
	var filter dto.APIKeyFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"query_params": "Invalid filter parameters: " + err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	// Create cursor request
	cursorReq := &pagination.CursorRequest{
		Cursor: filter.Cursor,
		Limit:  pagination.ValidateLimit(filter.Limit),
	}

	// Build filters map
	filters := make(map[string]interface{})
	if filter.Status != nil {
		filters["status"] = *filter.Status
	}
	if filter.Search != "" {
		filters["search"] = filter.Search
	}
	if filter.CreatedBy != nil {
		filters["created_by"] = *filter.CreatedBy
	}
	if filter.ExpiresIn != nil {
		filters["expires_in"] = *filter.ExpiresIn
	}
	if filter.SortBy != "" {
		filters["sort_by"] = filter.SortBy
	}
	if filter.SortOrder != "" {
		filters["sort_order"] = filter.SortOrder
	}

	serviceResponse, err := h.apiKeyService.ListAPIKeysWithCursor(c.Request.Context(), tenantID, websiteID, cursorReq, filters)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to retrieve API keys", err.Error())
		return
	}

	// Use cursor paginated response format
	response.CursorPaginatedWithContext(c, serviceResponse.APIKeys, *serviceResponse.Pagination)
}

// UpdateAPIKey updates an existing API key
func (h *APIKeyHandler) UpdateAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	var req models.UpdateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	apiKey, err := h.apiKeyService.UpdateAPIKey(c.Request.Context(), tenantID, websiteID, uint(id), &req)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to update API key", err.Error())
		return
	}

	// Convert to DTO response
	apiKeyResponse := h.convertToAPIKeyResponse(apiKey)
	response.SuccessWithContext(c, apiKeyResponse)
}

// DeleteAPIKey deletes an API key
func (h *APIKeyHandler) DeleteAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	err = h.apiKeyService.DeleteAPIKey(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to delete API key", err.Error())
		return
	}

	response.SuccessWithContext(c, gin.H{"message": "API key deleted successfully"})
}

// RotateAPIKey rotates an API key
func (h *APIKeyHandler) RotateAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	var req models.RotateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)
	_ = getUserIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	rotateResponse, err := h.apiKeyService.RotateAPIKey(c.Request.Context(), tenantID, websiteID, uint(id), &req)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to rotate API key", err.Error())
		return
	}

	response.Success(c.Writer, rotateResponse)
}

// RevokeAPIKey revokes an API key
func (h *APIKeyHandler) RevokeAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	var req models.RevokeAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	err = h.apiKeyService.RevokeAPIKey(c.Request.Context(), tenantID, websiteID, uint(id), &req)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to revoke API key", err.Error())
		return
	}

	response.SuccessWithContext(c, gin.H{"message": "API key revoked successfully"})
}

// ValidateAPIKey validates an API key
func (h *APIKeyHandler) ValidateAPIKey(c *gin.Context) {
	var req models.APIKeyValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	validationResponse, err := h.apiKeyService.ValidateAPIKey(c.Request.Context(), req.Key)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to validate API key", err.Error())
		return
	}

	response.Success(c.Writer, validationResponse)
}

// GetAPIKeyUsage retrieves API key usage analytics
func (h *APIKeyHandler) GetAPIKeyUsage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	var req models.APIKeyUsageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"query_params": "Invalid query parameters: " + err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	usageResponse, err := h.apiKeyService.GetUsage(c.Request.Context(), tenantID, uint(id), &req)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to retrieve usage data", err.Error())
		return
	}

	response.Success(c.Writer, usageResponse)
}

// GetAPIKeyAnalytics retrieves API key analytics
func (h *APIKeyHandler) GetAPIKeyAnalytics(c *gin.Context) {
	var req models.APIKeyAnalyticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"query_params": "Invalid query parameters: " + err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	analyticsResponse, err := h.apiKeyService.GetAnalytics(c.Request.Context(), tenantID, &req)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to retrieve analytics data", err.Error())
		return
	}

	response.Success(c.Writer, analyticsResponse)
}

// BulkAction performs bulk actions on API keys
func (h *APIKeyHandler) BulkAction(c *gin.Context) {
	var req models.APIKeyBulkActionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		response.BadRequestWithContext(c, "Missing tenant or website ID", "")
		return
	}

	bulkResponse, err := h.apiKeyService.BulkAction(c.Request.Context(), tenantID, websiteID, &req)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to perform bulk action", err.Error())
		return
	}

	response.Success(c.Writer, bulkResponse)
}

// CreateAPIKeyPermission creates a new API key permission
func (h *APIKeyHandler) CreateAPIKeyPermission(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	var req models.CreateAPIKeyPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ValidationErrorWithContext(c, map[string]string{
			"request_body": "Invalid JSON format: " + err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)

	if tenantID == 0 {
		response.BadRequestWithContext(c, "Missing tenant ID", "")
		return
	}

	permission, err := h.apiKeyService.CreatePermission(c.Request.Context(), tenantID, uint(id), &req)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to create permission", err.Error())
		return
	}

	response.CreatedWithContext(c, permission)
}

// GetAPIKeyPermissions retrieves API key permissions
func (h *APIKeyHandler) GetAPIKeyPermissions(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid API key ID", "")
		return
	}

	tenantID := getTenantIDFromContext(c)

	if tenantID == 0 {
		response.BadRequestWithContext(c, "Missing tenant ID", "")
		return
	}

	permissions, err := h.apiKeyService.GetPermissions(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to retrieve permissions", err.Error())
		return
	}

	response.Success(c.Writer, permissions)
}

// DeleteAPIKeyPermission deletes an API key permission
func (h *APIKeyHandler) DeleteAPIKeyPermission(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("permission_id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid permission ID", "")
		return
	}

	tenantID := getTenantIDFromContext(c)

	if tenantID == 0 {
		response.BadRequestWithContext(c, "Missing tenant ID", "")
		return
	}

	err = h.apiKeyService.DeletePermission(c.Request.Context(), tenantID, uint(permissionID))
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to delete permission", err.Error())
		return
	}

	response.SuccessWithContext(c, gin.H{"message": "Permission deleted successfully"})
}

// Helper functions to get context values
func getTenantIDFromContext(c *gin.Context) uint {
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(uint); ok {
			return id
		}
	}
	return 0
}

func getWebsiteIDFromContext(c *gin.Context) uint {
	if websiteID, exists := c.Get("website_id"); exists {
		if id, ok := websiteID.(uint); ok {
			return id
		}
	}
	return 0
}

func getUserIDFromContext(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint); ok {
			return id
		}
	}
	// Return a default system user ID instead of 0 to avoid foreign key constraint violation
	// Use user ID 1 as the system default user
	return 1
}

// Conversion functions
func (h *APIKeyHandler) convertToAPIKeyResponse(apiKey *models.APIKeyResponse) *dto.APIKeyResponse {
	return &dto.APIKeyResponse{
		ID:          apiKey.ID,
		TenantID:    apiKey.TenantID,
		WebsiteID:   apiKey.WebsiteID,
		Key:         apiKey.Key,
		KeyPrefix:   apiKey.KeyPrefix,
		Name:        apiKey.Name,
		Description: apiKey.Description,
		Status:      apiKey.Status,
		RateLimit:   apiKey.RateLimit,
		RateWindow:  apiKey.RateWindow,
		ExpiresAt:   apiKey.ExpiresAt,
		LastUsedAt:  apiKey.LastUsedAt,
		CreatedAt:   apiKey.CreatedAt,
		UpdatedAt:   apiKey.UpdatedAt,
	}
}

func (h *APIKeyHandler) convertToAPIKeyDetailResponse(apiKey *models.APIKeyDetailResponse) *dto.APIKeyDetailResponse {
	// First convert the base APIKeyResponse
	baseResponse := h.convertToAPIKeyResponse(&apiKey.APIKeyResponse)
	
	return &dto.APIKeyDetailResponse{
		APIKeyResponse:  *baseResponse,
		Permissions:     apiKey.Permissions,
		Scopes:          apiKey.Scopes,
		IPWhitelist:     apiKey.IPWhitelist,
		UsageStats:      convertToAPIKeyUsageStats(apiKey.UsageStats),
		PermissionList:  convertToAPIKeyPermissionList(apiKey.PermissionList),
		ScopeList:       convertToAPIKeyScopeList(apiKey.ScopeList),
		RecentUsage:     convertToAPIKeyUsageList(apiKey.RecentUsage),
		RotationHistory: convertToAPIKeyRotationList(apiKey.RotationHistory),
	}
}

// Helper conversion functions for nested types
func convertToAPIKeyUsageStats(stats *models.APIKeyUsageStats) *dto.APIKeyUsageStats {
	if stats == nil {
		return nil
	}
	return &dto.APIKeyUsageStats{
		TotalRequests:    stats.TotalRequests,
		SuccessfulRequests: stats.SuccessfulRequests,
		FailedRequests:   stats.FailedRequests,
		LastUsedAt:       stats.LastUsedAt,
		AverageResponseTime: stats.AverageResponseTime,
		RequestsToday:    stats.RequestsToday,
		RequestsThisWeek: stats.RequestsThisWeek,
		RequestsThisMonth: stats.RequestsThisMonth,
	}
}

func convertToAPIKeyPermissionList(permissions []models.APIKeyPermission) []dto.APIKeyPermission {
	if permissions == nil {
		return nil
	}
	result := make([]dto.APIKeyPermission, len(permissions))
	for i, perm := range permissions {
		// Convert JSON conditions to map[string]interface{}
		var conditions map[string]interface{}
		if err := json.Unmarshal(perm.Conditions, &conditions); err != nil {
			conditions = make(map[string]interface{})
		}
		
		result[i] = dto.APIKeyPermission{
			ID:          perm.ID,
			APIKeyID:    perm.APIKeyID,
			Resource:    perm.Resource,
			Action:      perm.Action,
			Conditions:  conditions,
			IsActive:    true, // Default value
			CreatedAt:   perm.CreatedAt,
			UpdatedAt:   time.Now(), // Default value
		}
	}
	return result
}

func convertToAPIKeyScopeList(scopes []models.APIKeyScope) []dto.APIKeyScope {
	if scopes == nil {
		return nil
	}
	result := make([]dto.APIKeyScope, len(scopes))
	for i, scope := range scopes {
		// Convert JSON conditions to map[string]interface{}
		var conditions map[string]interface{}
		if err := json.Unmarshal(scope.Conditions, &conditions); err != nil {
			conditions = make(map[string]interface{})
		}
		
		// Convert JSON actions to []string
		var actions []string
		if err := json.Unmarshal(scope.Actions, &actions); err != nil {
			actions = []string{}
		}
		
		result[i] = dto.APIKeyScope{
			ID:          scope.ID,
			APIKeyID:    scope.APIKeyID,
			Scope:       scope.Scope,
			Resource:    scope.Resource,
			Actions:     actions,
			Conditions:  conditions,
			IsActive:    scope.IsActive,
			ExpiresAt:   scope.ExpiresAt,
			CreatedAt:   scope.CreatedAt,
			UpdatedAt:   scope.UpdatedAt,
		}
	}
	return result
}

func convertToAPIKeyUsageList(usage []models.APIKeyUsage) []dto.APIKeyUsage {
	if usage == nil {
		return nil
	}
	result := make([]dto.APIKeyUsage, len(usage))
	for i, u := range usage {
		result[i] = dto.APIKeyUsage{
			ID:           u.ID,
			APIKeyID:     u.APIKeyID,
			IPAddress:    u.IPAddress,
			UserAgent:    u.UserAgent,
			Endpoint:     u.Endpoint,
			Method:       string(u.Method), // Convert HTTPMethod to string
			StatusCode:   u.ResponseCode,   // Use ResponseCode field
			ResponseTime: int64(u.ResponseTime), // Convert int to int64
			RequestSize:  int64(u.RequestSize),  // Add missing field
			ResponseSize: int64(u.ResponseSize), // Add missing field
			RequestedAt:  u.CreatedAt,          // Use CreatedAt as RequestedAt
		}
	}
	return result
}

func convertToAPIKeyRotationList(rotations []models.APIKeyRotation) []dto.APIKeyRotation {
	if rotations == nil {
		return nil
	}
	result := make([]dto.APIKeyRotation, len(rotations))
	for i, rot := range rotations {
		result[i] = dto.APIKeyRotation{
			ID:               rot.ID,
			APIKeyID:         rot.APIKeyID,
			OldKeyPrefix:     rot.OldKeyHash[:8], // Use first 8 chars as prefix
			NewKeyPrefix:     rot.NewKeyHash[:8], // Use first 8 chars as prefix
			RotationType:     rot.RotationType,
			RotationStatus:   rot.Status,
			Reason:           rot.Reason,
			GracePeriodHours: rot.GracePeriodHours,
			GraceExpiresAt:   rot.GracePeriodExpiresAt,
			RotatedAt:        rot.RotatedAt,
			CompletedAt:      rot.CompletedAt,
		}
	}
	return result
}
