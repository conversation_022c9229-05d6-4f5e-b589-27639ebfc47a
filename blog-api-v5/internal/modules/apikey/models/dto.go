package models

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// CreateAPIKeyRequest represents the request to create a new API key
type CreateAPIKeyRequest struct {
	Name        string                 `json:"name" binding:"required,min=1,max=100"`
	Description string                 `json:"description" binding:"omitempty,max=1000"`
	Permissions map[string]interface{} `json:"permissions" binding:"omitempty"`
	Scopes      []string               `json:"scopes" binding:"omitempty"`
	IPWhitelist []string               `json:"ip_whitelist" binding:"omitempty"`
	RateLimit   int                    `json:"rate_limit" binding:"omitempty,min=1,max=100000"`
	RateWindow  int                    `json:"rate_window" binding:"omitempty,min=1,max=86400"`
	ExpiresIn   int                    `json:"expires_in" binding:"omitempty,min=1"`
}

// UpdateAPIKeyRequest represents the request to update an API key
type UpdateAPIKeyRequest struct {
	Name        *string                 `json:"name" binding:"omitempty,min=1,max=100"`
	Description *string                 `json:"description" binding:"omitempty,max=1000"`
	Permissions *map[string]interface{} `json:"permissions" binding:"omitempty"`
	Scopes      *[]string               `json:"scopes" binding:"omitempty"`
	IPWhitelist *[]string               `json:"ip_whitelist" binding:"omitempty"`
	RateLimit   *int                    `json:"rate_limit" binding:"omitempty,min=1,max=100000"`
	RateWindow  *int                    `json:"rate_window" binding:"omitempty,min=1,max=86400"`
	Status      *APIKeyStatus           `json:"status" binding:"omitempty,oneof=active inactive"`
}

// APIKeyResponse represents the response when returning an API key
type APIKeyResponse struct {
	ID          uint         `json:"id"`
	TenantID    uint         `json:"tenant_id"`
	WebsiteID   uint         `json:"website_id"`
	Key         string       `json:"key,omitempty"` // Only shown once during creation
	KeyPrefix   string       `json:"key_prefix"`
	Name        string       `json:"name"`
	Description string       `json:"description"`
	Status      APIKeyStatus `json:"status"`
	RateLimit   int          `json:"rate_limit"`
	RateWindow  int          `json:"rate_window"`
	ExpiresAt   *time.Time   `json:"expires_at"`
	LastUsedAt  *time.Time   `json:"last_used_at"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
}

// APIKeyDetailResponse represents detailed API key information
type APIKeyDetailResponse struct {
	APIKeyResponse
	Permissions     map[string]interface{} `json:"permissions"`
	Scopes          []string               `json:"scopes"`
	IPWhitelist     []string               `json:"ip_whitelist"`
	UsageStats      *APIKeyUsageStats      `json:"usage_stats,omitempty"`
	PermissionList  []APIKeyPermission     `json:"permission_list,omitempty"`
	ScopeList       []APIKeyScope          `json:"scope_list,omitempty"`
	RecentUsage     []APIKeyUsage          `json:"recent_usage,omitempty"`
	RotationHistory []APIKeyRotation       `json:"rotation_history,omitempty"`
}

// APIKeyUsageStats represents usage statistics for an API key
type APIKeyUsageStats struct {
	TotalRequests       int64      `json:"total_requests"`
	SuccessfulRequests  int64      `json:"successful_requests"`
	FailedRequests      int64      `json:"failed_requests"`
	AverageResponseTime int64      `json:"average_response_time"`
	LastUsedAt          *time.Time `json:"last_used_at"`
	MostUsedEndpoint    string     `json:"most_used_endpoint"`
	RequestsToday       int64      `json:"requests_today"`
	RequestsThisWeek    int64      `json:"requests_this_week"`
	RequestsThisMonth   int64      `json:"requests_this_month"`
	ErrorRate           float64    `json:"error_rate"`
}

// APIKeyListResponse represents the response for listing API keys
type APIKeyListResponse struct {
	APIKeys    []APIKeyResponse           `json:"api_keys"`
	Total      int64                      `json:"total,omitempty"`
	Page       int                        `json:"page,omitempty"`
	Limit      int                        `json:"limit,omitempty"`
	TotalPages int                        `json:"total_pages,omitempty"`
	HasNext    bool                       `json:"has_next,omitempty"`
	HasPrev    bool                       `json:"has_prev,omitempty"`
	Pagination *pagination.CursorResponse `json:"pagination,omitempty"`
}

// APIKeyFilter represents filters for querying API keys
type APIKeyFilter struct {
	Status    *APIKeyStatus `form:"status" binding:"omitempty,oneof=active inactive expired revoked"`
	Search    string        `form:"search" binding:"omitempty,max=100"`
	CreatedBy *uint         `form:"created_by" binding:"omitempty,min=1"`
	ExpiresIn *int          `form:"expires_in" binding:"omitempty,min=1"`
	Page      int           `form:"page" binding:"omitempty,min=1"`
	Limit     int           `form:"limit" binding:"omitempty,min=1,max=100"`
	SortBy    string        `form:"sort_by" binding:"omitempty,oneof=name created_at updated_at last_used_at"`
	SortOrder string        `form:"sort_order" binding:"omitempty,oneof=asc desc"`
}

// RotateAPIKeyRequest represents the request to rotate an API key
type RotateAPIKeyRequest struct {
	Reason           string `json:"reason" binding:"omitempty,max=255"`
	GracePeriodHours int    `json:"grace_period_hours" binding:"omitempty,min=0,max=168"`
}

// RotateAPIKeyResponse represents the response when rotating an API key
type RotateAPIKeyResponse struct {
	ID             uint           `json:"id"`
	NewKey         string         `json:"new_key"`
	NewKeyPrefix   string         `json:"new_key_prefix"`
	GracePeriod    int            `json:"grace_period_hours"`
	GraceExpiresAt *time.Time     `json:"grace_expires_at"`
	RotationType   RotationType   `json:"rotation_type"`
	RotationStatus RotationStatus `json:"rotation_status"`
	RotatedAt      time.Time      `json:"rotated_at"`
}

// RevokeAPIKeyRequest represents the request to revoke an API key
type RevokeAPIKeyRequest struct {
	Reason string `json:"reason" binding:"omitempty,max=255"`
}

// APIKeyUsageRequest represents the request for API key usage analytics
type APIKeyUsageRequest struct {
	Period      string `form:"period" binding:"omitempty,oneof=1h 24h 7d 30d 90d"`
	StartDate   string `form:"start_date" binding:"omitempty"`
	EndDate     string `form:"end_date" binding:"omitempty"`
	Granularity string `form:"granularity" binding:"omitempty,oneof=hour day week month"`
}

// APIKeyUsageResponse represents the response for API key usage analytics
type APIKeyUsageResponse struct {
	Summary    APIKeyUsageStats `json:"summary"`
	Timeline   []UsageDataPoint `json:"timeline"`
	ByEndpoint []EndpointUsage  `json:"by_endpoint"`
	ByStatus   map[string]int64 `json:"by_status"`
	ByMethod   map[string]int64 `json:"by_method"`
	TopIPs     []IPUsage        `json:"top_ips"`
}

// UsageDataPoint represents a single point in usage timeline
type UsageDataPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Requests  int64     `json:"requests"`
	Errors    int64     `json:"errors"`
	AvgTime   int64     `json:"avg_response_time"`
}

// EndpointUsage represents usage statistics for a specific endpoint
type EndpointUsage struct {
	Endpoint        string    `json:"endpoint"`
	Method          string    `json:"method"`
	Count           int64     `json:"count"`
	AvgResponseTime int64     `json:"avg_response_time"`
	ErrorRate       float64   `json:"error_rate"`
	LastUsed        time.Time `json:"last_used"`
}

// IPUsage represents usage statistics for a specific IP address
type IPUsage struct {
	IPAddress string    `json:"ip_address"`
	Count     int64     `json:"count"`
	LastUsed  time.Time `json:"last_used"`
	ErrorRate float64   `json:"error_rate"`
}

// CreateAPIKeyPermissionRequest represents the request to create API key permissions
type CreateAPIKeyPermissionRequest struct {
	Resource   string                 `json:"resource" binding:"required,min=1,max=100"`
	Action     string                 `json:"action" binding:"required,min=1,max=50"`
	Conditions map[string]interface{} `json:"conditions" binding:"omitempty"`
}

// APIKeyPermissionResponse represents the response for API key permissions
type APIKeyPermissionResponse struct {
	ID         uint                   `json:"id"`
	Resource   string                 `json:"resource"`
	Action     string                 `json:"action"`
	Conditions map[string]interface{} `json:"conditions"`
	CreatedAt  time.Time              `json:"created_at"`
}

// CreateAPIKeyScopeRequest represents the request to create API key scopes
type CreateAPIKeyScopeRequest struct {
	Scope      string                 `json:"scope" binding:"required,min=1,max=100"`
	Resource   string                 `json:"resource" binding:"required,min=1,max=100"`
	Actions    []string               `json:"actions" binding:"required,min=1"`
	Conditions map[string]interface{} `json:"conditions" binding:"omitempty"`
	ExpiresIn  int                    `json:"expires_in" binding:"omitempty,min=1"`
}

// APIKeyScopeResponse represents the response for API key scopes
type APIKeyScopeResponse struct {
	ID         uint                   `json:"id"`
	Scope      string                 `json:"scope"`
	Resource   string                 `json:"resource"`
	Actions    []string               `json:"actions"`
	Conditions map[string]interface{} `json:"conditions"`
	IsActive   bool                   `json:"is_active"`
	ExpiresAt  *time.Time             `json:"expires_at"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// APIKeyValidationRequest represents the request to validate an API key
type APIKeyValidationRequest struct {
	Key      string `json:"key" binding:"required"`
	Resource string `json:"resource" binding:"omitempty"`
	Action   string `json:"action" binding:"omitempty"`
}

// APIKeyValidationResponse represents the response for API key validation
type APIKeyValidationResponse struct {
	Valid       bool                   `json:"valid"`
	APIKey      *APIKeyResponse        `json:"api_key,omitempty"`
	Permissions map[string]interface{} `json:"permissions,omitempty"`
	Scopes      []string               `json:"scopes,omitempty"`
	RateLimit   *RateLimitInfo         `json:"rate_limit,omitempty"`
	Error       string                 `json:"error,omitempty"`
}

// RateLimitInfo represents rate limiting information
type RateLimitInfo struct {
	Limit      int   `json:"limit"`
	Remaining  int   `json:"remaining"`
	Reset      int64 `json:"reset"`
	RetryAfter int   `json:"retry_after,omitempty"`
}

// APIKeyAnalyticsRequest represents the request for API key analytics
type APIKeyAnalyticsRequest struct {
	APIKeyIDs []uint   `form:"api_key_ids" binding:"omitempty"`
	Period    string   `form:"period" binding:"omitempty,oneof=1h 24h 7d 30d 90d"`
	StartDate string   `form:"start_date" binding:"omitempty"`
	EndDate   string   `form:"end_date" binding:"omitempty"`
	Metrics   []string `form:"metrics" binding:"omitempty"`
}

// APIKeyAnalyticsResponse represents the response for API key analytics
type APIKeyAnalyticsResponse struct {
	Summary     map[string]interface{} `json:"summary"`
	KeyMetrics  []KeyMetric            `json:"key_metrics"`
	Timeline    []AnalyticsDataPoint   `json:"timeline"`
	Comparisons []KeyComparison        `json:"comparisons"`
}

// KeyMetric represents metrics for a specific API key
type KeyMetric struct {
	APIKeyID        uint       `json:"api_key_id"`
	APIKeyName      string     `json:"api_key_name"`
	TotalRequests   int64      `json:"total_requests"`
	SuccessRate     float64    `json:"success_rate"`
	ErrorRate       float64    `json:"error_rate"`
	AvgResponseTime int64      `json:"avg_response_time"`
	LastUsed        *time.Time `json:"last_used"`
}

// AnalyticsDataPoint represents a single analytics data point
type AnalyticsDataPoint struct {
	Timestamp       time.Time `json:"timestamp"`
	TotalRequests   int64     `json:"total_requests"`
	SuccessRequests int64     `json:"success_requests"`
	ErrorRequests   int64     `json:"error_requests"`
	AvgResponseTime int64     `json:"avg_response_time"`
}

// KeyComparison represents comparison between API keys
type KeyComparison struct {
	APIKeyID1        uint    `json:"api_key_id_1"`
	APIKeyName1      string  `json:"api_key_name_1"`
	APIKeyID2        uint    `json:"api_key_id_2"`
	APIKeyName2      string  `json:"api_key_name_2"`
	RequestsDiff     int64   `json:"requests_diff"`
	ErrorRateDiff    float64 `json:"error_rate_diff"`
	ResponseTimeDiff int64   `json:"response_time_diff"`
}

// APIKeyBulkActionRequest represents the request for bulk actions on API keys
type APIKeyBulkActionRequest struct {
	APIKeyIDs []uint `json:"api_key_ids" binding:"required,min=1"`
	Action    string `json:"action" binding:"required,oneof=activate deactivate revoke delete"`
	Reason    string `json:"reason" binding:"omitempty,max=255"`
}

// APIKeyBulkActionResponse represents the response for bulk actions
type APIKeyBulkActionResponse struct {
	Success     []uint              `json:"success"`
	Failed      []BulkActionFailure `json:"failed"`
	Total       int                 `json:"total"`
	Successful  int                 `json:"successful"`
	FailedCount int                 `json:"failed_count"`
}

// BulkActionFailure represents a failed bulk action
type BulkActionFailure struct {
	APIKeyID uint   `json:"api_key_id"`
	Error    string `json:"error"`
}
