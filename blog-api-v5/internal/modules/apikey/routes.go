package apikey

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/middleware"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/services"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
)

// RegisterCMSRoutes registers CMS API key management routes
func RegisterCMSRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// Initialize dependencies
	apiKeyRepo := repositories.NewAPIKeyRepository(db)
	apiKeyService := services.NewAPIKeyService(apiKeyRepo, nil) // TODO: Add proper logger
	apiKeyHandler := handlers.NewAPIKeyHandler(apiKeyService)

	// Protected API key management routes
	// These require authentication (JWT token) and tenant context
	apiKeyRoutes := router.Group("/api-keys")
	// Add authentication middleware here when available
	// apiKeyRoutes.Use(authMiddleware.RequireAuth())
	apiKeyRoutes.Use(httpmiddleware.TenantContextMiddleware())
	{
		// CRUD operations
		apiKeyRoutes.POST("", apiKeyHandler.CreateAPIKey)
		apiKeyRoutes.GET("", apiKeyHandler.ListAPIKeys)
		apiKeyRoutes.GET("/:id", apiKeyHandler.GetAPIKey)
		apiKeyRoutes.GET("/:id/detail", apiKeyHandler.GetAPIKeyDetail)
		apiKeyRoutes.PUT("/:id", apiKeyHandler.UpdateAPIKey)
		apiKeyRoutes.DELETE("/:id", apiKeyHandler.DeleteAPIKey)

		// Key management operations
		apiKeyRoutes.POST("/:id/rotate", apiKeyHandler.RotateAPIKey)
		apiKeyRoutes.POST("/:id/revoke", apiKeyHandler.RevokeAPIKey)
		apiKeyRoutes.POST("/:id/regenerate", apiKeyHandler.RotateAPIKey) // Alias for rotate

		// Usage and analytics
		apiKeyRoutes.GET("/:id/usage", apiKeyHandler.GetAPIKeyUsage)
		apiKeyRoutes.GET("/analytics", apiKeyHandler.GetAPIKeyAnalytics)

		// Bulk operations
		apiKeyRoutes.POST("/bulk-action", apiKeyHandler.BulkAction)

		// Permission management
		apiKeyRoutes.POST("/:id/permissions", apiKeyHandler.CreateAPIKeyPermission)
		apiKeyRoutes.GET("/:id/permissions", apiKeyHandler.GetAPIKeyPermissions)
		apiKeyRoutes.DELETE("/permissions/:permission_id", apiKeyHandler.DeleteAPIKeyPermission)
	}
}

// RegisterRoutes registers API key routes
func RegisterRoutes(router *gin.Engine, db *gorm.DB) {
	// Initialize dependencies
	apiKeyRepo := repositories.NewAPIKeyRepository(db)
	apiKeyService := services.NewAPIKeyService(apiKeyRepo, nil) // TODO: Add proper logger
	apiKeyHandler := handlers.NewAPIKeyHandler(apiKeyService)
	apiKeyMiddleware := middleware.NewAPIKeyMiddleware(apiKeyService)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Public validation endpoint
		v1.POST("/validate-api-key", apiKeyHandler.ValidateAPIKey)

		// Protected API key management routes
		// These require authentication (JWT token) and tenant context
		apiKeyRoutes := v1.Group("/api-keys")
		// Add authentication middleware here when available
		// apiKeyRoutes.Use(authMiddleware.RequireAuth())
		apiKeyRoutes.Use(httpmiddleware.TenantContextMiddleware())
		{
			// CRUD operations
			apiKeyRoutes.POST("", apiKeyHandler.CreateAPIKey)
			apiKeyRoutes.GET("", apiKeyHandler.ListAPIKeys)
			apiKeyRoutes.GET("/:id", apiKeyHandler.GetAPIKey)
			apiKeyRoutes.GET("/:id/detail", apiKeyHandler.GetAPIKeyDetail)
			apiKeyRoutes.PUT("/:id", apiKeyHandler.UpdateAPIKey)
			apiKeyRoutes.DELETE("/:id", apiKeyHandler.DeleteAPIKey)

			// Key management operations
			apiKeyRoutes.POST("/:id/rotate", apiKeyHandler.RotateAPIKey)
			apiKeyRoutes.POST("/:id/revoke", apiKeyHandler.RevokeAPIKey)

			// Usage and analytics
			apiKeyRoutes.GET("/:id/usage", apiKeyHandler.GetAPIKeyUsage)
			apiKeyRoutes.GET("/analytics", apiKeyHandler.GetAPIKeyAnalytics)

			// Bulk operations
			apiKeyRoutes.POST("/bulk-action", apiKeyHandler.BulkAction)

			// Permission management
			apiKeyRoutes.POST("/:id/permissions", apiKeyHandler.CreateAPIKeyPermission)
			apiKeyRoutes.GET("/:id/permissions", apiKeyHandler.GetAPIKeyPermissions)
			apiKeyRoutes.DELETE("/permissions/:permission_id", apiKeyHandler.DeleteAPIKeyPermission)
		}
	}

	// Example protected routes using API key authentication
	// These demonstrate how to use the API key middleware
	exampleRoutes := v1.Group("/example")
	exampleRoutes.Use(apiKeyMiddleware.Authenticate())
	exampleRoutes.Use(apiKeyMiddleware.RateLimit())
	exampleRoutes.Use(apiKeyMiddleware.IPWhitelist())
	exampleRoutes.Use(apiKeyMiddleware.TrackUsage())
	{
		// Example endpoint requiring read permission
		exampleRoutes.GET("/data",
			apiKeyMiddleware.RequirePermission("data", "read"),
			func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{
					"message": "Data access granted",
					"data":    []string{"item1", "item2", "item3"},
				})
			})

		// Example endpoint requiring write permission
		exampleRoutes.POST("/data",
			apiKeyMiddleware.RequirePermission("data", "write"),
			func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{
					"message": "Data write operation successful",
				})
			})

		// Example endpoint requiring specific scope
		exampleRoutes.GET("/admin/users",
			apiKeyMiddleware.RequireScope("admin:users"),
			func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{
					"message": "Admin user list access granted",
					"users":   []string{"admin1", "admin2"},
				})
			})

		// Example endpoint with multiple permission requirements
		exampleRoutes.DELETE("/data/:id",
			apiKeyMiddleware.RequirePermission("data", "delete"),
			apiKeyMiddleware.RequireScope("admin:data"),
			func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{
					"message": "Data deletion successful",
				})
			})
	}
}

// RegisterAPIKeyProtectedRoutes adds API key authentication to existing routes
func RegisterAPIKeyProtectedRoutes(router *gin.Engine, db *gorm.DB) {
	// Initialize dependencies
	apiKeyRepo := repositories.NewAPIKeyRepository(db)
	apiKeyService := services.NewAPIKeyService(apiKeyRepo, nil) // TODO: Add proper logger
	apiKeyMiddleware := middleware.NewAPIKeyMiddleware(apiKeyService)

	// Example of how to protect existing API routes with API key authentication
	// This would be used for external API access

	// Public API v1 routes (for external developers)
	publicAPI := router.Group("/api/public/v1")
	publicAPI.Use(apiKeyMiddleware.Authenticate())
	publicAPI.Use(apiKeyMiddleware.RateLimit())
	publicAPI.Use(apiKeyMiddleware.IPWhitelist())
	publicAPI.Use(apiKeyMiddleware.TrackUsage())
	{
		// Blog API endpoints
		blogRoutes := publicAPI.Group("/blog")
		blogRoutes.Use(apiKeyMiddleware.RequireScope("blog:read"))
		{
			blogRoutes.GET("/posts", func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{"message": "Blog posts endpoint"})
			})
			blogRoutes.GET("/posts/:id", func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{"message": "Blog post detail endpoint"})
			})
		}

		// User API endpoints
		userRoutes := publicAPI.Group("/users")
		userRoutes.Use(apiKeyMiddleware.RequireScope("users:read"))
		{
			userRoutes.GET("/profile", func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{"message": "User profile endpoint"})
			})
		}

		// Analytics API endpoints
		analyticsRoutes := publicAPI.Group("/analytics")
		analyticsRoutes.Use(apiKeyMiddleware.RequireScope("analytics:read"))
		{
			analyticsRoutes.GET("/reports", func(c *gin.Context) {
				response.SuccessWithContext(c, gin.H{"message": "Analytics reports endpoint"})
			})
		}
	}

	// Admin API endpoints (higher permissions required)
	adminAPI := router.Group("/api/cms/v1")
	adminAPI.Use(apiKeyMiddleware.Authenticate())
	adminAPI.Use(apiKeyMiddleware.RequireScope("admin:all"))
	adminAPI.Use(apiKeyMiddleware.RateLimit())
	adminAPI.Use(apiKeyMiddleware.TrackUsage())
	{
		adminAPI.GET("/system/health", func(c *gin.Context) {
			response.SuccessWithContext(c, gin.H{"message": "System health endpoint"})
		})

		adminAPI.GET("/system/stats", func(c *gin.Context) {
			response.SuccessWithContext(c, gin.H{"message": "System statistics endpoint"})
		})
	}
}

// RegisterWebhookRoutes registers webhook routes that can be called with API keys
func RegisterWebhookRoutes(router *gin.Engine, db *gorm.DB) {
	// Initialize dependencies
	apiKeyRepo := repositories.NewAPIKeyRepository(db)
	apiKeyService := services.NewAPIKeyService(apiKeyRepo, nil) // TODO: Add proper logger
	apiKeyMiddleware := middleware.NewAPIKeyMiddleware(apiKeyService)

	// Webhook endpoints
	webhooks := router.Group("/webhooks")
	webhooks.Use(apiKeyMiddleware.Authenticate())
	webhooks.Use(apiKeyMiddleware.RequireScope("webhooks:receive"))
	webhooks.Use(apiKeyMiddleware.TrackUsage())
	{
		webhooks.POST("/blog/post-created", func(c *gin.Context) {
			response.SuccessWithContext(c, gin.H{"message": "Blog post created webhook received"})
		})

		webhooks.POST("/user/registered", func(c *gin.Context) {
			response.SuccessWithContext(c, gin.H{"message": "User registered webhook received"})
		})

		webhooks.POST("/payment/completed", func(c *gin.Context) {
			response.SuccessWithContext(c, gin.H{"message": "Payment completed webhook received"})
		})
	}
}

// GetAPIKeyMiddleware returns the API key middleware for use in other modules
func GetAPIKeyMiddleware(db *gorm.DB) *middleware.APIKeyMiddleware {
	apiKeyRepo := repositories.NewAPIKeyRepository(db)
	apiKeyService := services.NewAPIKeyService(apiKeyRepo, nil) // TODO: Add proper logger
	return middleware.NewAPIKeyMiddleware(apiKeyService)
}

// GetAPIKeyService returns the API key service for use in other modules
func GetAPIKeyService(db *gorm.DB) services.APIKeyService {
	apiKeyRepo := repositories.NewAPIKeyRepository(db)
	return services.NewAPIKeyService(apiKeyRepo, nil) // TODO: Add proper logger
}
