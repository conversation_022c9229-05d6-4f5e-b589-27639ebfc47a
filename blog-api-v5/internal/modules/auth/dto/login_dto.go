package dto

// LoginRequest represents the request payload for user login
type LoginRequest struct {
	Email         string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Password      string `json:"password" validate:"required" example:"Admin@123"`
	TwoFactorCode string `json:"two_factor_code,omitempty" example:""`
	DeviceName    string `json:"device_name,omitempty" example:"iPhone 15 Pro"`
	IPAddress     string `json:"-"`
	UserAgent     string `json:"-"`
}

// LoginResponse represents the response payload for successful login
type LoginResponse struct {
	AccessToken               string `json:"access_token"`
	RefreshToken              string `json:"refresh_token"`
	ExpiresIn                 int    `json:"expires_in"`
	TokenType                 string `json:"token_type"`
	SessionID                 uint   `json:"session_id"`
	RequiresTwoFactor         bool   `json:"requires_two_factor"`
	RequiresEmailVerification bool   `json:"requires_email_verification"`
	RequiresOnboarding        bool   `json:"requires_onboarding"`
}
