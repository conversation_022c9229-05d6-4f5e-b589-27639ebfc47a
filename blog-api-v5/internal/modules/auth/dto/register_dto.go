package dto

// RegisterRequest represents the request payload for user registration
type RegisterRequest struct {
	Email           string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Username        string `json:"username,omitempty" validate:"omitempty,min=3,max=50" example:"johndoe"`
	Password        string `json:"password" validate:"required,min=8" example:"SecurePass123!"`
	FirstName       string `json:"first_name,omitempty" validate:"omitempty,max=100" example:"John"`
	LastName        string `json:"last_name,omitempty" validate:"omitempty,max=100" example:"Doe"`
	InvitationToken string `json:"invitation_token,omitempty" example:""`
	IPAddress       string `json:"-"`
	UserAgent       string `json:"-"`
}

// RegisterResponse represents the response payload for user registration
type RegisterResponse struct {
	Message                   string `json:"message"`
	RequiresEmailVerification bool   `json:"requires_email_verification"`
	EmailVerificationSent     bool   `json:"email_verification_sent"`
}
