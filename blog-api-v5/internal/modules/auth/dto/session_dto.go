package dto

import "time"

// LogoutResponse represents the response payload for logout
type LogoutResponse struct {
	SessionsTerminated int `json:"sessions_terminated"`
}

// SessionResponse represents a single session in the response
// KEEP_OMITEMPTY: DeviceName may be optional/empty
type SessionResponse struct {
	ID         uint      `json:"id"`
	DeviceType string    `json:"device_type"`
	DeviceName string    `json:"device_name,omitempty"`
	IPAddress  string    `json:"ip_address"`
	LastUsedAt time.Time `json:"last_used_at"`
	ExpiresAt  time.Time `json:"expires_at"`
}

// GetActiveSessionsResponse represents the response payload for getting active sessions
type GetActiveSessionsResponse struct {
	Sessions []SessionResponse `json:"sessions"`
}

// RevokeSessionResponse represents the response payload for revoking a session
type RevokeSessionResponse struct {
	Revoked      bool `json:"revoked"`
	RevokedCount int  `json:"revoked_count"`
}

// LogoutAllDevicesResponse represents the response payload for logout all devices
type LogoutAllDevicesResponse struct {
	SessionsTerminated int `json:"sessions_terminated"`
}
