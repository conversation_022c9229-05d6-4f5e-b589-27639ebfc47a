package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// Keep imports for swagger generation
var (
	_ = models.OAuthConnectionResponse{}
)

// OAuthHandler handles OAuth-related HTTP requests
type OAuthHandler struct {
	oauthService *services.OAuthService
	logger       utils.Logger
}

// NewOAuthHandler creates a new OAuth handler instance
func NewOAuthHandler(oauthService *services.OAuthService, logger utils.Logger) *OAuthHandler {
	return &OAuthHandler{
		oauthService: oauthService,
		logger:       logger,
	}
}

// GetOAuthAuthURL generates OAuth authorization URL for any provider
// @Summary Get OAuth authorization URL
// @Description Generate OAuth authorization URL for user authentication
// @Tags OAuth
// @Accept json
// @Produce json
// @Param X-Website-ID header string true "Website ID"
// @Param request body dto.OAuthAuthURLRequest true "OAuth authorization URL request"
// @Success 200 {object} response.Response{data=dto.OAuthAuthURLResponse} "OAuth authorization URL"
// @Failure 400 {object} response.Response "Bad Request"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /auth/oauth/auth-url [post]
func (h *OAuthHandler) GetOAuthAuthURL(c *gin.Context) {
	ctx := c.Request.Context()

	// Get website ID from header
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		h.logger.Error("Missing X-Website-ID header")
		response.BadRequestWithContext(c, "X-Website-ID header is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid X-Website-ID header", "value", websiteIDStr, "error", err)
		response.BadRequestWithContext(c, "Invalid X-Website-ID header")
		return
	}

	// Parse request
	var req dto.OAuthAuthURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.ValidationError(c.Writer, err)
		return
	}

	// Generate authorization URL
	result, err := h.oauthService.GetOAuthAuthURL(ctx, uint(websiteID), &req, c.Request.Host)
	if err != nil {
		h.logger.Error("Failed to generate OAuth auth URL", "error", err, "website_id", websiteID, "provider", req.Provider)
		response.InternalServerErrorWithContext(c, "Failed to generate authorization URL")
		return
	}

	response.SuccessWithContext(c, result)
}

// GetGoogleAuthURL generates Google OAuth authorization URL
// @Summary Get Google OAuth authorization URL
// @Description Generate Google OAuth authorization URL for user authentication
// @Tags OAuth
// @Accept json
// @Produce json
// @Param X-Website-ID header string true "Website ID"
// @Param request body dto.OAuthAuthURLRequest true "OAuth authorization URL request"
// @Success 200 {object} response.Response{data=dto.OAuthAuthURLResponse} "Google OAuth authorization URL"
// @Failure 400 {object} response.Response "Bad Request"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /auth/oauth/google/auth-url [post]
func (h *OAuthHandler) GetGoogleAuthURL(c *gin.Context) {
	ctx := c.Request.Context()

	// Get website ID from header
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		h.logger.Error("Missing X-Website-ID header")
		response.BadRequestWithContext(c, "X-Website-ID header is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid X-Website-ID header", "value", websiteIDStr, "error", err)
		response.BadRequestWithContext(c, "Invalid X-Website-ID header")
		return
	}

	// Parse request
	var req dto.OAuthAuthURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.ValidationError(c.Writer, err)
		return
	}

	// Generate authorization URL
	result, err := h.oauthService.GetGoogleAuthURL(ctx, uint(websiteID), &req, c.Request.Host)
	if err != nil {
		h.logger.Error("Failed to generate Google auth URL", "error", err, "website_id", websiteID)
		response.InternalServerErrorWithContext(c, "Failed to generate authorization URL")
		return
	}

	response.SuccessWithContext(c, result)
}

// LoginWithOAuth handles OAuth login for any provider
// @Summary Login with OAuth
// @Description Authenticate user using OAuth authorization code
// @Tags OAuth
// @Accept json
// @Produce json
// @Param X-Website-ID header string true "Website ID"
// @Param request body dto.OAuthLoginRequest true "OAuth login request"
// @Success 200 {object} response.Response{data=dto.OAuthLoginResponse} "Login successful"
// @Failure 400 {object} response.Response "Bad Request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /auth/oauth/login [post]
func (h *OAuthHandler) LoginWithOAuth(c *gin.Context) {
	ctx := c.Request.Context()

	// Get website ID from header
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		h.logger.Error("Missing X-Website-ID header")
		response.BadRequestWithContext(c, "X-Website-ID header is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid X-Website-ID header", "value", websiteIDStr, "error", err)
		response.BadRequestWithContext(c, "Invalid X-Website-ID header")
		return
	}

	// Parse request
	var req dto.OAuthLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.ValidationError(c.Writer, err)
		return
	}

	// Perform OAuth login
	result, err := h.oauthService.LoginWithOAuth(ctx, uint(websiteID), &req, c.Request.Host)
	if err != nil {
		h.logger.Error("OAuth login failed", "error", err, "website_id", websiteID, "provider", req.Provider)

		// Check if it's an authentication error
		if err.Error() == "invalid state parameter" || err.Error() == "failed to exchange authorization code" {
			response.UnauthorizedWithContext(c, err.Error())
			return
		}

		response.InternalServerErrorWithContext(c, "OAuth login failed")
		return
	}

	h.logger.Info("OAuth login successful",
		"is_new_user", result.IsNewUser,
		"provider", req.Provider,
		"website_id", websiteID)

	response.SuccessWithContext(c, result)
}

// LoginWithGoogle handles Google OAuth login
// @Summary Login with Google OAuth
// @Description Authenticate user using Google OAuth authorization code
// @Tags OAuth
// @Accept json
// @Produce json
// @Param X-Website-ID header string true "Website ID"
// @Param request body dto.OAuthLoginRequest true "OAuth login request"
// @Success 200 {object} response.Response{data=dto.OAuthLoginResponse} "Login successful"
// @Failure 400 {object} response.Response "Bad Request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /auth/oauth/google/login [post]
func (h *OAuthHandler) LoginWithGoogle(c *gin.Context) {
	ctx := c.Request.Context()

	// Get website ID from header
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		h.logger.Error("Missing X-Website-ID header")
		response.BadRequestWithContext(c, "X-Website-ID header is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid X-Website-ID header", "value", websiteIDStr, "error", err)
		response.BadRequestWithContext(c, "Invalid X-Website-ID header")
		return
	}

	// Parse request
	var req dto.OAuthLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.ValidationError(c.Writer, err)
		return
	}

	// Perform OAuth login
	result, err := h.oauthService.LoginWithGoogle(ctx, uint(websiteID), &req, c.Request.Host)
	if err != nil {
		h.logger.Error("Google OAuth login failed", "error", err, "website_id", websiteID)

		// Check if it's an authentication error
		if err.Error() == "invalid state parameter" || err.Error() == "failed to exchange authorization code" {
			response.UnauthorizedWithContext(c, err.Error())
			return
		}

		response.InternalServerErrorWithContext(c, "OAuth login failed")
		return
	}

	h.logger.Info("Google OAuth login successful",
		"is_new_user", result.IsNewUser,
		"website_id", websiteID)

	response.SuccessWithContext(c, result)
}

// GetFacebookAuthURL generates Facebook OAuth authorization URL
// @Summary Get Facebook OAuth authorization URL
// @Description Generate Facebook OAuth authorization URL for user authentication
// @Tags OAuth
// @Accept json
// @Produce json
// @Param X-Website-ID header string true "Website ID"
// @Param request body dto.OAuthAuthURLRequest true "OAuth authorization URL request"
// @Success 200 {object} response.Response{data=dto.OAuthAuthURLResponse} "Facebook OAuth authorization URL"
// @Failure 400 {object} response.Response "Bad Request"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /auth/oauth/facebook/auth-url [post]
func (h *OAuthHandler) GetFacebookAuthURL(c *gin.Context) {
	ctx := c.Request.Context()

	// Get website ID from header
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		h.logger.Error("Missing X-Website-ID header")
		response.BadRequestWithContext(c, "X-Website-ID header is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid X-Website-ID header", "value", websiteIDStr, "error", err)
		response.BadRequestWithContext(c, "Invalid X-Website-ID header")
		return
	}

	// Parse request
	var req dto.OAuthAuthURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.ValidationError(c.Writer, err)
		return
	}

	// Force provider to facebook for this endpoint
	req.Provider = "facebook"

	// Generate authorization URL
	result, err := h.oauthService.GetFacebookAuthURL(ctx, uint(websiteID), &req, c.Request.Host)
	if err != nil {
		h.logger.Error("Failed to generate Facebook auth URL", "error", err, "website_id", websiteID)
		response.InternalServerErrorWithContext(c, "Failed to generate authorization URL")
		return
	}

	response.SuccessWithContext(c, result)
}

// LoginWithFacebook handles Facebook OAuth login
// @Summary Login with Facebook OAuth
// @Description Authenticate user using Facebook OAuth authorization code
// @Tags OAuth
// @Accept json
// @Produce json
// @Param X-Website-ID header string true "Website ID"
// @Param request body dto.OAuthLoginRequest true "OAuth login request"
// @Success 200 {object} response.Response{data=dto.OAuthLoginResponse} "Login successful"
// @Failure 400 {object} response.Response "Bad Request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /auth/oauth/facebook/login [post]
func (h *OAuthHandler) LoginWithFacebook(c *gin.Context) {
	ctx := c.Request.Context()

	// Get website ID from header
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		h.logger.Error("Missing X-Website-ID header")
		response.BadRequestWithContext(c, "X-Website-ID header is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid X-Website-ID header", "value", websiteIDStr, "error", err)
		response.BadRequestWithContext(c, "Invalid X-Website-ID header")
		return
	}

	// Parse request
	var req dto.OAuthLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.ValidationError(c.Writer, err)
		return
	}

	// Force provider to facebook for this endpoint
	req.Provider = "facebook"

	// Perform OAuth login
	result, err := h.oauthService.LoginWithFacebook(ctx, uint(websiteID), &req, c.Request.Host)
	if err != nil {
		h.logger.Error("Facebook OAuth login failed", "error", err, "website_id", websiteID)

		// Check if it's an authentication error
		if err.Error() == "invalid state parameter" || err.Error() == "failed to exchange authorization code" {
			response.UnauthorizedWithContext(c, err.Error())
			return
		}

		response.InternalServerErrorWithContext(c, "OAuth login failed")
		return
	}

	h.logger.Info("Facebook OAuth login successful",
		"is_new_user", result.IsNewUser,
		"website_id", websiteID)

	response.SuccessWithContext(c, result)
}

// GetOAuthConnections retrieves user's OAuth connections
// @Summary Get OAuth connections
// @Description Retrieve authenticated user's OAuth connections
// @Tags OAuth
// @Accept json
// @Produce json
// @Security BearerToken
// @Param X-Website-ID header string true "Website ID"
// @Success 200 {object} response.Response{data=[]models.OAuthConnectionResponse} "OAuth connections retrieved successfully"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /auth/oauth/connections [get]
func (h *OAuthHandler) GetOAuthConnections(c *gin.Context) {
	ctx := c.Request.Context()

	// Get authenticated user
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("User ID not found in context")
		response.UnauthorizedWithContext(c, "Authentication required")
		return
	}

	// Get website ID from header
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		h.logger.Error("Missing X-Website-ID header")
		response.BadRequestWithContext(c, "X-Website-ID header is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid X-Website-ID header", "value", websiteIDStr, "error", err)
		response.BadRequestWithContext(c, "Invalid X-Website-ID header")
		return
	}

	// Get user's OAuth connections
	connections, err := h.oauthService.GetUserOAuthConnections(ctx, userID.(uint), uint(websiteID))
	if err != nil {
		h.logger.Error("Failed to get OAuth connections", "error", err, "user_id", userID)
		response.InternalServerErrorWithContext(c, "Failed to retrieve OAuth connections")
		return
	}

	response.SuccessWithContext(c, connections)
}

// DisconnectOAuth disconnects an OAuth provider
// @Summary Disconnect OAuth provider
// @Description Disconnect and revoke OAuth connection for a provider
// @Tags OAuth
// @Accept json
// @Produce json
// @Security BearerToken
// @Param X-Website-ID header string true "Website ID"
// @Param provider path string true "OAuth provider name"
// @Success 200 {object} response.Response "OAuth provider disconnected successfully"
// @Failure 400 {object} response.Response "Bad Request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "OAuth connection not found"
// @Failure 500 {object} response.Response "Internal Server Error"
// @Router /auth/oauth/{provider}/disconnect [post]
func (h *OAuthHandler) DisconnectOAuth(c *gin.Context) {
	ctx := c.Request.Context()

	// Get authenticated user
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("User ID not found in context")
		response.UnauthorizedWithContext(c, "Authentication required")
		return
	}

	// Get provider from URL parameter
	provider := c.Param("provider")
	if provider == "" {
		h.logger.Error("Missing provider parameter")
		response.BadRequestWithContext(c, "Provider parameter is required")
		return
	}

	// Get website ID from header
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr == "" {
		h.logger.Error("Missing X-Website-ID header")
		response.BadRequestWithContext(c, "X-Website-ID header is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid X-Website-ID header", "value", websiteIDStr, "error", err)
		response.BadRequestWithContext(c, "Invalid X-Website-ID header")
		return
	}

	// Disconnect OAuth provider
	err = h.oauthService.DisconnectOAuthProvider(ctx, userID.(uint), uint(websiteID), provider)
	if err != nil {
		h.logger.Error("Failed to disconnect OAuth provider", "error", err, "user_id", userID, "provider", provider)
		if err.Error() == "OAuth connection not found" {
			response.NotFound(c.Writer, "OAuth connection not found")
			return
		}
		response.InternalServerErrorWithContext(c, "Failed to disconnect OAuth provider")
		return
	}

	h.logger.Info("OAuth provider disconnected", "user_id", userID, "provider", provider, "website_id", websiteID)
	response.SuccessWithContext(c, map[string]string{"message": "OAuth provider disconnected successfully"})
}
