package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// TwoFactorHandler handles two-factor authentication operations
type TwoFactorHandler struct {
	authService services.AuthService
	validator   validator.Validator
	logger      utils.Logger
}

// NewTwoFactorHandler creates a new two-factor handler
func NewTwoFactorHandler(
	authService services.AuthService,
	validator validator.Validator,
	logger utils.Logger,
) *TwoFactorHandler {
	return &TwoFactorHandler{
		authService: authService,
		validator:   validator,
		logger:      logger,
	}
}

// EnableTwoFactor godoc
// @Summary Enable two-factor authentication
// @Description Enable 2FA for the authenticated user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=dto.EnableTwoFactorResponse} "Two-factor authentication enabled"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /2fa/enable [post]
func (h *TwoFactorHandler) EnableTwoFactor(c *gin.Context) {
	// Get user ID from JWT claims
	userID, exists := c.Get(middleware.UserIDKey)
	if !exists {
		response.UnauthorizedWithContext(c, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user context")
		return
	}

	// Enable two-factor authentication
	result, err := h.authService.EnableTwoFactor(c.Request.Context(), uid)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": uid,
		}).Error("Failed to enable two-factor authentication")

		response.InternalError(c.Writer, "Failed to enable two-factor authentication", err.Error())
		return
	}

	// Successful two-factor setup
	h.logger.WithFields(map[string]interface{}{
		"user_id": uid,
	}).Info("Two-factor authentication enabled successfully")

	// Convert service response to DTO response
	enableResponse := &dto.EnableTwoFactorResponse{
		Secret:      result.Secret,
		QRCode:      result.QRCode,
		BackupCodes: result.BackupCodes,
		Status:      "enabled",
	}

	response.SuccessWithContext(c, enableResponse)
}

// DisableTwoFactor godoc
// @Summary Disable two-factor authentication
// @Description Disable 2FA for the authenticated user with verification code
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body dto.DisableTwoFactorRequest true "Disable 2FA request with verification code"
// @Success 200 {object} response.Response{data=dto.DisableTwoFactorResponse} "Two-factor authentication disabled"
// @Failure 400 {object} response.Response "Invalid request or verification code"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /2fa/disable [post]
func (h *TwoFactorHandler) DisableTwoFactor(c *gin.Context) {
	var req dto.DisableTwoFactorRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get user ID from JWT claims
	userID, exists := c.Get(middleware.UserIDKey)
	if !exists {
		response.UnauthorizedWithContext(c, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user context")
		return
	}

	// Disable two-factor authentication
	result, err := h.authService.DisableTwoFactor(c.Request.Context(), uid, req.Code)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": uid,
		}).Error("Failed to disable two-factor authentication")

		response.BadRequestWithContext(c, "Failed to disable two-factor authentication", err.Error())
		return
	}

	// Successful two-factor disable
	h.logger.WithFields(map[string]interface{}{
		"user_id": uid,
	}).Info("Two-factor authentication disabled successfully")

	// Convert service response to DTO response
	disableResponse := &dto.DisableTwoFactorResponse{
		Verified: result.Verified,
		Status:   "disabled",
	}

	response.SuccessWithContext(c, disableResponse)
}

// VerifyTwoFactor godoc
// @Summary Verify two-factor authentication code
// @Description Verify 2FA code for the authenticated user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body dto.VerifyTwoFactorRequest true "2FA verification code"
// @Success 200 {object} response.Response{data=dto.VerifyTwoFactorResponse} "Two-factor authentication verified"
// @Failure 400 {object} response.Response "Invalid verification code"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /2fa/verify [post]
func (h *TwoFactorHandler) VerifyTwoFactor(c *gin.Context) {
	var req dto.VerifyTwoFactorRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get user ID from JWT claims
	userID, exists := c.Get(middleware.UserIDKey)
	if !exists {
		response.UnauthorizedWithContext(c, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user context")
		return
	}

	// Verify two-factor code
	result, err := h.authService.VerifyTwoFactor(c.Request.Context(), uid, req.Code)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": uid,
		}).Error("Two-factor verification failed")

		response.BadRequestWithContext(c, "Invalid two-factor code")
		return
	}

	// Successful two-factor verification
	h.logger.WithFields(map[string]interface{}{
		"user_id": uid,
	}).Info("Two-factor verification successful")

	// Convert service response to DTO response
	verifyResponse := &dto.VerifyTwoFactorResponse{
		Verified:     result.Verified,
		BackupCodes:  result.BackupCodes,
		RecoveryUsed: result.RecoveryUsed,
		Status:       "verified",
	}

	response.SuccessWithContext(c, verifyResponse)
}

// CompleteTwoFactorSetup godoc
// @Summary Complete two-factor authentication setup
// @Description Complete 2FA setup by verifying the TOTP code and enabling 2FA
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body dto.VerifyTwoFactorRequest true "2FA verification code to complete setup"
// @Success 200 {object} response.Response{data=dto.VerifyTwoFactorResponse} "Two-factor authentication setup completed"
// @Failure 400 {object} response.Response "Invalid verification code or setup not initiated"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /2fa/complete-setup [post]
func (h *TwoFactorHandler) CompleteTwoFactorSetup(c *gin.Context) {
	var req dto.VerifyTwoFactorRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Get user ID from JWT claims
	userID, exists := c.Get(middleware.UserIDKey)
	if !exists {
		response.UnauthorizedWithContext(c, "Authentication required")
		return
	}

	uid, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user context")
		return
	}

	// Complete two-factor setup
	result, err := h.authService.CompleteTwoFactorSetup(c.Request.Context(), uid, req.Code)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"user_id": uid,
		}).Error("Two-factor setup completion failed")

		response.BadRequestWithContext(c, "Failed to complete two-factor setup", err.Error())
		return
	}

	// Successful two-factor setup completion
	h.logger.WithFields(map[string]interface{}{
		"user_id": uid,
	}).Info("Two-factor authentication setup completed successfully")

	// Convert service response to DTO response
	completeResponse := &dto.VerifyTwoFactorResponse{
		Verified:     result.Verified,
		BackupCodes:  result.BackupCodes,
		RecoveryUsed: result.RecoveryUsed,
		Status:       "setup_completed",
	}

	response.SuccessWithContext(c, completeResponse)
}

// CompleteLogin godoc
// @Summary Complete login with 2FA
// @Description Complete login process when user has 2FA enabled
// @Tags auth
// @Accept json
// @Produce json
// @Param body body dto.CompleteTwoFactorLoginRequest true "Complete login with 2FA code"
// @Success 200 {object} response.Response{data=dto.CompleteTwoFactorLoginResponse} "Login completed successfully"
// @Failure 400 {object} response.Response "Invalid request or 2FA code"
// @Failure 401 {object} response.Response "Authentication failed"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /2fa/complete-login [post]
func (h *TwoFactorHandler) CompleteLogin(c *gin.Context) {
	var req dto.CompleteTwoFactorLoginRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Complete two-factor login
	authResponse, err := h.authService.CompleteTwoFactorLogin(c.Request.Context(), req.Email, req.TwoFactorCode)
	if err != nil {
		h.logger.WithError(err).WithFields(map[string]interface{}{
			"email": req.Email,
		}).Error("Two-factor login completion failed")

		response.BadRequestWithContext(c, "Failed to complete two-factor login", err.Error())
		return
	}

	// Successful two-factor login completion
	h.logger.WithFields(map[string]interface{}{
		"email":   req.Email,
		"user_id": authResponse.User.ID,
	}).Info("Two-factor login completed successfully")

	// Convert service response to DTO response
	completeLoginResponse := &dto.CompleteTwoFactorLoginResponse{
		AccessToken:  authResponse.AccessToken,
		RefreshToken: authResponse.RefreshToken,
		ExpiresIn:    authResponse.ExpiresIn,
		TokenType:    authResponse.TokenType,
		SessionID:    0, // Will be set if session ID is available
	}

	response.SuccessWithContext(c, completeLoginResponse)
}
