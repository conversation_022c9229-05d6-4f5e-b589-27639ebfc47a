package models

import (
	"database/sql/driver"
	"encoding/json"
	"strings"
	"time"
)

// OAuthProviderStatus represents the status of an OAuth provider
// @Enum active,inactive,disabled
type OAuthProviderStatus string

const (
	OAuthProviderStatusActive   OAuthProviderStatus = "active"
	OAuthProviderStatusInactive OAuthProviderStatus = "inactive"
	OAuthProviderStatusDisabled OAuthProviderStatus = "disabled"
)

// Scan implements sql.Scanner interface
func (ops *OAuthProviderStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*ops = OAuthProviderStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (ops OAuthProviderStatus) Value() (driver.Value, error) {
	return string(ops), nil
}

// OAuthProviderConfig represents OAuth provider configuration
type OAuthProviderConfig map[string]interface{}

// Scan implements sql.Scanner interface
func (opc *OAuthProviderConfig) Scan(value interface{}) error {
	if value == nil {
		*opc = make(OAuthProviderConfig)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, opc)
}

// Value implements driver.Valuer interface
func (opc OAuthProviderConfig) Value() (driver.Value, error) {
	if opc == nil {
		return "{}", nil
	}
	return json.Marshal(opc)
}

// RedirectURLs represents multiple OAuth redirect URLs for multi-domain support
type RedirectURLs []string

// Scan implements sql.Scanner interface
func (rurl *RedirectURLs) Scan(value interface{}) error {
	if value == nil {
		*rurl = make(RedirectURLs, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, rurl)
}

// Value implements driver.Valuer interface
func (rurl RedirectURLs) Value() (driver.Value, error) {
	if rurl == nil {
		return "[]", nil
	}
	return json.Marshal(rurl)
}

// Contains checks if a redirect URL exists in the list
func (rurl RedirectURLs) Contains(url string) bool {
	for _, u := range rurl {
		if u == url {
			return true
		}
	}
	return false
}

// GetForDomain returns the redirect URL that matches the given domain
func (rurl RedirectURLs) GetForDomain(domain string) string {
	for _, url := range rurl {
		if strings.Contains(url, domain) {
			return url
		}
	}
	// Return first URL as fallback
	if len(rurl) > 0 {
		return rurl[0]
	}
	return ""
}

// OAuthProvider represents an OAuth provider configuration
type OAuthProvider struct {
	ID        uint `json:"id" gorm:"primaryKey"`
	WebsiteID uint `json:"website_id" gorm:"not null;index"`

	// Provider Information
	Name         string `json:"name" gorm:"not null" validate:"required,max=100"`
	Provider     string `json:"provider" gorm:"not null;index" validate:"required,oneof=google github facebook twitter linkedin microsoft discord"`
	ClientID     string `json:"client_id" gorm:"not null"`
	ClientSecret string `json:"-" gorm:"not null"` // Don't expose in JSON

	// Configuration
	Config OAuthProviderConfig `json:"config" gorm:"type:json"`

	// URLs
	AuthorizeURL string       `json:"authorize_url" gorm:"not null"`
	TokenURL     string       `json:"token_url" gorm:"not null"`
	UserInfoURL  string       `json:"user_info_url" gorm:"not null"`
	RedirectURL  string       `json:"redirect_url"`                   // Single redirect URL (backward compatibility)
	RedirectURLs RedirectURLs `json:"redirect_urls" gorm:"type:json"` // Multiple redirect URLs for multi-domain

	// Scopes
	Scopes string `json:"scopes" gorm:"not null;default:'email profile'"`

	// Status
	Status    OAuthProviderStatus `json:"status" gorm:"type:enum('active','inactive','disabled');default:'active';not null"`
	IsEnabled bool                `json:"is_enabled" gorm:"default:true"`

	// Display
	DisplayName *string `json:"display_name,omitempty"`
	IconURL     *string `json:"icon_url,omitempty"`
	SortOrder   int     `json:"sort_order" gorm:"default:0"`

	// Statistics
	TotalConnections int `json:"total_connections" gorm:"default:0"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	// Website     *models.Website     `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// Connections []OAuthConnection   `json:"connections,omitempty" gorm:"foreignKey:ProviderID"`
}

// TableName specifies the table name for OAuthProvider
func (OAuthProvider) TableName() string {
	return "auth_oauth_providers"
}

// IsActive checks if OAuth provider is active
func (op *OAuthProvider) IsActive() bool {
	return op.Status == OAuthProviderStatusActive && op.IsEnabled
}

// GetDisplayName returns the display name or falls back to name
func (op *OAuthProvider) GetDisplayName() string {
	if op.DisplayName != nil && *op.DisplayName != "" {
		return *op.DisplayName
	}
	return op.Name
}

// GetRedirectURLForDomain returns the appropriate redirect URL for the given domain
func (op *OAuthProvider) GetRedirectURLForDomain(domain string) string {
	if len(op.RedirectURLs) > 0 {
		return op.RedirectURLs.GetForDomain(domain)
	}
	// Fallback to single redirect URL
	return op.RedirectURL
}

// GetRedirectURLForRequest returns the appropriate redirect URL for the HTTP request
func (op *OAuthProvider) GetRedirectURLForRequest(host string) string {
	if len(op.RedirectURLs) > 0 {
		// Try to find a redirect URL that matches the current host
		for _, url := range op.RedirectURLs {
			if strings.Contains(url, host) {
				return url
			}
		}
		// Return first available redirect URL if no exact match
		return op.RedirectURLs[0]
	}
	// Fallback to single redirect URL
	return op.RedirectURL
}

// AddRedirectURL adds a new redirect URL to the list
func (op *OAuthProvider) AddRedirectURL(url string) {
	if op.RedirectURLs == nil {
		op.RedirectURLs = make(RedirectURLs, 0)
	}
	if !op.RedirectURLs.Contains(url) {
		op.RedirectURLs = append(op.RedirectURLs, url)
	}
}

// BeforeCreate hook for OAuthProvider
func (op *OAuthProvider) BeforeCreate() error {
	if op.Config == nil {
		op.Config = make(OAuthProviderConfig)
	}
	if op.RedirectURLs == nil {
		op.RedirectURLs = make(RedirectURLs, 0)
	}
	return nil
}

// OAuthConnectionStatus represents the status of an OAuth connection
// @Enum active,revoked,expired,disabled,disconnected
type OAuthConnectionStatus string

const (
	OAuthConnectionStatusActive       OAuthConnectionStatus = "active"
	OAuthConnectionStatusRevoked      OAuthConnectionStatus = "revoked"
	OAuthConnectionStatusExpired      OAuthConnectionStatus = "expired"
	OAuthConnectionStatusDisabled     OAuthConnectionStatus = "disabled"
	OAuthConnectionStatusDisconnected OAuthConnectionStatus = "disconnected"
)

// Scan implements sql.Scanner interface
func (ocs *OAuthConnectionStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*ocs = OAuthConnectionStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (ocs OAuthConnectionStatus) Value() (driver.Value, error) {
	return string(ocs), nil
}

// OAuthUserInfo represents user information from OAuth provider
type OAuthUserInfo map[string]interface{}

// Scan implements sql.Scanner interface
func (oui *OAuthUserInfo) Scan(value interface{}) error {
	if value == nil {
		*oui = make(OAuthUserInfo)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, oui)
}

// Value implements driver.Valuer interface
func (oui OAuthUserInfo) Value() (driver.Value, error) {
	if oui == nil {
		return "{}", nil
	}
	return json.Marshal(oui)
}

// GetString safely gets a string value from OAuth user info
func (oui OAuthUserInfo) GetString(key string) string {
	if val, ok := oui[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

// OAuthConnection represents a connection between a user and an OAuth provider
type OAuthConnection struct {
	ID         uint `json:"id" gorm:"primaryKey"`
	TenantID   uint `json:"tenant_id" gorm:"not null;index"`
	WebsiteID  uint `json:"website_id" gorm:"not null;index"`
	UserID     uint `json:"user_id" gorm:"not null;index"`
	ProviderID uint `json:"provider_id" gorm:"not null;index"`

	// OAuth Information
	ProviderUserID string `json:"provider_user_id" gorm:"not null;index"`
	Email          string `json:"email" gorm:"not null;index"`

	// Tokens
	AccessToken  string     `json:"-" gorm:"not null"` // Don't expose in JSON
	RefreshToken *string    `json:"-"`                 // Don't expose in JSON
	TokenType    string     `json:"token_type" gorm:"default:'Bearer'"`
	ExpiresAt    *time.Time `json:"expires_at,omitempty"`

	// Scopes
	Scopes string `json:"scopes" gorm:"not null"`

	// User Information from Provider
	UserInfo OAuthUserInfo `json:"user_info" gorm:"type:json"`

	// Status
	Status    OAuthConnectionStatus `json:"status" gorm:"type:enum('active','revoked','expired','disabled');default:'active';not null"`
	IsEnabled bool                  `json:"is_enabled" gorm:"default:true"`

	// Usage Tracking
	LastUsedAt     *time.Time `json:"last_used_at,omitempty"`
	DisconnectedAt *time.Time `json:"disconnected_at,omitempty"`
	UsageCount     uint       `json:"usage_count" gorm:"default:0"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	// Website  *models.Website  `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User     *User            `json:"user,omitempty" gorm:"foreignKey:UserID"`
	// Provider *OAuthProvider   `json:"provider,omitempty" gorm:"foreignKey:ProviderID"`
}

// TableName specifies the table name for OAuthConnection
func (OAuthConnection) TableName() string {
	return "auth_oauth_connections"
}

// IsActive checks if OAuth connection is active
func (oc *OAuthConnection) IsActive() bool {
	return oc.Status == OAuthConnectionStatusActive && oc.IsEnabled
}

// IsExpired checks if OAuth connection token is expired
func (oc *OAuthConnection) IsExpired() bool {
	return oc.ExpiresAt != nil && time.Now().After(*oc.ExpiresAt)
}

// IsValid checks if OAuth connection is valid for use
func (oc *OAuthConnection) IsValid() bool {
	return oc.IsActive() && !oc.IsExpired()
}

// UpdateLastUsed updates the last used timestamp and increments usage count
func (oc *OAuthConnection) UpdateLastUsed() {
	now := time.Now()
	oc.LastUsedAt = &now
	oc.UsageCount++
}

// Revoke marks the OAuth connection as revoked
func (oc *OAuthConnection) Revoke() {
	oc.Status = OAuthConnectionStatusRevoked
}

// BeforeCreate hook for OAuthConnection
func (oc *OAuthConnection) BeforeCreate() error {
	if oc.UserInfo == nil {
		oc.UserInfo = make(OAuthUserInfo)
	}
	return nil
}

// OAuth Request/Response Models

// OAuthProviderFilter represents filters for querying OAuth providers
type OAuthProviderFilter struct {
	WebsiteID uint                `json:"website_id,omitempty"`
	Provider  string              `json:"provider,omitempty"`
	Status    OAuthProviderStatus `json:"status,omitempty"`
	IsEnabled *bool               `json:"is_enabled,omitempty"`
	Page      int                 `json:"page,omitempty"`
	PageSize  int                 `json:"page_size,omitempty"`
	SortBy    string              `json:"sort_by,omitempty"`
	SortOrder string              `json:"sort_order,omitempty"`
}

// CreateOAuthProviderRequest represents the request to create an OAuth provider
type CreateOAuthProviderRequest struct {
	WebsiteID    uint                `json:"website_id" validate:"required,min=1"`
	Name         string              `json:"name" validate:"required,max=100"`
	Provider     string              `json:"provider" validate:"required,oneof=google github facebook twitter linkedin microsoft discord"`
	ClientID     string              `json:"client_id" validate:"required"`
	ClientSecret string              `json:"client_secret" validate:"required"`
	Config       OAuthProviderConfig `json:"config,omitempty"`
	AuthorizeURL string              `json:"authorize_url" validate:"required,url"`
	TokenURL     string              `json:"token_url" validate:"required,url"`
	UserInfoURL  string              `json:"user_info_url" validate:"required,url"`
	RedirectURL  string              `json:"redirect_url" validate:"omitempty,url"`
	RedirectURLs RedirectURLs        `json:"redirect_urls,omitempty"`
	Scopes       string              `json:"scopes,omitempty"`
	DisplayName  *string             `json:"display_name,omitempty"`
	IconURL      *string             `json:"icon_url,omitempty" validate:"omitempty,url"`
	SortOrder    int                 `json:"sort_order,omitempty"`
}

// UpdateOAuthProviderRequest represents the request to update an OAuth provider
type UpdateOAuthProviderRequest struct {
	Name         *string              `json:"name,omitempty" validate:"omitempty,max=100"`
	ClientID     *string              `json:"client_id,omitempty"`
	ClientSecret *string              `json:"client_secret,omitempty"`
	Config       *OAuthProviderConfig `json:"config,omitempty"`
	AuthorizeURL *string              `json:"authorize_url,omitempty" validate:"omitempty,url"`
	TokenURL     *string              `json:"token_url,omitempty" validate:"omitempty,url"`
	UserInfoURL  *string              `json:"user_info_url,omitempty" validate:"omitempty,url"`
	RedirectURL  *string              `json:"redirect_url,omitempty" validate:"omitempty,url"`
	RedirectURLs *RedirectURLs        `json:"redirect_urls,omitempty"`
	Scopes       *string              `json:"scopes,omitempty"`
	Status       *OAuthProviderStatus `json:"status,omitempty"`
	IsEnabled    *bool                `json:"is_enabled,omitempty"`
	DisplayName  *string              `json:"display_name,omitempty"`
	IconURL      *string              `json:"icon_url,omitempty" validate:"omitempty,url"`
	SortOrder    *int                 `json:"sort_order,omitempty"`
}

// OAuthConnectionFilter represents filters for querying OAuth connections
type OAuthConnectionFilter struct {
	TenantID       uint                  `json:"tenant_id,omitempty"`
	WebsiteID      uint                  `json:"website_id,omitempty"`
	UserID         uint                  `json:"user_id,omitempty"`
	ProviderID     uint                  `json:"provider_id,omitempty"`
	Provider       string                `json:"provider,omitempty"`
	Status         OAuthConnectionStatus `json:"status,omitempty"`
	IsEnabled      *bool                 `json:"is_enabled,omitempty"`
	ProviderUserID string                `json:"provider_user_id,omitempty"`
	Email          string                `json:"email,omitempty"`
	Page           int                   `json:"page,omitempty"`
	PageSize       int                   `json:"page_size,omitempty"`
	SortBy         string                `json:"sort_by,omitempty"`
	SortOrder      string                `json:"sort_order,omitempty"`
}

// OAuthConnectionResponse represents an OAuth connection response
type OAuthConnectionResponse struct {
	ID             uint                  `json:"id"`
	ProviderName   string                `json:"provider_name"`
	Provider       string                `json:"provider"`
	ProviderUserID string                `json:"provider_user_id"`
	Email          string                `json:"email"`
	TokenType      string                `json:"token_type"`
	ExpiresAt      *time.Time            `json:"expires_at,omitempty"`
	Scopes         string                `json:"scopes"`
	Status         OAuthConnectionStatus `json:"status"`
	IsEnabled      bool                  `json:"is_enabled"`
	LastUsedAt     *time.Time            `json:"last_used_at,omitempty"`
	UsageCount     uint                  `json:"usage_count"`
	CreatedAt      time.Time             `json:"created_at"`
	IsExpired      bool                  `json:"is_expired"` // Computed field
	UserInfo       OAuthUserInfo         `json:"user_info"`
}

// ToResponse converts an OAuthConnection to OAuthConnectionResponse
func (oc *OAuthConnection) ToResponse(providerName string) *OAuthConnectionResponse {
	return &OAuthConnectionResponse{
		ID:             oc.ID,
		ProviderName:   providerName,
		ProviderUserID: oc.ProviderUserID,
		Email:          oc.Email,
		TokenType:      oc.TokenType,
		ExpiresAt:      oc.ExpiresAt,
		Scopes:         oc.Scopes,
		Status:         oc.Status,
		IsEnabled:      oc.IsEnabled,
		LastUsedAt:     oc.LastUsedAt,
		UsageCount:     oc.UsageCount,
		CreatedAt:      oc.CreatedAt,
		IsExpired:      oc.IsExpired(),
		UserInfo:       oc.UserInfo,
	}
}

// OAuthAuthorizationRequest represents an OAuth authorization request
type OAuthAuthorizationRequest struct {
	ProviderID  uint    `json:"provider_id" validate:"required,min=1"`
	RedirectURL *string `json:"redirect_url,omitempty" validate:"omitempty,url"`
	State       *string `json:"state,omitempty"`
	Scopes      *string `json:"scopes,omitempty"`
}

// OAuthCallbackRequest represents an OAuth callback request
type OAuthCallbackRequest struct {
	ProviderID uint   `json:"provider_id" validate:"required,min=1"`
	Code       string `json:"code" validate:"required"`
	State      string `json:"state,omitempty"`
}
