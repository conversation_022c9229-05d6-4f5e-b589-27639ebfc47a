package models

import (
	"time"
)

// PasswordReset represents a password reset request
type PasswordReset struct {
	ID        uint `json:"id" gorm:"primaryKey"`
	TenantID  uint `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint `json:"website_id" gorm:"not null;index"`
	UserID    uint `json:"user_id" gorm:"not null;index"`

	// Reset Information
	Token string `json:"-" gorm:"type:varchar(255);not null;uniqueIndex"` // Don't expose token in JSON
	Email string `json:"email" gorm:"not null;index"`

	// Request Information
	IPAddress *string `json:"ip_address,omitempty"`
	UserAgent *string `json:"user_agent,omitempty"`

	// Status
	IsUsed    bool       `json:"is_used" gorm:"default:false;index"`
	UsedAt    *time.Time `json:"used_at,omitempty"`
	ExpiresAt time.Time  `json:"expires_at" gorm:"not null;index"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (loaded separately to avoid circular references)
	// Website *models.Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User    *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName specifies the table name for PasswordReset
func (PasswordReset) TableName() string {
	return "auth_password_resets"
}

// IsExpired checks if password reset token is expired
func (pr *PasswordReset) IsExpired() bool {
	return time.Now().After(pr.ExpiresAt)
}

// IsValid checks if password reset token is valid for use
func (pr *PasswordReset) IsValid() bool {
	return !pr.IsUsed && !pr.IsExpired()
}

// MarkAsUsed marks the password reset token as used
func (pr *PasswordReset) MarkAsUsed() {
	now := time.Now()
	pr.IsUsed = true
	pr.UsedAt = &now
}

// GetTimeUntilExpiry returns the duration until expiry
func (pr *PasswordReset) GetTimeUntilExpiry() time.Duration {
	if pr.IsExpired() {
		return 0
	}
	return time.Until(pr.ExpiresAt)
}

// PasswordResetFilter represents filters for querying password resets
type PasswordResetFilter struct {
	TenantID  uint   `json:"tenant_id,omitempty"`
	WebsiteID uint   `json:"website_id,omitempty"`
	UserID    uint   `json:"user_id,omitempty"`
	Email     string `json:"email,omitempty"`
	IsUsed    *bool  `json:"is_used,omitempty"`
	IsExpired *bool  `json:"is_expired,omitempty"`
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
}

// CreatePasswordResetRequest represents the request to create a password reset
type CreatePasswordResetRequest struct {
	TenantID  uint    `json:"tenant_id" validate:"required,min=1"`
	WebsiteID uint    `json:"website_id" validate:"required,min=1"`
	Email     string  `json:"email" validate:"required,email"`
	IPAddress *string `json:"ip_address,omitempty" validate:"omitempty,ip"`
	UserAgent *string `json:"user_agent,omitempty"`
}

// ResetPasswordRequest represents the request to reset password
type ResetPasswordRequest struct {
	Token           string `json:"token" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8,max=128"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=NewPassword"`
}

// ForgotPasswordRequest represents the forgot password request
type ForgotPasswordRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// PasswordResetResponse represents a safe password reset response
type PasswordResetResponse struct {
	ID              uint       `json:"id"`
	Email           string     `json:"email"`
	IsUsed          bool       `json:"is_used"`
	UsedAt          *time.Time `json:"used_at,omitempty"`
	ExpiresAt       time.Time  `json:"expires_at"`
	CreatedAt       time.Time  `json:"created_at"`
	IsExpired       bool       `json:"is_expired"`                  // Computed field
	TimeUntilExpiry string     `json:"time_until_expiry,omitempty"` // Computed field
}

// ToResponse converts a PasswordReset to PasswordResetResponse
func (pr *PasswordReset) ToResponse() *PasswordResetResponse {
	resp := &PasswordResetResponse{
		ID:        pr.ID,
		Email:     pr.Email,
		IsUsed:    pr.IsUsed,
		UsedAt:    pr.UsedAt,
		ExpiresAt: pr.ExpiresAt,
		CreatedAt: pr.CreatedAt,
		IsExpired: pr.IsExpired(),
	}

	// Add time until expiry if not expired
	if !pr.IsExpired() {
		duration := pr.GetTimeUntilExpiry()
		if duration > time.Hour {
			resp.TimeUntilExpiry = duration.Truncate(time.Hour).String()
		} else {
			resp.TimeUntilExpiry = duration.Truncate(time.Minute).String()
		}
	}

	return resp
}
