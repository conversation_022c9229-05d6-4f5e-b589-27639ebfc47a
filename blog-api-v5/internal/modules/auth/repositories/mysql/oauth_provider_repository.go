package mysql

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"gorm.io/gorm"
)

type oauthProviderRepository struct {
	db *gorm.DB
}

// NewOAuthProviderRepository creates a new MySQL OAuth provider repository
func NewOAuthProviderRepository(db *gorm.DB) repositories.OAuthProviderRepository {
	return &oauthProviderRepository{db: db}
}

// Create creates a new OAuth provider
func (r *oauthProviderRepository) Create(ctx context.Context, provider *models.OAuthProvider) error {
	if err := r.db.WithContext(ctx).Create(provider).Error; err != nil {
		return fmt.Errorf("failed to create OAuth provider: %w", err)
	}
	return nil
}

// GetByID retrieves an OAuth provider by ID
func (r *oauthProviderRepository) GetByID(ctx context.Context, id uint) (*models.OAuthProvider, error) {
	var provider models.OAuthProvider
	if err := r.db.WithContext(ctx).First(&provider, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get OAuth provider by id: %w", err)
	}
	return &provider, nil
}

// GetByName retrieves an OAuth provider by name and website
func (r *oauthProviderRepository) GetByName(ctx context.Context, websiteID uint, name string) (*models.OAuthProvider, error) {
	var provider models.OAuthProvider
	if err := r.db.WithContext(ctx).
		Where("website_id = ? AND provider = ?", websiteID, name).
		First(&provider).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get OAuth provider by name: %w", err)
	}
	return &provider, nil
}

// Update updates an OAuth provider
func (r *oauthProviderRepository) Update(ctx context.Context, provider *models.OAuthProvider) error {
	if err := r.db.WithContext(ctx).Save(provider).Error; err != nil {
		return fmt.Errorf("failed to update OAuth provider: %w", err)
	}
	return nil
}

// Delete deletes an OAuth provider
func (r *oauthProviderRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.OAuthProvider{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete OAuth provider: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("OAuth provider not found")
	}
	return nil
}

// GetByWebsite retrieves all OAuth providers for a website
func (r *oauthProviderRepository) GetByWebsite(ctx context.Context, websiteID uint) ([]*models.OAuthProvider, error) {
	var providers []*models.OAuthProvider
	if err := r.db.WithContext(ctx).
		Where("website_id = ?", websiteID).
		Order("sort_order ASC, created_at ASC").
		Find(&providers).Error; err != nil {
		return nil, fmt.Errorf("failed to get OAuth providers by website: %w", err)
	}
	return providers, nil
}

// GetEnabledProviders retrieves enabled OAuth providers for a website
func (r *oauthProviderRepository) GetEnabledProviders(ctx context.Context, websiteID uint) ([]*models.OAuthProvider, error) {
	var providers []*models.OAuthProvider
	if err := r.db.WithContext(ctx).
		Where("website_id = ? AND is_enabled = ? AND status = ?", websiteID, true, models.OAuthProviderStatusActive).
		Order("sort_order ASC, created_at ASC").
		Find(&providers).Error; err != nil {
		return nil, fmt.Errorf("failed to get enabled OAuth providers: %w", err)
	}
	return providers, nil
}

// IsProviderEnabled checks if a provider is enabled for a website
func (r *oauthProviderRepository) IsProviderEnabled(ctx context.Context, websiteID uint, providerName string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&models.OAuthProvider{}).
		Where("website_id = ? AND provider = ? AND is_enabled = ? AND status = ?",
			websiteID, providerName, true, models.OAuthProviderStatusActive).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if provider is enabled: %w", err)
	}
	return count > 0, nil
}

// UpdateConfiguration updates OAuth provider configuration
func (r *oauthProviderRepository) UpdateConfiguration(ctx context.Context, id uint, config models.OAuthProviderConfig) error {
	if err := r.db.WithContext(ctx).
		Model(&models.OAuthProvider{}).
		Where("id = ?", id).
		Update("config", config).Error; err != nil {
		return fmt.Errorf("failed to update configuration: %w", err)
	}
	return nil
}

// UpdateStatus updates OAuth provider status
func (r *oauthProviderRepository) UpdateStatus(ctx context.Context, id uint, enabled bool) error {
	updates := map[string]interface{}{
		"is_enabled": enabled,
	}

	if !enabled {
		updates["status"] = models.OAuthProviderStatusInactive
	} else {
		updates["status"] = models.OAuthProviderStatusActive
	}

	if err := r.db.WithContext(ctx).
		Model(&models.OAuthProvider{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update status: %w", err)
	}
	return nil
}
