package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
)

// OAuthConnectionRepository defines the interface for OAuth connection operations
type OAuthConnectionRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, connection *models.OAuthConnection) error
	GetByID(ctx context.Context, id uint) (*models.OAuthConnection, error)
	GetByProviderID(ctx context.Context, providerName, providerUserID string) (*models.OAuthConnection, error)
	Update(ctx context.Context, connection *models.OAuthConnection) error
	Delete(ctx context.Context, id uint) error

	// User connections
	GetByUser(ctx context.Context, userID uint) ([]*models.OAuthConnection, error)
	GetByUserAndProvider(ctx context.Context, userID uint, providerName string) (*models.OAuthConnection, error)
	DisconnectProvider(ctx context.Context, userID uint, providerName string) error

	// Token management
	UpdateTokens(ctx context.Context, id uint, accessToken, refreshToken string) error
	GetActiveConnection(ctx context.Context, userID uint, providerName string) (*models.OAuthConnection, error)

	// Query operations
	CountUserConnections(ctx context.Context, userID uint) (int64, error)
	HasConnection(ctx context.Context, userID uint, providerName string) (bool, error)

	// Security
	RevokeConnection(ctx context.Context, id uint) error
	GetConnectionsByEmail(ctx context.Context, email string) ([]*models.OAuthConnection, error)
}
