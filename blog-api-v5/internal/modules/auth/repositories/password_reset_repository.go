package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
)

// PasswordResetRepository defines the interface for password reset operations
type PasswordResetRepository interface {
	// Basic operations
	Create(ctx context.Context, reset *models.PasswordReset) error
	GetByToken(ctx context.Context, token string) (*models.PasswordReset, error)
	GetByID(ctx context.Context, id uint) (*models.PasswordReset, error)
	Update(ctx context.Context, reset *models.PasswordReset) error
	Delete(ctx context.Context, id uint) error

	// Token management
	InvalidateToken(ctx context.Context, token string) error
	InvalidateAllUserTokens(ctx context.Context, userID uint) error
	IsTokenValid(ctx context.Context, token string) (bool, error)

	// Query operations
	GetActiveTokenByUser(ctx context.Context, userID uint) (*models.PasswordReset, error)
	CountRecentRequests(ctx context.Context, userID uint, duration time.Duration) (int64, error)
	GetRecentRequests(ctx context.Context, userID uint, limit int) ([]*models.PasswordReset, error)

	// Cleanup
	CleanupExpiredTokens(ctx context.Context) (int64, error)

	// Security
	HasRecentRequest(ctx context.Context, email string, duration time.Duration) (bool, error)
	GetRequestsByIP(ctx context.Context, ip string, duration time.Duration) ([]*models.PasswordReset, error)
}
