package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
)

// TokenBlacklistRepository defines the interface for token blacklist operations
type TokenBlacklistRepository interface {
	Add(ctx context.Context, tokenID string, expiresAt time.Time, userID uint, reason models.TokenBlacklistReason) error
	AddWithDetails(ctx context.Context, blacklist *models.TokenBlacklist) error
	Exists(ctx context.Context, tokenID string) (bool, error)
	Get(ctx context.Context, tokenID string) (*models.TokenBlacklist, error)
	CleanupExpired(ctx context.Context) (int64, error)
	GetAll(ctx context.Context) ([]*models.TokenBlacklist, error)
	GetByUser(ctx context.Context, userID uint) ([]*models.TokenBlacklist, error)
	Remove(ctx context.Context, tokenID string) error
}
