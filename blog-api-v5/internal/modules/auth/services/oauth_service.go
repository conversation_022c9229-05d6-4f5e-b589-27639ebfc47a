package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"golang.org/x/oauth2"
	"golang.org/x/oauth2/facebook"
	"golang.org/x/oauth2/google"

	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	userRepositories "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/cache"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// OAuthService handles OAuth authentication operations
type OAuthService struct {
	oauthProviderRepo   repositories.OAuthProviderRepository
	oauthConnectionRepo repositories.OAuthConnectionRepository
	// Removed userService to avoid import cycle
	userRepo    userRepositories.UserRepository
	cache       cache.Cache
	logger      utils.Logger
	config      *config.Config
	jwtService  JWTService
	sessionRepo repositories.SessionRepository
}

// NewOAuthService creates a new OAuth service instance
func NewOAuthService(
	oauthProviderRepo repositories.OAuthProviderRepository,
	oauthConnectionRepo repositories.OAuthConnectionRepository,
	userRepo userRepositories.UserRepository,
	cache cache.Cache,
	logger utils.Logger,
	config *config.Config,
	jwtService JWTService,
	sessionRepo repositories.SessionRepository,
) *OAuthService {
	return &OAuthService{
		oauthProviderRepo:   oauthProviderRepo,
		oauthConnectionRepo: oauthConnectionRepo,
		userRepo:            userRepo,
		cache:               cache,
		logger:              logger,
		config:              config,
		jwtService:          jwtService,
		sessionRepo:         sessionRepo,
	}
}

// GetOAuthAuthURL generates OAuth authorization URL for any provider
func (s *OAuthService) GetOAuthAuthURL(ctx context.Context, websiteID uint, req *dto.OAuthAuthURLRequest, host string) (*dto.OAuthAuthURLResponse, error) {
	switch req.Provider {
	case "google":
		return s.GetGoogleAuthURL(ctx, websiteID, req, host)
	case "facebook":
		return s.GetFacebookAuthURL(ctx, websiteID, req, host)
	default:
		return nil, fmt.Errorf("unsupported OAuth provider: %s", req.Provider)
	}
}

// LoginWithOAuth handles OAuth login for any provider
func (s *OAuthService) LoginWithOAuth(ctx context.Context, websiteID uint, req *dto.OAuthLoginRequest, host string) (*dto.OAuthLoginResponse, error) {
	switch req.Provider {
	case "google":
		return s.LoginWithGoogle(ctx, websiteID, req, host)
	case "facebook":
		return s.LoginWithFacebook(ctx, websiteID, req, host)
	default:
		return nil, fmt.Errorf("unsupported OAuth provider: %s", req.Provider)
	}
}

// GetGoogleAuthURL generates Google OAuth authorization URL
func (s *OAuthService) GetGoogleAuthURL(ctx context.Context, websiteID uint, req *dto.OAuthAuthURLRequest, host string) (*dto.OAuthAuthURLResponse, error) {
	// Get Google OAuth provider configuration
	provider, err := s.oauthProviderRepo.GetByName(ctx, websiteID, "google")
	if err != nil {
		s.logger.Error("Failed to get Google OAuth provider", "error", err, "website_id", websiteID)
		return nil, fmt.Errorf("OAuth provider not configured")
	}

	if !provider.IsActive() {
		return nil, fmt.Errorf("Google OAuth is not active")
	}

	// Create OAuth2 config with dynamic redirect URL
	redirectURL := provider.GetRedirectURLForRequest(host)
	if redirectURL == "" {
		s.logger.Error("No redirect URL available for Google OAuth", "host", host, "provider_id", provider.ID)
		return nil, fmt.Errorf("no redirect URL configured for Google OAuth")
	}

	oauthConfig := &oauth2.Config{
		ClientID:     provider.ClientID,
		ClientSecret: provider.ClientSecret,
		RedirectURL:  redirectURL,
		Scopes:       strings.Split(provider.Scopes, " "),
		Endpoint:     google.Endpoint,
	}

	// Generate state if not provided
	state := req.State
	if state == "" {
		stateBytes := make([]byte, 16)
		rand.Read(stateBytes)
		state = hex.EncodeToString(stateBytes)
	}

	// Cache the state for verification
	stateKey := fmt.Sprintf("oauth_state:%s", state)
	stateData := map[string]interface{}{
		"website_id": websiteID,
		"provider":   "google",
		"timestamp":  time.Now().Unix(),
	}

	stateDataBytes, _ := json.Marshal(stateData)
	if err := s.cache.Set(ctx, stateKey, stateDataBytes, 10*time.Minute); err != nil {
		s.logger.Error("Failed to cache OAuth state", "error", err, "state", state)
	}

	// Generate authorization URL
	authURL := oauthConfig.AuthCodeURL(state, oauth2.AccessTypeOffline)

	return &dto.OAuthAuthURLResponse{
		URL:   authURL,
		State: state,
	}, nil
}

// LoginWithGoogle handles Google OAuth login
func (s *OAuthService) LoginWithGoogle(ctx context.Context, websiteID uint, req *dto.OAuthLoginRequest, host string) (*dto.OAuthLoginResponse, error) {
	// Verify state if provided
	if req.State != "" {
		if err := s.verifyOAuthState(ctx, req.State, websiteID, "google"); err != nil {
			s.logger.Error("OAuth state verification failed", "error", err, "state", req.State)
			return nil, fmt.Errorf("invalid state parameter")
		}
	}

	// Get Google OAuth provider configuration
	provider, err := s.oauthProviderRepo.GetByName(ctx, websiteID, "google")
	if err != nil {
		s.logger.Error("Failed to get Google OAuth provider", "error", err, "website_id", websiteID)
		return nil, fmt.Errorf("OAuth provider not configured")
	}

	if !provider.IsActive() {
		return nil, fmt.Errorf("Google OAuth is not active")
	}

	// Create OAuth2 config with dynamic redirect URL
	redirectURL := provider.GetRedirectURLForRequest(host)
	if redirectURL == "" {
		s.logger.Error("No redirect URL available for Google OAuth", "host", host, "provider_id", provider.ID)
		return nil, fmt.Errorf("no redirect URL configured for Google OAuth")
	}

	oauthConfig := &oauth2.Config{
		ClientID:     provider.ClientID,
		ClientSecret: provider.ClientSecret,
		RedirectURL:  redirectURL,
		Scopes:       strings.Split(provider.Scopes, " "),
		Endpoint:     google.Endpoint,
	}

	// Exchange authorization code for token
	token, err := oauthConfig.Exchange(ctx, req.Code)
	if err != nil {
		s.logger.Error("Failed to exchange OAuth code", "error", err, "provider", "google")
		return nil, fmt.Errorf("failed to exchange authorization code")
	}

	// Get user information from Google
	googleUser, err := s.getGoogleUserInfo(ctx, token.AccessToken)
	if err != nil {
		s.logger.Error("Failed to get Google user info", "error", err)
		return nil, fmt.Errorf("failed to get user information from Google")
	}

	// Find or create user
	user, isNewUser, err := s.findOrCreateUser(ctx, googleUser)
	if err != nil {
		s.logger.Error("Failed to find or create user", "error", err, "google_id", googleUser.ID)
		return nil, fmt.Errorf("failed to process user account")
	}

	// Create or update OAuth connection
	connection, err := s.createOrUpdateConnection(ctx, user.ID, websiteID, provider.ID, token, googleUser)
	if err != nil {
		s.logger.Error("Failed to create OAuth connection", "error", err, "user_id", user.ID)
		return nil, fmt.Errorf("failed to create OAuth connection")
	}

	// Generate JWT tokens and create session
	accessToken, refreshToken, err := s.generateOAuthTokens(ctx, user, websiteID)
	if err != nil {
		s.logger.Error("Failed to generate OAuth tokens", "error", err, "user_id", user.ID)
		return nil, fmt.Errorf("failed to generate authentication tokens")
	}

	// Create session
	session, err := s.createOAuthSession(ctx, user, websiteID, refreshToken)
	if err != nil {
		s.logger.Error("Failed to create OAuth session", "error", err, "user_id", user.ID)
		return nil, fmt.Errorf("failed to create session")
	}

	return &dto.OAuthLoginResponse{
		AccessToken:               accessToken,
		RefreshToken:              refreshToken,
		ExpiresIn:                 3600, // 1 hour for access token
		TokenType:                 "Bearer",
		SessionID:                 session.ID,
		RequiresTwoFactor:         false,
		RequiresEmailVerification: !user.EmailVerified,
		RequiresOnboarding:        true, // OAuth users need onboarding
		IsNewUser:                 isNewUser,
		ConnectedAt:               connection.CreatedAt,
	}, nil
}

// getGoogleUserInfo retrieves user information from Google API
func (s *OAuthService) getGoogleUserInfo(ctx context.Context, accessToken string) (*dto.GoogleUserInfo, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", "https://www.googleapis.com/oauth2/v2/userinfo", nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Google API returned status %d", resp.StatusCode)
	}

	var googleUser dto.GoogleUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&googleUser); err != nil {
		return nil, err
	}

	return &googleUser, nil
}

// findOrCreateUser finds existing user by email or creates a new one
func (s *OAuthService) findOrCreateUser(ctx context.Context, googleUser *dto.GoogleUserInfo) (*userModels.User, bool, error) {
	// Try to find existing user by email
	existingUser, err := s.userRepo.GetByEmail(ctx, googleUser.Email)
	if err == nil {
		// User exists, update profile if needed
		s.updateUserFromGoogle(existingUser, googleUser)

		if err := s.userRepo.Update(ctx, existingUser); err != nil {
			s.logger.Error("Failed to update existing user", "error", err, "user_id", existingUser.ID)
		}

		return existingUser, false, nil
	}

	// Create new user
	newUser := &userModels.User{
		Email:         googleUser.Email,
		EmailVerified: googleUser.VerifiedEmail,
		Status:        userModels.UserStatusActive, // OAuth users are automatically active
		FirstName:     &googleUser.GivenName,
		LastName:      &googleUser.FamilyName,
		DisplayName:   &googleUser.Name,
		AvatarURL:     &googleUser.Picture,
	}

	if err := s.userRepo.Create(ctx, newUser); err != nil {
		return nil, false, err
	}

	s.logger.Info("Created new user from Google OAuth", "user_id", newUser.ID, "email", newUser.Email)

	return newUser, true, nil
}

// updateUserFromGoogle updates user information from Google profile
func (s *OAuthService) updateUserFromGoogle(user *userModels.User, googleUser *dto.GoogleUserInfo) {
	// Update email verification status
	if googleUser.VerifiedEmail {
		user.EmailVerified = true
	}

	// Update profile information if not set
	if user.FirstName == nil || *user.FirstName == "" {
		user.FirstName = &googleUser.GivenName
	}
	if user.LastName == nil || *user.LastName == "" {
		user.LastName = &googleUser.FamilyName
	}
	if user.DisplayName == nil || *user.DisplayName == "" {
		user.DisplayName = &googleUser.Name
	}
	if user.AvatarURL == nil || *user.AvatarURL == "" {
		user.AvatarURL = &googleUser.Picture
	}
}

// createOrUpdateConnection creates or updates OAuth connection
func (s *OAuthService) createOrUpdateConnection(ctx context.Context, userID, websiteID, providerID uint, token *oauth2.Token, googleUser *dto.GoogleUserInfo) (*models.OAuthConnection, error) {
	// Try to find existing connection by user and provider
	existingConnection, err := s.oauthConnectionRepo.GetByUserAndProvider(ctx, userID, "google")
	if err != nil && err.Error() != "connection not found" {
		return nil, err
	}

	userInfo := models.OAuthUserInfo{
		"id":             googleUser.ID,
		"email":          googleUser.Email,
		"verified_email": googleUser.VerifiedEmail,
		"name":           googleUser.Name,
		"given_name":     googleUser.GivenName,
		"family_name":    googleUser.FamilyName,
		"picture":        googleUser.Picture,
		"locale":         googleUser.Locale,
	}

	if existingConnection != nil {
		// Update existing connection
		connection := existingConnection
		connection.AccessToken = token.AccessToken
		if token.RefreshToken != "" {
			connection.RefreshToken = &token.RefreshToken
		}
		connection.ExpiresAt = &token.Expiry
		connection.UserInfo = userInfo
		connection.Status = models.OAuthConnectionStatusActive
		connection.IsEnabled = true
		connection.UpdateLastUsed()

		if err := s.oauthConnectionRepo.Update(ctx, connection); err != nil {
			return nil, err
		}

		return connection, nil
	}

	// Create new connection
	connection := &models.OAuthConnection{
		TenantID:       1, // Default tenant, will be set properly during onboarding
		WebsiteID:      websiteID,
		UserID:         userID,
		ProviderID:     providerID,
		ProviderUserID: googleUser.ID,
		Email:          googleUser.Email,
		AccessToken:    token.AccessToken,
		TokenType:      token.TokenType,
		ExpiresAt:      &token.Expiry,
		Scopes:         strings.Join(token.Extra("scope").([]string), " "),
		UserInfo:       userInfo,
		Status:         models.OAuthConnectionStatusActive,
		IsEnabled:      true,
	}

	if token.RefreshToken != "" {
		connection.RefreshToken = &token.RefreshToken
	}

	if err := s.oauthConnectionRepo.Create(ctx, connection); err != nil {
		return nil, err
	}

	return connection, nil
}

// GetFacebookAuthURL generates Facebook OAuth authorization URL
func (s *OAuthService) GetFacebookAuthURL(ctx context.Context, websiteID uint, req *dto.OAuthAuthURLRequest, host string) (*dto.OAuthAuthURLResponse, error) {
	// Get Facebook OAuth provider configuration
	provider, err := s.oauthProviderRepo.GetByName(ctx, websiteID, "facebook")
	if err != nil {
		s.logger.Error("Failed to get Facebook OAuth provider", "error", err, "website_id", websiteID)
		return nil, fmt.Errorf("OAuth provider not configured")
	}

	if !provider.IsActive() {
		return nil, fmt.Errorf("Facebook OAuth is not active")
	}

	// Create OAuth2 config with dynamic redirect URL
	redirectURL := provider.GetRedirectURLForRequest(host)
	if redirectURL == "" {
		s.logger.Error("No redirect URL available for Facebook OAuth", "host", host, "provider_id", provider.ID)
		return nil, fmt.Errorf("no redirect URL configured for Facebook OAuth")
	}

	oauthConfig := &oauth2.Config{
		ClientID:     provider.ClientID,
		ClientSecret: provider.ClientSecret,
		RedirectURL:  redirectURL,
		Scopes:       strings.Split(provider.Scopes, " "),
		Endpoint:     facebook.Endpoint,
	}

	// Generate state if not provided
	state := req.State
	if state == "" {
		stateBytes := make([]byte, 16)
		rand.Read(stateBytes)
		state = hex.EncodeToString(stateBytes)
	}

	// Cache the state for verification
	stateKey := fmt.Sprintf("oauth_state:%s", state)
	stateData := map[string]interface{}{
		"website_id": websiteID,
		"provider":   "facebook",
		"timestamp":  time.Now().Unix(),
	}

	stateDataBytes, _ := json.Marshal(stateData)
	if err := s.cache.Set(ctx, stateKey, stateDataBytes, 10*time.Minute); err != nil {
		s.logger.Error("Failed to cache OAuth state", "error", err, "state", state)
	}

	// Generate authorization URL
	authURL := oauthConfig.AuthCodeURL(state, oauth2.AccessTypeOffline)

	return &dto.OAuthAuthURLResponse{
		URL:   authURL,
		State: state,
	}, nil
}

// LoginWithFacebook handles Facebook OAuth login
func (s *OAuthService) LoginWithFacebook(ctx context.Context, websiteID uint, req *dto.OAuthLoginRequest, host string) (*dto.OAuthLoginResponse, error) {
	// Verify state if provided
	if req.State != "" {
		if err := s.verifyOAuthState(ctx, req.State, websiteID, "facebook"); err != nil {
			s.logger.Error("OAuth state verification failed", "error", err, "state", req.State)
			return nil, fmt.Errorf("invalid state parameter")
		}
	}

	// Get Facebook OAuth provider configuration
	provider, err := s.oauthProviderRepo.GetByName(ctx, websiteID, "facebook")
	if err != nil {
		s.logger.Error("Failed to get Facebook OAuth provider", "error", err, "website_id", websiteID)
		return nil, fmt.Errorf("OAuth provider not configured")
	}

	if !provider.IsActive() {
		return nil, fmt.Errorf("Facebook OAuth is not active")
	}

	// Create OAuth2 config with dynamic redirect URL
	redirectURL := provider.GetRedirectURLForRequest(host)
	if redirectURL == "" {
		s.logger.Error("No redirect URL available for Facebook OAuth", "host", host, "provider_id", provider.ID)
		return nil, fmt.Errorf("no redirect URL configured for Facebook OAuth")
	}

	oauthConfig := &oauth2.Config{
		ClientID:     provider.ClientID,
		ClientSecret: provider.ClientSecret,
		RedirectURL:  redirectURL,
		Scopes:       strings.Split(provider.Scopes, " "),
		Endpoint:     facebook.Endpoint,
	}

	// Exchange authorization code for token
	token, err := oauthConfig.Exchange(ctx, req.Code)
	if err != nil {
		s.logger.Error("Failed to exchange OAuth code", "error", err, "provider", "facebook")
		return nil, fmt.Errorf("failed to exchange authorization code")
	}

	// Get user information from Facebook
	facebookUser, err := s.getFacebookUserInfo(ctx, token.AccessToken)
	if err != nil {
		s.logger.Error("Failed to get Facebook user info", "error", err)
		return nil, fmt.Errorf("failed to get user information from Facebook")
	}

	// Find or create user
	user, isNewUser, err := s.findOrCreateUserFromFacebook(ctx, facebookUser)
	if err != nil {
		s.logger.Error("Failed to find or create user", "error", err, "facebook_id", facebookUser.ID)
		return nil, fmt.Errorf("failed to process user account")
	}

	// Create or update OAuth connection
	connection, err := s.createOrUpdateFacebookConnection(ctx, user.ID, websiteID, provider.ID, token, facebookUser)
	if err != nil {
		s.logger.Error("Failed to create OAuth connection", "error", err, "user_id", user.ID)
		return nil, fmt.Errorf("failed to create OAuth connection")
	}

	// Generate JWT tokens and create session
	accessToken, refreshToken, err := s.generateOAuthTokens(ctx, user, websiteID)
	if err != nil {
		s.logger.Error("Failed to generate OAuth tokens", "error", err, "user_id", user.ID)
		return nil, fmt.Errorf("failed to generate authentication tokens")
	}

	// Create session
	session, err := s.createOAuthSession(ctx, user, websiteID, refreshToken)
	if err != nil {
		s.logger.Error("Failed to create OAuth session", "error", err, "user_id", user.ID)
		return nil, fmt.Errorf("failed to create session")
	}

	return &dto.OAuthLoginResponse{
		AccessToken:               accessToken,
		RefreshToken:              refreshToken,
		ExpiresIn:                 3600, // 1 hour for access token
		TokenType:                 "Bearer",
		SessionID:                 session.ID,
		RequiresTwoFactor:         false,
		RequiresEmailVerification: !user.EmailVerified,
		RequiresOnboarding:        true, // OAuth users need onboarding
		IsNewUser:                 isNewUser,
		ConnectedAt:               connection.CreatedAt,
	}, nil
}

// getFacebookUserInfo retrieves user information from Facebook API
func (s *OAuthService) getFacebookUserInfo(ctx context.Context, accessToken string) (*dto.FacebookUserInfo, error) {
	url := "https://graph.facebook.com/me?fields=id,email,name,first_name,last_name,picture"
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Facebook API returned status %d", resp.StatusCode)
	}

	var facebookUser dto.FacebookUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&facebookUser); err != nil {
		return nil, err
	}

	return &facebookUser, nil
}

// findOrCreateUserFromFacebook finds existing user by email or creates a new one
func (s *OAuthService) findOrCreateUserFromFacebook(ctx context.Context, facebookUser *dto.FacebookUserInfo) (*userModels.User, bool, error) {
	// Try to find existing user by email
	existingUser, err := s.userRepo.GetByEmail(ctx, facebookUser.Email)
	if err == nil {
		// User exists, update profile if needed
		s.updateUserFromFacebook(existingUser, facebookUser)

		if err := s.userRepo.Update(ctx, existingUser); err != nil {
			s.logger.Error("Failed to update existing user", "error", err, "user_id", existingUser.ID)
		}

		return existingUser, false, nil
	}

	// Create new user
	newUser := &userModels.User{
		Email:         facebookUser.Email,
		EmailVerified: true,                        // Facebook emails are considered verified
		Status:        userModels.UserStatusActive, // OAuth users are automatically active
		FirstName:     &facebookUser.FirstName,
		LastName:      &facebookUser.LastName,
		DisplayName:   &facebookUser.Name,
		AvatarURL:     &facebookUser.Picture.Data.URL,
	}

	if err := s.userRepo.Create(ctx, newUser); err != nil {
		return nil, false, err
	}

	s.logger.Info("Created new user from Facebook OAuth", "user_id", newUser.ID, "email", newUser.Email)

	return newUser, true, nil
}

// updateUserFromFacebook updates user information from Facebook profile
func (s *OAuthService) updateUserFromFacebook(user *userModels.User, facebookUser *dto.FacebookUserInfo) {
	// Update email verification status (Facebook emails are verified)
	user.EmailVerified = true

	// Update profile information if not set
	if user.FirstName == nil || *user.FirstName == "" {
		user.FirstName = &facebookUser.FirstName
	}
	if user.LastName == nil || *user.LastName == "" {
		user.LastName = &facebookUser.LastName
	}
	if user.DisplayName == nil || *user.DisplayName == "" {
		user.DisplayName = &facebookUser.Name
	}
	if user.AvatarURL == nil || *user.AvatarURL == "" {
		user.AvatarURL = &facebookUser.Picture.Data.URL
	}
}

// createOrUpdateFacebookConnection creates or updates Facebook OAuth connection
func (s *OAuthService) createOrUpdateFacebookConnection(ctx context.Context, userID, websiteID, providerID uint, token *oauth2.Token, facebookUser *dto.FacebookUserInfo) (*models.OAuthConnection, error) {
	// Try to find existing connection by user and provider
	existingConnection, err := s.oauthConnectionRepo.GetByUserAndProvider(ctx, userID, "facebook")
	if err != nil && err.Error() != "connection not found" {
		return nil, err
	}

	userInfo := models.OAuthUserInfo{
		"id":         facebookUser.ID,
		"email":      facebookUser.Email,
		"name":       facebookUser.Name,
		"first_name": facebookUser.FirstName,
		"last_name":  facebookUser.LastName,
		"picture":    facebookUser.Picture.Data.URL,
	}

	if existingConnection != nil {
		// Update existing connection
		connection := existingConnection
		connection.AccessToken = token.AccessToken
		if token.RefreshToken != "" {
			connection.RefreshToken = &token.RefreshToken
		}
		connection.ExpiresAt = &token.Expiry
		connection.UserInfo = userInfo
		connection.Status = models.OAuthConnectionStatusActive
		connection.IsEnabled = true
		connection.UpdateLastUsed()

		if err := s.oauthConnectionRepo.Update(ctx, connection); err != nil {
			return nil, err
		}

		return connection, nil
	}

	// Create new connection
	connection := &models.OAuthConnection{
		TenantID:       1, // Default tenant, will be set properly during onboarding
		WebsiteID:      websiteID,
		UserID:         userID,
		ProviderID:     providerID,
		ProviderUserID: facebookUser.ID,
		Email:          facebookUser.Email,
		AccessToken:    token.AccessToken,
		TokenType:      token.TokenType,
		ExpiresAt:      &token.Expiry,
		Scopes:         "email public_profile", // Default Facebook scopes
		UserInfo:       userInfo,
		Status:         models.OAuthConnectionStatusActive,
		IsEnabled:      true,
	}

	if token.RefreshToken != "" {
		connection.RefreshToken = &token.RefreshToken
	}

	if err := s.oauthConnectionRepo.Create(ctx, connection); err != nil {
		return nil, err
	}

	return connection, nil
}

// verifyOAuthState verifies the OAuth state parameter
func (s *OAuthService) verifyOAuthState(ctx context.Context, state string, websiteID uint, provider string) error {
	stateKey := fmt.Sprintf("oauth_state:%s", state)

	stateDataBytes, err := s.cache.Get(ctx, stateKey)
	if err != nil {
		return fmt.Errorf("invalid or expired state")
	}

	var stateData map[string]interface{}
	if err := json.Unmarshal(stateDataBytes, &stateData); err != nil {
		return fmt.Errorf("invalid state format")
	}

	// Verify state data
	if stateData["website_id"] != float64(websiteID) || stateData["provider"] != provider {
		return fmt.Errorf("state mismatch")
	}

	// Clean up state from cache
	s.cache.Delete(ctx, stateKey)

	return nil
}

// GetUserOAuthConnections retrieves all OAuth connections for a user
func (s *OAuthService) GetUserOAuthConnections(ctx context.Context, userID, websiteID uint) ([]models.OAuthConnection, error) {
	connectionPointers, err := s.oauthConnectionRepo.GetByUser(ctx, userID)
	if err != nil {
		s.logger.Error("Failed to get user OAuth connections", "error", err, "user_id", userID)
		return nil, fmt.Errorf("failed to retrieve OAuth connections")
	}

	// Convert pointers to values and filter by website ID and active status
	var activeConnections []models.OAuthConnection
	for _, connPtr := range connectionPointers {
		if connPtr.WebsiteID == websiteID && connPtr.Status == models.OAuthConnectionStatusActive && connPtr.IsEnabled {
			activeConnections = append(activeConnections, *connPtr)
		}
	}

	return activeConnections, nil
}

// DisconnectOAuthProvider disconnects and disables an OAuth provider connection
func (s *OAuthService) DisconnectOAuthProvider(ctx context.Context, userID, websiteID uint, provider string) error {
	// Find the connection
	connection, err := s.oauthConnectionRepo.GetByUserAndProvider(ctx, userID, provider)
	if err != nil {
		s.logger.Error("Failed to find OAuth connection", "error", err, "user_id", userID, "provider", provider)
		return fmt.Errorf("OAuth connection not found")
	}

	// Verify website access
	if connection.WebsiteID != websiteID {
		s.logger.Error("OAuth connection website mismatch", "connection_website", connection.WebsiteID, "requested_website", websiteID)
		return fmt.Errorf("OAuth connection not found")
	}

	// Update connection status to disabled
	connection.Status = models.OAuthConnectionStatusDisconnected
	connection.IsEnabled = false
	connection.DisconnectedAt = &time.Time{}
	now := time.Now()
	connection.DisconnectedAt = &now

	if err := s.oauthConnectionRepo.Update(ctx, connection); err != nil {
		s.logger.Error("Failed to disconnect OAuth connection", "error", err, "connection_id", connection.ID)
		return fmt.Errorf("failed to disconnect OAuth provider")
	}

	s.logger.Info("OAuth provider disconnected successfully",
		"user_id", userID,
		"provider", provider,
		"website_id", websiteID,
		"connection_id", connection.ID)

	return nil
}

// RefreshOAuthToken refreshes an OAuth access token using refresh token
func (s *OAuthService) RefreshOAuthToken(ctx context.Context, userID uint, provider string) (*models.OAuthConnection, error) {
	// Find the connection
	connection, err := s.oauthConnectionRepo.GetByUserAndProvider(ctx, userID, provider)
	if err != nil {
		s.logger.Error("Failed to find OAuth connection for refresh", "error", err, "user_id", userID, "provider", provider)
		return nil, fmt.Errorf("OAuth connection not found")
	}

	if connection.RefreshToken == nil || *connection.RefreshToken == "" {
		return nil, fmt.Errorf("no refresh token available")
	}

	// Get provider configuration
	oauthProvider, err := s.oauthProviderRepo.GetByID(ctx, connection.ProviderID)
	if err != nil {
		s.logger.Error("Failed to get OAuth provider for refresh", "error", err, "provider_id", connection.ProviderID)
		return nil, fmt.Errorf("OAuth provider not configured")
	}

	// Create OAuth2 config based on provider
	var oauthConfig *oauth2.Config
	switch provider {
	case "google":
		oauthConfig = &oauth2.Config{
			ClientID:     oauthProvider.ClientID,
			ClientSecret: oauthProvider.ClientSecret,
			RedirectURL:  oauthProvider.RedirectURL,
			Scopes:       strings.Split(oauthProvider.Scopes, " "),
			Endpoint:     google.Endpoint,
		}
	case "facebook":
		oauthConfig = &oauth2.Config{
			ClientID:     oauthProvider.ClientID,
			ClientSecret: oauthProvider.ClientSecret,
			RedirectURL:  oauthProvider.RedirectURL,
			Scopes:       strings.Split(oauthProvider.Scopes, " "),
			Endpoint:     facebook.Endpoint,
		}
	default:
		return nil, fmt.Errorf("unsupported OAuth provider for refresh: %s", provider)
	}

	// Create token from stored data
	token := &oauth2.Token{
		AccessToken:  connection.AccessToken,
		RefreshToken: *connection.RefreshToken,
		TokenType:    connection.TokenType,
	}
	if connection.ExpiresAt != nil {
		token.Expiry = *connection.ExpiresAt
	}

	// Refresh the token
	tokenSource := oauthConfig.TokenSource(ctx, token)
	newToken, err := tokenSource.Token()
	if err != nil {
		s.logger.Error("Failed to refresh OAuth token", "error", err, "provider", provider, "user_id", userID)
		return nil, fmt.Errorf("failed to refresh OAuth token")
	}

	// Update connection with new token
	connection.AccessToken = newToken.AccessToken
	if newToken.RefreshToken != "" {
		connection.RefreshToken = &newToken.RefreshToken
	}
	connection.ExpiresAt = &newToken.Expiry
	connection.UpdateLastUsed()

	if err := s.oauthConnectionRepo.Update(ctx, connection); err != nil {
		s.logger.Error("Failed to update OAuth connection with refreshed token", "error", err, "connection_id", connection.ID)
		return nil, fmt.Errorf("failed to update OAuth connection")
	}

	s.logger.Info("OAuth token refreshed successfully",
		"user_id", userID,
		"provider", provider,
		"connection_id", connection.ID)

	return connection, nil
}

// generateOAuthTokens generates JWT access and refresh tokens for OAuth login
func (s *OAuthService) generateOAuthTokens(ctx context.Context, user *userModels.User, websiteID uint) (accessToken, refreshToken string, err error) {
	if s.jwtService == nil {
		return "", "", fmt.Errorf("JWT service not available")
	}

	// Create JWT claims
	claims := &models.JWTClaims{
		UserID: user.ID,
		Email:  user.Email,
		// Note: CurrentTenantID not set for OAuth users until onboarding
	}

	// Generate token pair using JWT service
	accessToken, refreshToken, err = s.jwtService.GenerateTokenPair(claims)
	if err != nil {
		s.logger.Error("Failed to generate JWT token pair", "error", err, "user_id", user.ID)
		return "", "", err
	}

	return accessToken, refreshToken, nil
}

// createOAuthSession creates a new session for OAuth login
func (s *OAuthService) createOAuthSession(ctx context.Context, user *userModels.User, websiteID uint, refreshToken string) (*models.Session, error) {
	if s.sessionRepo == nil {
		return nil, fmt.Errorf("session repository not available")
	}

	// Create session with proper fields from session model
	session := &models.Session{
		TenantID:   1, // Default tenant, will be updated during onboarding
		UserID:     user.ID,
		Token:      refreshToken, // Use Token field instead of RefreshToken
		DeviceType: models.DeviceTypeUnknown,
		IsRevoked:  false,
		ExpiresAt:  time.Now().Add(24 * time.Hour), // 24 hours
	}

	if err := s.sessionRepo.Create(ctx, session); err != nil {
		s.logger.Error("Failed to create OAuth session", "error", err, "user_id", user.ID)
		return nil, err
	}

	s.logger.Info("OAuth session created successfully",
		"user_id", user.ID,
		"session_id", session.ID,
		"website_id", websiteID)

	return session, nil
}

// generateRandomString generates a random string of specified length
func generateRandomString(length int) string {
	bytes := make([]byte, length/2)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
