package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/cache"
)

// TokenBlacklistService handles token blacklisting operations
type TokenBlacklistService interface {
	BlacklistToken(ctx context.Context, tokenID string, expiresAt time.Time, userID uint, reason models.TokenBlacklistReason) error
	BlacklistTokenWithDetails(ctx context.Context, blacklist *models.TokenBlacklist) error
	IsBlacklisted(ctx context.Context, tokenID string) (bool, error)
	BlacklistAllUserTokens(ctx context.Context, userID uint) error
	BlacklistSessionTokens(ctx context.Context, sessionID uint) error
	CleanupExpiredTokens(ctx context.Context) error
}

type tokenBlacklistService struct {
	repo  repositories.TokenBlacklistRepository
	cache cache.Cache
}

// NewTokenBlacklistService creates a new token blacklist service
func NewTokenBlacklistService(repo repositories.TokenBlacklistRepository, cache cache.Cache) TokenBlacklistService {
	return &tokenBlacklistService{
		repo:  repo,
		cache: cache,
	}
}

// BlacklistToken adds a token to the blacklist
func (s *tokenBlacklistService) BlacklistToken(ctx context.Context, tokenID string, expiresAt time.Time, userID uint, reason models.TokenBlacklistReason) error {
	// Calculate TTL
	ttl := time.Until(expiresAt)
	if ttl <= 0 {
		// Token already expired, no need to blacklist
		return nil
	}

	// Add to cache for fast lookup (if cache available)
	if s.cache != nil {
		cacheKey := s.getCacheKey(tokenID)
		if err := s.cache.Set(ctx, cacheKey, []byte("1"), ttl); err != nil {
			// Log error but continue with DB operation
			fmt.Printf("Failed to cache blacklisted token: %v\n", err)
		}
	}

	// Add to database for persistence
	return s.repo.Add(ctx, tokenID, expiresAt, userID, reason)
}

// BlacklistTokenWithDetails adds a token to the blacklist with full details
func (s *tokenBlacklistService) BlacklistTokenWithDetails(ctx context.Context, blacklist *models.TokenBlacklist) error {
	// Calculate TTL
	ttl := time.Until(blacklist.ExpiresAt)
	if ttl <= 0 {
		// Token already expired, no need to blacklist
		return nil
	}

	// Add to cache for fast lookup (if cache available)
	if s.cache != nil {
		cacheKey := s.getCacheKey(blacklist.TokenJTI)
		if err := s.cache.Set(ctx, cacheKey, []byte("1"), ttl); err != nil {
			// Log error but continue with DB operation
			fmt.Printf("Failed to cache blacklisted token: %v\n", err)
		}
	}

	// Add to database for persistence
	return s.repo.AddWithDetails(ctx, blacklist)
}

// IsBlacklisted checks if a token is blacklisted
func (s *tokenBlacklistService) IsBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	// Check cache first (if available)
	if s.cache != nil {
		cacheKey := s.getCacheKey(tokenID)
		data, err := s.cache.Get(ctx, cacheKey)
		if err == nil && len(data) > 0 {
			return true, nil
		}
	}

	// Check database
	exists, err := s.repo.Exists(ctx, tokenID)
	if err != nil {
		return false, fmt.Errorf("failed to check blacklist: %w", err)
	}

	// If found in DB but not in cache, add to cache (if cache available)
	if exists && s.cache != nil {
		// Get expiration time from DB
		entry, err := s.repo.Get(ctx, tokenID)
		if err == nil && entry != nil {
			ttl := time.Until(entry.ExpiresAt)
			if ttl > 0 {
				cacheKey := s.getCacheKey(tokenID)
				_ = s.cache.Set(ctx, cacheKey, []byte("1"), ttl)
			}
		}
	}

	return exists, nil
}

// BlacklistAllUserTokens blacklists all tokens for a specific user
func (s *tokenBlacklistService) BlacklistAllUserTokens(ctx context.Context, userID uint) error {
	// This requires tracking user tokens separately
	// For now, this is a placeholder that would need integration
	// with the session/token management system

	// In a real implementation, you would:
	// 1. Get all active sessions for the user
	// 2. Blacklist all tokens associated with those sessions
	// 3. Invalidate any API tokens for the user

	return fmt.Errorf("not implemented: requires token tracking system")
}

// BlacklistSessionTokens blacklists all tokens for a specific session
func (s *tokenBlacklistService) BlacklistSessionTokens(ctx context.Context, sessionID uint) error {
	// This requires tracking session tokens
	// For now, this is a placeholder

	// In a real implementation, you would:
	// 1. Get all tokens issued for this session
	// 2. Blacklist each token

	return fmt.Errorf("not implemented: requires session token tracking")
}

// CleanupExpiredTokens removes expired tokens from the blacklist
func (s *tokenBlacklistService) CleanupExpiredTokens(ctx context.Context) error {
	count, err := s.repo.CleanupExpired(ctx)
	if err != nil {
		return fmt.Errorf("failed to cleanup expired tokens: %w", err)
	}

	if count > 0 {
		fmt.Printf("Cleaned up %d expired blacklisted tokens\n", count)
	}

	return nil
}

// getCacheKey generates a cache key for a token ID
func (s *tokenBlacklistService) getCacheKey(tokenID string) string {
	return fmt.Sprintf("token:blacklist:%s", tokenID)
}
