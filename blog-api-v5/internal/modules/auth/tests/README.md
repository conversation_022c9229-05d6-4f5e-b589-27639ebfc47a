# Auth Module Test Suite

This directory contains comprehensive unit and integration tests for the auth module.

## Test Structure

```
tests/
├── README.md                          # This file
├── run_tests.sh                       # Test runner script
├── coverage_test.go                   # Coverage coordination
├── auth_test_suite.go                 # Integration test suite
├── mocks/                             # Mock implementations
│   └── auth_mocks.go                  # All auth-related mocks
└── unit/                              # Unit tests
    ├── repositories/                  # Repository tests
    │   ├── user_repository_test.go
    │   ├── session_repository_test.go
    │   └── login_attempt_repository_test.go
    ├── services/                      # Service tests
    │   ├── password_service_test.go
    │   ├── jwt_service_test.go
    │   └── rate_limiting_service_test.go
    ├── handlers/                      # Handler tests
    │   └── auth_handler_test.go
    ├── models/                        # Model tests
    │   └── user_model_test.go
    └── middleware/                    # Middleware tests
        └── user_journey_tracing_test.go
```

## Running Tests

### Quick Start

```bash
# Run all unit tests with coverage
./tests/run_tests.sh

# Run with integration tests
./tests/run_tests.sh integration

# Clean up test artifacts
./tests/run_tests.sh clean
```

### Individual Test Components

```bash
# Run specific test suites
go test -v ./tests/unit/repositories/
go test -v ./tests/unit/services/
go test -v ./tests/unit/handlers/
go test -v ./tests/unit/models/
go test -v ./tests/unit/middleware/

# Run with coverage
go test -coverprofile=coverage.out ./tests/unit/services/
go tool cover -html=coverage.out -o coverage.html
```

### Integration Tests

```bash
# Enable integration tests
export INTEGRATION_TESTS=true
go test -v ./tests/ -run "Integration"
```

## Test Categories

### Unit Tests

**Repositories** (`unit/repositories/`)
- Database operations testing
- CRUD operations validation
- Query filtering and sorting
- Error handling scenarios
- Transaction management

**Services** (`unit/services/`)
- Business logic validation
- Service integration testing
- Error propagation
- Authentication flows
- Authorization checks

**Handlers** (`unit/handlers/`)
- HTTP request/response testing
- Input validation
- Status code verification
- JSON serialization/deserialization
- Middleware integration

**Models** (`unit/models/`)
- Data structure validation
- Custom type behavior
- Database value conversion
- Model method testing
- Validation logic

**Middleware** (`unit/middleware/`)
- Request processing
- Context manipulation
- Header handling
- Journey tracking

### Integration Tests

**Complete Auth Flows** (`auth_test_suite.go`)
- End-to-end registration process
- Complete login/logout flow
- Token refresh mechanics
- Session management
- Rate limiting behavior
- Multi-device scenarios

## Test Configuration

### Environment Variables

```bash
# Test execution mode
INTEGRATION_TESTS=true        # Enable integration tests
GIN_MODE=test                 # Set Gin to test mode
LOG_LEVEL=error               # Reduce log noise

# Database configuration
TEST_DATABASE_URL=sqlite://file::memory:?cache=shared
```

### Test Database

Tests use an in-memory SQLite database by default for speed and isolation. For integration tests requiring MySQL compatibility, set:

```bash
TEST_DATABASE_URL="mysql://user:pass@localhost:3306/test_db"
```

## Coverage Requirements

- **Minimum Coverage**: 80%
- **Target Coverage**: 95%
- **Critical Paths**: 100% (authentication, authorization)

### Coverage Reports

```bash
# Generate coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html

# View coverage summary
go tool cover -func=coverage.out
```

## Mock Usage

All external dependencies are mocked using testify/mock:

```go
// Example mock usage
mockAuthService := &mocks.MockAuthService{}
mockAuthService.On("Login", mock.Anything, mock.Anything).Return(expectedResponse, nil)

// Verify expectations
mockAuthService.AssertExpectations(t)
```

### Available Mocks

- `MockUserRepository`
- `MockSessionRepository` 
- `MockLoginAttemptRepository`
- `MockAuthService`
- `MockJWTService`
- `MockPasswordService`
- `MockRateLimitingService`
- `MockTenantService`
- `MockNotificationService`

## Test Patterns

### Repository Testing Pattern

```go
func (suite *RepositoryTestSuite) TestCreate_Success() {
    // Arrange
    entity := &models.Entity{Field: "value"}
    
    // Act
    err := suite.repo.Create(suite.ctx, entity)
    
    // Assert
    assert.NoError(suite.T(), err)
    assert.NotZero(suite.T(), entity.ID)
}
```

### Service Testing Pattern

```go
func (suite *ServiceTestSuite) TestMethod_Success() {
    // Arrange
    suite.mockRepo.On("Method", mock.Anything).Return(expectedResult, nil)
    
    // Act
    result, err := suite.service.Method(suite.ctx, input)
    
    // Assert
    assert.NoError(suite.T(), err)
    assert.Equal(suite.T(), expected, result)
    suite.mockRepo.AssertExpectations(suite.T())
}
```

### Handler Testing Pattern

```go
func (suite *HandlerTestSuite) TestEndpoint_Success() {
    // Arrange
    req := dto.Request{Field: "value"}
    suite.mockService.On("Method", mock.Anything).Return(expectedResponse, nil)
    
    // Act
    reqBody, _ := json.Marshal(req)
    httpReq, _ := http.NewRequest("POST", "/endpoint", bytes.NewBuffer(reqBody))
    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, httpReq)
    
    // Assert
    assert.Equal(suite.T(), http.StatusOK, w.Code)
    suite.mockService.AssertExpectations(suite.T())
}
```

## Best Practices

### Test Organization

1. **One test file per implementation file**
2. **Group related tests in test suites**
3. **Use descriptive test names** (`TestMethod_Scenario_ExpectedResult`)
4. **Follow AAA pattern** (Arrange, Act, Assert)

### Test Data Management

1. **Use test fixtures** for complex data setup
2. **Clean database** between tests
3. **Create minimal test data** for each test
4. **Use factories** for test object creation

### Assertions

1. **Use specific assertions** (`assert.Equal` vs `assert.True`)
2. **Test error cases** explicitly
3. **Verify mock expectations** in every test
4. **Check side effects** (database state, logs, etc.)

### Performance

1. **Use table-driven tests** for multiple scenarios
2. **Avoid unnecessary setup/teardown**
3. **Parallel test execution** where possible
4. **Profile slow tests** and optimize

## Debugging Tests

### Running Specific Tests

```bash
# Run specific test
go test -run TestLogin_Success ./tests/unit/handlers/

# Run with verbose output
go test -v -run TestLogin ./tests/unit/handlers/

# Run with race detection
go test -race ./tests/unit/services/
```

### Debug Output

```bash
# Enable debug logging in tests
LOG_LEVEL=debug go test -v ./tests/unit/services/

# Print test coverage during execution
go test -v -cover ./tests/unit/repositories/
```

## Continuous Integration

The test suite is designed to run in CI/CD pipelines:

```yaml
# Example GitHub Actions
- name: Run Auth Module Tests
  run: |
    cd internal/modules/auth
    ./tests/run_tests.sh
    
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./internal/modules/auth/auth_coverage.out
```

## Troubleshooting

### Common Issues

1. **Database connection errors**
   - Check TEST_DATABASE_URL environment variable
   - Ensure database is accessible and has proper permissions

2. **Mock assertion failures**
   - Verify mock setup matches actual calls
   - Check parameter matching in mock expectations

3. **Race condition failures**
   - Use proper synchronization in concurrent tests
   - Avoid shared state between parallel tests

4. **Import cycle errors**
   - Review package dependencies
   - Use interfaces to break cycles

### Getting Help

1. Check existing test examples for patterns
2. Review mock documentation for testify/mock
3. Consult Go testing documentation
4. Ask team members for code review

## Contributing

When adding new tests:

1. **Follow existing patterns** and naming conventions
2. **Add tests for new features** before implementing
3. **Update mocks** when changing interfaces
4. **Maintain coverage** above minimum threshold
5. **Document complex test scenarios**

## Maintenance

### Regular Tasks

1. **Update test dependencies** regularly
2. **Review and refactor** slow or flaky tests
3. **Update mocks** when interfaces change
4. **Monitor coverage trends** and improve low-coverage areas
5. **Clean up obsolete tests** when features are removed