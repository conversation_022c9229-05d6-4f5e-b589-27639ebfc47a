package tests

import (
	"context"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

var (
	db         *gorm.DB
	testHelper *helpers.TestHelper

	// Repositories
	userRepo         repositories.UserRepository
	sessionRepo      repositories.SessionRepository
	loginAttemptRepo repositories.LoginAttemptRepository

	// Services
	authService     services.AuthService
	jwtService      services.JWTService
	passwordService services.PasswordService
	emailService    services.EmailService
	rateLimiter     services.RateLimitingService

	// Test logger
	logger utils.Logger
)

func TestAuth(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Auth Module Suite")
}

var _ = BeforeSuite(func() {
	// Setup test database
	var err error
	db, err = helpers.SetupTestDatabase()
	Expect(err).NotTo(HaveOccurred())

	// Run migrations
	err = helpers.RunMigrations(db)
	Expect(err).NotTo(HaveOccurred())

	// Initialize test helper
	testHelper = helpers.NewTestHelper(db)

	// Initialize repositories
	userRepo = mysql.NewUserRepository(db)
	sessionRepo = mysql.NewSessionRepository(db)
	loginAttemptRepo = mysql.NewLoginAttemptRepository(db)

	// Initialize logger
	logger = utils.NewLogger("test")

	// Initialize services
	jwtService = services.NewJWTService(&services.JWTConfig{
		SecretKey:       "test-secret-key-32-bytes-long-for-jwt",
		AccessTokenTTL:  time.Hour,
		RefreshTokenTTL: time.Hour * 24 * 7,
		Issuer:          "test-issuer",
	}, nil) // No blacklist service for now

	passwordService = services.NewPasswordService(&services.PasswordConfig{
		MinLength:        8,
		RequireUppercase: true,
		RequireLowercase: true,
		RequireNumbers:   true,
		RequireSymbols:   false,
	})

	emailService = services.NewEmailService(&services.EmailConfig{
		SMTPHost:     "localhost",
		SMTPPort:     587,
		SMTPUsername: "<EMAIL>",
		SMTPPassword: "test",
		FromAddress:  "<EMAIL>",
		FromName:     "Test",
	})

	rateLimiter = services.NewRateLimitingService(loginAttemptRepo, &services.RateLimitConfig{
		MaxAttempts:     5,
		LockoutDuration: time.Minute * 15,
		WindowDuration:  time.Minute * 5,
	})

	authService = services.NewAuthService(
		userRepo,
		sessionRepo,
		loginAttemptRepo,
		jwtService,
		passwordService,
		rateLimiter,
		emailService,
		logger,
		&services.AuthConfig{
			RequireEmailVerification: false,
			AllowRegistration:        true,
			MaxLoginAttempts:         5,
			LockoutDuration:          time.Minute * 15,
			SessionTimeout:           time.Hour * 24,
			RefreshTokenTTL:          time.Hour * 24 * 7,
			TwoFactorIssuer:          "test-app",
		},
	)
})

var _ = AfterSuite(func() {
	// Cleanup
	if db != nil {
		sqlDB, _ := db.DB()
		sqlDB.Close()
	}
})

// Reset database between tests
var _ = BeforeEach(func() {
	testHelper.CleanDatabase()
	testHelper.SeedBasicData()
})

// Test context with timeout
var testCtx = context.WithValue(context.Background(), "test", true)

// Helper function to create test user
func createTestUser(email, password string, tenantID uint) *models.User {
	hashedPassword, err := passwordService.HashPassword(password)
	Expect(err).NotTo(HaveOccurred())

	user := &models.User{
		TenantID:      tenantID,
		Email:         email,
		PasswordHash:  hashedPassword,
		EmailVerified: true,
		Status:        models.UserStatusActive,
		Role:          models.UserRoleUser,
	}

	err = userRepo.Create(testCtx, user)
	Expect(err).NotTo(HaveOccurred())

	return user
}

// Helper function to create test tenant
func createTestTenant(name string) uint {
	return testHelper.CreateTenant(name).ID
}
