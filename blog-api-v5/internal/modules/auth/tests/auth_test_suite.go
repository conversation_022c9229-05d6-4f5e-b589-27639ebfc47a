package tests

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/database"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// AuthIntegrationTestSuite runs integration tests for the entire auth module
type AuthIntegrationTestSuite struct {
	suite.Suite
	db                  *gorm.DB
	ctx                 context.Context
	
	// Repositories
	userRepo            *mysql.UserRepository
	sessionRepo         *mysql.SessionRepository
	loginAttemptRepo    *mysql.LoginAttemptRepository
	tokenBlacklistRepo  *mysql.TokenBlacklistRepository
	
	// Services
	authService         services.AuthService
	jwtService          services.JWTService
	passwordService     services.PasswordService
	rateLimitingService services.RateLimitingService
	
	// Test data
	testUser            *userModels.User
	testSession         *models.Session
}

func (suite *AuthIntegrationTestSuite) SetupSuite() {
	// Skip integration tests if not in integration test mode
	if os.Getenv("INTEGRATION_TESTS") != "true" {
		suite.T().Skip("Skipping integration tests (set INTEGRATION_TESTS=true to run)")
	}
	
	// Initialize test database
	testDB, err := database.NewTestDatabase()
	suite.Require().NoError(err)
	suite.db = testDB
	
	// Auto-migrate all auth tables
	err = suite.db.AutoMigrate(
		&userModels.User{},
		&models.Session{},
		&models.LoginAttempt{},
		&models.TokenBlacklist{},
		&models.PasswordReset{},
		&models.EmailVerificationToken{},
		&models.OAuthProvider{},
		&models.OAuthConnection{},
	)
	suite.Require().NoError(err)
	
	// Initialize repositories
	suite.userRepo = mysql.NewUserRepository(suite.db)
	suite.sessionRepo = mysql.NewSessionRepository(suite.db)
	suite.loginAttemptRepo = mysql.NewLoginAttemptRepository(suite.db)
	suite.tokenBlacklistRepo = mysql.NewTokenBlacklistRepository(suite.db)
	
	// Initialize services
	passwordConfig := &services.PasswordConfig{
		MinLength:           8,
		RequireUppercase:    true,
		RequireLowercase:    true,
		RequireNumbers:      true,
		RequireSpecialChars: true,
		MaxLength:           128,
		MinUniqueChars:      4,
		DisallowCommon:      true,
		DisallowRepeating:   true,
		DisallowSequential:  true,
		CheckCompromised:    false, // Disable for tests
	}
	suite.passwordService = services.NewPasswordService(passwordConfig)
	
	secretKey := "test-secret-key-for-integration-testing-should-be-very-long-and-secure"
	suite.jwtService = services.NewJWTService(secretKey, suite.tokenBlacklistRepo)
	
	suite.rateLimitingService = services.NewRateLimitingService(suite.loginAttemptRepo)
	
	// Initialize main auth service
	authConfig := &services.AuthConfig{
		RequireEmailVerification: true,
		AllowUserRegistration:    true,
		MaxFailedAttempts:        5,
		LockoutDuration:          15, // minutes
	}
	
	// Create a mock logger for testing
	logger := &utils.MockLogger{}
	
	suite.authService = services.NewAuthService(
		suite.userRepo,
		suite.sessionRepo,
		suite.loginAttemptRepo,
		suite.jwtService,
		suite.passwordService,
		suite.rateLimitingService,
		nil, // tenant service (mock if needed)
		nil, // tenant membership repo (mock if needed)
		nil, // email service (mock if needed)
		logger,
		authConfig,
	)
	
	suite.ctx = context.Background()
}

func (suite *AuthIntegrationTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

func (suite *AuthIntegrationTestSuite) SetupTest() {
	// Clean all tables before each test
	suite.cleanDatabase()
	
	// Create test user
	suite.createTestUser()
}

func (suite *AuthIntegrationTestSuite) TearDownTest() {
	// Clean up after each test
	suite.cleanDatabase()
}

func (suite *AuthIntegrationTestSuite) cleanDatabase() {
	tables := []string{
		"sessions",
		"login_attempts", 
		"token_blacklist",
		"password_resets",
		"email_verification_tokens",
		"oauth_connections",
		"oauth_providers",
		"users",
	}
	
	for _, table := range tables {
		suite.db.Exec("DELETE FROM " + table)
	}
}

func (suite *AuthIntegrationTestSuite) createTestUser() {
	hashedPassword, err := suite.passwordService.HashPassword("TestPassword123!")
	suite.Require().NoError(err)
	
	suite.testUser = &userModels.User{
		Email:     "<EMAIL>",
		Username:  "testuser",
		Password:  hashedPassword,
		FirstName: "Test",
		LastName:  "User",
		Status:    userModels.UserStatusActive,
	}
	
	err = suite.userRepo.Create(suite.ctx, suite.testUser)
	suite.Require().NoError(err)
	
	// Verify email for test user
	err = suite.userRepo.VerifyEmail(suite.ctx, suite.testUser.ID)
	suite.Require().NoError(err)
}

func (suite *AuthIntegrationTestSuite) TestCompleteRegistrationFlow() {
	// Test the complete registration flow
	registerReq := &services.RegisterRequest{
		Email:     "<EMAIL>",
		Username:  "newuser",
		Password:  "NewPassword123!",
		FirstName: "New",
		LastName:  "User",
	}
	
	// Register user
	registerResp, err := suite.authService.Register(suite.ctx, registerReq)
	
	suite.NoError(err)
	suite.NotNil(registerResp)
	suite.NotNil(registerResp.User)
	suite.Equal("<EMAIL>", registerResp.User.Email)
	suite.Equal("newuser", registerResp.User.Username)
	suite.Equal(userModels.UserStatusPendingVerification, registerResp.User.Status)
	
	// Verify that user exists in database
	dbUser, err := suite.userRepo.GetByEmail(suite.ctx, "<EMAIL>")
	suite.NoError(err)
	suite.NotNil(dbUser)
	suite.Equal("<EMAIL>", dbUser.Email)
}

func (suite *AuthIntegrationTestSuite) TestCompleteLoginFlow() {
	// Test complete login flow
	loginReq := &services.LoginRequest{
		Email:    suite.testUser.Email,
		Password: "TestPassword123!",
		DeviceInfo: services.DeviceInfo{
			UserAgent:  "Test Browser",
			IPAddress:  "127.0.0.1",
			DeviceType: "desktop",
		},
	}
	
	// Perform login
	loginResp, err := suite.authService.Login(suite.ctx, loginReq)
	
	suite.NoError(err)
	suite.NotNil(loginResp)
	suite.NotNil(loginResp.User)
	suite.NotEmpty(loginResp.AccessToken)
	suite.NotEmpty(loginResp.RefreshToken)
	suite.Greater(loginResp.ExpiresIn, int64(0))
	
	// Verify tokens are valid
	accessClaims, err := suite.jwtService.ValidateAccessToken(suite.ctx, loginResp.AccessToken)
	suite.NoError(err)
	suite.NotNil(accessClaims)
	suite.Equal(suite.testUser.ID, accessClaims.UserID)
	
	refreshClaims, err := suite.jwtService.ValidateRefreshToken(suite.ctx, loginResp.RefreshToken)
	suite.NoError(err)
	suite.NotNil(refreshClaims)
	suite.Equal(suite.testUser.ID, refreshClaims.UserID)
	
	// Verify session is created
	sessions, err := suite.sessionRepo.GetActiveSessionsByUser(suite.ctx, suite.testUser.ID)
	suite.NoError(err)
	suite.Len(sessions, 1)
	
	// Verify login attempt is recorded
	attempts, err := suite.loginAttemptRepo.GetRecentAttempts(suite.ctx, suite.testUser.ID, 5*60, 10) // 5 minutes
	suite.NoError(err)
	suite.Len(attempts, 1)
	suite.Equal(models.AttemptResultSuccess, attempts[0].Result)
}

func (suite *AuthIntegrationTestSuite) TestRefreshTokenFlow() {
	// First, login to get tokens
	loginReq := &services.LoginRequest{
		Email:    suite.testUser.Email,
		Password: "TestPassword123!",
		DeviceInfo: services.DeviceInfo{
			UserAgent:  "Test Browser",
			IPAddress:  "127.0.0.1",
			DeviceType: "desktop",
		},
	}
	
	loginResp, err := suite.authService.Login(suite.ctx, loginReq)
	suite.Require().NoError(err)
	
	// Now test refresh token
	refreshReq := &services.RefreshTokenRequest{
		RefreshToken: loginResp.RefreshToken,
	}
	
	refreshResp, err := suite.authService.RefreshToken(suite.ctx, refreshReq)
	
	suite.NoError(err)
	suite.NotNil(refreshResp)
	suite.NotEmpty(refreshResp.AccessToken)
	suite.NotEmpty(refreshResp.RefreshToken)
	
	// Verify new tokens are different
	suite.NotEqual(loginResp.AccessToken, refreshResp.AccessToken)
	suite.NotEqual(loginResp.RefreshToken, refreshResp.RefreshToken)
	
	// Verify new tokens are valid
	newAccessClaims, err := suite.jwtService.ValidateAccessToken(suite.ctx, refreshResp.AccessToken)
	suite.NoError(err)
	suite.Equal(suite.testUser.ID, newAccessClaims.UserID)
	
	newRefreshClaims, err := suite.jwtService.ValidateRefreshToken(suite.ctx, refreshResp.RefreshToken)
	suite.NoError(err)
	suite.Equal(suite.testUser.ID, newRefreshClaims.UserID)
}

func (suite *AuthIntegrationTestSuite) TestLogoutFlow() {
	// First, login to get a session
	loginReq := &services.LoginRequest{
		Email:    suite.testUser.Email,
		Password: "TestPassword123!",
		DeviceInfo: services.DeviceInfo{
			UserAgent:  "Test Browser",
			IPAddress:  "127.0.0.1",
			DeviceType: "desktop",
		},
	}
	
	loginResp, err := suite.authService.Login(suite.ctx, loginReq)
	suite.Require().NoError(err)
	
	// Get the session ID (this would typically come from the JWT or context)
	sessions, err := suite.sessionRepo.GetActiveSessionsByUser(suite.ctx, suite.testUser.ID)
	suite.Require().NoError(err)
	suite.Require().Len(sessions, 1)
	sessionID := sessions[0].Token // Using token as session identifier
	
	// Perform logout
	logoutResp, err := suite.authService.Logout(suite.ctx, suite.testUser.ID, sessionID)
	
	suite.NoError(err)
	suite.NotNil(logoutResp)
	suite.NotEmpty(logoutResp.Message)
	
	// Verify session is invalidated
	activeSessions, err := suite.sessionRepo.GetActiveSessionsByUser(suite.ctx, suite.testUser.ID)
	suite.NoError(err)
	suite.Len(activeSessions, 0)
	
	// Verify tokens are blacklisted
	isBlacklisted, err := suite.tokenBlacklistRepo.Exists(suite.ctx, loginResp.AccessToken)
	suite.NoError(err)
	suite.True(isBlacklisted)
}

func (suite *AuthIntegrationTestSuite) TestFailedLoginAttempts() {
	// Test multiple failed login attempts
	loginReq := &services.LoginRequest{
		Email:    suite.testUser.Email,
		Password: "WrongPassword123!",
		DeviceInfo: services.DeviceInfo{
			UserAgent:  "Test Browser",
			IPAddress:  "127.0.0.1",
			DeviceType: "desktop",
		},
	}
	
	// Attempt login with wrong password multiple times
	for i := 0; i < 3; i++ {
		_, err := suite.authService.Login(suite.ctx, loginReq)
		suite.Error(err)
		suite.Equal(services.ErrInvalidCredentials, err)
	}
	
	// Verify failed attempts are recorded
	failedCount, err := suite.rateLimitingService.GetFailedAttemptCount(suite.ctx, suite.testUser.ID, 5*60) // 5 minutes
	suite.NoError(err)
	suite.Equal(int64(3), failedCount)
	
	// Verify attempts are in database
	attempts, err := suite.loginAttemptRepo.GetFailedAttemptsByUser(suite.ctx, suite.testUser.ID, 5*60, 10)
	suite.NoError(err)
	suite.Len(attempts, 3)
	
	for _, attempt := range attempts {
		suite.Equal(models.AttemptResultFailure, attempt.Result)
		suite.Equal(suite.testUser.ID, *attempt.UserID)
	}
}

func (suite *AuthIntegrationTestSuite) TestSessionManagement() {
	// Create multiple sessions for the user
	deviceInfos := []services.DeviceInfo{
		{UserAgent: "Browser 1", IPAddress: "127.0.0.1", DeviceType: "desktop"},
		{UserAgent: "Browser 2", IPAddress: "***********", DeviceType: "mobile"},
		{UserAgent: "Browser 3", IPAddress: "********", DeviceType: "tablet"},
	}
	
	var loginResponses []*dto.LoginResponse
	
	// Create multiple sessions
	for _, deviceInfo := range deviceInfos {
		loginReq := &services.LoginRequest{
			Email:      suite.testUser.Email,
			Password:   "TestPassword123!",
			DeviceInfo: deviceInfo,
		}
		
		loginResp, err := suite.authService.Login(suite.ctx, loginReq)
		suite.Require().NoError(err)
		loginResponses = append(loginResponses, loginResp)
	}
	
	// Verify all sessions exist
	sessions, err := suite.sessionRepo.GetActiveSessionsByUser(suite.ctx, suite.testUser.ID)
	suite.NoError(err)
	suite.Len(sessions, 3)
	
	// Get active sessions through service
	activeSessionsResp, err := suite.authService.GetActiveSessions(suite.ctx, suite.testUser.ID)
	suite.NoError(err)
	suite.NotNil(activeSessionsResp)
	suite.Len(activeSessionsResp.Sessions, 3)
	
	// Test logout all devices
	logoutAllResp, err := suite.authService.LogoutAllDevices(suite.ctx, suite.testUser.ID)
	suite.NoError(err)
	suite.NotNil(logoutAllResp)
	
	// Verify all sessions are invalidated
	remainingSessions, err := suite.sessionRepo.GetActiveSessionsByUser(suite.ctx, suite.testUser.ID)
	suite.NoError(err)
	suite.Len(remainingSessions, 0)
}

func (suite *AuthIntegrationTestSuite) TestPasswordService_Integration() {
	// Test password hashing and verification
	password := "TestPassword123!"
	
	hashedPassword, err := suite.passwordService.HashPassword(password)
	suite.NoError(err)
	suite.NotEmpty(hashedPassword)
	suite.NotEqual(password, hashedPassword)
	
	// Test verification
	isValid := suite.passwordService.VerifyPassword(password, hashedPassword)
	suite.True(isValid)
	
	// Test wrong password
	isValid = suite.passwordService.VerifyPassword("WrongPassword", hashedPassword)
	suite.False(isValid)
	
	// Test password validation
	err = suite.passwordService.ValidatePassword(password)
	suite.NoError(err)
	
	// Test weak password
	err = suite.passwordService.ValidatePassword("weak")
	suite.Error(err)
}

func (suite *AuthIntegrationTestSuite) TestJWTService_Integration() {
	// Test token generation and validation
	tenantMemberships := []*models.TenantMembership{
		{TenantID: 1, IsPrimary: true},
		{TenantID: 2, IsPrimary: false},
	}
	
	// Generate access token
	accessToken, err := suite.jwtService.GenerateAccessToken(suite.testUser, tenantMemberships)
	suite.NoError(err)
	suite.NotEmpty(accessToken)
	
	// Generate refresh token
	tokenFamily := "test-family"
	refreshToken, err := suite.jwtService.GenerateRefreshToken(suite.testUser, tokenFamily)
	suite.NoError(err)
	suite.NotEmpty(refreshToken)
	
	// Validate access token
	accessClaims, err := suite.jwtService.ValidateAccessToken(suite.ctx, accessToken)
	suite.NoError(err)
	suite.NotNil(accessClaims)
	suite.Equal(suite.testUser.ID, accessClaims.UserID)
	suite.Equal(suite.testUser.Email, accessClaims.Email)
	suite.Len(accessClaims.TenantMemberships, 2)
	
	// Validate refresh token
	refreshClaims, err := suite.jwtService.ValidateRefreshToken(suite.ctx, refreshToken)
	suite.NoError(err)
	suite.NotNil(refreshClaims)
	suite.Equal(suite.testUser.ID, refreshClaims.UserID)
	suite.Equal(tokenFamily, refreshClaims.TokenFamily)
	
	// Test token refresh
	newAccessToken, err := suite.jwtService.RefreshAccessToken(suite.ctx, refreshToken, suite.testUser, tenantMemberships)
	suite.NoError(err)
	suite.NotEmpty(newAccessToken)
	suite.NotEqual(accessToken, newAccessToken)
}

// Run the integration test suite
func TestAuthIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(AuthIntegrationTestSuite))
}