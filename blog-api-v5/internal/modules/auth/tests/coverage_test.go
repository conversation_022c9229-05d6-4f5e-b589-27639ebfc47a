package tests

import (
	"os"
	"testing"

	// Import all test packages to ensure they run when testing coverage
	_ "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/unit/handlers"
	_ "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/unit/middleware"
	_ "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/unit/models"
	_ "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/unit/repositories"
	_ "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/unit/services"
)

// TestCoverage is a dummy test that ensures all test packages are included
// when running coverage analysis
func TestCoverage(t *testing.T) {
	// This test doesn't do anything but ensures the test packages are loaded
	// when running coverage analysis with: go test -coverprofile=coverage.out ./...
	
	if os.Getenv("SKIP_COVERAGE_TEST") == "true" {
		t.Skip("Skipping coverage test")
	}
	
	t.Log("Coverage test executed - all test packages imported")
}

// TestMain can be used to set up and tear down test environment
func TestMain(m *testing.M) {
	// Set up test environment
	setupTestEnvironment()
	
	// Run tests
	code := m.Run()
	
	// Clean up test environment
	teardownTestEnvironment()
	
	// Exit with the same code as the tests
	os.Exit(code)
}

func setupTestEnvironment() {
	// Set test-specific environment variables
	os.Setenv("GIN_MODE", "test")
	os.Setenv("LOG_LEVEL", "error")
	
	// Set database URL for tests if not already set
	if os.Getenv("TEST_DATABASE_URL") == "" {
		os.Setenv("TEST_DATABASE_URL", "sqlite://file::memory:?cache=shared")
	}
}

func teardownTestEnvironment() {
	// Clean up any test-specific environment variables or resources
	testEnvVars := []string{
		"GIN_MODE",
		"LOG_LEVEL",
		"TEST_DATABASE_URL",
	}
	
	for _, envVar := range testEnvVars {
		os.Unsetenv(envVar)
	}
}