package focused

import (
	"context"
	"fmt"
	"testing"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

func TestRegistration(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Registration Test Suite")
}

var (
	testDB     *gorm.DB
	testHelper *helpers.TestHelper
	logger     utils.Logger
)

var _ = BeforeSuite(func() {
	// Setup test database
	var err error
	testDB, err = helpers.SetupTestDatabase()
	Expect(err).NotTo(HaveOccurred())

	// Run migrations
	err = helpers.RunMigrations(testDB)
	Expect(err).NotTo(HaveOccurred())

	// Initialize test helper
	testHelper = helpers.NewTestHelper(testDB)

	// Initialize logger
	logger = utils.NewLogger()
})

var _ = AfterSuite(func() {
	// Cleanup
	if testDB != nil {
		sqlDB, _ := testDB.DB()
		sqlDB.Close()
	}
})

// Reset database between tests
var _ = BeforeEach(func() {
	testHelper.CleanDatabase()
	testHelper.SeedBasicData()
})

var _ = Describe("Registration BDD Testing", func() {
	var (
		userRepo repositories.UserRepository
		tenant   *tenantModels.Tenant
		ctx      context.Context
	)

	BeforeEach(func() {
		ctx = context.Background()
		tenant = testHelper.CreateTenant("Test Tenant")
		userRepo = mysql.NewUserRepository(testDB)
	})

	Describe("Given a user wants to register", func() {
		Context("When they provide valid information", func() {
			It("should create a new user account", func() {
				// Given
				user := &models.User{
					TenantID:      tenant.ID,
					Email:         "<EMAIL>",
					PasswordHash:  "$2a$10$test.password.hash",
					EmailVerified: false,
					Status:        models.UserStatusActive,
					Role:          models.UserRoleUser,
					Language:      "en",
					Timezone:      "UTC",
				}

				// When
				err := userRepo.Create(ctx, user)

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(user.ID).NotTo(BeZero())
				Expect(user.CreatedAt).NotTo(BeZero())
				Expect(user.UpdatedAt).NotTo(BeZero())

				// And the user should exist in database
				var dbUser models.User
				err = testDB.Where("email = ?", "<EMAIL>").First(&dbUser).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(dbUser.TenantID).To(Equal(tenant.ID))
				Expect(dbUser.Status).To(Equal(models.UserStatusActive))
			})

			It("should assign default user role", func() {
				// Given
				user := &models.User{
					TenantID:     tenant.ID,
					Email:        "<EMAIL>",
					PasswordHash: "$2a$10$test.password.hash",
					Language:     "en",
					Timezone:     "UTC",
				}

				// When
				err := testDB.Create(user).Error

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(user.Role).To(Equal(models.UserRoleUser))
				Expect(user.Status).To(Equal(models.UserStatusActive))
			})

			It("should handle optional profile information", func() {
				// Given
				firstName := "John"
				lastName := "Doe"
				username := "johndoe"

				user := &models.User{
					TenantID:     tenant.ID,
					Email:        "<EMAIL>",
					Username:     &username,
					FirstName:    &firstName,
					LastName:     &lastName,
					PasswordHash: "$2a$10$test.password.hash",
					Language:     "en",
					Timezone:     "UTC",
				}

				// When
				err := userRepo.Create(ctx, user)

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(user.GetFullName()).To(Equal("John Doe"))

				// And the profile information should be stored
				var dbUser models.User
				err = testDB.Where("email = ?", "<EMAIL>").First(&dbUser).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(*dbUser.Username).To(Equal("johndoe"))
				Expect(*dbUser.FirstName).To(Equal("John"))
				Expect(*dbUser.LastName).To(Equal("Doe"))
			})
		})

		Context("When they provide duplicate email", func() {
			It("should detect email already exists", func() {
				// Given an existing user
				existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
				Expect(existingUser).NotTo(BeNil())

				// When checking if email exists
				exists, err := userRepo.Exists(ctx, "<EMAIL>")

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(exists).To(BeTrue())
			})

			It("should detect unique email is available", func() {
				// Given no existing user with this email

				// When checking if email exists
				exists, err := userRepo.Exists(ctx, "<EMAIL>")

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(exists).To(BeFalse())
			})
		})

		Context("When they provide duplicate username", func() {
			It("should detect username already exists", func() {
				// Given an existing user with username
				existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
				username := "duplicateuser"
				existingUser.Username = &username
				err := testDB.Save(existingUser).Error
				Expect(err).NotTo(HaveOccurred())

				// When checking if username exists
				exists, err := userRepo.ExistsByUsername(ctx, "duplicateuser")

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(exists).To(BeTrue())
			})

			It("should detect unique username is available", func() {
				// Given no existing user with this username

				// When checking if username exists
				exists, err := userRepo.ExistsByUsername(ctx, "uniqueuser")

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(exists).To(BeFalse())
			})
		})
	})

	Describe("Given a user has registered", func() {
		Context("When they login for the first time", func() {
			It("should track login information", func() {
				// Given a registered user
				user := testHelper.CreateUser("<EMAIL>", tenant.ID)

				// When they login
				err := userRepo.UpdateLastLogin(ctx, user.ID, "*************")

				// Then
				Expect(err).NotTo(HaveOccurred())

				// And login information should be tracked
				var dbUser models.User
				err = testDB.Where("id = ?", user.ID).First(&dbUser).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(dbUser.LastLoginAt).NotTo(BeNil())
				Expect(dbUser.LastLoginIP).NotTo(BeNil())
				Expect(*dbUser.LastLoginIP).To(Equal("*************"))
				Expect(dbUser.LoginCount).To(Equal(uint(1)))
			})

			It("should increment login count on subsequent logins", func() {
				// Given a user who has logged in before
				user := testHelper.CreateUser("<EMAIL>", tenant.ID)

				// When they login multiple times
				for i := 0; i < 3; i++ {
					err := userRepo.UpdateLastLogin(ctx, user.ID, "*************")
					Expect(err).NotTo(HaveOccurred())
				}

				// Then login count should be updated
				var dbUser models.User
				err := testDB.Where("id = ?", user.ID).First(&dbUser).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(dbUser.LoginCount).To(Equal(uint(3)))
			})
		})

		Context("When their account status changes", func() {
			It("should reflect status in user methods", func() {
				// Given a user with active status
				user := &models.User{
					TenantID:      tenant.ID,
					Email:         "<EMAIL>",
					Status:        models.UserStatusActive,
					EmailVerified: true,
				}

				// When user is active
				Expect(user.IsActive()).To(BeTrue())
				Expect(user.IsDeleted()).To(BeFalse())
				Expect(user.CanLogin()).To(BeTrue())

				// When user is suspended
				user.Status = models.UserStatusSuspended
				Expect(user.IsActive()).To(BeFalse())
				Expect(user.CanLogin()).To(BeFalse())

				// When user is deleted
				user.Status = models.UserStatusDeleted
				Expect(user.IsActive()).To(BeFalse())
				Expect(user.IsDeleted()).To(BeTrue())
				Expect(user.CanLogin()).To(BeFalse())
			})
		})
	})

	Describe("Given multiple users in the system", func() {
		Context("When counting users by tenant", func() {
			It("should return correct count", func() {
				// Given multiple users in the tenant
				for i := 0; i < 5; i++ {
					testHelper.CreateUser(fmt.Sprintf("<EMAIL>", i), tenant.ID)
				}

				// When counting users
				count, err := userRepo.CountByTenant(ctx, tenant.ID)

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(count).To(Equal(int64(5)))
			})

			It("should not count users from other tenants", func() {
				// Given users in different tenants
				otherTenant := testHelper.CreateTenant("Other Tenant")

				// Create users in first tenant
				for i := 0; i < 3; i++ {
					testHelper.CreateUser(fmt.Sprintf("<EMAIL>", i), tenant.ID)
				}

				// Create users in second tenant
				for i := 0; i < 2; i++ {
					testHelper.CreateUser(fmt.Sprintf("<EMAIL>", i), otherTenant.ID)
				}

				// When counting users for first tenant
				count, err := userRepo.CountByTenant(ctx, tenant.ID)

				// Then should only count users from first tenant
				Expect(err).NotTo(HaveOccurred())
				Expect(count).To(Equal(int64(3)))
			})
		})
	})

	Describe("Given the registration process", func() {
		Context("When user lookup is needed", func() {
			It("should find user by email", func() {
				// Given a registered user
				existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)

				// When looking up by email
				user, err := userRepo.GetByEmail(ctx, "<EMAIL>")

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(user).NotTo(BeNil())
				Expect(user.ID).To(Equal(existingUser.ID))
				Expect(user.Email).To(Equal("<EMAIL>"))
			})

			It("should find user by email or username", func() {
				// Given a user with both email and username
				user := testHelper.CreateUser("<EMAIL>", tenant.ID)
				username := "testuser"
				user.Username = &username
				err := testDB.Save(user).Error
				Expect(err).NotTo(HaveOccurred())

				// When looking up by email
				foundUser, err := userRepo.GetByEmailOrUsername(ctx, "<EMAIL>")
				Expect(err).NotTo(HaveOccurred())
				Expect(foundUser).NotTo(BeNil())
				Expect(foundUser.ID).To(Equal(user.ID))

				// When looking up by username
				foundUser, err = userRepo.GetByEmailOrUsername(ctx, "testuser")
				Expect(err).NotTo(HaveOccurred())
				Expect(foundUser).NotTo(BeNil())
				Expect(foundUser.ID).To(Equal(user.ID))
			})

			It("should return nil for non-existent user", func() {
				// Given no user with this email

				// When looking up non-existent user
				user, err := userRepo.GetByEmail(ctx, "<EMAIL>")

				// Then
				Expect(err).NotTo(HaveOccurred())
				Expect(user).To(BeNil())
			})
		})
	})
})
