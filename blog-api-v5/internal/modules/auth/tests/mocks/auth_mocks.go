package mocks

import (
	"context"
	"time"

	"github.com/stretchr/testify/mock"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	notificationModels "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// MockUserRepository mocks the user repository
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *userModels.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uint) (*userModels.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*userModels.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*userModels.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*userModels.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *userModels.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) UpdateStatus(ctx context.Context, id uint, status userModels.UserStatus) error {
	args := m.Called(ctx, id, status)
	return args.Error(0)
}

func (m *MockUserRepository) UpdatePassword(ctx context.Context, id uint, hashedPassword string) error {
	args := m.Called(ctx, id, hashedPassword)
	return args.Error(0)
}

func (m *MockUserRepository) GetByUsername(ctx context.Context, username string) (*userModels.User, error) {
	args := m.Called(ctx, username)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*userModels.User), args.Error(1)
}

func (m *MockUserRepository) List(ctx context.Context, limit, offset int) ([]*userModels.User, int, error) {
	args := m.Called(ctx, limit, offset)
	return args.Get(0).([]*userModels.User), args.Int(1), args.Error(2)
}

func (m *MockUserRepository) ListByTenant(ctx context.Context, tenantID uint, limit, offset int) ([]*userModels.User, int, error) {
	args := m.Called(ctx, tenantID, limit, offset)
	return args.Get(0).([]*userModels.User), args.Int(1), args.Error(2)
}

func (m *MockUserRepository) CountByTenant(ctx context.Context, tenantID uint) (int, error) {
	args := m.Called(ctx, tenantID)
	return args.Int(0), args.Error(1)
}

func (m *MockUserRepository) UpdateLastLogin(ctx context.Context, userID uint) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockUserRepository) SearchUsers(ctx context.Context, query string, limit, offset int) ([]*userModels.User, int, error) {
	args := m.Called(ctx, query, limit, offset)
	return args.Get(0).([]*userModels.User), args.Int(1), args.Error(2)
}

func (m *MockUserRepository) GetUsersByRole(ctx context.Context, tenantID uint, role string, limit, offset int) ([]*userModels.User, int, error) {
	args := m.Called(ctx, tenantID, role, limit, offset)
	return args.Get(0).([]*userModels.User), args.Int(1), args.Error(2)
}

func (m *MockUserRepository) GetActiveUsers(ctx context.Context, tenantID uint, limit, offset int) ([]*userModels.User, int, error) {
	args := m.Called(ctx, tenantID, limit, offset)
	return args.Get(0).([]*userModels.User), args.Int(1), args.Error(2)
}

func (m *MockUserRepository) GetUserActivity(ctx context.Context, userID uint, days int) (map[string]interface{}, error) {
	args := m.Called(ctx, userID, days)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockUserRepository) BulkUpdateStatus(ctx context.Context, userIDs []uint, status userModels.UserStatus) error {
	args := m.Called(ctx, userIDs, status)
	return args.Error(0)
}

func (m *MockUserRepository) GetUserStats(ctx context.Context, tenantID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockUserRepository) VerifyEmail(ctx context.Context, userID uint) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockUserRepository) UpdateEmailVerificationStatus(ctx context.Context, userID uint, verified bool) error {
	args := m.Called(ctx, userID, verified)
	return args.Error(0)
}

// MockSessionRepository mocks the session repository
type MockSessionRepository struct {
	mock.Mock
}

func (m *MockSessionRepository) Create(ctx context.Context, session *models.Session) error {
	args := m.Called(ctx, session)
	return args.Error(0)
}

func (m *MockSessionRepository) GetByID(ctx context.Context, id uint) (*models.Session, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Session), args.Error(1)
}

func (m *MockSessionRepository) Update(ctx context.Context, session *models.Session) error {
	args := m.Called(ctx, session)
	return args.Error(0)
}

func (m *MockSessionRepository) InvalidateSession(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockSessionRepository) GetActiveSessionsByUser(ctx context.Context, userID uint) ([]*models.Session, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*models.Session), args.Error(1)
}

func (m *MockSessionRepository) InvalidateAllUserSessions(ctx context.Context, userID uint) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockSessionRepository) CleanupExpiredSessions(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockSessionRepository) ExtendExpiration(ctx context.Context, sessionID uint, newExpiry time.Time) error {
	args := m.Called(ctx, sessionID, newExpiry)
	return args.Error(0)
}

func (m *MockSessionRepository) GetByRefreshToken(ctx context.Context, refreshToken string) (*models.Session, error) {
	args := m.Called(ctx, refreshToken)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Session), args.Error(1)
}

func (m *MockSessionRepository) UpdateLastUsed(ctx context.Context, sessionID uint) error {
	args := m.Called(ctx, sessionID)
	return args.Error(0)
}

func (m *MockSessionRepository) GetSessionCount(ctx context.Context, userID uint) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockSessionRepository) GetExpiredSessions(ctx context.Context, limit int) ([]*models.Session, error) {
	args := m.Called(ctx, limit)
	return args.Get(0).([]*models.Session), args.Error(1)
}

func (m *MockSessionRepository) BulkInvalidate(ctx context.Context, sessionIDs []uint) error {
	args := m.Called(ctx, sessionIDs)
	return args.Error(0)
}

func (m *MockSessionRepository) GetUserSessionHistory(ctx context.Context, userID uint, limit, offset int) ([]*models.Session, int, error) {
	args := m.Called(ctx, userID, limit, offset)
	return args.Get(0).([]*models.Session), args.Int(1), args.Error(2)
}

func (m *MockSessionRepository) GetSessionsByIPAddress(ctx context.Context, ipAddress string, limit, offset int) ([]*models.Session, int, error) {
	args := m.Called(ctx, ipAddress, limit, offset)
	return args.Get(0).([]*models.Session), args.Int(1), args.Error(2)
}

func (m *MockSessionRepository) GetSessionsByDevice(ctx context.Context, deviceType models.DeviceType, limit, offset int) ([]*models.Session, int, error) {
	args := m.Called(ctx, deviceType, limit, offset)
	return args.Get(0).([]*models.Session), args.Int(1), args.Error(2)
}

func (m *MockSessionRepository) GetSessionStatistics(ctx context.Context, days int) (map[string]interface{}, error) {
	args := m.Called(ctx, days)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

// MockLoginAttemptRepository mocks the login attempt repository
type MockLoginAttemptRepository struct {
	mock.Mock
}

func (m *MockLoginAttemptRepository) Create(ctx context.Context, attempt *models.LoginAttempt) error {
	args := m.Called(ctx, attempt)
	return args.Error(0)
}

func (m *MockLoginAttemptRepository) GetRecentAttempts(ctx context.Context, userID uint, since time.Time) ([]*models.LoginAttempt, error) {
	args := m.Called(ctx, userID, since)
	return args.Get(0).([]*models.LoginAttempt), args.Error(1)
}

func (m *MockLoginAttemptRepository) GetFailedAttempts(ctx context.Context, userID uint, since time.Time) ([]*models.LoginAttempt, error) {
	args := m.Called(ctx, userID, since)
	return args.Get(0).([]*models.LoginAttempt), args.Error(1)
}

func (m *MockLoginAttemptRepository) GetAttemptsByIP(ctx context.Context, ipAddress string, since time.Time) ([]*models.LoginAttempt, error) {
	args := m.Called(ctx, ipAddress, since)
	return args.Get(0).([]*models.LoginAttempt), args.Error(1)
}

func (m *MockLoginAttemptRepository) CleanupOldAttempts(ctx context.Context, before time.Time) error {
	args := m.Called(ctx, before)
	return args.Error(0)
}

func (m *MockLoginAttemptRepository) GetAttemptsByUserAndIP(ctx context.Context, userID uint, ipAddress string, since time.Time) ([]*models.LoginAttempt, error) {
	args := m.Called(ctx, userID, ipAddress, since)
	return args.Get(0).([]*models.LoginAttempt), args.Error(1)
}

func (m *MockLoginAttemptRepository) CountFailedAttempts(ctx context.Context, userID uint, since time.Time) (int, error) {
	args := m.Called(ctx, userID, since)
	return args.Int(0), args.Error(1)
}

func (m *MockLoginAttemptRepository) CountFailedAttemptsByIP(ctx context.Context, ipAddress string, since time.Time) (int, error) {
	args := m.Called(ctx, ipAddress, since)
	return args.Int(0), args.Error(1)
}

func (m *MockLoginAttemptRepository) GetLastSuccessfulAttempt(ctx context.Context, userID uint) (*models.LoginAttempt, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.LoginAttempt), args.Error(1)
}

func (m *MockLoginAttemptRepository) GetAttemptStatistics(ctx context.Context, days int) (map[string]interface{}, error) {
	args := m.Called(ctx, days)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

// MockJWTService mocks the JWT service
type MockJWTService struct {
	mock.Mock
}

func (m *MockJWTService) GenerateTokenPair(claims *models.JWTClaims) (string, string, error) {
	args := m.Called(claims)
	return args.String(0), args.String(1), args.Error(2)
}

func (m *MockJWTService) ValidateAccessToken(tokenString string) (*models.JWTClaims, error) {
	args := m.Called(tokenString)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.JWTClaims), args.Error(1)
}

func (m *MockJWTService) ValidateRefreshToken(tokenString string) (*models.JWTClaims, error) {
	args := m.Called(tokenString)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.JWTClaims), args.Error(1)
}

func (m *MockJWTService) RefreshAccessToken(refreshToken string) (string, error) {
	args := m.Called(refreshToken)
	return args.String(0), args.Error(1)
}

func (m *MockJWTService) InvalidateToken(tokenString string) error {
	args := m.Called(tokenString)
	return args.Error(0)
}

func (m *MockJWTService) GetClaims(tokenString string) (*models.JWTClaims, error) {
	args := m.Called(tokenString)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.JWTClaims), args.Error(1)
}

func (m *MockJWTService) IsTokenBlacklisted(tokenString string) bool {
	args := m.Called(tokenString)
	return args.Bool(0)
}

func (m *MockJWTService) BlacklistToken(tokenString string, expiry time.Time) error {
	args := m.Called(tokenString, expiry)
	return args.Error(0)
}

func (m *MockJWTService) CleanupExpiredTokens() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockJWTService) GetTokenExpiry(tokenString string) (time.Time, error) {
	args := m.Called(tokenString)
	return args.Get(0).(time.Time), args.Error(1)
}

// MockPasswordService mocks the password service
type MockPasswordService struct {
	mock.Mock
}

func (m *MockPasswordService) HashPassword(password string) (string, error) {
	args := m.Called(password)
	return args.String(0), args.Error(1)
}

func (m *MockPasswordService) VerifyPassword(hashedPassword, password string) (bool, error) {
	args := m.Called(hashedPassword, password)
	return args.Bool(0), args.Error(1)
}

func (m *MockPasswordService) ValidatePassword(password string) error {
	args := m.Called(password)
	return args.Error(0)
}

func (m *MockPasswordService) GenerateRandomPassword(length int) (string, error) {
	args := m.Called(length)
	return args.String(0), args.Error(1)
}

func (m *MockPasswordService) CheckPasswordStrength(password string) (int, error) {
	args := m.Called(password)
	return args.Int(0), args.Error(1)
}

func (m *MockPasswordService) IsPasswordCompromised(password string) (bool, error) {
	args := m.Called(password)
	return args.Bool(0), args.Error(1)
}

func (m *MockPasswordService) GetPasswordRequirements() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

// MockRateLimitingService mocks the rate limiting service
type MockRateLimitingService struct {
	mock.Mock
}

func (m *MockRateLimitingService) CheckLoginAttempt(ctx context.Context, userID uint, ipAddress string) (bool, error) {
	args := m.Called(ctx, userID, ipAddress)
	return args.Bool(0), args.Error(1)
}

func (m *MockRateLimitingService) RecordFailedLoginAttempt(ctx context.Context, userID uint, ipAddress string) error {
	args := m.Called(ctx, userID, ipAddress)
	return args.Error(0)
}

func (m *MockRateLimitingService) RecordSuccessfulLoginAttempt(ctx context.Context, userID uint, ipAddress string) error {
	args := m.Called(ctx, userID, ipAddress)
	return args.Error(0)
}

func (m *MockRateLimitingService) IsAccountLocked(ctx context.Context, userID uint) (bool, time.Time, error) {
	args := m.Called(ctx, userID)
	return args.Bool(0), args.Get(1).(time.Time), args.Error(2)
}

func (m *MockRateLimitingService) IsIPBlocked(ctx context.Context, ipAddress string) (bool, time.Time, error) {
	args := m.Called(ctx, ipAddress)
	return args.Bool(0), args.Get(1).(time.Time), args.Error(2)
}

func (m *MockRateLimitingService) UnlockAccount(ctx context.Context, userID uint) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockRateLimitingService) UnblockIP(ctx context.Context, ipAddress string) error {
	args := m.Called(ctx, ipAddress)
	return args.Error(0)
}

func (m *MockRateLimitingService) GetFailedAttemptCount(ctx context.Context, userID uint) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockRateLimitingService) GetIPAttemptCount(ctx context.Context, ipAddress string) (int, error) {
	args := m.Called(ctx, ipAddress)
	return args.Int(0), args.Error(1)
}

func (m *MockRateLimitingService) CleanupExpiredAttempts(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// MockTenantService mocks the tenant service
type MockTenantService struct {
	mock.Mock
}

func (m *MockTenantService) CreateTenant(ctx context.Context, req interface{}) (*tenantModels.Tenant, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*tenantModels.Tenant), args.Error(1)
}

func (m *MockTenantService) GetTenantByID(ctx context.Context, id uint) (*tenantModels.Tenant, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*tenantModels.Tenant), args.Error(1)
}

func (m *MockTenantService) GetTenantByDomain(ctx context.Context, domain string) (*tenantModels.Tenant, error) {
	args := m.Called(ctx, domain)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*tenantModels.Tenant), args.Error(1)
}

func (m *MockTenantService) UpdateTenant(ctx context.Context, id uint, req interface{}) (*tenantModels.Tenant, error) {
	args := m.Called(ctx, id, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*tenantModels.Tenant), args.Error(1)
}

func (m *MockTenantService) DeleteTenant(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTenantService) ListTenants(ctx context.Context, req interface{}) (interface{}, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0), args.Error(1)
}

func (m *MockTenantService) UpdateTenantStatus(ctx context.Context, id uint, status tenantModels.TenantStatus) error {
	args := m.Called(ctx, id, status)
	return args.Error(0)
}

func (m *MockTenantService) GetTenantStats(ctx context.Context, id uint) (interface{}, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0), args.Error(1)
}

func (m *MockTenantService) AssignPlan(ctx context.Context, tenantID uint, planID uint) error {
	args := m.Called(ctx, tenantID, planID)
	return args.Error(0)
}

func (m *MockTenantService) ValidateDomain(domain string) error {
	args := m.Called(domain)
	return args.Error(0)
}

func (m *MockTenantService) CheckDomainAvailability(ctx context.Context, domain string) (bool, error) {
	args := m.Called(ctx, domain)
	return args.Bool(0), args.Error(1)
}

func (m *MockTenantService) GetTenantsByPlan(ctx context.Context, planID uint) ([]*tenantModels.Tenant, error) {
	args := m.Called(ctx, planID)
	return args.Get(0).([]*tenantModels.Tenant), args.Error(1)
}

func (m *MockTenantService) GetTenantConfiguration(ctx context.Context, tenantID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockTenantService) UpdateTenantConfiguration(ctx context.Context, tenantID uint, config map[string]interface{}) error {
	args := m.Called(ctx, tenantID, config)
	return args.Error(0)
}

func (m *MockTenantService) GetTenantUsage(ctx context.Context, tenantID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockTenantService) GetTenantBySubdomain(ctx context.Context, subdomain string) (*tenantModels.Tenant, error) {
	args := m.Called(ctx, subdomain)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*tenantModels.Tenant), args.Error(1)
}

// MockTenantMembershipRepository mocks the tenant membership repository
type MockTenantMembershipRepository struct {
	mock.Mock
}

func (m *MockTenantMembershipRepository) Create(ctx context.Context, membership *userModels.TenantMembership) error {
	args := m.Called(ctx, membership)
	return args.Error(0)
}

func (m *MockTenantMembershipRepository) GetByID(ctx context.Context, id uint) (*userModels.TenantMembership, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) GetByUserAndTenant(ctx context.Context, userID, tenantID uint) (*userModels.TenantMembership, error) {
	args := m.Called(ctx, userID, tenantID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) GetByUserID(ctx context.Context, userID uint) ([]*userModels.TenantMembership, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) GetByTenantID(ctx context.Context, tenantID uint) ([]*userModels.TenantMembership, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).([]*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) Update(ctx context.Context, membership *userModels.TenantMembership) error {
	args := m.Called(ctx, membership)
	return args.Error(0)
}

func (m *MockTenantMembershipRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTenantMembershipRepository) UserBelongsToTenant(ctx context.Context, userID, tenantID uint) (bool, error) {
	args := m.Called(ctx, userID, tenantID)
	return args.Bool(0), args.Error(1)
}

func (m *MockTenantMembershipRepository) GetPrimaryTenantForUser(ctx context.Context, userID uint) (*userModels.TenantMembership, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) SetPrimaryTenant(ctx context.Context, userID, tenantID uint) error {
	args := m.Called(ctx, userID, tenantID)
	return args.Error(0)
}

func (m *MockTenantMembershipRepository) GetMembershipsByRole(ctx context.Context, tenantID uint, role string) ([]*userModels.TenantMembership, error) {
	args := m.Called(ctx, tenantID, role)
	return args.Get(0).([]*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) UpdateRole(ctx context.Context, membershipID uint, role string) error {
	args := m.Called(ctx, membershipID, role)
	return args.Error(0)
}

func (m *MockTenantMembershipRepository) UpdateStatus(ctx context.Context, membershipID uint, status interface{}) error {
	args := m.Called(ctx, membershipID, status)
	return args.Error(0)
}

func (m *MockTenantMembershipRepository) GetActiveMemberships(ctx context.Context, tenantID uint) ([]*userModels.TenantMembership, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).([]*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) GetMembershipStats(ctx context.Context, tenantID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockTenantMembershipRepository) BulkUpdateStatus(ctx context.Context, membershipIDs []uint, status interface{}) error {
	args := m.Called(ctx, membershipIDs, status)
	return args.Error(0)
}

func (m *MockTenantMembershipRepository) GetMembershipHistory(ctx context.Context, userID uint) ([]*userModels.TenantMembership, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) SearchMemberships(ctx context.Context, tenantID uint, query string, limit, offset int) ([]*userModels.TenantMembership, int, error) {
	args := m.Called(ctx, tenantID, query, limit, offset)
	return args.Get(0).([]*userModels.TenantMembership), args.Int(1), args.Error(2)
}

func (m *MockTenantMembershipRepository) GetMembershipsByDateRange(ctx context.Context, tenantID uint, startDate, endDate time.Time) ([]*userModels.TenantMembership, error) {
	args := m.Called(ctx, tenantID, startDate, endDate)
	return args.Get(0).([]*userModels.TenantMembership), args.Error(1)
}

func (m *MockTenantMembershipRepository) GetUserTenantCount(ctx context.Context, userID uint) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockTenantMembershipRepository) GetTenantMemberCount(ctx context.Context, tenantID uint) (int, error) {
	args := m.Called(ctx, tenantID)
	return args.Int(0), args.Error(1)
}

// MockOnboardingIntegrationService mocks the onboarding integration service
type MockOnboardingIntegrationService struct {
	mock.Mock
}

func (m *MockOnboardingIntegrationService) CreateUserOnboardingJourney(ctx context.Context, userID, tenantID uint) (interface{}, error) {
	args := m.Called(ctx, userID, tenantID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0), args.Error(1)
}

func (m *MockOnboardingIntegrationService) CompleteOnboardingStep(ctx context.Context, userID, tenantID uint, stepKey string) error {
	args := m.Called(ctx, userID, tenantID, stepKey)
	return args.Error(0)
}

func (m *MockOnboardingIntegrationService) GetUserOnboardingProgress(ctx context.Context, userID, tenantID uint) (interface{}, error) {
	args := m.Called(ctx, userID, tenantID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0), args.Error(1)
}

func (m *MockOnboardingIntegrationService) StartOnboardingFlow(ctx context.Context, userID, tenantID uint, flowType string) error {
	args := m.Called(ctx, userID, tenantID, flowType)
	return args.Error(0)
}

func (m *MockOnboardingIntegrationService) GetOnboardingAnalytics(ctx context.Context, tenantID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockOnboardingIntegrationService) GetRecommendedSteps(ctx context.Context, userID, tenantID uint) (interface{}, error) {
	args := m.Called(ctx, userID, tenantID)
	return args.Get(0), args.Error(1)
}

func (m *MockOnboardingIntegrationService) SkipOnboardingStep(ctx context.Context, userID, tenantID uint, stepKey string, reason string) error {
	args := m.Called(ctx, userID, tenantID, stepKey, reason)
	return args.Error(0)
}

func (m *MockOnboardingIntegrationService) ResetOnboardingProgress(ctx context.Context, userID, tenantID uint) error {
	args := m.Called(ctx, userID, tenantID)
	return args.Error(0)
}

func (m *MockOnboardingIntegrationService) GetOnboardingTemplates(ctx context.Context, tenantID uint) (interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0), args.Error(1)
}

func (m *MockOnboardingIntegrationService) ApplyOnboardingTemplate(ctx context.Context, userID, tenantID uint, templateID uint) error {
	args := m.Called(ctx, userID, tenantID, templateID)
	return args.Error(0)
}

// MockNotificationService mocks the notification service
type MockNotificationService struct {
	mock.Mock
}

func (m *MockNotificationService) SendNotification(ctx context.Context, req interface{}) (interface{}, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0), args.Error(1)
}

func (m *MockNotificationService) GetNotificationHistory(ctx context.Context, userID uint, limit, offset int) ([]*notificationModels.Notification, int, error) {
	args := m.Called(ctx, userID, limit, offset)
	return args.Get(0).([]*notificationModels.Notification), args.Int(1), args.Error(2)
}

func (m *MockNotificationService) MarkAsRead(ctx context.Context, notificationID uint) error {
	args := m.Called(ctx, notificationID)
	return args.Error(0)
}

func (m *MockNotificationService) GetUnreadCount(ctx context.Context, userID uint) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockNotificationService) GetNotificationPreferences(ctx context.Context, userID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockNotificationService) UpdateNotificationPreferences(ctx context.Context, userID uint, preferences map[string]interface{}) error {
	args := m.Called(ctx, userID, preferences)
	return args.Error(0)
}

func (m *MockNotificationService) SendBulkNotification(ctx context.Context, req interface{}) error {
	args := m.Called(ctx, req)
	return args.Error(0)
}

func (m *MockNotificationService) GetNotificationTemplates(ctx context.Context, tenantID uint) (interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0), args.Error(1)
}

func (m *MockNotificationService) GetNotificationStats(ctx context.Context, tenantID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockNotificationService) DeleteNotification(ctx context.Context, notificationID uint) error {
	args := m.Called(ctx, notificationID)
	return args.Error(0)
}

// MockEmailVerificationService mocks the email verification service
type MockEmailVerificationService struct {
	mock.Mock
}

func (m *MockEmailVerificationService) SendVerificationEmail(ctx context.Context, userID uint, email string) error {
	args := m.Called(ctx, userID, email)
	return args.Error(0)
}

func (m *MockEmailVerificationService) VerifyEmail(ctx context.Context, token string) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockEmailVerificationService) ResendVerificationEmail(ctx context.Context, email string) error {
	args := m.Called(ctx, email)
	return args.Error(0)
}

func (m *MockEmailVerificationService) GetVerificationStatus(ctx context.Context, email string) (bool, error) {
	args := m.Called(ctx, email)
	return args.Bool(0), args.Error(1)
}

func (m *MockEmailVerificationService) GetVerificationTokenStats(ctx context.Context, userID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockEmailVerificationService) CleanupExpiredTokens(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockEmailVerificationService) IsVerificationRequired(ctx context.Context, userID uint) (bool, error) {
	args := m.Called(ctx, userID)
	return args.Bool(0), args.Error(1)
}

func (m *MockEmailVerificationService) GetVerificationHistory(ctx context.Context, userID uint) ([]map[string]interface{}, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]map[string]interface{}), args.Error(1)
}

func (m *MockEmailVerificationService) UpdateVerificationSettings(ctx context.Context, userID uint, settings map[string]interface{}) error {
	args := m.Called(ctx, userID, settings)
	return args.Error(0)
}

func (m *MockEmailVerificationService) GetVerificationMetrics(ctx context.Context, tenantID uint) (map[string]interface{}, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}
