package tests

import (
	"context"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

func TestRegistrationSimple(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Registration Simple Suite")
}

var _ = BeforeSuite(func() {
	// Setup test database
	var err error
	db, err = helpers.SetupTestDatabase()
	Expect(err).NotTo(HaveOccurred())

	// Run migrations
	err = helpers.RunMigrations(db)
	Expect(err).NotTo(HaveOccurred())

	// Initialize test helper
	testHelper = helpers.NewTestHelper(db)

	// Initialize logger
	logger = utils.NewLogger("test")
})

var _ = AfterSuite(func() {
	// Cleanup
	if db != nil {
		sqlDB, _ := db.DB()
		sqlDB.Close()
	}
})

// Reset database between tests
var _ = BeforeEach(func() {
	testHelper.CleanDatabase()
	testHelper.SeedBasicData()
})

var _ = Describe("Registration Service Integration", func() {
	var (
		userRepo    *mysql.UserRepository
		sessionRepo *mysql.SessionRepository
		loginRepo   *mysql.LoginAttemptRepository
		authService services.AuthService
		tenant      *tenantModels.Tenant
		ctx         context.Context
	)

	BeforeEach(func() {
		ctx = context.Background()

		// Create test tenant
		tenant = testHelper.CreateTenant("Test Tenant")

		// Initialize repositories
		userRepo = mysql.NewUserRepository(db)
		sessionRepo = mysql.NewSessionRepository(db)
		loginRepo = mysql.NewLoginAttemptRepository(db)

		// Initialize services
		jwtService := services.NewJWTService(&services.JWTConfig{
			SecretKey:       "test-secret-key-32-bytes-long-for-jwt",
			AccessTokenTTL:  time.Hour,
			RefreshTokenTTL: time.Hour * 24 * 7,
			Issuer:          "test-issuer",
		}, nil)

		passwordService := services.NewPasswordService(&services.PasswordConfig{
			MinLength:        8,
			RequireUppercase: true,
			RequireLowercase: true,
			RequireNumbers:   true,
			RequireSymbols:   false,
		})

		emailService := services.NewEmailService(&services.EmailConfig{
			SMTPHost:     "localhost",
			SMTPPort:     587,
			SMTPUsername: "<EMAIL>",
			SMTPPassword: "test",
			FromAddress:  "<EMAIL>",
			FromName:     "Test",
		})

		rateLimiter := services.NewRateLimitingService(loginRepo, &services.RateLimitConfig{
			MaxAttempts:     5,
			LockoutDuration: time.Minute * 15,
			WindowDuration:  time.Minute * 5,
		})

		authService = services.NewAuthService(
			userRepo,
			sessionRepo,
			loginRepo,
			jwtService,
			passwordService,
			rateLimiter,
			emailService,
			logger,
			&services.AuthConfig{
				RequireEmailVerification: false,
				AllowRegistration:        true,
				MaxLoginAttempts:         5,
				LockoutDuration:          time.Minute * 15,
				SessionTimeout:           time.Hour * 24,
				RefreshTokenTTL:          time.Hour * 24 * 7,
				TwoFactorIssuer:          "test-app",
			},
		)
	})

	Describe("User Registration", func() {
		It("should register a new user successfully", func() {
			// Arrange
			request := &services.RegisterRequest{
				TenantID:  tenant.ID,
				WebsiteID: 1,
				Email:     "<EMAIL>",
				Username:  "testuser",
				Password:  "SecurePass123",
				FirstName: "Test",
				LastName:  "User",
				IPAddress: "127.0.0.1",
				UserAgent: "Test Browser",
			}

			// Act
			response, err := authService.Register(ctx, request)

			// Assert
			Expect(err).NotTo(HaveOccurred())
			Expect(response).NotTo(BeNil())
			Expect(response.AccessToken).NotTo(BeEmpty())
			Expect(response.RefreshToken).NotTo(BeEmpty())
			Expect(response.TokenType).To(Equal("Bearer"))
			Expect(response.SessionID).NotTo(BeZero())

			// Verify user was created in database
			var user models.User
			err = db.Where("email = ?", "<EMAIL>").First(&user).Error
			Expect(err).NotTo(HaveOccurred())
			Expect(user.TenantID).To(Equal(tenant.ID))
			Expect(user.Status).To(Equal(models.UserStatusActive))
			Expect(user.EmailVerified).To(BeTrue())
			Expect(*user.Username).To(Equal("testuser"))
			Expect(*user.FirstName).To(Equal("Test"))
			Expect(*user.LastName).To(Equal("User"))

			// Verify session was created
			var session models.Session
			err = db.Where("id = ?", response.SessionID).First(&session).Error
			Expect(err).NotTo(HaveOccurred())
			Expect(session.UserID).To(Equal(user.ID))
			Expect(session.IsRevoked).To(BeFalse())
			Expect(session.IsExpired()).To(BeFalse())
		})

		It("should register user with minimal required fields", func() {
			// Arrange
			request := &services.RegisterRequest{
				TenantID:  tenant.ID,
				WebsiteID: 1,
				Email:     "<EMAIL>",
				Password:  "SecurePass123",
				IPAddress: "127.0.0.1",
				UserAgent: "Test Browser",
			}

			// Act
			response, err := authService.Register(ctx, request)

			// Assert
			Expect(err).NotTo(HaveOccurred())
			Expect(response).NotTo(BeNil())

			// Verify user was created in database
			var user models.User
			err = db.Where("email = ?", "<EMAIL>").First(&user).Error
			Expect(err).NotTo(HaveOccurred())
			Expect(user.Username).To(BeNil())
			Expect(user.FirstName).To(BeNil())
			Expect(user.LastName).To(BeNil())
		})

		It("should return error for duplicate email", func() {
			// Arrange - create existing user
			existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
			Expect(existingUser).NotTo(BeNil())

			request := &services.RegisterRequest{
				TenantID:  tenant.ID,
				WebsiteID: 1,
				Email:     "<EMAIL>",
				Password:  "SecurePass123",
				IPAddress: "127.0.0.1",
				UserAgent: "Test Browser",
			}

			// Act
			response, err := authService.Register(ctx, request)

			// Assert
			Expect(err).To(HaveOccurred())
			Expect(err).To(Equal(services.ErrEmailAlreadyExists))
			Expect(response).To(BeNil())
		})

		It("should return error for weak password", func() {
			// Arrange
			request := &services.RegisterRequest{
				TenantID:  tenant.ID,
				WebsiteID: 1,
				Email:     "<EMAIL>",
				Password:  "weak",
				IPAddress: "127.0.0.1",
				UserAgent: "Test Browser",
			}

			// Act
			response, err := authService.Register(ctx, request)

			// Assert
			Expect(err).To(HaveOccurred())
			Expect(err).To(Equal(services.ErrWeakPassword))
			Expect(response).To(BeNil())
		})

		It("should login successfully after registration", func() {
			// Arrange & Act - register user
			registerRequest := &services.RegisterRequest{
				TenantID:  tenant.ID,
				WebsiteID: 1,
				Email:     "<EMAIL>",
				Password:  "SecurePass123",
				IPAddress: "127.0.0.1",
				UserAgent: "Test Browser",
			}

			registerResponse, err := authService.Register(ctx, registerRequest)
			Expect(err).NotTo(HaveOccurred())
			Expect(registerResponse).NotTo(BeNil())

			// Act - login with same credentials
			loginRequest := &services.LoginRequest{
				Email:     "<EMAIL>",
				Password:  "SecurePass123",
				WebsiteID: 1,
				IPAddress: "127.0.0.1",
				UserAgent: "Test Browser",
			}

			loginResponse, err := authService.Login(ctx, loginRequest)

			// Assert
			Expect(err).NotTo(HaveOccurred())
			Expect(loginResponse).NotTo(BeNil())
			Expect(loginResponse.AccessToken).NotTo(BeEmpty())
			Expect(loginResponse.RefreshToken).NotTo(BeEmpty())
		})
	})
})
