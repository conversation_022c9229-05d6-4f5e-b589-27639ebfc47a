#!/bin/bash

# Auth Module Test Runner Script
# This script runs all unit tests for the auth module with coverage reporting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test configuration
AUTH_MODULE_PATH="./internal/modules/auth"
COVERAGE_FILE="auth_coverage.out"
COVERAGE_HTML="auth_coverage.html"
MIN_COVERAGE=80

echo -e "${GREEN}🧪 Running Auth Module Tests${NC}"
echo "=================================================="

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to run tests with coverage
run_tests() {
    local test_path=$1
    local test_name=$2
    
    echo -e "\n${YELLOW}Running $test_name tests...${NC}"
    
    if ! go test -v -race -coverprofile="$test_name.out" "$test_path"; then
        print_error "$test_name tests failed"
        return 1
    fi
    
    print_status "$test_name tests passed"
    return 0
}

# Function to merge coverage files
merge_coverage() {
    echo -e "\n${YELLOW}Merging coverage files...${NC}"
    
    # Find all coverage files
    coverage_files=$(find . -name "*.out" -type f | grep -E "(repositories|services|handlers|models|middleware)\.out$")
    
    if [ -z "$coverage_files" ]; then
        print_warning "No coverage files found"
        return 1
    fi
    
    # Create merged coverage file
    echo "mode: atomic" > "$COVERAGE_FILE"
    
    for file in $coverage_files; do
        if [ -f "$file" ]; then
            # Skip the first line (mode line) and append to merged file
            tail -n +2 "$file" >> "$COVERAGE_FILE"
            rm "$file"
        fi
    done
    
    print_status "Coverage files merged into $COVERAGE_FILE"
}

# Function to generate coverage report
generate_coverage_report() {
    echo -e "\n${YELLOW}Generating coverage report...${NC}"
    
    if [ ! -f "$COVERAGE_FILE" ]; then
        print_error "Coverage file not found: $COVERAGE_FILE"
        return 1
    fi
    
    # Calculate coverage percentage
    coverage_pct=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    if [ -z "$coverage_pct" ]; then
        print_error "Failed to calculate coverage percentage"
        return 1
    fi
    
    echo -e "\n${GREEN}📊 Coverage Summary${NC}"
    echo "=================================================="
    go tool cover -func="$COVERAGE_FILE" | grep -E "(total:|func)"
    
    # Generate HTML report
    go tool cover -html="$COVERAGE_FILE" -o "$COVERAGE_HTML"
    print_status "HTML coverage report generated: $COVERAGE_HTML"
    
    # Check if coverage meets minimum requirement
    if (( $(echo "$coverage_pct >= $MIN_COVERAGE" | bc -l) )); then
        print_status "Coverage $coverage_pct% meets minimum requirement of $MIN_COVERAGE%"
    else
        print_error "Coverage $coverage_pct% below minimum requirement of $MIN_COVERAGE%"
        return 1
    fi
    
    echo -e "\n${GREEN}🎉 Coverage: $coverage_pct%${NC}"
}

# Function to run integration tests
run_integration_tests() {
    echo -e "\n${YELLOW}Running integration tests...${NC}"
    
    # Set environment variable to enable integration tests
    export INTEGRATION_TESTS=true
    
    if ! go test -v -race "$AUTH_MODULE_PATH/tests" -run "Integration"; then
        print_error "Integration tests failed"
        unset INTEGRATION_TESTS
        return 1
    fi
    
    unset INTEGRATION_TESTS
    print_status "Integration tests passed"
}

# Function to clean up
cleanup() {
    echo -e "\n${YELLOW}Cleaning up...${NC}"
    
    # Remove temporary files
    find . -name "*.out" -type f -delete 2>/dev/null || true
    
    print_status "Cleanup completed"
}

# Main execution
main() {
    echo "Starting test execution..."
    
    # Change to the auth module directory
    cd "$AUTH_MODULE_PATH" || {
        print_error "Failed to change to auth module directory: $AUTH_MODULE_PATH"
        exit 1
    }
    
    local exit_code=0
    
    # Run unit tests
    echo -e "\n${GREEN}🔧 Running Unit Tests${NC}"
    echo "=================================================="
    
    # Run tests for each component
    run_tests "./tests/unit/repositories" "repositories" || exit_code=1
    run_tests "./tests/unit/services" "services" || exit_code=1
    run_tests "./tests/unit/handlers" "handlers" || exit_code=1
    run_tests "./tests/unit/models" "models" || exit_code=1
    run_tests "./tests/unit/middleware" "middleware" || exit_code=1
    
    # Merge coverage files and generate report
    if [ $exit_code -eq 0 ]; then
        merge_coverage || exit_code=1
        generate_coverage_report || exit_code=1
    fi
    
    # Run integration tests if requested
    if [ "${RUN_INTEGRATION:-false}" = "true" ]; then
        run_integration_tests || exit_code=1
    fi
    
    # Final status
    echo -e "\n=================================================="
    if [ $exit_code -eq 0 ]; then
        print_status "All auth module tests completed successfully!"
        echo -e "${GREEN}📈 View detailed coverage report: $COVERAGE_HTML${NC}"
    else
        print_error "Some tests failed. Check the output above for details."
    fi
    
    return $exit_code
}

# Handle script arguments
case "${1:-}" in
    "clean")
        cleanup
        ;;
    "integration")
        export RUN_INTEGRATION=true
        main
        ;;
    "coverage-only")
        generate_coverage_report
        ;;
    *)
        main
        ;;
esac

exit $?