package unit

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/mocks"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// MockValidator for testing
type MockValidator struct {
	mock.Mock
}

// Define missing validator types locally for testing
type Func func(validator.FieldLevel) bool
type CustomTypeFunc func(field reflect.Value) interface{}
type StructLevelFunc func(validator.StructLevel)
type TranslationFunc func(ut.Translator, validator.FieldError) string
type TagNameFunc func(fld reflect.StructField) string
type Translator interface{}
type FieldLevel interface{}
type StructLevel interface{}

func (m *MockValidator) Validate(ctx context.Context, s interface{}) error {
	args := m.Called(ctx, s)
	return args.Error(0)
}

func (m *MockValidator) ValidateStruct(s interface{}) error {
	args := m.Called(s)
	return args.Error(0)
}

func (m *MockValidator) ValidateVar(field interface{}, tag string) error {
	args := m.Called(field, tag)
	return args.Error(0)
}

func (m *MockValidator) RegisterValidation(tag string, fn Func) error {
	args := m.Called(tag, fn)
	return args.Error(0)
}

func (m *MockValidator) RegisterCustomTypeFunc(fn CustomTypeFunc, types ...interface{}) {
	m.Called(fn, types)
}

func (m *MockValidator) RegisterAlias(alias, tags string) {
	m.Called(alias, tags)
}

func (m *MockValidator) RegisterStructValidation(fn StructLevelFunc, types ...interface{}) {
	m.Called(fn, types)
}

func (m *MockValidator) RegisterTagNameFunc(fn TagNameFunc) {
	m.Called(fn)
}

func (m *MockValidator) RegisterTranslation(tag string, trans Translator, fn TranslationFunc) error {
	args := m.Called(tag, trans, fn)
	return args.Error(0)
}

func (m *MockValidator) Engine() interface{} {
	args := m.Called()
	return args.Get(0)
}

// Test suite for AuthHandler
type AuthHandlerTestSuite struct {
	handler                  *handlers.AuthHandler
	mockAuthService          *MockAuthService
	mockJWTService           *mocks.MockJWTService
	mockPasswordService      *mocks.MockPasswordService
	mockTenantMembershipRepo *mocks.MockTenantMembershipRepository
	mockValidator            *MockValidator
	mockLogger               *MockLogger
	router                   *gin.Engine
}

// AuthResponse represents a simplified auth response for testing
type AuthResponse struct {
	User                      *userModels.User
	AccessToken               string
	RefreshToken              string
	TokenType                 string
	ExpiresIn                 int64
	SessionID                 uint
	RequiresTwoFactor         bool
	RequiresEmailVerification bool
	Message                   string
}

// TwoFactorSetupResponse represents two factor setup response for testing
type TwoFactorSetupResponse struct {
	Secret string
	QRCode string
}

// MockAuthService for handler tests
type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) Login(ctx context.Context, req interface{}) (*AuthResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*AuthResponse), args.Error(1)
}

func (m *MockAuthService) Logout(ctx context.Context, userID uint, sessionID uint) error {
	args := m.Called(ctx, userID, sessionID)
	return args.Error(0)
}

func (m *MockAuthService) LogoutAllDevices(ctx context.Context, userID uint) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockAuthService) RefreshToken(ctx context.Context, refreshToken string) (*AuthResponse, error) {
	args := m.Called(ctx, refreshToken)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*AuthResponse), args.Error(1)
}

func (m *MockAuthService) Register(ctx context.Context, req interface{}) (*AuthResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*AuthResponse), args.Error(1)
}

func (m *MockAuthService) VerifyEmail(ctx context.Context, token string) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockAuthService) ResendVerificationEmail(ctx context.Context, email string) error {
	args := m.Called(ctx, email)
	return args.Error(0)
}

func (m *MockAuthService) GetActiveSessions(ctx context.Context, userID uint) ([]*models.Session, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*models.Session), args.Error(1)
}

func (m *MockAuthService) RevokeSession(ctx context.Context, userID uint, sessionID uint) error {
	args := m.Called(ctx, userID, sessionID)
	return args.Error(0)
}

func (m *MockAuthService) EnableTwoFactor(ctx context.Context, userID uint) (*TwoFactorSetupResponse, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*TwoFactorSetupResponse), args.Error(1)
}

func (m *MockAuthService) DisableTwoFactor(ctx context.Context, userID uint, code string) error {
	args := m.Called(ctx, userID, code)
	return args.Error(0)
}

func (m *MockAuthService) VerifyTwoFactor(ctx context.Context, userID uint, code string) error {
	args := m.Called(ctx, userID, code)
	return args.Error(0)
}

func setupAuthHandlerTest() *AuthHandlerTestSuite {
	gin.SetMode(gin.TestMode)

	suite := &AuthHandlerTestSuite{
		mockAuthService:          &MockAuthService{},
		mockJWTService:           &mocks.MockJWTService{},
		mockPasswordService:      &mocks.MockPasswordService{},
		mockTenantMembershipRepo: &mocks.MockTenantMembershipRepository{},
		mockValidator:            &MockValidator{},
		mockLogger:               &MockLogger{},
		router:                   gin.New(),
	}

	// Create handler
	suite.handler = handlers.NewAuthHandler(
		suite.mockAuthService,
		suite.mockJWTService,
		suite.mockPasswordService,
		suite.mockTenantMembershipRepo,
		suite.mockValidator,
		suite.mockLogger,
	)

	return suite
}

func TestAuthHandler_Login_Success(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	loginRequest := dto.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	user := &userModels.User{
		ID:        1,
		Email:     "<EMAIL>",
		FirstName: "John",
		LastName:  "Doe",
		Status:    userModels.UserStatusActive,
	}

	authResponse := &AuthResponse{
		User:         user,
		AccessToken:  "access_token_123",
		RefreshToken: "refresh_token_123",
		TokenType:    "Bearer",
		ExpiresIn:    3600,
		SessionID:    100,
	}

	// Mock expectations
	suite.mockValidator.On("Validate", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*services.LoginRequest")).Return(nil)
	suite.mockAuthService.On("Login", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*services.LoginRequest")).Return(authResponse, nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	requestBody, _ := json.Marshal(loginRequest)
	req, _ := http.NewRequest("POST", "/auth/login", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute
	suite.handler.Login(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	data := response["data"].(map[string]interface{})
	assert.Equal(t, "access_token_123", data["access_token"])
	assert.Equal(t, "refresh_token_123", data["refresh_token"])
	assert.Equal(t, "Bearer", data["token_type"])
	assert.Equal(t, float64(3600), data["expires_in"])
	assert.Equal(t, float64(100), data["session_id"])

	// Verify mocks
	suite.mockValidator.AssertExpectations(t)
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_Login_InvalidCredentials(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	loginRequest := dto.LoginRequest{
		Email:    "<EMAIL>",
		Password: "wrongpassword",
	}

	// Mock expectations
	suite.mockValidator.On("Validate", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*services.LoginRequest")).Return(nil)
	suite.mockAuthService.On("Login", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*services.LoginRequest")).Return(nil, services.ErrInvalidCredentials)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	requestBody, _ := json.Marshal(loginRequest)
	req, _ := http.NewRequest("POST", "/auth/login", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute
	suite.handler.Login(c)

	// Assertions
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response["success"].(bool))
	errorData := response["error"].(map[string]interface{})
	assert.Equal(t, "Invalid email or password", errorData["message"])

	// Verify mocks
	suite.mockValidator.AssertExpectations(t)
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_Login_ValidationError(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	loginRequest := dto.LoginRequest{
		Email:    "invalid-email",
		Password: "",
	}

	// Mock expectations
	suite.mockValidator.On("Validate", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*services.LoginRequest")).Return(assert.AnError)

	// Create request
	requestBody, _ := json.Marshal(loginRequest)
	req, _ := http.NewRequest("POST", "/auth/login", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute
	suite.handler.Login(c)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Verify mocks
	suite.mockValidator.AssertExpectations(t)
}

func TestAuthHandler_Register_Success(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	registerRequest := dto.RegisterRequest{
		Email:     "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
	}

	user := &userModels.User{
		ID:        1,
		Email:     "<EMAIL>",
		FirstName: "John",
		LastName:  "Doe",
		Status:    userModels.UserStatusPendingVerification,
	}

	authResponse := &AuthResponse{
		User:                      user,
		RequiresEmailVerification: true,
		Message:                   "Registration successful. Please check your email for verification.",
	}

	// Mock expectations
	suite.mockValidator.On("Validate", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*dto.RegisterRequest")).Return(nil)
	suite.mockAuthService.On("Register", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*services.RegisterRequest")).Return(authResponse, nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	requestBody, _ := json.Marshal(registerRequest)
	req, _ := http.NewRequest("POST", "/auth/register", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute
	suite.handler.Register(c)

	// Assertions
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	data := response["data"].(map[string]interface{})
	assert.True(t, data["requires_email_verification"].(bool))
	assert.Equal(t, "Registration successful. Please check your email for verification.", data["message"])

	// Verify mocks
	suite.mockValidator.AssertExpectations(t)
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_Register_EmailAlreadyExists(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	registerRequest := dto.RegisterRequest{
		Email:     "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
	}

	// Mock expectations
	suite.mockValidator.On("Validate", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*dto.RegisterRequest")).Return(nil)
	suite.mockAuthService.On("Register", mock.AnythingOfType("*context.valueCtx"), mock.AnythingOfType("*services.RegisterRequest")).Return(nil, services.ErrEmailAlreadyExists)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	requestBody, _ := json.Marshal(registerRequest)
	req, _ := http.NewRequest("POST", "/auth/register", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute
	suite.handler.Register(c)

	// Assertions
	assert.Equal(t, http.StatusConflict, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response["success"].(bool))
	errorData := response["error"].(map[string]interface{})
	assert.Equal(t, "Email already exists", errorData["message"])

	// Verify mocks
	suite.mockValidator.AssertExpectations(t)
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_Logout_Success(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	userID := uint(1)
	sessionID := uint(100)

	// Mock expectations
	suite.mockAuthService.On("Logout", mock.AnythingOfType("*context.valueCtx"), userID, sessionID).Return(nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	req, _ := http.NewRequest("POST", "/auth/logout", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up context with user ID and JWT claims
	c.Set("user_id", userID)
	c.Set("jwt_claims", &models.JWTClaims{
		UserID:    userID,
		SessionID: &sessionID,
	})

	// Execute
	suite.handler.Logout(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Logged out successfully", data["message"])

	// Verify mocks
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_Logout_MissingUserID(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Create request
	req, _ := http.NewRequest("POST", "/auth/logout", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Don't set user_id in context to simulate missing authentication

	// Execute
	suite.handler.Logout(c)

	// Assertions
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response["success"].(bool))
	errorData := response["error"].(map[string]interface{})
	assert.Equal(t, "Authentication required", errorData["message"])
}

func TestAuthHandler_RefreshToken_Success(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	refreshRequest := map[string]string{
		"refresh_token": "valid_refresh_token",
	}

	user := &userModels.User{
		ID:        1,
		Email:     "<EMAIL>",
		FirstName: "John",
		LastName:  "Doe",
		Status:    userModels.UserStatusActive,
	}

	authResponse := &AuthResponse{
		User:         user,
		AccessToken:  "new_access_token",
		RefreshToken: "new_refresh_token",
		TokenType:    "Bearer",
		ExpiresIn:    3600,
		SessionID:    100,
	}

	// Mock expectations
	suite.mockAuthService.On("RefreshToken", mock.AnythingOfType("*context.valueCtx"), "valid_refresh_token").Return(authResponse, nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	requestBody, _ := json.Marshal(refreshRequest)
	req, _ := http.NewRequest("POST", "/auth/refresh", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute
	suite.handler.RefreshToken(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	data := response["data"].(map[string]interface{})
	assert.Equal(t, "new_access_token", data["access_token"])
	assert.Equal(t, "new_refresh_token", data["refresh_token"])
	assert.Equal(t, "Bearer", data["token_type"])
	assert.Equal(t, float64(3600), data["expires_in"])

	// Verify mocks
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_RefreshToken_InvalidToken(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	refreshRequest := map[string]string{
		"refresh_token": "invalid_refresh_token",
	}

	// Mock expectations
	suite.mockAuthService.On("RefreshToken", mock.AnythingOfType("*context.valueCtx"), "invalid_refresh_token").Return(nil, services.ErrInvalidRefreshToken)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	requestBody, _ := json.Marshal(refreshRequest)
	req, _ := http.NewRequest("POST", "/auth/refresh", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute
	suite.handler.RefreshToken(c)

	// Assertions
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response["success"].(bool))
	errorData := response["error"].(map[string]interface{})
	assert.Equal(t, "Invalid refresh token", errorData["message"])

	// Verify mocks
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_GetProfile_Success(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	userID := uint(1)
	claims := &models.JWTClaims{
		UserID: userID,
		Email:  "<EMAIL>",
	}

	// Create request
	req, _ := http.NewRequest("GET", "/auth/profile", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up context with user ID and JWT claims
	c.Set("user_id", userID)
	c.Set("jwt_claims", claims)

	// Execute
	suite.handler.GetProfile(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	data := response["data"].(map[string]interface{})
	assert.Equal(t, float64(userID), data["user_id"])
	assert.Equal(t, "<EMAIL>", data["email"])
	// No longer expect role, tenant_id, or website_id in user-only JWT structure
}

func TestAuthHandler_GetActiveSessions_Success(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	userID := uint(1)
	sessions := []*models.Session{
		{
			ID:         1,
			UserID:     userID,
			DeviceType: models.DeviceTypeWeb,
			DeviceName: stringPtr("Chrome Browser"),
			IPAddress:  stringPtr("127.0.0.1"),
			LastUsedAt: time.Now(),
			ExpiresAt:  time.Now().Add(24 * time.Hour),
		},
		{
			ID:         2,
			UserID:     userID,
			DeviceType: models.DeviceTypeMobile,
			DeviceName: stringPtr("iPhone"),
			IPAddress:  stringPtr("***********"),
			LastUsedAt: time.Now(),
			ExpiresAt:  time.Now().Add(24 * time.Hour),
		},
	}

	// Mock expectations
	suite.mockAuthService.On("GetActiveSessions", mock.AnythingOfType("*context.valueCtx"), userID).Return(sessions, nil)

	// Create request
	req, _ := http.NewRequest("GET", "/auth/sessions", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up context with user ID
	c.Set("user_id", userID)

	// Execute
	suite.handler.GetActiveSessions(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	data := response["data"].(map[string]interface{})
	sessionsList := data["sessions"].([]interface{})
	assert.Len(t, sessionsList, 2)

	session1 := sessionsList[0].(map[string]interface{})
	assert.Equal(t, float64(1), session1["id"])
	assert.Equal(t, "web", session1["device_type"])
	assert.Equal(t, "Chrome Browser", session1["device_name"])
	assert.Equal(t, "127.0.0.1", session1["ip_address"])

	// Verify mocks
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_RevokeSession_Success(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	userID := uint(1)
	sessionID := uint(100)

	// Mock expectations
	suite.mockAuthService.On("RevokeSession", mock.AnythingOfType("*context.valueCtx"), userID, sessionID).Return(nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	req, _ := http.NewRequest("DELETE", "/auth/sessions/100", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up context with user ID and URL param
	c.Set("user_id", userID)
	c.AddParam("sessionId", "100")

	// Execute
	suite.handler.RevokeSession(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Session revoked successfully", data["message"])

	// Verify mocks
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_RevokeSession_Unauthorized(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	userID := uint(1)
	sessionID := uint(100)

	// Mock expectations
	suite.mockAuthService.On("RevokeSession", mock.AnythingOfType("*context.valueCtx"), userID, sessionID).Return(services.ErrUnauthorized)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	req, _ := http.NewRequest("DELETE", "/auth/sessions/100", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up context with user ID and URL param
	c.Set("user_id", userID)
	c.AddParam("sessionId", "100")

	// Execute
	suite.handler.RevokeSession(c)

	// Assertions
	assert.Equal(t, http.StatusForbidden, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.False(t, response["success"].(bool))
	errorData := response["error"].(map[string]interface{})
	assert.Equal(t, "Unauthorized to revoke this session", errorData["message"])

	// Verify mocks
	suite.mockAuthService.AssertExpectations(t)
}

func TestAuthHandler_LogoutAllDevices_Success(t *testing.T) {
	suite := setupAuthHandlerTest()

	// Test data
	userID := uint(1)

	// Mock expectations
	suite.mockAuthService.On("LogoutAllDevices", mock.AnythingOfType("*context.valueCtx"), userID).Return(nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Create request
	req, _ := http.NewRequest("POST", "/auth/logout-all", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Set up context with user ID
	c.Set("user_id", userID)

	// Execute
	suite.handler.LogoutAllDevices(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.True(t, response["success"].(bool))
	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Logged out from all devices successfully", data["message"])

	// Verify mocks
	suite.mockAuthService.AssertExpectations(t)
}

// Helper function
func stringPtr(s string) *string {
	return &s
}
