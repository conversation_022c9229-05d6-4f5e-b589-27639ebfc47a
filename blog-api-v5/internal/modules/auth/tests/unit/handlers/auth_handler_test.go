package handlers

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/mocks"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type AuthHandlerTestSuite struct {
	suite.Suite
	handler          *handlers.AuthHandler
	mockAuthService  *mocks.MockAuthService
	mockValidator    *mocks.MockValidator
	mockLogger       *mocks.MockLogger
	router           *gin.Engine
}

func (suite *AuthHandlerTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
}

func (suite *AuthHandlerTestSuite) SetupTest() {
	suite.mockAuthService = &mocks.MockAuthService{}
	suite.mockValidator = &mocks.MockValidator{}
	suite.mockLogger = &mocks.MockLogger{}

	suite.handler = handlers.NewAuthHandler(
		suite.mockAuthService,
		suite.mockValidator,
		suite.mockLogger,
	)

	suite.router = gin.New()
	suite.setupRoutes()
}

func (suite *AuthHandlerTestSuite) setupRoutes() {
	api := suite.router.Group("/api/auth")
	{
		api.POST("/login", suite.handler.Login)
		api.POST("/register", suite.handler.Register)
		api.POST("/logout", suite.handler.Logout)
		api.POST("/refresh", suite.handler.RefreshToken)
		api.GET("/profile", suite.handler.GetProfile)
		api.GET("/sessions", suite.handler.GetActiveSessions)
		api.DELETE("/sessions/:id", suite.handler.RevokeSession)
		api.POST("/logout-all", suite.handler.LogoutAllDevices)
	}
}

func (suite *AuthHandlerTestSuite) TestLogin_Success() {
	loginReq := dto.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	expectedResponse := &dto.LoginResponse{
		User: &userModels.User{
			ID:       1,
			Email:    "<EMAIL>",
			Username: "testuser",
			Status:   userModels.UserStatusActive,
		},
		AccessToken:  "access_token",
		RefreshToken: "refresh_token",
		ExpiresIn:    3600,
	}

	// Mock validation
	suite.mockValidator.On("Validate", mock.AnythingOfType("dto.LoginRequest")).Return(nil)

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service
	suite.mockAuthService.On("Login", mock.AnythingOfType("*gin.Context"), mock.MatchedBy(func(req *services.LoginRequest) bool {
		return req.Email == "<EMAIL>" && req.Password == "password123"
	})).Return(expectedResponse, nil)

	// Prepare request
	reqBody, _ := json.Marshal(loginReq)
	req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.True(suite.T(), response.Status.Success)
	assert.NotNil(suite.T(), response.Data)

	suite.mockAuthService.AssertExpectations(suite.T())
	suite.mockValidator.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestLogin_ValidationError() {
	loginReq := dto.LoginRequest{
		Email:    "invalid-email",
		Password: "",
	}

	// Mock validation error
	suite.mockValidator.On("Validate", mock.AnythingOfType("dto.LoginRequest")).Return(errors.New("validation failed"))

	// Mock logger calls
	suite.mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()

	// Prepare request
	reqBody, _ := json.Marshal(loginReq)
	req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.False(suite.T(), response.Status.Success)

	suite.mockValidator.AssertExpectations(suite.T())
	// AuthService should not be called
	suite.mockAuthService.AssertNotCalled(suite.T(), "Login")
}

func (suite *AuthHandlerTestSuite) TestLogin_InvalidCredentials() {
	loginReq := dto.LoginRequest{
		Email:    "<EMAIL>",
		Password: "wrongpassword",
	}

	// Mock validation
	suite.mockValidator.On("Validate", mock.AnythingOfType("dto.LoginRequest")).Return(nil)

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service to return invalid credentials error
	suite.mockAuthService.On("Login", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("*services.LoginRequest")).Return(nil, services.ErrInvalidCredentials)

	// Prepare request
	reqBody, _ := json.Marshal(loginReq)
	req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.False(suite.T(), response.Status.Success)

	suite.mockAuthService.AssertExpectations(suite.T())
	suite.mockValidator.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestLogin_EmailNotVerified() {
	loginReq := dto.LoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
	}

	// Mock validation
	suite.mockValidator.On("Validate", mock.AnythingOfType("dto.LoginRequest")).Return(nil)

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service to return email not verified error
	suite.mockAuthService.On("Login", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("*services.LoginRequest")).Return(nil, services.ErrEmailNotVerified)

	// Prepare request
	reqBody, _ := json.Marshal(loginReq)
	req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusForbidden, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.False(suite.T(), response.Status.Success)

	suite.mockAuthService.AssertExpectations(suite.T())
	suite.mockValidator.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestRegister_Success() {
	registerReq := dto.RegisterRequest{
		Email:     "<EMAIL>",
		Username:  "newuser",
		Password:  "StrongPassword123!",
		FirstName: "New",
		LastName:  "User",
	}

	expectedResponse := &dto.RegisterResponse{
		User: &userModels.User{
			ID:        1,
			Email:     "<EMAIL>",
			Username:  "newuser",
			FirstName: "New",
			LastName:  "User",
			Status:    userModels.UserStatusPendingVerification,
		},
		Message: "Registration successful. Please check your email to verify your account.",
	}

	// Mock validation
	suite.mockValidator.On("Validate", mock.AnythingOfType("dto.RegisterRequest")).Return(nil)

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service
	suite.mockAuthService.On("Register", mock.AnythingOfType("*gin.Context"), mock.MatchedBy(func(req *services.RegisterRequest) bool {
		return req.Email == "<EMAIL>" && req.Username == "newuser"
	})).Return(expectedResponse, nil)

	// Prepare request
	reqBody, _ := json.Marshal(registerReq)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.True(suite.T(), response.Status.Success)
	assert.NotNil(suite.T(), response.Data)

	suite.mockAuthService.AssertExpectations(suite.T())
	suite.mockValidator.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestRegister_EmailAlreadyExists() {
	registerReq := dto.RegisterRequest{
		Email:     "<EMAIL>",
		Username:  "newuser",
		Password:  "StrongPassword123!",
		FirstName: "New",
		LastName:  "User",
	}

	// Mock validation
	suite.mockValidator.On("Validate", mock.AnythingOfType("dto.RegisterRequest")).Return(nil)

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service to return email already exists error
	suite.mockAuthService.On("Register", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("*services.RegisterRequest")).Return(nil, services.ErrEmailAlreadyExists)

	// Prepare request
	reqBody, _ := json.Marshal(registerReq)
	req, _ := http.NewRequest("POST", "/api/auth/register", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusConflict, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.False(suite.T(), response.Status.Success)

	suite.mockAuthService.AssertExpectations(suite.T())
	suite.mockValidator.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestLogout_Success() {
	// Setup context with user ID
	suite.router.Use(func(c *gin.Context) {
		c.Set("user_id", uint(1))
		c.Set("session_id", "session123")
		c.Next()
	})

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service
	suite.mockAuthService.On("Logout", mock.AnythingOfType("*gin.Context"), uint(1), "session123").Return(&dto.LogoutResponse{
		Message: "Logged out successfully",
	}, nil)

	// Prepare request
	req, _ := http.NewRequest("POST", "/api/auth/logout", nil)
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.True(suite.T(), response.Status.Success)

	suite.mockAuthService.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestLogout_MissingUserID() {
	// Don't set user_id in context

	// Mock logger calls
	suite.mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()

	// Prepare request
	req, _ := http.NewRequest("POST", "/api/auth/logout", nil)
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)

	// AuthService should not be called
	suite.mockAuthService.AssertNotCalled(suite.T(), "Logout")
}

func (suite *AuthHandlerTestSuite) TestRefreshToken_Success() {
	refreshReq := dto.RefreshTokenRequest{
		RefreshToken: "valid_refresh_token",
	}

	expectedResponse := &dto.RefreshTokenResponse{
		AccessToken:  "new_access_token",
		RefreshToken: "new_refresh_token",
		ExpiresIn:    3600,
	}

	// Mock validation
	suite.mockValidator.On("Validate", mock.AnythingOfType("dto.RefreshTokenRequest")).Return(nil)

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service
	suite.mockAuthService.On("RefreshToken", mock.AnythingOfType("*gin.Context"), mock.MatchedBy(func(req *services.RefreshTokenRequest) bool {
		return req.RefreshToken == "valid_refresh_token"
	})).Return(expectedResponse, nil)

	// Prepare request
	reqBody, _ := json.Marshal(refreshReq)
	req, _ := http.NewRequest("POST", "/api/auth/refresh", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.True(suite.T(), response.Status.Success)
	assert.NotNil(suite.T(), response.Data)

	suite.mockAuthService.AssertExpectations(suite.T())
	suite.mockValidator.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestRefreshToken_InvalidToken() {
	refreshReq := dto.RefreshTokenRequest{
		RefreshToken: "invalid_refresh_token",
	}

	// Mock validation
	suite.mockValidator.On("Validate", mock.AnythingOfType("dto.RefreshTokenRequest")).Return(nil)

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service to return invalid token error
	suite.mockAuthService.On("RefreshToken", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("*services.RefreshTokenRequest")).Return(nil, services.ErrInvalidRefreshToken)

	// Prepare request
	reqBody, _ := json.Marshal(refreshReq)
	req, _ := http.NewRequest("POST", "/api/auth/refresh", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.False(suite.T(), response.Status.Success)

	suite.mockAuthService.AssertExpectations(suite.T())
	suite.mockValidator.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestGetProfile_Success() {
	// Setup context with user ID
	suite.router.Use(func(c *gin.Context) {
		c.Set("user_id", uint(1))
		c.Next()
	})

	expectedUser := &userModels.User{
		ID:        1,
		Email:     "<EMAIL>",
		Username:  "testuser",
		FirstName: "Test",
		LastName:  "User",
		Status:    userModels.UserStatusActive,
	}

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service
	suite.mockAuthService.On("GetProfile", mock.AnythingOfType("*gin.Context"), uint(1)).Return(&dto.GetProfileResponse{
		User: expectedUser,
	}, nil)

	// Prepare request
	req, _ := http.NewRequest("GET", "/api/auth/profile", nil)
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.True(suite.T(), response.Status.Success)
	assert.NotNil(suite.T(), response.Data)

	suite.mockAuthService.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestGetActiveSessions_Success() {
	// Setup context with user ID
	suite.router.Use(func(c *gin.Context) {
		c.Set("user_id", uint(1))
		c.Next()
	})

	expectedSessions := []models.Session{
		{
			ID:         1,
			UserID:     1,
			DeviceInfo: "Browser 1",
			IPAddress:  "127.0.0.1",
			IsActive:   true,
		},
		{
			ID:         2,
			UserID:     1,
			DeviceInfo: "Browser 2",
			IPAddress:  "***********",
			IsActive:   true,
		},
	}

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service
	suite.mockAuthService.On("GetActiveSessions", mock.AnythingOfType("*gin.Context"), uint(1)).Return(&dto.GetActiveSessionsResponse{
		Sessions: expectedSessions,
	}, nil)

	// Prepare request
	req, _ := http.NewRequest("GET", "/api/auth/sessions", nil)
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.True(suite.T(), response.Status.Success)
	assert.NotNil(suite.T(), response.Data)

	suite.mockAuthService.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestRevokeSession_Success() {
	// Setup context with user ID
	suite.router.Use(func(c *gin.Context) {
		c.Set("user_id", uint(1))
		c.Next()
	})

	sessionID := "session123"

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service
	suite.mockAuthService.On("RevokeSession", mock.AnythingOfType("*gin.Context"), uint(1), sessionID).Return(&dto.RevokeSessionResponse{
		Message: "Session revoked successfully",
	}, nil)

	// Prepare request
	req, _ := http.NewRequest("DELETE", "/api/auth/sessions/"+sessionID, nil)
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.True(suite.T(), response.Status.Success)

	suite.mockAuthService.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestLogoutAllDevices_Success() {
	// Setup context with user ID
	suite.router.Use(func(c *gin.Context) {
		c.Set("user_id", uint(1))
		c.Next()
	})

	// Mock logger calls
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()

	// Mock auth service
	suite.mockAuthService.On("LogoutAllDevices", mock.AnythingOfType("*gin.Context"), uint(1)).Return(&dto.LogoutAllDevicesResponse{
		Message: "Logged out from all devices successfully",
	}, nil)

	// Prepare request
	req, _ := http.NewRequest("POST", "/api/auth/logout-all", nil)
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.True(suite.T(), response.Status.Success)

	suite.mockAuthService.AssertExpectations(suite.T())
}

func (suite *AuthHandlerTestSuite) TestInvalidJSONRequest() {
	// Mock logger calls for invalid JSON
	suite.mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()

	// Prepare invalid JSON request
	req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	
	// Perform request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
	
	var response response.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	
	assert.False(suite.T(), response.Status.Success)

	// Services should not be called
	suite.mockAuthService.AssertNotCalled(suite.T(), "Login")
	suite.mockValidator.AssertNotCalled(suite.T(), "Validate")
}

func TestAuthHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(AuthHandlerTestSuite))
}