package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/middleware"
)

type UserJourneyTracingTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *UserJourneyTracingTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
}

func (suite *UserJourneyTracingTestSuite) SetupTest() {
	suite.router = gin.New()
	suite.router.Use(middleware.UserJourneyTracingMiddleware())
}

func (suite *UserJourneyTracingTestSuite) TestUserJourneyTracingMiddleware_NewJourney() {
	// Setup test route
	suite.router.POST("/auth/register", func(c *gin.Context) {
		// Check that journey context is set
		journeyID := middleware.GetUserJourneyID(c)
		journeyType := middleware.GetUserJourneyType(c)
		journeyStep := middleware.GetUserJourneyStep(c)

		assert.NotEmpty(suite.T(), journeyID)
		assert.Equal(suite.T(), middleware.JourneyTypeRegistration, journeyType)
		assert.Equal(suite.T(), middleware.StepRegister, journeyStep)

		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// Make request
	req, _ := http.NewRequest("POST", "/auth/register", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestUserJourneyTracingMiddleware_ExistingJourney() {
	existingJourneyID := "existing-journey-123"

	// Setup test route
	suite.router.POST("/auth/verify-email", func(c *gin.Context) {
		journeyID := middleware.GetUserJourneyID(c)
		journeyType := middleware.GetUserJourneyType(c)
		journeyStep := middleware.GetUserJourneyStep(c)

		assert.Equal(suite.T(), existingJourneyID, journeyID)
		assert.Equal(suite.T(), middleware.JourneyTypeRegistration, journeyType)
		assert.Equal(suite.T(), middleware.StepEmailVerification, journeyStep)

		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// Make request with existing journey ID
	req, _ := http.NewRequest("POST", "/auth/verify-email", nil)
	req.Header.Set("X-Journey-ID", existingJourneyID)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestUserJourneyTracingMiddleware_OnboardingJourney() {
	// Setup test route for onboarding
	suite.router.POST("/onboarding/create-organization", func(c *gin.Context) {
		journeyID := middleware.GetUserJourneyID(c)
		journeyType := middleware.GetUserJourneyType(c)
		journeyStep := middleware.GetUserJourneyStep(c)

		assert.NotEmpty(suite.T(), journeyID)
		assert.Equal(suite.T(), middleware.JourneyTypeOnboarding, journeyType)
		assert.Equal(suite.T(), middleware.StepCreateOrganization, journeyStep)

		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// Make request
	req, _ := http.NewRequest("POST", "/onboarding/create-organization", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestUserJourneyTracingMiddleware_UnknownPath() {
	// Setup test route for unknown path
	suite.router.GET("/unknown/path", func(c *gin.Context) {
		journeyID := middleware.GetUserJourneyID(c)
		journeyType := middleware.GetUserJourneyType(c)
		journeyStep := middleware.GetUserJourneyStep(c)

		// Should still have a journey ID but no specific type/step
		assert.NotEmpty(suite.T(), journeyID)
		assert.Empty(suite.T(), journeyType)
		assert.Empty(suite.T(), journeyStep)

		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// Make request
	req, _ := http.NewRequest("GET", "/unknown/path", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestGetUserJourneyID_WithoutMiddleware() {
	// Test without middleware
	router := gin.New()
	router.GET("/test", func(c *gin.Context) {
		journeyID := middleware.GetUserJourneyID(c)
		assert.Empty(suite.T(), journeyID)
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestGetUserJourneyType_WithoutMiddleware() {
	// Test without middleware
	router := gin.New()
	router.GET("/test", func(c *gin.Context) {
		journeyType := middleware.GetUserJourneyType(c)
		assert.Empty(suite.T(), journeyType)
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestGetUserJourneyStep_WithoutMiddleware() {
	// Test without middleware
	router := gin.New()
	router.GET("/test", func(c *gin.Context) {
		journeyStep := middleware.GetUserJourneyStep(c)
		assert.Empty(suite.T(), journeyStep)
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestAddJourneyEvent_Success() {
	// Setup test route
	suite.router.POST("/auth/login", func(c *gin.Context) {
		// Add custom journey event
		middleware.AddJourneyEvent(c, "login_attempt", map[string]interface{}{
			"email": "<EMAIL>",
			"success": true,
		})

		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// Make request
	req, _ := http.NewRequest("POST", "/auth/login", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestAddJourneyEvent_WithoutMiddleware() {
	// Test without middleware
	router := gin.New()
	router.POST("/test", func(c *gin.Context) {
		// This should not panic even without middleware
		middleware.AddJourneyEvent(c, "test_event", map[string]interface{}{
			"key": "value",
		})
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	req, _ := http.NewRequest("POST", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestJourneyConstants() {
	// Test that constants are defined correctly
	assert.Equal(suite.T(), "registration", middleware.JourneyTypeRegistration)
	assert.Equal(suite.T(), "onboarding", middleware.JourneyTypeOnboarding)
	
	assert.Equal(suite.T(), "register", middleware.StepRegister)
	assert.Equal(suite.T(), "email_verification", middleware.StepEmailVerification)
	assert.Equal(suite.T(), "onboarding_start", middleware.StepOnboardingStart)
	assert.Equal(suite.T(), "create_organization", middleware.StepCreateOrganization)
	assert.Equal(suite.T(), "onboarding_complete", middleware.StepOnboardingComplete)
}

func (suite *UserJourneyTracingTestSuite) TestJourneyIDGeneration_Uniqueness() {
	journeyIDs := make(map[string]bool)
	
	// Test multiple requests generate unique journey IDs
	for i := 0; i < 10; i++ {
		suite.router.GET("/test", func(c *gin.Context) {
			journeyID := middleware.GetUserJourneyID(c)
			assert.NotEmpty(suite.T(), journeyID)
			
			// Check uniqueness
			assert.False(suite.T(), journeyIDs[journeyID], "Duplicate journey ID generated: %s", journeyID)
			journeyIDs[journeyID] = true
			
			c.JSON(http.StatusOK, gin.H{"journey_id": journeyID})
		})

		req, _ := http.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)
		
		assert.Equal(suite.T(), http.StatusOK, w.Code)
	}
}

func (suite *UserJourneyTracingTestSuite) TestJourneyPathMatching() {
	testCases := []struct {
		name         string
		path         string
		method       string
		expectedType string
		expectedStep string
	}{
		{
			name:         "Register endpoint",
			path:         "/auth/register",
			method:       "POST",
			expectedType: middleware.JourneyTypeRegistration,
			expectedStep: middleware.StepRegister,
		},
		{
			name:         "Email verification endpoint",
			path:         "/auth/verify-email",
			method:       "POST",
			expectedType: middleware.JourneyTypeRegistration,
			expectedStep: middleware.StepEmailVerification,
		},
		{
			name:         "Onboarding start endpoint",
			path:         "/onboarding/start",
			method:       "POST",
			expectedType: middleware.JourneyTypeOnboarding,
			expectedStep: middleware.StepOnboardingStart,
		},
		{
			name:         "Create organization endpoint",
			path:         "/onboarding/create-organization",
			method:       "POST",
			expectedType: middleware.JourneyTypeOnboarding,
			expectedStep: middleware.StepCreateOrganization,
		},
		{
			name:         "Complete onboarding endpoint",
			path:         "/onboarding/complete",
			method:       "POST",
			expectedType: middleware.JourneyTypeOnboarding,
			expectedStep: middleware.StepOnboardingComplete,
		},
		{
			name:         "Unknown endpoint",
			path:         "/api/unknown",
			method:       "GET",
			expectedType: "",
			expectedStep: "",
		},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			router := gin.New()
			router.Use(middleware.UserJourneyTracingMiddleware())
			
			router.Handle(tc.method, tc.path, func(c *gin.Context) {
				journeyType := middleware.GetUserJourneyType(c)
				journeyStep := middleware.GetUserJourneyStep(c)
				
				assert.Equal(t, tc.expectedType, journeyType)
				assert.Equal(t, tc.expectedStep, journeyStep)
				
				c.JSON(http.StatusOK, gin.H{"status": "ok"})
			})

			req, _ := http.NewRequest(tc.method, tc.path, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			assert.Equal(t, http.StatusOK, w.Code)
		})
	}
}

func (suite *UserJourneyTracingTestSuite) TestMiddleware_HeaderPropagation() {
	journeyID := "test-journey-123"
	
	// Setup test route
	suite.router.GET("/test", func(c *gin.Context) {
		// Check that journey ID from header is used
		actualJourneyID := middleware.GetUserJourneyID(c)
		assert.Equal(suite.T(), journeyID, actualJourneyID)
		
		c.JSON(http.StatusOK, gin.H{"journey_id": actualJourneyID})
	})

	// Make request with journey ID header
	req, _ := http.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Journey-ID", journeyID)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *UserJourneyTracingTestSuite) TestMiddleware_EmptyHeaderGeneration() {
	// Setup test route
	suite.router.GET("/test", func(c *gin.Context) {
		journeyID := middleware.GetUserJourneyID(c)
		assert.NotEmpty(suite.T(), journeyID)
		assert.NotEqual(suite.T(), "", journeyID)
		
		c.JSON(http.StatusOK, gin.H{"journey_id": journeyID})
	})

	// Make request with empty journey ID header
	req, _ := http.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Journey-ID", "")
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func TestUserJourneyTracingTestSuite(t *testing.T) {
	suite.Run(t, new(UserJourneyTracingTestSuite))
}