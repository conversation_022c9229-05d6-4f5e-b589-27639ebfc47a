package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

type UserModelTestSuite struct {
	suite.Suite
}

func (suite *UserModelTestSuite) TestUserStatus_String() {
	testCases := []struct {
		status   userModels.UserStatus
		expected string
	}{
		{userModels.UserStatusActive, "active"},
		{userModels.UserStatusSuspended, "suspended"},
		{userModels.UserStatusInactive, "inactive"},
		{userModels.UserStatusPendingVerification, "pending_verification"},
		{userModels.UserStatusDeleted, "deleted"},
	}

	for _, tc := range testCases {
		suite.T().Run(string(tc.status), func(t *testing.T) {
			assert.Equal(t, tc.expected, string(tc.status))
		})
	}
}

func (suite *UserModelTestSuite) TestUserRole_String() {
	testCases := []struct {
		role     userModels.UserRole
		expected string
	}{
		{userModels.UserRoleAdmin, "admin"},
		{userModels.UserRoleUser, "user"},
		{userModels.UserRoleGuest, "guest"},
	}

	for _, tc := range testCases {
		suite.T().Run(string(tc.role), func(t *testing.T) {
			assert.Equal(t, tc.expected, string(tc.role))
		})
	}
}

func (suite *UserModelTestSuite) TestUser_TableName() {
	user := userModels.User{}
	assert.Equal(suite.T(), "users", user.TableName())
}

func (suite *UserModelTestSuite) TestUser_IsActive() {
	testCases := []struct {
		name     string
		status   userModels.UserStatus
		expected bool
	}{
		{"Active user", userModels.UserStatusActive, true},
		{"Suspended user", userModels.UserStatusSuspended, false},
		{"Inactive user", userModels.UserStatusInactive, false},
		{"Pending verification user", userModels.UserStatusPendingVerification, false},
		{"Deleted user", userModels.UserStatusDeleted, false},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			user := userModels.User{Status: tc.status}
			assert.Equal(t, tc.expected, user.IsActive())
		})
	}
}

func (suite *UserModelTestSuite) TestUser_IsVerified() {
	now := time.Now()
	
	testCases := []struct {
		name              string
		emailVerifiedAt   *time.Time
		expected          bool
	}{
		{"Verified user", &now, true},
		{"Unverified user", nil, false},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			user := userModels.User{EmailVerifiedAt: tc.emailVerifiedAt}
			assert.Equal(t, tc.expected, user.IsVerified())
		})
	}
}

func (suite *UserModelTestSuite) TestUser_IsDeleted() {
	testCases := []struct {
		name     string
		status   userModels.UserStatus
		expected bool
	}{
		{"Active user", userModels.UserStatusActive, false},
		{"Suspended user", userModels.UserStatusSuspended, false},
		{"Deleted user", userModels.UserStatusDeleted, true},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			user := userModels.User{Status: tc.status}
			assert.Equal(t, tc.expected, user.IsDeleted())
		})
	}
}

func (suite *UserModelTestSuite) TestUser_CanLogin() {
	now := time.Now()
	
	testCases := []struct {
		name              string
		status            userModels.UserStatus
		emailVerifiedAt   *time.Time
		expected          bool
	}{
		{"Active verified user", userModels.UserStatusActive, &now, true},
		{"Active unverified user", userModels.UserStatusActive, nil, false},
		{"Suspended verified user", userModels.UserStatusSuspended, &now, false},
		{"Inactive verified user", userModels.UserStatusInactive, &now, false},
		{"Deleted verified user", userModels.UserStatusDeleted, &now, false},
		{"Pending verification user", userModels.UserStatusPendingVerification, nil, false},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			user := userModels.User{
				Status:          tc.status,
				EmailVerifiedAt: tc.emailVerifiedAt,
			}
			assert.Equal(t, tc.expected, user.CanLogin())
		})
	}
}

func (suite *UserModelTestSuite) TestUser_HasTwoFactorEnabled() {
	testCases := []struct {
		name               string
		twoFactorEnabled   bool
		expected           bool
	}{
		{"Two factor enabled", true, true},
		{"Two factor disabled", false, false},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			user := userModels.User{TwoFactorEnabled: tc.twoFactorEnabled}
			assert.Equal(t, tc.expected, user.HasTwoFactorEnabled())
		})
	}
}

func (suite *UserModelTestSuite) TestUser_GetFullName() {
	testCases := []struct {
		name      string
		firstName string
		lastName  string
		expected  string
	}{
		{"Both names provided", "John", "Doe", "John Doe"},
		{"Only first name", "John", "", "John"},
		{"Only last name", "", "Doe", "Doe"},
		{"No names", "", "", ""},
		{"Names with extra spaces", "  John  ", "  Doe  ", "John Doe"},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			user := userModels.User{
				FirstName: tc.firstName,
				LastName:  tc.lastName,
			}
			assert.Equal(t, tc.expected, user.GetFullName())
		})
	}
}

func (suite *UserModelTestSuite) TestUser_BeforeCreate() {
	user := userModels.User{
		Email:    "<EMAIL>",
		Username: "testuser",
		Password: "password123",
	}

	// Call BeforeCreate
	err := user.BeforeCreate(nil) // nil for gorm.DB in this test

	assert.NoError(suite.T(), err)
	
	// Check that timestamps are set
	assert.False(suite.T(), user.CreatedAt.IsZero())
	assert.False(suite.T(), user.UpdatedAt.IsZero())
	
	// Check that default status is set
	if user.Status == "" {
		// If no status was set, it should default to pending verification
		assert.Equal(suite.T(), userModels.UserStatusPendingVerification, user.Status)
	}
}

func (suite *UserModelTestSuite) TestUserFilter_Validation() {
	// Test that UserFilter struct can be created and used
	filter := userModels.UserFilter{
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
		Role:     userModels.UserRoleUser,
	}

	assert.Equal(suite.T(), "<EMAIL>", filter.Email)
	assert.Equal(suite.T(), "testuser", filter.Username)
	assert.Equal(suite.T(), userModels.UserStatusActive, filter.Status)
	assert.Equal(suite.T(), userModels.UserRoleUser, filter.Role)
}

func (suite *UserModelTestSuite) TestUserCreateRequest_Validation() {
	// Test that UserCreateRequest struct can be created
	createReq := userModels.UserCreateRequest{
		Email:     "<EMAIL>",
		Username:  "testuser",
		Password:  "password123",
		FirstName: "Test",
		LastName:  "User",
		Role:      userModels.UserRoleUser,
	}

	assert.Equal(suite.T(), "<EMAIL>", createReq.Email)
	assert.Equal(suite.T(), "testuser", createReq.Username)
	assert.Equal(suite.T(), "password123", createReq.Password)
	assert.Equal(suite.T(), "Test", createReq.FirstName)
	assert.Equal(suite.T(), "User", createReq.LastName)
	assert.Equal(suite.T(), userModels.UserRoleUser, createReq.Role)
}

func (suite *UserModelTestSuite) TestUserUpdateRequest_Validation() {
	// Test that UserUpdateRequest struct can be created
	updateReq := userModels.UserUpdateRequest{
		FirstName: stringPtr("Updated"),
		LastName:  stringPtr("Name"),
		Username:  stringPtr("updateduser"),
	}

	assert.Equal(suite.T(), "Updated", *updateReq.FirstName)
	assert.Equal(suite.T(), "Name", *updateReq.LastName)
	assert.Equal(suite.T(), "updateduser", *updateReq.Username)
}

func (suite *UserModelTestSuite) TestChangePasswordRequest_Validation() {
	changeReq := userModels.ChangePasswordRequest{
		CurrentPassword: "oldpassword123",
		NewPassword:     "newpassword456",
	}

	assert.Equal(suite.T(), "oldpassword123", changeReq.CurrentPassword)
	assert.Equal(suite.T(), "newpassword456", changeReq.NewPassword)
}

func (suite *UserModelTestSuite) TestUserLoginRequest_Validation() {
	loginReq := userModels.UserLoginRequest{
		Email:    "<EMAIL>",
		Password: "password123",
		DeviceInfo: userModels.DeviceInfo{
			DeviceType: "desktop",
			UserAgent:  "Mozilla/5.0",
			IPAddress:  "127.0.0.1",
		},
	}

	assert.Equal(suite.T(), "<EMAIL>", loginReq.Email)
	assert.Equal(suite.T(), "password123", loginReq.Password)
	assert.Equal(suite.T(), "desktop", loginReq.DeviceInfo.DeviceType)
	assert.Equal(suite.T(), "Mozilla/5.0", loginReq.DeviceInfo.UserAgent)
	assert.Equal(suite.T(), "127.0.0.1", loginReq.DeviceInfo.IPAddress)
}

func (suite *UserModelTestSuite) TestUserRegistrationRequest_Validation() {
	regReq := userModels.UserRegistrationRequest{
		Email:     "<EMAIL>",
		Username:  "testuser",
		Password:  "password123",
		FirstName: "Test",
		LastName:  "User",
		DeviceInfo: userModels.DeviceInfo{
			DeviceType: "mobile",
			UserAgent:  "Mobile Browser",
			IPAddress:  "***********",
		},
	}

	assert.Equal(suite.T(), "<EMAIL>", regReq.Email)
	assert.Equal(suite.T(), "testuser", regReq.Username)
	assert.Equal(suite.T(), "password123", regReq.Password)
	assert.Equal(suite.T(), "Test", regReq.FirstName)
	assert.Equal(suite.T(), "User", regReq.LastName)
	assert.Equal(suite.T(), "mobile", regReq.DeviceInfo.DeviceType)
}

func (suite *UserModelTestSuite) TestRecoveryCodes_JSONHandling() {
	// Test RecoveryCodes custom type
	codes := userModels.RecoveryCodes{"code1", "code2", "code3"}
	
	// Test that it can be converted to/from database values
	value, err := codes.Value()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), value)
	
	// Test scanning from database
	var scannedCodes userModels.RecoveryCodes
	err = scannedCodes.Scan(value)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), codes, scannedCodes)
}

func (suite *UserModelTestSuite) TestUserStatus_DatabaseHandling() {
	// Test UserStatus custom type database handling
	status := userModels.UserStatusActive
	
	// Test Value method
	value, err := status.Value()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "active", value)
	
	// Test Scan method
	var scannedStatus userModels.UserStatus
	err = scannedStatus.Scan("active")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), userModels.UserStatusActive, scannedStatus)
	
	// Test Scan with invalid value
	err = scannedStatus.Scan("invalid_status")
	assert.NoError(suite.T(), err) // Should not error but may not set the value
}

func (suite *UserModelTestSuite) TestUserRole_DatabaseHandling() {
	// Test UserRole custom type database handling
	role := userModels.UserRoleAdmin
	
	// Test Value method
	value, err := role.Value()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "admin", value)
	
	// Test Scan method
	var scannedRole userModels.UserRole
	err = scannedRole.Scan("admin")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), userModels.UserRoleAdmin, scannedRole)
}

// Helper function for pointer to string
func stringPtr(s string) *string {
	return &s
}

func TestUserModelTestSuite(t *testing.T) {
	suite.Run(t, new(UserModelTestSuite))
}