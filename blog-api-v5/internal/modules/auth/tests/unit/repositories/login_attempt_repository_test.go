package repositories

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type LoginAttemptRepositoryTestSuite struct {
	suite.Suite
	db       *gorm.DB
	repo     repositories.LoginAttemptRepository
	userRepo repositories.UserRepository
	ctx      context.Context
	testUser *userModels.User
	logger   utils.Logger
}


func (suite *LoginAttemptRepositoryTestSuite) SetupSuite() {
	// Initialize test database (SQLite in memory)
	testDB, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = testDB
	suite.logger = &MockLogger{}

	// Auto-migrate tables using SQLite-compatible models  
	err = suite.db.AutoMigrate(&TestUser{}, &TestLoginAttempt{})
	require.NoError(suite.T(), err)

	suite.repo = mysql.NewLoginAttemptRepository(suite.db)
	suite.userRepo = mysql.NewUserRepository(suite.db, suite.logger)
	suite.ctx = context.Background()
}

func (suite *LoginAttemptRepositoryTestSuite) TearDownSuite() {
	// Clean up test database
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *LoginAttemptRepositoryTestSuite) SetupTest() {
	// Clean tables before each test
	suite.db.Exec("DELETE FROM auth_login_attempts")
	suite.db.Exec("DELETE FROM users")

	// Create test user
	suite.testUser = &userModels.User{
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		FirstName:    stringPtr("Test"),
		LastName:     stringPtr("User"),
		PasswordHash: "hashedpassword",
		Status:       userModels.UserStatusActive,
		Timezone:     "UTC",
		Language:     "en",
	}
	err := suite.userRepo.Create(suite.ctx, suite.testUser)
	require.NoError(suite.T(), err)
}

func (suite *LoginAttemptRepositoryTestSuite) TestCreate_Success() {
	attempt := &authModels.LoginAttempt{
		TenantID:     1,
		WebsiteID:    1,
		UserID:       &suite.testUser.ID,
		Email:        "<EMAIL>",
		IPAddress:    stringPtr("127.0.0.1"),
		UserAgent:    stringPtr("Test Browser"),
		Result:       authModels.AttemptResultSuccess,
		DeviceInfo:   stringPtr("Test Device"),
		FailureReason: nil,
	}

	err := suite.repo.Create(suite.ctx, attempt)
	
	assert.NoError(suite.T(), err)
	assert.NotZero(suite.T(), attempt.ID)
	assert.NotZero(suite.T(), attempt.CreatedAt)
}

func (suite *LoginAttemptRepositoryTestSuite) TestGetByID_Success() {
	attempt := &authModels.LoginAttempt{
		TenantID:     1,
		WebsiteID:    1,
		UserID:       &suite.testUser.ID,
		Email:        "<EMAIL>",
		IPAddress:    stringPtr("127.0.0.1"),
		UserAgent:    stringPtr("Test Browser"),
		Result:       authModels.AttemptResultSuccess,
		
		DeviceInfo:   stringPtr("Test Device"),
	}

	err := suite.repo.Create(suite.ctx, attempt)
	require.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, attempt.ID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved)
	assert.Equal(suite.T(), attempt.ID, retrieved.ID)
	assert.Equal(suite.T(), attempt.Email, retrieved.Email)
	assert.Equal(suite.T(), attempt.IPAddress, retrieved.IPAddress)
}

func (suite *LoginAttemptRepositoryTestSuite) TestGetByID_NotFound() {
	retrieved, err := suite.repo.GetByID(suite.ctx, 999)
	
	assert.Error(suite.T(), err)
	assert.True(suite.T(), errors.Is(err, gorm.ErrRecordNotFound))
	assert.Nil(suite.T(), retrieved)
}

func (suite *LoginAttemptRepositoryTestSuite) TestCountRecentAttempts_Success() {
	// Create multiple recent attempts
	for i := 0; i < 3; i++ {
		attempt := &authModels.LoginAttempt{
			TenantID:     1,
		WebsiteID:    1,
			UserID:       &suite.testUser.ID,
			Email:        "<EMAIL>",
			IPAddress:    stringPtr("127.0.0.1"),
			UserAgent:    stringPtr("Test Browser"),
			Result:       authModels.AttemptResultFailure,
			
			DeviceInfo:   stringPtr("Test Device"),
		}
		err := suite.repo.Create(suite.ctx, attempt)
		require.NoError(suite.T(), err)
	}

	// Create an old attempt (beyond the time window)
	oldAttempt := &authModels.LoginAttempt{
		TenantID:     1,
		WebsiteID:    1,
		UserID:       &suite.testUser.ID,
		Email:        "<EMAIL>",
		IPAddress:    stringPtr("127.0.0.1"),
		UserAgent:    stringPtr("Test Browser"),
		Result:       authModels.AttemptResultFailure,
		
		DeviceInfo:   stringPtr("Test Device"),
	}
	err := suite.repo.Create(suite.ctx, oldAttempt)
	require.NoError(suite.T(), err)

	// Manually update the old attempt's timestamp
	suite.db.Model(oldAttempt).Update("created_at", time.Now().Add(-2*time.Hour))

	count, err := suite.repo.CountRecentAttempts(suite.ctx, suite.testUser.Email, 1*time.Hour)
	
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(3), count) // Only recent attempts
}

func (suite *LoginAttemptRepositoryTestSuite) TestCountRecentAttemptsByIP_Success() {
	ipAddress := "***********"

	// Create multiple recent attempts from the same IP
	for i := 0; i < 2; i++ {
		attempt := &authModels.LoginAttempt{
			TenantID:     1,
		WebsiteID:    1,
			UserID:       &suite.testUser.ID,
			Email:        "<EMAIL>",
			IPAddress:    stringPtr(ipAddress),
			UserAgent:    stringPtr("Test Browser"),
			Result:       authModels.AttemptResultFailure,
			
			DeviceInfo:   stringPtr("Test Device"),
		}
		err := suite.repo.Create(suite.ctx, attempt)
		require.NoError(suite.T(), err)
	}

	// Create attempt from different IP
	differentIPAttempt := &authModels.LoginAttempt{
		TenantID:     1,
		WebsiteID:    1,
		UserID:       &suite.testUser.ID,
		Email:        "<EMAIL>",
		IPAddress:    stringPtr("127.0.0.1"),
		UserAgent:    stringPtr("Test Browser"),
		Result:       authModels.AttemptResultFailure,
		
		DeviceInfo:   stringPtr("Test Device"),
	}
	err := suite.repo.Create(suite.ctx, differentIPAttempt)
	require.NoError(suite.T(), err)

	count, err := suite.repo.CountRecentAttemptsbyIP(suite.ctx, ipAddress, 1*time.Hour)
	
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), count) // Only attempts from the specified IP
}

func (suite *LoginAttemptRepositoryTestSuite) TestGetRecentAttempts_Success() {
	// Create multiple attempts
	for i := 0; i < 3; i++ {
		attempt := &authModels.LoginAttempt{
			TenantID:     1,
		WebsiteID:    1,
			UserID:       &suite.testUser.ID,
			Email:        "<EMAIL>",
			IPAddress:    stringPtr("127.0.0.1"),
			UserAgent:    stringPtr("Test Browser"),
			Result:       authModels.AttemptResultFailure,
			
			DeviceInfo:   stringPtr("Test Device"),
		}
		err := suite.repo.Create(suite.ctx, attempt)
		require.NoError(suite.T(), err)
	}

	attempts, err := suite.repo.GetRecentAttempts(suite.ctx, suite.testUser.Email, 1*time.Hour)
	
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), attempts, 3)
}

func (suite *LoginAttemptRepositoryTestSuite) TestIsAccountLocked_Success() {
	// Create account lock record
	err := suite.repo.LockAccount(suite.ctx, suite.testUser.Email, time.Now().Add(1*time.Hour))
	require.NoError(suite.T(), err)

	isLocked, _, err := suite.repo.IsAccountLocked(suite.ctx, suite.testUser.Email)
	
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), isLocked)
}

func (suite *LoginAttemptRepositoryTestSuite) TestIsAccountLocked_NotLocked() {
	isLocked, _, err := suite.repo.IsAccountLocked(suite.ctx, suite.testUser.Email)
	
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), isLocked)
}

func (suite *LoginAttemptRepositoryTestSuite) TestLockAccount_Success() {
	err := suite.repo.LockAccount(suite.ctx, suite.testUser.Email, time.Now().Add(1*time.Hour))
	
	assert.NoError(suite.T(), err)

	// Verify account is locked
	isLocked, _, err := suite.repo.IsAccountLocked(suite.ctx, suite.testUser.Email)
	require.NoError(suite.T(), err)
	assert.True(suite.T(), isLocked)
}

func (suite *LoginAttemptRepositoryTestSuite) TestUnlockAccount_Success() {
	// First lock the account
	err := suite.repo.LockAccount(suite.ctx, suite.testUser.Email, time.Now().Add(1*time.Hour))
	require.NoError(suite.T(), err)

	// Then unlock it
	err = suite.repo.UnlockAccount(suite.ctx, suite.testUser.Email)
	
	assert.NoError(suite.T(), err)

	// Verify account is unlocked
	isLocked, _, err := suite.repo.IsAccountLocked(suite.ctx, suite.testUser.Email)
	require.NoError(suite.T(), err)
	assert.False(suite.T(), isLocked)
}

func (suite *LoginAttemptRepositoryTestSuite) TestRecordSuccessfulLogin_Success() {
	err := suite.repo.RecordSuccessfulLogin(suite.ctx, suite.testUser.ID, "127.0.0.1")
	
	assert.NoError(suite.T(), err)

	// Verify the attempt was recorded
	attempts, err := suite.repo.GetRecentAttempts(suite.ctx, suite.testUser.Email, 1*time.Hour)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), attempts, 1)
	assert.Equal(suite.T(), authModels.AttemptResultSuccess, attempts[0].Result)
}

func (suite *LoginAttemptRepositoryTestSuite) TestGetLastSuccessfulLogin_Success() {
	// Record a successful login
	err := suite.repo.RecordSuccessfulLogin(suite.ctx, suite.testUser.ID, "127.0.0.1")
	require.NoError(suite.T(), err)

	lastLogin, err := suite.repo.GetLastSuccessfulLogin(suite.ctx, suite.testUser.ID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), lastLogin)
	assert.Equal(suite.T(), authModels.AttemptResultSuccess, lastLogin.Result)
}

func (suite *LoginAttemptRepositoryTestSuite) TestGetFailedAttemptsByUser_Success() {
	// Create mix of successful and failed attempts
	successAttempt := &authModels.LoginAttempt{
		TenantID:     1,
		WebsiteID:    1,
		UserID:       &suite.testUser.ID,
		Email:        "<EMAIL>",
		IPAddress:    stringPtr("127.0.0.1"),
		UserAgent:    stringPtr("Test Browser"),
		Result:       authModels.AttemptResultSuccess,
		
		DeviceInfo:   stringPtr("Test Device"),
	}

	failedAttempt := &authModels.LoginAttempt{
		TenantID:     1,
		WebsiteID:    1,
		UserID:       &suite.testUser.ID,
		Email:        "<EMAIL>",
		IPAddress:    stringPtr("127.0.0.1"),
		UserAgent:    stringPtr("Test Browser"),
		Result:       authModels.AttemptResultFailure,
		
		DeviceInfo:   stringPtr("Test Device"),
	}

	err := suite.repo.Create(suite.ctx, successAttempt)
	require.NoError(suite.T(), err)
	
	err = suite.repo.Create(suite.ctx, failedAttempt)
	require.NoError(suite.T(), err)

	failedAttempts, err := suite.repo.GetFailedAttemptsByUser(suite.ctx, suite.testUser.ID, 10)
	
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), failedAttempts, 1) // Only failed attempts
	assert.Equal(suite.T(), authModels.AttemptResultFailure, failedAttempts[0].Result)
}

func (suite *LoginAttemptRepositoryTestSuite) TestGetFailedAttemptsByIP_Success() {
	ipAddress := "***********"

	// Create failed attempts from the IP
	for i := 0; i < 2; i++ {
		attempt := &authModels.LoginAttempt{
			TenantID:     1,
		WebsiteID:    1,
			UserID:       &suite.testUser.ID,
			Email:        "<EMAIL>",
			IPAddress:    stringPtr(ipAddress),
			UserAgent:    stringPtr("Test Browser"),
			Result:       authModels.AttemptResultFailure,
			
			DeviceInfo:   stringPtr("Test Device"),
		}
		err := suite.repo.Create(suite.ctx, attempt)
		require.NoError(suite.T(), err)
	}

	failedAttempts, err := suite.repo.GetFailedAttemptsByIP(suite.ctx, ipAddress, 10)
	
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), failedAttempts, 2)
}

func (suite *LoginAttemptRepositoryTestSuite) TestCountFailedAttemptsByUser_Success() {
	// Create multiple failed attempts
	for i := 0; i < 3; i++ {
		attempt := &authModels.LoginAttempt{
			TenantID:     1,
		WebsiteID:    1,
			UserID:       &suite.testUser.ID,
			Email:        "<EMAIL>",
			IPAddress:    stringPtr("127.0.0.1"),
			UserAgent:    stringPtr("Test Browser"),
			Result:       authModels.AttemptResultFailure,
			
			DeviceInfo:   stringPtr("Test Device"),
		}
		err := suite.repo.Create(suite.ctx, attempt)
		require.NoError(suite.T(), err)
	}

	// Create a successful attempt (should not be counted)
	successAttempt := &authModels.LoginAttempt{
		TenantID:     1,
		WebsiteID:    1,
		UserID:       &suite.testUser.ID,
		Email:        "<EMAIL>",
		IPAddress:    stringPtr("127.0.0.1"),
		UserAgent:    stringPtr("Test Browser"),
		Result:       authModels.AttemptResultSuccess,
		
		DeviceInfo:   stringPtr("Test Device"),
	}
	err := suite.repo.Create(suite.ctx, successAttempt)
	require.NoError(suite.T(), err)

	count, err := suite.repo.CountFailedAttemptsByUser(suite.ctx, suite.testUser.ID, 1*time.Hour)
	
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(3), count) // Only failed attempts
}

func (suite *LoginAttemptRepositoryTestSuite) TestCleanupOldAttempts_Success() {
	// Create old attempts
	for i := 0; i < 2; i++ {
		attempt := &authModels.LoginAttempt{
			TenantID:     1,
		WebsiteID:    1,
			UserID:       &suite.testUser.ID,
			Email:        "<EMAIL>",
			IPAddress:    stringPtr("127.0.0.1"),
			UserAgent:    stringPtr("Test Browser"),
			Result:       authModels.AttemptResultFailure,
			
			DeviceInfo:   stringPtr("Test Device"),
		}
		err := suite.repo.Create(suite.ctx, attempt)
		require.NoError(suite.T(), err)

		// Make it old
		suite.db.Model(attempt).Update("created_at", time.Now().Add(-48*time.Hour))
	}

	// Create recent attempt
	recentAttempt := &authModels.LoginAttempt{
		TenantID:     1,
		WebsiteID:    1,
		UserID:       &suite.testUser.ID,
		Email:        "<EMAIL>",
		IPAddress:    stringPtr("127.0.0.1"),
		UserAgent:    stringPtr("Test Browser"),
		Result:       authModels.AttemptResultFailure,
		
		DeviceInfo:   stringPtr("Test Device"),
	}
	err := suite.repo.Create(suite.ctx, recentAttempt)
	require.NoError(suite.T(), err)

	deletedCount, err := suite.repo.CleanupOldAttempts(suite.ctx, time.Now().Add(-24*time.Hour))
	
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(2), deletedCount)

	// Verify only recent attempt remains
	allAttempts, err := suite.repo.GetRecentAttempts(suite.ctx, suite.testUser.Email, 72*time.Hour)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), allAttempts, 1)
}


func TestLoginAttemptRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(LoginAttemptRepositoryTestSuite))
}