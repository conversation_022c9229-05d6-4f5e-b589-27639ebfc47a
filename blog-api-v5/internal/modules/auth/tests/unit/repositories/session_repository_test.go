package repositories

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type SessionRepositoryTestSuite struct {
	suite.Suite
	db       *gorm.DB
	repo     repositories.SessionRepository
	userRepo repositories.UserRepository
	ctx      context.Context
	testUser *userModels.User
	logger   utils.Logger
}

func (suite *SessionRepositoryTestSuite) SetupSuite() {
	// Initialize test database (SQLite in memory)
	testDB, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = testDB
	suite.logger = &MockLogger{}

	// Auto-migrate tables using SQLite-compatible models
	err = suite.db.AutoMigrate(&TestUser{}, &TestSession{})
	require.NoError(suite.T(), err)

	suite.repo = mysql.NewSessionRepository(suite.db)
	suite.userRepo = mysql.NewUserRepository(suite.db, suite.logger)
	suite.ctx = context.Background()
}

func (suite *SessionRepositoryTestSuite) TearDownSuite() {
	// Clean up test database
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *SessionRepositoryTestSuite) SetupTest() {
	// Clean tables before each test
	suite.db.Exec("DELETE FROM auth_sessions")
	suite.db.Exec("DELETE FROM users")

	// Create test user
	suite.testUser = &userModels.User{
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		FirstName:    stringPtr("Test"),
		LastName:     stringPtr("User"),
		PasswordHash: "hashedpassword",
		Status:       userModels.UserStatusActive,
		Timezone:     "UTC",
		Language:     "en",
	}
	err := suite.userRepo.Create(suite.ctx, suite.testUser)
	require.NoError(suite.T(), err)
}

func (suite *SessionRepositoryTestSuite) TestCreate_Success() {
	session := &authModels.Session{
		UserID:    suite.testUser.ID,
		TenantID:  1,
		Token:     "session_token_123",
		IPAddress: stringPtr("127.0.0.1"),
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}

	err := suite.repo.Create(suite.ctx, session)
	
	assert.NoError(suite.T(), err)
	assert.NotZero(suite.T(), session.ID)
	assert.NotZero(suite.T(), session.CreatedAt)
	assert.NotZero(suite.T(), session.UpdatedAt)
}

func (suite *SessionRepositoryTestSuite) TestGetByID_Success() {
	session := &authModels.Session{
		UserID:    suite.testUser.ID,
		TenantID:  1,
		Token:     "session_token_123",
		IPAddress: stringPtr("127.0.0.1"),
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}

	err := suite.repo.Create(suite.ctx, session)
	require.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, session.ID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved)
	assert.Equal(suite.T(), session.ID, retrieved.ID)
	assert.Equal(suite.T(), session.Token, retrieved.Token)
	assert.Equal(suite.T(), session.UserID, retrieved.UserID)
}

func TestSessionRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(SessionRepositoryTestSuite))
}