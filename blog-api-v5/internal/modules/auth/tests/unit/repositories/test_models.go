package repositories

import (
	"time"

	authModels "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"gorm.io/datatypes"
)

// TestSession is a SQLite-compatible version of Session model for testing
type TestSession struct {
	ID       uint `gorm:"primaryKey"`
	TenantID uint `gorm:"not null;index"`
	UserID   uint `gorm:"not null;index"`

	// Session Information
	Token              string     `gorm:"type:varchar(255);not null;uniqueIndex"`
	RefreshTokenHash   *string    `gorm:"type:varchar(64);index"`
	TokenFamily        *string    `gorm:"type:varchar(64);index"`
	RefreshTokenUsedAt *time.Time `gorm:"index"`
	RefreshTokenExpiry *time.Time `gorm:"index"`

	// Device Information
	DeviceName *string                   `json:"device_name,omitempty"`
	DeviceType authModels.DeviceType     `gorm:"type:varchar(20);default:'unknown'"` // Changed from ENUM to VARCHAR
	OS         *string                   `json:"os,omitempty"`
	Browser    *string                   `json:"browser,omitempty"`
	DeviceID   *string                   `gorm:"index"`

	// Location Information
	IPAddress *string `json:"ip_address,omitempty"`
	Country   *string `json:"country,omitempty"`
	City      *string `json:"city,omitempty"`

	// Session State
	IsRevoked  bool       `gorm:"default:false;index"`
	LastUsedAt *time.Time `gorm:"index"`
	ExpiresAt  time.Time  `gorm:"not null;index"`

	// Timestamps
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
}

func (TestSession) TableName() string {
	return "auth_sessions"
}

// TestLoginAttempt is a SQLite-compatible version of LoginAttempt model for testing
type TestLoginAttempt struct {
	ID        uint  `gorm:"primaryKey"`
	TenantID  uint  `gorm:"not null;index"`
	WebsiteID uint  `gorm:"not null;index"`
	UserID    *uint `gorm:"index"`

	// Attempt Information
	Email  string                       `gorm:"not null;index"`
	Result authModels.AttemptResult     `gorm:"type:varchar(50);not null;index"` // Changed from ENUM to VARCHAR

	// Request Information
	IPAddress *string `gorm:"index"`
	UserAgent *string
	Country   *string
	City      *string

	// Device Information
	DeviceInfo *string

	// Failure Details
	FailureReason *string

	// Timestamps
	CreatedAt time.Time `gorm:"autoCreateTime;index"`
}

func (TestLoginAttempt) TableName() string {
	return "auth_login_attempts"
}

// TestUser is a simple User model for testing - doesn't need associations
type TestUser struct {
	ID uint `gorm:"primaryKey;autoIncrement"`

	// Basic Information
	Email       string  `gorm:"type:varchar(255);not null;uniqueIndex"`
	Username    *string `gorm:"type:varchar(255);uniqueIndex"`
	FirstName   *string `gorm:"type:varchar(255)"`
	LastName    *string `gorm:"type:varchar(255)"`
	DisplayName *string `gorm:"type:varchar(255)"`

	// Authentication
	PasswordHash    string     `gorm:"type:varchar(255);not null"`
	EmailVerified   bool       `gorm:"default:false;index"`
	EmailVerifiedAt *time.Time

	// Status and Configuration
	Status userModels.UserStatus `gorm:"type:varchar(50);default:'active';not null;index"`

	// Contact Information
	Phone           *string    `gorm:"type:varchar(50)"`
	PhoneVerified   bool       `gorm:"default:false"`
	PhoneVerifiedAt *time.Time

	// Profile Information
	AvatarURL *string `gorm:"type:varchar(500)"`
	Timezone  string  `gorm:"type:varchar(100);default:'UTC'"`
	Language  string  `gorm:"type:varchar(10);default:'en'"`

	// Security
	TwoFactorEnabled bool           `gorm:"default:false"`
	TwoFactorSecret  *string        `gorm:"type:varchar(255)"`
	RecoveryCodes    datatypes.JSON `gorm:"type:json"`

	// Activity Tracking
	LastLoginAt *time.Time `json:"last_login_at,omitempty"`
	LastLoginIP *string    `gorm:"type:varchar(45)"`
	LoginCount  uint       `gorm:"default:0"`

	// Timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
}

func (TestUser) TableName() string {
	return "users"
}