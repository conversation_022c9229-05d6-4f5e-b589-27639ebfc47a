package repositories

import (
	"time"

	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// TestTenantMembership is a simple TenantMembership model for testing
type TestTenantMembership struct {
	ID        uint                                  `gorm:"primaryKey;autoIncrement"`
	UserID    uint                                  `gorm:"not null;index"`
	TenantID  uint                                  `gorm:"not null;index"`
	Status    userModels.TenantMembershipStatus     `gorm:"type:varchar(50);default:'active';not null"`
	IsPrimary bool                                  `gorm:"default:false"`
	CreatedAt time.Time                             `gorm:"autoCreateTime"`
	UpdatedAt time.Time                             `gorm:"autoUpdateTime"`
}

func (TestTenantMembership) TableName() string {
	return "tenant_memberships"
}

// TestUserProfile is a simple UserProfile model for testing
type TestUserProfile struct {
	ID        uint      `gorm:"primaryKey;autoIncrement"`
	UserID    uint      `gorm:"not null;index;unique"`
	Bio       *string   `gorm:"type:text"`
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
}

func (TestUserProfile) TableName() string {
	return "user_profiles"
}

// TestUserPreferences is a simple UserPreferences model for testing
type TestUserPreferences struct {
	ID        uint      `gorm:"primaryKey;autoIncrement"`
	UserID    uint      `gorm:"not null;index;unique"`
	Theme     string    `gorm:"type:varchar(50);default:'light'"`
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
}

func (TestUserPreferences) TableName() string {
	return "user_preferences"
}

// TestUserSocialLink is a simple UserSocialLink model for testing
type TestUserSocialLink struct {
	ID        uint      `gorm:"primaryKey;autoIncrement"`
	UserID    uint      `gorm:"not null;index"`
	Platform  string    `gorm:"type:varchar(50);not null"`
	URL       string    `gorm:"type:varchar(500);not null"`
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
}

func (TestUserSocialLink) TableName() string {
	return "user_social_links"
}