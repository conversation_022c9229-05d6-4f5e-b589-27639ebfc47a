package repositories

import (
	"context"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MockLogger implements utils.Logger for testing
type MockLogger struct{}

func (m *<PERSON>ckLogger) Debug(args ...interface{}) {}
func (m *MockLogger) Info(args ...interface{}) {}
func (m *MockLogger) Warn(args ...interface{}) {}
func (m *MockLogger) Error(args ...interface{}) {}
func (m *MockLogger) Fatal(args ...interface{}) {}
func (m *MockLogger) Panic(args ...interface{}) {}

func (m *MockLogger) Debugf(format string, args ...interface{}) {}
func (m *MockLogger) Infof(format string, args ...interface{}) {}
func (m *MockLogger) Warnf(format string, args ...interface{}) {}
func (m *<PERSON>ckLogger) Errorf(format string, args ...interface{}) {}
func (m *<PERSON>ckLogger) Fatalf(format string, args ...interface{}) {}
func (m *MockLogger) Panicf(format string, args ...interface{}) {}

func (m *MockLogger) WithField(key string, value interface{}) utils.Logger { return m }
func (m *MockLogger) WithFields(fields utils.Fields) utils.Logger { return m }
func (m *MockLogger) WithError(err error) utils.Logger { return m }
func (m *MockLogger) WithContext(ctx context.Context) utils.Logger { return m }
func (m *MockLogger) GetLogger() interface{} { return m }

// Helper function for pointer to string
func stringPtr(s string) *string {
	return &s
}