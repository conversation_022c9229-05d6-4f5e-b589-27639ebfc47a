package repositories

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type UserRepositoryTestSuite struct {
	suite.Suite
	db     *gorm.DB
	repo   repositories.UserRepository
	ctx    context.Context
	logger utils.Logger
}


func (suite *UserRepositoryTestSuite) SetupSuite() {
	// Initialize test database (SQLite in memory)
	testDB, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = testDB
	suite.logger = &MockLogger{}

	// Auto-migrate tables using SQLite-compatible models
	err = suite.db.AutoMigrate(
		&TestUser{}, 
		&TestTenantMembership{}, 
		&TestUserProfile{}, 
		&TestUserPreferences{}, 
		&TestUserSocialLink{},
	)
	require.NoError(suite.T(), err)

	suite.repo = mysql.NewUserRepository(suite.db, suite.logger)
	suite.ctx = context.Background()
}

func (suite *UserRepositoryTestSuite) TearDownSuite() {
	// Clean up test database
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *UserRepositoryTestSuite) SetupTest() {
	// Clean tables before each test
	suite.db.Exec("DELETE FROM users")
}

func (suite *UserRepositoryTestSuite) TestCreate_Success() {
	user := &models.User{
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		FirstName:    stringPtr("Test"),
		LastName:     stringPtr("User"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	
	assert.NoError(suite.T(), err)
	assert.NotZero(suite.T(), user.ID)
	assert.NotZero(suite.T(), user.CreatedAt)
	assert.NotZero(suite.T(), user.UpdatedAt)
}

func (suite *UserRepositoryTestSuite) TestCreate_DuplicateEmail() {
	user1 := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser1"),
		PasswordHash: "hashedpassword",
		Timezone:     "UTC",
		Language:     "en",
	}

	user2 := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser2"),
		PasswordHash: "hashedpassword",
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user1)
	assert.NoError(suite.T(), err)

	err = suite.repo.Create(suite.ctx, user2)
	assert.Error(suite.T(), err)
}

func (suite *UserRepositoryTestSuite) TestGetByID_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved)
	assert.Equal(suite.T(), user.ID, retrieved.ID)
	assert.Equal(suite.T(), user.Email, retrieved.Email)
	assert.Equal(suite.T(), *user.Username, *retrieved.Username)
}

func (suite *UserRepositoryTestSuite) TestGetByID_NotFound() {
	retrieved, err := suite.repo.GetByID(suite.ctx, 999)
	
	assert.Error(suite.T(), err)
	assert.True(suite.T(), errors.Is(err, gorm.ErrRecordNotFound))
	assert.Nil(suite.T(), retrieved)
}

func (suite *UserRepositoryTestSuite) TestGetByEmail_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByEmail(suite.ctx, "<EMAIL>")
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved)
	assert.Equal(suite.T(), user.Email, retrieved.Email)
}

func (suite *UserRepositoryTestSuite) TestGetByEmail_NotFound() {
	retrieved, err := suite.repo.GetByEmail(suite.ctx, "<EMAIL>")
	
	assert.Error(suite.T(), err)
	assert.True(suite.T(), errors.Is(err, gorm.ErrRecordNotFound))
	assert.Nil(suite.T(), retrieved)
}

func (suite *UserRepositoryTestSuite) TestGetByUsername_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByUsername(suite.ctx, "testuser")
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved)
	assert.Equal(suite.T(), *user.Username, *retrieved.Username)
}

func (suite *UserRepositoryTestSuite) TestUpdate_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	user.FirstName = stringPtr("Updated")
	user.LastName = stringPtr("Name")
	
	err = suite.repo.Update(suite.ctx, user)
	
	assert.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Updated", *retrieved.FirstName)
	assert.Equal(suite.T(), "Name", *retrieved.LastName)
}

func (suite *UserRepositoryTestSuite) TestDelete_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	err = suite.repo.Delete(suite.ctx, user.ID)
	
	assert.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), retrieved)
}

func (suite *UserRepositoryTestSuite) TestUpdateStatus_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	err = suite.repo.UpdateStatus(suite.ctx, user.ID, models.UserStatusSuspended)
	
	assert.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.UserStatusSuspended, retrieved.Status)
}

func (suite *UserRepositoryTestSuite) TestUpdatePassword_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	newPassword := "newhashedpassword"
	err = suite.repo.UpdatePassword(suite.ctx, user.ID, newPassword)
	
	assert.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), newPassword, retrieved.PasswordHash)
}

func (suite *UserRepositoryTestSuite) TestVerifyEmail_Success() {
	user := &models.User{
		Email:         "<EMAIL>",
		Username:      stringPtr("testuser"),
		PasswordHash:  "hashedpassword",
		Status:        models.UserStatusPendingVerification,
		EmailVerified: false,
		Timezone:      "UTC",
		Language:      "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	err = suite.repo.VerifyEmail(suite.ctx, user.ID)
	
	assert.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.True(suite.T(), retrieved.EmailVerified)
	assert.NotNil(suite.T(), retrieved.EmailVerifiedAt)
	assert.Equal(suite.T(), models.UserStatusActive, retrieved.Status)
}

func (suite *UserRepositoryTestSuite) TestUpdateLastLogin_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	err = suite.repo.UpdateLastLogin(suite.ctx, user.ID, "127.0.0.1")
	
	assert.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.NotNil(suite.T(), retrieved.LastLoginAt)
	assert.Equal(suite.T(), "127.0.0.1", *retrieved.LastLoginIP)
	assert.Greater(suite.T(), retrieved.LoginCount, uint(0))
}

func (suite *UserRepositoryTestSuite) TestEnableTwoFactor_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	secret := "ABCDEFGHIJKLMNOP"
	
	err = suite.repo.EnableTwoFactor(suite.ctx, user.ID, secret)
	
	assert.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.True(suite.T(), retrieved.TwoFactorEnabled)
	assert.Equal(suite.T(), secret, *retrieved.TwoFactorSecret)
}

func (suite *UserRepositoryTestSuite) TestDisableTwoFactor_Success() {
	user := &models.User{
		Email:            "<EMAIL>",
		Username:         stringPtr("testuser"),
		PasswordHash:     "hashedpassword",
		Status:           models.UserStatusActive,
		TwoFactorEnabled: true,
		TwoFactorSecret:  stringPtr("secret"),
		Timezone:         "UTC",
		Language:         "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	err = suite.repo.DisableTwoFactor(suite.ctx, user.ID)
	
	assert.NoError(suite.T(), err)

	retrieved, err := suite.repo.GetByID(suite.ctx, user.ID)
	require.NoError(suite.T(), err)
	assert.False(suite.T(), retrieved.TwoFactorEnabled)
	assert.Nil(suite.T(), retrieved.TwoFactorSecret)
}

func (suite *UserRepositoryTestSuite) TestGetByIDs_Success() {
	user1 := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser1"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	user2 := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser2"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user1)
	require.NoError(suite.T(), err)
	
	err = suite.repo.Create(suite.ctx, user2)
	require.NoError(suite.T(), err)

	users, err := suite.repo.GetByIDs(suite.ctx, []uint{user1.ID, user2.ID})
	
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), users, 2)
}

func (suite *UserRepositoryTestSuite) TestExists_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	exists, err := suite.repo.Exists(suite.ctx, "<EMAIL>")
	
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), exists)

	exists, err = suite.repo.Exists(suite.ctx, "<EMAIL>")
	
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), exists)
}

func (suite *UserRepositoryTestSuite) TestExistsByUsername_Success() {
	user := &models.User{
		
		Email:        "<EMAIL>",
		Username:     stringPtr("testuser"),
		PasswordHash: "hashedpassword",
		Status:       models.UserStatusActive,
		
		Timezone:     "UTC",
		Language:     "en",
	}

	err := suite.repo.Create(suite.ctx, user)
	require.NoError(suite.T(), err)

	exists, err := suite.repo.ExistsByUsername(suite.ctx, "testuser")
	
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), exists)

	exists, err = suite.repo.ExistsByUsername(suite.ctx, "nonexistent")
	
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), exists)
}


func TestUserRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(UserRepositoryTestSuite))
}