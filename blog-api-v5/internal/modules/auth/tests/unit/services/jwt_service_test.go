package services

import (
	"context"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/mocks"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

type JWTServiceTestSuite struct {
	suite.Suite
	service             services.JWTService
	mockBlacklistRepo   *mocks.MockTokenBlacklistRepository
	secretKey           string
	ctx                 context.Context
}

func (suite *JWTServiceTestSuite) SetupSuite() {
	suite.secretKey = "test-secret-key-for-jwt-testing-should-be-very-long-and-secure"
	suite.mockBlacklistRepo = &mocks.MockTokenBlacklistRepository{}
	suite.service = services.NewJWTService(suite.secretKey, suite.mockBlacklistRepo)
	suite.ctx = context.Background()
}

func (suite *JWTServiceTestSuite) SetupTest() {
	// Reset mocks before each test
	suite.mockBlacklistRepo.Mock = mock.Mock{}
}

func (suite *JWTServiceTestSuite) TestGenerateTokenID_Success() {
	tokenID := suite.service.GenerateTokenID()
	
	assert.NotEmpty(suite.T(), tokenID)
	assert.True(suite.T(), len(tokenID) > 10) // Should be a reasonable length
	
	// Generate another and ensure they're different
	tokenID2 := suite.service.GenerateTokenID()
	assert.NotEqual(suite.T(), tokenID, tokenID2)
}

func (suite *JWTServiceTestSuite) TestGenerateAccessToken_Success() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
		{
			TenantID:  2,
			IsPrimary: false,
		},
	}
	
	token, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), token)
	
	// Verify token can be parsed
	parsedToken, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return []byte(suite.secretKey), nil
	})
	
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), parsedToken.Valid)
}

func (suite *JWTServiceTestSuite) TestGenerateRefreshToken_Success() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tokenFamily := "test-family-id"
	
	token, err := suite.service.GenerateRefreshToken(user, tokenFamily)
	
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), token)
	
	// Verify token can be parsed
	parsedToken, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return []byte(suite.secretKey), nil
	})
	
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), parsedToken.Valid)
}

func (suite *JWTServiceTestSuite) TestGenerateTokenPair_Success() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	tokenFamily := "test-family-id"
	
	accessToken, refreshToken, err := suite.service.GenerateTokenPair(user, tenantMemberships, tokenFamily)
	
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), accessToken)
	assert.NotEmpty(suite.T(), refreshToken)
	assert.NotEqual(suite.T(), accessToken, refreshToken)
}

func (suite *JWTServiceTestSuite) TestValidateToken_ValidToken() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	// Mock blacklist check
	suite.mockBlacklistRepo.On("Exists", suite.ctx, mock.AnythingOfType("string")).Return(false, nil)
	
	token, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	require.NoError(suite.T(), err)
	
	claims, err := suite.service.ValidateToken(suite.ctx, token)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), claims)
	assert.Equal(suite.T(), uint(1), claims.UserID)
	assert.Equal(suite.T(), "<EMAIL>", claims.Email)
}

func (suite *JWTServiceTestSuite) TestValidateToken_BlacklistedToken() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	token, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	require.NoError(suite.T(), err)
	
	// Mock blacklist check to return true (token is blacklisted)
	suite.mockBlacklistRepo.On("Exists", suite.ctx, mock.AnythingOfType("string")).Return(true, nil)
	
	claims, err := suite.service.ValidateToken(suite.ctx, token)
	
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), services.ErrTokenBlacklisted, err)
	assert.Nil(suite.T(), claims)
}

func (suite *JWTServiceTestSuite) TestValidateToken_ExpiredToken() {
	// Create a token that expires in the past
	now := time.Now()
	claims := &models.JWTClaims{
		UserID:   1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Type:     "access",
		RegisteredClaims: jwt.RegisteredClaims{
			Subject:   "1",
			IssuedAt:  jwt.NewNumericDate(now.Add(-2 * time.Hour)),
			ExpiresAt: jwt.NewNumericDate(now.Add(-1 * time.Hour)), // Expired
			NotBefore: jwt.NewNumericDate(now.Add(-2 * time.Hour)),
			Issuer:    "blog-api-v3",
			Audience:  []string{"blog-api-v3"},
		},
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(suite.secretKey))
	require.NoError(suite.T(), err)
	
	// Mock blacklist check
	suite.mockBlacklistRepo.On("Exists", suite.ctx, mock.AnythingOfType("string")).Return(false, nil)
	
	validatedClaims, err := suite.service.ValidateToken(suite.ctx, tokenString)
	
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), services.ErrTokenExpired, err)
	assert.Nil(suite.T(), validatedClaims)
}

func (suite *JWTServiceTestSuite) TestValidateToken_InvalidSignature() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	token, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	require.NoError(suite.T(), err)
	
	// Tamper with the token
	tamperedToken := token[:len(token)-5] + "XXXXX"
	
	claims, err := suite.service.ValidateToken(suite.ctx, tamperedToken)
	
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), claims)
}

func (suite *JWTServiceTestSuite) TestValidateAccessToken_Success() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	// Mock blacklist check
	suite.mockBlacklistRepo.On("Exists", suite.ctx, mock.AnythingOfType("string")).Return(false, nil)
	
	token, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	require.NoError(suite.T(), err)
	
	claims, err := suite.service.ValidateAccessToken(suite.ctx, token)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), claims)
	assert.Equal(suite.T(), "access", claims.Type)
}

func (suite *JWTServiceTestSuite) TestValidateAccessToken_WrongType() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tokenFamily := "test-family"
	
	// Mock blacklist check
	suite.mockBlacklistRepo.On("Exists", suite.ctx, mock.AnythingOfType("string")).Return(false, nil)
	
	// Generate refresh token instead of access token
	refreshToken, err := suite.service.GenerateRefreshToken(user, tokenFamily)
	require.NoError(suite.T(), err)
	
	claims, err := suite.service.ValidateAccessToken(suite.ctx, refreshToken)
	
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), services.ErrInvalidTokenType, err)
	assert.Nil(suite.T(), claims)
}

func (suite *JWTServiceTestSuite) TestValidateRefreshToken_Success() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tokenFamily := "test-family"
	
	// Mock blacklist check
	suite.mockBlacklistRepo.On("Exists", suite.ctx, mock.AnythingOfType("string")).Return(false, nil)
	
	token, err := suite.service.GenerateRefreshToken(user, tokenFamily)
	require.NoError(suite.T(), err)
	
	claims, err := suite.service.ValidateRefreshToken(suite.ctx, token)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), claims)
	assert.Equal(suite.T(), "refresh", claims.Type)
	assert.Equal(suite.T(), tokenFamily, claims.TokenFamily)
}

func (suite *JWTServiceTestSuite) TestValidateRefreshToken_WrongType() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	// Mock blacklist check
	suite.mockBlacklistRepo.On("Exists", suite.ctx, mock.AnythingOfType("string")).Return(false, nil)
	
	// Generate access token instead of refresh token
	accessToken, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	require.NoError(suite.T(), err)
	
	claims, err := suite.service.ValidateRefreshToken(suite.ctx, accessToken)
	
	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), services.ErrInvalidTokenType, err)
	assert.Nil(suite.T(), claims)
}

func (suite *JWTServiceTestSuite) TestRefreshAccessToken_Success() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	tokenFamily := "test-family"
	
	// Mock blacklist check
	suite.mockBlacklistRepo.On("Exists", suite.ctx, mock.AnythingOfType("string")).Return(false, nil)
	
	refreshToken, err := suite.service.GenerateRefreshToken(user, tokenFamily)
	require.NoError(suite.T(), err)
	
	newAccessToken, err := suite.service.RefreshAccessToken(suite.ctx, refreshToken, user, tenantMemberships)
	
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), newAccessToken)
	
	// Validate the new access token
	claims, err := suite.service.ValidateAccessToken(suite.ctx, newAccessToken)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), claims)
	assert.Equal(suite.T(), uint(1), claims.UserID)
}

func (suite *JWTServiceTestSuite) TestExtractClaims_Success() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	token, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	require.NoError(suite.T(), err)
	
	claims, err := suite.service.ExtractClaims(token)
	
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), claims)
	assert.Equal(suite.T(), uint(1), claims.UserID)
	assert.Equal(suite.T(), "<EMAIL>", claims.Email)
	assert.Equal(suite.T(), "testuser", claims.Username)
}

func (suite *JWTServiceTestSuite) TestExtractClaims_InvalidToken() {
	claims, err := suite.service.ExtractClaims("invalid.token.here")
	
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), claims)
}

func (suite *JWTServiceTestSuite) TestTokenExpiry_AccessToken() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
	}
	
	before := time.Now()
	token, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	require.NoError(suite.T(), err)
	after := time.Now()
	
	claims, err := suite.service.ExtractClaims(token)
	require.NoError(suite.T(), err)
	
	// Access token should expire in about 15 minutes (default)
	expectedExpiry := before.Add(15 * time.Minute)
	actualExpiry := claims.ExpiresAt.Time
	
	assert.True(suite.T(), actualExpiry.After(expectedExpiry.Add(-1*time.Minute)))
	assert.True(suite.T(), actualExpiry.Before(after.Add(16*time.Minute)))
}

func (suite *JWTServiceTestSuite) TestTokenExpiry_RefreshToken() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tokenFamily := "test-family"
	
	before := time.Now()
	token, err := suite.service.GenerateRefreshToken(user, tokenFamily)
	require.NoError(suite.T(), err)
	after := time.Now()
	
	claims, err := suite.service.ExtractClaims(token)
	require.NoError(suite.T(), err)
	
	// Refresh token should expire in about 7 days (default)
	expectedExpiry := before.Add(7 * 24 * time.Hour)
	actualExpiry := claims.ExpiresAt.Time
	
	assert.True(suite.T(), actualExpiry.After(expectedExpiry.Add(-1*time.Minute)))
	assert.True(suite.T(), actualExpiry.Before(after.Add(7*24*time.Hour+1*time.Minute)))
}

func (suite *JWTServiceTestSuite) TestTenantMemberships_InClaims() {
	user := &userModels.User{
		ID:       1,
		Email:    "<EMAIL>",
		Username: "testuser",
		Status:   userModels.UserStatusActive,
	}
	
	tenantMemberships := []*models.TenantMembership{
		{
			TenantID:  1,
			IsPrimary: true,
		},
		{
			TenantID:  2,
			IsPrimary: false,
		},
		{
			TenantID:  3,
			IsPrimary: false,
		},
	}
	
	token, err := suite.service.GenerateAccessToken(user, tenantMemberships)
	require.NoError(suite.T(), err)
	
	claims, err := suite.service.ExtractClaims(token)
	require.NoError(suite.T(), err)
	
	assert.Len(suite.T(), claims.TenantMemberships, 3)
	
	// Check primary tenant
	var primaryFound bool
	for _, membership := range claims.TenantMemberships {
		if membership.IsPrimary {
			assert.Equal(suite.T(), uint(1), membership.TenantID)
			primaryFound = true
			break
		}
	}
	assert.True(suite.T(), primaryFound, "Primary tenant membership not found")
}

func TestJWTServiceTestSuite(t *testing.T) {
	suite.Run(t, new(JWTServiceTestSuite))
}