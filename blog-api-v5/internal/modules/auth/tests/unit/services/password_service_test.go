package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
)

type PasswordServiceTestSuite struct {
	suite.Suite
	service services.PasswordService
}

func (suite *PasswordServiceTestSuite) SetupSuite() {
	config := &services.PasswordConfig{
		MinLength:            8,
		RequireUppercase:     true,
		RequireLowercase:     true,
		RequireNumbers:       true,
		RequireSpecialChars:  true,
		MaxLength:            128,
		MinUniqueChars:       4,
		DisallowCommon:       true,
		DisallowRepeating:    true,
		DisallowSequential:   true,
		CheckCompromised:     false, // Disable for tests
	}
	
	suite.service = services.NewPasswordService(config)
}

func (suite *PasswordServiceTestSuite) TestHashPassword_Success() {
	password := "StrongPassword123!"
	
	hashedPassword, err := suite.service.HashPassword(password)
	
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), hashedPassword)
	assert.NotEqual(suite.T(), password, hashedPassword)
	assert.True(suite.T(), len(hashedPassword) > 0)
}

func (suite *PasswordServiceTestSuite) TestHashPassword_EmptyPassword() {
	hashedPassword, err := suite.service.HashPassword("")
	
	assert.Error(suite.T(), err)
	assert.Empty(suite.T(), hashedPassword)
}

func (suite *PasswordServiceTestSuite) TestVerifyPassword_Success() {
	password := "StrongPassword123!"
	
	hashedPassword, err := suite.service.HashPassword(password)
	require.NoError(suite.T(), err)
	
	isValid := suite.service.VerifyPassword(password, hashedPassword)
	
	assert.True(suite.T(), isValid)
}

func (suite *PasswordServiceTestSuite) TestVerifyPassword_WrongPassword() {
	password := "StrongPassword123!"
	wrongPassword := "WrongPassword456!"
	
	hashedPassword, err := suite.service.HashPassword(password)
	require.NoError(suite.T(), err)
	
	isValid := suite.service.VerifyPassword(wrongPassword, hashedPassword)
	
	assert.False(suite.T(), isValid)
}

func (suite *PasswordServiceTestSuite) TestVerifyPassword_EmptyPassword() {
	hashedPassword := "$2a$10$somevalidhash"
	
	isValid := suite.service.VerifyPassword("", hashedPassword)
	
	assert.False(suite.T(), isValid)
}

func (suite *PasswordServiceTestSuite) TestVerifyPassword_EmptyHash() {
	password := "StrongPassword123!"
	
	isValid := suite.service.VerifyPassword(password, "")
	
	assert.False(suite.T(), isValid)
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_ValidPassword() {
	password := "StrongPassword123!"
	
	err := suite.service.ValidatePassword(password)
	
	assert.NoError(suite.T(), err)
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_TooShort() {
	password := "Short1!"
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "at least 8 characters")
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_TooLong() {
	password := string(make([]byte, 130)) // 130 characters
	for i := range password {
		password = password[:i] + "a" + password[i+1:]
	}
	password += "A1!"
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "maximum")
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_NoUppercase() {
	password := "strongpassword123!"
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "uppercase")
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_NoLowercase() {
	password := "STRONGPASSWORD123!"
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "lowercase")
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_NoNumbers() {
	password := "StrongPassword!"
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "number")
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_NoSpecialChars() {
	password := "StrongPassword123"
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "special")
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_CommonPassword() {
	password := "password123!" // Common password
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "common")
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_RepeatingChars() {
	password := "Aaaaaaa1!" // Too many repeating characters
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "repeating")
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_SequentialChars() {
	password := "Abcdefg1!" // Sequential characters
	
	err := suite.service.ValidatePassword(password)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "sequential")
}

func (suite *PasswordServiceTestSuite) TestGenerateRandomPassword_Success() {
	password, err := suite.service.GenerateRandomPassword(12)
	
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), password, 12)
	
	// Verify generated password meets requirements
	err = suite.service.ValidatePassword(password)
	assert.NoError(suite.T(), err)
}

func (suite *PasswordServiceTestSuite) TestGenerateRandomPassword_MinLength() {
	password, err := suite.service.GenerateRandomPassword(8)
	
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), password, 8)
}

func (suite *PasswordServiceTestSuite) TestGenerateRandomPassword_MaxLength() {
	password, err := suite.service.GenerateRandomPassword(64)
	
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), password, 64)
}

func (suite *PasswordServiceTestSuite) TestGenerateRandomPassword_TooShort() {
	password, err := suite.service.GenerateRandomPassword(4)
	
	assert.Error(suite.T(), err)
	assert.Empty(suite.T(), password)
}

func (suite *PasswordServiceTestSuite) TestGenerateRandomPassword_TooLong() {
	password, err := suite.service.GenerateRandomPassword(200)
	
	assert.Error(suite.T(), err)
	assert.Empty(suite.T(), password)
}

func (suite *PasswordServiceTestSuite) TestGenerateRandomPassword_Uniqueness() {
	// Generate multiple passwords and ensure they're different
	passwords := make([]string, 10)
	
	for i := 0; i < 10; i++ {
		password, err := suite.service.GenerateRandomPassword(16)
		require.NoError(suite.T(), err)
		passwords[i] = password
	}
	
	// Check that all passwords are unique
	passwordMap := make(map[string]bool)
	for _, password := range passwords {
		assert.False(suite.T(), passwordMap[password], "Generated duplicate password: %s", password)
		passwordMap[password] = true
	}
}

func (suite *PasswordServiceTestSuite) TestPasswordStrength_VeryWeak() {
	password := "123"
	
	strength := services.PasswordStrength(password)
	
	assert.Equal(suite.T(), "very-weak", strength)
}

func (suite *PasswordServiceTestSuite) TestPasswordStrength_Weak() {
	password := "password"
	
	strength := services.PasswordStrength(password)
	
	assert.Equal(suite.T(), "weak", strength)
}

func (suite *PasswordServiceTestSuite) TestPasswordStrength_Fair() {
	password := "Password1"
	
	strength := services.PasswordStrength(password)
	
	assert.Equal(suite.T(), "fair", strength)
}

func (suite *PasswordServiceTestSuite) TestPasswordStrength_Good() {
	password := "Password123!"
	
	strength := services.PasswordStrength(password)
	
	assert.Equal(suite.T(), "good", strength)
}

func (suite *PasswordServiceTestSuite) TestPasswordStrength_Strong() {
	password := "Str0ng!P@ssw0rd#2023"
	
	strength := services.PasswordStrength(password)
	
	assert.Equal(suite.T(), "strong", strength)
}

func (suite *PasswordServiceTestSuite) TestHashPassword_ConsistentOutput() {
	password := "TestPassword123!"
	
	hash1, err1 := suite.service.HashPassword(password)
	hash2, err2 := suite.service.HashPassword(password)
	
	assert.NoError(suite.T(), err1)
	assert.NoError(suite.T(), err2)
	
	// Hashes should be different due to salt
	assert.NotEqual(suite.T(), hash1, hash2)
	
	// But both should verify correctly
	assert.True(suite.T(), suite.service.VerifyPassword(password, hash1))
	assert.True(suite.T(), suite.service.VerifyPassword(password, hash2))
}

func (suite *PasswordServiceTestSuite) TestValidatePassword_EdgeCases() {
	testCases := []struct {
		name        string
		password    string
		shouldError bool
		errorMsg    string
	}{
		{
			name:        "Exactly minimum length",
			password:    "Pass123!",
			shouldError: false,
		},
		{
			name:        "Exactly maximum length",
			password:    "Pass123!" + string(make([]byte, 120)), // 128 chars total
			shouldError: false,
		},
		{
			name:        "Unicode characters",
			password:    "Pássw0rd!",
			shouldError: false,
		},
		{
			name:        "Multiple special chars",
			password:    "P@ssw0rd!#$%",
			shouldError: false,
		},
		{
			name:        "Whitespace",
			password:    "Pass 123!",
			shouldError: false,
		},
	}
	
	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			// Adjust test password to meet minimum length if needed
			if len(tc.password) < 8 {
				tc.password += "123456789"[:8-len(tc.password)]
			}
			
			// Ensure it has required character types for valid cases
			if !tc.shouldError && tc.name != "Exactly maximum length" {
				if tc.name == "Exactly minimum length" {
					// Already valid
				} else if tc.name == "Unicode characters" {
					tc.password = "Pássw0rd123!"
				} else if tc.name == "Multiple special chars" {
					tc.password = "P@ssw0rd123!#$%"
				} else if tc.name == "Whitespace" {
					tc.password = "Pass Word123!"
				}
			}
			
			err := suite.service.ValidatePassword(tc.password)
			
			if tc.shouldError {
				assert.Error(t, err)
				if tc.errorMsg != "" {
					assert.Contains(t, err.Error(), tc.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPasswordServiceTestSuite(t *testing.T) {
	suite.Run(t, new(PasswordServiceTestSuite))
}