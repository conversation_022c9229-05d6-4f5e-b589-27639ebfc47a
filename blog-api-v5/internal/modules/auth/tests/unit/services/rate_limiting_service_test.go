package services

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/mocks"
)

type RateLimitingServiceTestSuite struct {
	suite.Suite
	service                 services.RateLimitingService
	mockLoginAttemptRepo    *mocks.MockLoginAttemptRepository
	ctx                     context.Context
	testUserID              uint
	testIP                  string
}

func (suite *RateLimitingServiceTestSuite) SetupSuite() {
	suite.mockLoginAttemptRepo = &mocks.MockLoginAttemptRepository{}
	suite.service = services.NewRateLimitingService(suite.mockLoginAttemptRepo)
	suite.ctx = context.Background()
	suite.testUserID = 1
	suite.testIP = "127.0.0.1"
}

func (suite *RateLimitingServiceTestSuite) SetupTest() {
	// Reset mocks before each test
	suite.mockLoginAttemptRepo.Mock = mock.Mock{}
}

func (suite *RateLimitingServiceTestSuite) TestIsAccountLocked_NotLocked() {
	// Mock: Account is not locked
	suite.mockLoginAttemptRepo.On("IsAccountLocked", suite.ctx, suite.testUserID).Return(false, nil)

	isLocked, err := suite.service.IsAccountLocked(suite.ctx, suite.testUserID)

	assert.NoError(suite.T(), err)
	assert.False(suite.T(), isLocked)
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestIsAccountLocked_Locked() {
	// Mock: Account is locked
	suite.mockLoginAttemptRepo.On("IsAccountLocked", suite.ctx, suite.testUserID).Return(true, nil)

	isLocked, err := suite.service.IsAccountLocked(suite.ctx, suite.testUserID)

	assert.NoError(suite.T(), err)
	assert.True(suite.T(), isLocked)
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestIsAccountLocked_RepositoryError() {
	// Mock: Repository returns error
	suite.mockLoginAttemptRepo.On("IsAccountLocked", suite.ctx, suite.testUserID).Return(false, errors.New("database error"))

	isLocked, err := suite.service.IsAccountLocked(suite.ctx, suite.testUserID)

	assert.Error(suite.T(), err)
	assert.False(suite.T(), isLocked)
	assert.Contains(suite.T(), err.Error(), "database error")
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestRecordFailedAttempt_Success() {
	// Mock: Successfully record failed attempt
	suite.mockLoginAttemptRepo.On("Create", suite.ctx, mock.MatchedBy(func(attempt *models.LoginAttempt) bool {
		return attempt.UserID != nil && *attempt.UserID == suite.testUserID &&
			attempt.IPAddress == suite.testIP &&
			attempt.Result == models.AttemptResultFailure
	})).Return(nil)

	err := suite.service.RecordFailedAttempt(suite.ctx, suite.testUserID, suite.testIP, "Invalid credentials", "Mozilla/5.0")

	assert.NoError(suite.T(), err)
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestRecordFailedAttempt_RepositoryError() {
	// Mock: Repository returns error
	suite.mockLoginAttemptRepo.On("Create", suite.ctx, mock.AnythingOfType("*models.LoginAttempt")).Return(errors.New("insert error"))

	err := suite.service.RecordFailedAttempt(suite.ctx, suite.testUserID, suite.testIP, "Invalid credentials", "Mozilla/5.0")

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "insert error")
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestRecordSuccessfulLogin_Success() {
	// Mock: Successfully record successful login
	suite.mockLoginAttemptRepo.On("RecordSuccessfulLogin", suite.ctx, suite.testUserID, suite.testIP, "Mozilla/5.0").Return(nil)

	err := suite.service.RecordSuccessfulLogin(suite.ctx, suite.testUserID, suite.testIP, "Mozilla/5.0")

	assert.NoError(suite.T(), err)
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestRecordSuccessfulLogin_RepositoryError() {
	// Mock: Repository returns error
	suite.mockLoginAttemptRepo.On("RecordSuccessfulLogin", suite.ctx, suite.testUserID, suite.testIP, "Mozilla/5.0").Return(errors.New("record error"))

	err := suite.service.RecordSuccessfulLogin(suite.ctx, suite.testUserID, suite.testIP, "Mozilla/5.0")

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "record error")
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestResetFailedAttempts_Success() {
	// Mock: Successfully reset failed attempts
	suite.mockLoginAttemptRepo.On("UnlockAccount", suite.ctx, suite.testUserID).Return(nil)

	err := suite.service.ResetFailedAttempts(suite.ctx, suite.testUserID)

	assert.NoError(suite.T(), err)
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestResetFailedAttempts_RepositoryError() {
	// Mock: Repository returns error
	suite.mockLoginAttemptRepo.On("UnlockAccount", suite.ctx, suite.testUserID).Return(errors.New("unlock error"))

	err := suite.service.ResetFailedAttempts(suite.ctx, suite.testUserID)

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "unlock error")
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestGetFailedAttemptCount_Success() {
	expectedCount := int64(3)
	timeWindow := 30 * time.Minute

	// Mock: Return failed attempt count
	suite.mockLoginAttemptRepo.On("CountFailedAttemptsByUser", suite.ctx, suite.testUserID, timeWindow).Return(expectedCount, nil)

	count, err := suite.service.GetFailedAttemptCount(suite.ctx, suite.testUserID, timeWindow)

	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), expectedCount, count)
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestGetFailedAttemptCount_RepositoryError() {
	timeWindow := 30 * time.Minute

	// Mock: Repository returns error
	suite.mockLoginAttemptRepo.On("CountFailedAttemptsByUser", suite.ctx, suite.testUserID, timeWindow).Return(int64(0), errors.New("count error"))

	count, err := suite.service.GetFailedAttemptCount(suite.ctx, suite.testUserID, timeWindow)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), int64(0), count)
	assert.Contains(suite.T(), err.Error(), "count error")
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestGetFailedAttemptCount_ZeroCount() {
	timeWindow := 30 * time.Minute

	// Mock: Return zero failed attempts
	suite.mockLoginAttemptRepo.On("CountFailedAttemptsByUser", suite.ctx, suite.testUserID, timeWindow).Return(int64(0), nil)

	count, err := suite.service.GetFailedAttemptCount(suite.ctx, suite.testUserID, timeWindow)

	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(0), count)
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestRecordFailedAttempt_WithNilUserID() {
	// Test recording failed attempt for non-existing user (nil userID)
	var nilUserID *uint = nil

	// Mock: Successfully record failed attempt with nil user ID
	suite.mockLoginAttemptRepo.On("Create", suite.ctx, mock.MatchedBy(func(attempt *models.LoginAttempt) bool {
		return attempt.UserID == nil &&
			attempt.IPAddress == suite.testIP &&
			attempt.Result == models.AttemptResultFailure &&
			attempt.Email == "<EMAIL>"
	})).Return(nil)

	err := suite.service.RecordFailedAttempt(suite.ctx, 0, suite.testIP, "User not found", "Mozilla/5.0")

	assert.NoError(suite.T(), err)
	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestIntegration_FailedAttemptScenario() {
	// This test simulates a real scenario where multiple failed attempts occur
	userID := uint(123)
	ipAddress := "*************"
	timeWindow := 15 * time.Minute

	// 1. Initially account is not locked
	suite.mockLoginAttemptRepo.On("IsAccountLocked", suite.ctx, userID).Return(false, nil).Once()

	isLocked, err := suite.service.IsAccountLocked(suite.ctx, userID)
	require.NoError(suite.T(), err)
	assert.False(suite.T(), isLocked)

	// 2. Record first failed attempt
	suite.mockLoginAttemptRepo.On("Create", suite.ctx, mock.MatchedBy(func(attempt *models.LoginAttempt) bool {
		return *attempt.UserID == userID && attempt.Result == models.AttemptResultFailure
	})).Return(nil).Once()

	err = suite.service.RecordFailedAttempt(suite.ctx, userID, ipAddress, "Wrong password", "Browser")
	require.NoError(suite.T(), err)

	// 3. Check failed attempt count
	suite.mockLoginAttemptRepo.On("CountFailedAttemptsByUser", suite.ctx, userID, timeWindow).Return(int64(1), nil).Once()

	count, err := suite.service.GetFailedAttemptCount(suite.ctx, userID, timeWindow)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(1), count)

	// 4. Record second failed attempt
	suite.mockLoginAttemptRepo.On("Create", suite.ctx, mock.MatchedBy(func(attempt *models.LoginAttempt) bool {
		return *attempt.UserID == userID && attempt.Result == models.AttemptResultFailure
	})).Return(nil).Once()

	err = suite.service.RecordFailedAttempt(suite.ctx, userID, ipAddress, "Wrong password", "Browser")
	require.NoError(suite.T(), err)

	// 5. Now account might be locked (depending on business rules)
	suite.mockLoginAttemptRepo.On("IsAccountLocked", suite.ctx, userID).Return(true, nil).Once()

	isLocked, err = suite.service.IsAccountLocked(suite.ctx, userID)
	require.NoError(suite.T(), err)
	assert.True(suite.T(), isLocked)

	// 6. Successful login should reset the failed attempts
	suite.mockLoginAttemptRepo.On("RecordSuccessfulLogin", suite.ctx, userID, ipAddress, "Browser").Return(nil).Once()

	err = suite.service.RecordSuccessfulLogin(suite.ctx, userID, ipAddress, "Browser")
	require.NoError(suite.T(), err)

	// 7. Reset failed attempts
	suite.mockLoginAttemptRepo.On("UnlockAccount", suite.ctx, userID).Return(nil).Once()

	err = suite.service.ResetFailedAttempts(suite.ctx, userID)
	require.NoError(suite.T(), err)

	// 8. Account should no longer be locked
	suite.mockLoginAttemptRepo.On("IsAccountLocked", suite.ctx, userID).Return(false, nil).Once()

	isLocked, err = suite.service.IsAccountLocked(suite.ctx, userID)
	require.NoError(suite.T(), err)
	assert.False(suite.T(), isLocked)

	suite.mockLoginAttemptRepo.AssertExpectations(suite.T())
}

func (suite *RateLimitingServiceTestSuite) TestRateLimiting_EdgeCases() {
	testCases := []struct {
		name           string
		userID         uint
		ipAddress      string
		reason         string
		userAgent      string
		expectedError  bool
	}{
		{
			name:          "Empty IP address",
			userID:        1,
			ipAddress:     "",
			reason:        "Invalid credentials",
			userAgent:     "Browser",
			expectedError: false, // Should still work
		},
		{
			name:          "Empty user agent",
			userID:        1,
			ipAddress:     "127.0.0.1",
			reason:        "Invalid credentials",
			userAgent:     "",
			expectedError: false, // Should still work
		},
		{
			name:          "Empty reason",
			userID:        1,
			ipAddress:     "127.0.0.1",
			reason:        "",
			userAgent:     "Browser",
			expectedError: false, // Should still work
		},
		{
			name:          "Very long reason",
			userID:        1,
			ipAddress:     "127.0.0.1",
			reason:        string(make([]byte, 1000)) + "very long reason",
			userAgent:     "Browser",
			expectedError: false, // Should truncate or handle gracefully
		},
	}

	for _, tc := range testCases {
		suite.T().Run(tc.name, func(t *testing.T) {
			// Reset mock for each test case
			suite.mockLoginAttemptRepo.Mock = mock.Mock{}

			if tc.expectedError {
				suite.mockLoginAttemptRepo.On("Create", suite.ctx, mock.AnythingOfType("*models.LoginAttempt")).Return(errors.New("validation error"))
			} else {
				suite.mockLoginAttemptRepo.On("Create", suite.ctx, mock.AnythingOfType("*models.LoginAttempt")).Return(nil)
			}

			err := suite.service.RecordFailedAttempt(suite.ctx, tc.userID, tc.ipAddress, tc.reason, tc.userAgent)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestRateLimitingServiceTestSuite(t *testing.T) {
	suite.Run(t, new(RateLimitingServiceTestSuite))
}