package adapters

// This file is disabled - contains blog export adapter functionality that is not yet implemented
// All code is commented out to prevent build errors

/*
import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/export"
)

// BlogPostExportAdapter implements the Exportable interface for blog posts
type BlogPostExportAdapter struct {
	postRepo repositories.BlogPostRepository
}

// NewBlogPostExportAdapter creates a new blog post export adapter
func NewBlogPostExportAdapter(postRepo repositories.BlogPostRepository) *BlogPostExportAdapter {
	return &BlogPostExportAdapter{
		postRepo: postRepo,
	}
}

// GetExportHeaders returns the headers for blog post export
func (a *BlogPostExportAdapter) GetExportHeaders() []string {
	return []string{
		"id",
		"title",
		"slug",
		"excerpt",
		"content",
		"type",
		"status",
		"workflow_state",
		"category_id",
		"category_name",
		"author_id",
		"website_id",
		"tenant_id",
		"view_count",
		"reading_time",
		"is_featured",
		"allow_comments",
		"meta_title",
		"meta_description",
		"meta_keywords",
		"featured_image",
		"scheduled_at",
		"published_at",
		"created_at",
		"updated_at",
		"tags",
	}
}

// GetExportData returns blog post data for export
func (a *BlogPostExportAdapter) GetExportData(ctx context.Context, filters map[string]interface{}, offset, limit int) ([][]interface{}, error) {
	// Convert filters to BlogPostFilter
	filter := a.convertFilters(filters)

	// Apply pagination
	filter.Page = (offset / limit) + 1
	filter.PageSize = limit

	// Get posts
	posts, _, err := a.postRepo.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get posts: %w", err)
	}

	// Convert to export data
	var data [][]interface{}
	for _, post := range posts {
		row := a.postToRow(&post)
		data = append(data, row)
	}

	return data, nil
}

// GetTotalCount returns the total count of exportable records
func (a *BlogPostExportAdapter) GetTotalCount(ctx context.Context, filters map[string]interface{}) (int, error) {
	// Convert filters to BlogPostFilter
	filter := a.convertFilters(filters)

	// Get total count
	_, total, err := a.postRepo.List(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to get total count: %w", err)
	}

	return int(total), nil
}

// ValidateExportPermission validates if user can export posts
func (a *BlogPostExportAdapter) ValidateExportPermission(ctx context.Context, userID, tenantID uint) error {
	// TODO: Implement permission validation
	// For now, allow all authenticated users with tenant access
	if userID == 0 || tenantID == 0 {
		return fmt.Errorf("user and tenant required")
	}

	return nil
}

// convertFilters converts generic filters to BlogPostFilter
func (a *BlogPostExportAdapter) convertFilters(filters map[string]interface{}) *models.BlogPostFilter {
	filter := &models.BlogPostFilter{}

	if tenantID, ok := filters["tenant_id"].(uint); ok {
		filter.TenantID = tenantID
	} else if tenantIDFloat, ok := filters["tenant_id"].(float64); ok {
		filter.TenantID = uint(tenantIDFloat)
	}

	if websiteID, ok := filters["website_id"].(uint); ok {
		filter.WebsiteID = websiteID
	} else if websiteIDFloat, ok := filters["website_id"].(float64); ok {
		filter.WebsiteID = uint(websiteIDFloat)
	}

	if authorID, ok := filters["author_id"].(uint); ok {
		filter.UserID = authorID
	} else if authorIDFloat, ok := filters["author_id"].(float64); ok {
		filter.UserID = uint(authorIDFloat)
	}

	if categoryID, ok := filters["category_id"].(uint); ok {
		filter.CategoryID = &categoryID
	} else if categoryIDFloat, ok := filters["category_id"].(float64); ok {
		catID := uint(categoryIDFloat)
		filter.CategoryID = &catID
	}

	if postType, ok := filters["type"].(string); ok {
		filter.Type = postType
	}

	if status, ok := filters["status"].(string); ok {
		filter.Status = status
	}

	if featured, ok := filters["is_featured"].(bool); ok {
		filter.IsFeatured = &featured
	}

	if search, ok := filters["search"].(string); ok {
		filter.Search = search
	}

	if dateFromStr, ok := filters["date_from"].(string); ok {
		if dateFrom, err := time.Parse(time.RFC3339, dateFromStr); err == nil {
			filter.DateFrom = &dateFrom
		}
	}

	if dateToStr, ok := filters["date_to"].(string); ok {
		if dateTo, err := time.Parse(time.RFC3339, dateToStr); err == nil {
			filter.DateTo = &dateTo
		}
	}

	if sortBy, ok := filters["sort_by"].(string); ok {
		filter.SortBy = sortBy
	}

	if sortOrder, ok := filters["sort_order"].(string); ok {
		filter.SortOrder = sortOrder
	}

	return filter
}

// postToRow converts a blog post to export row
func (a *BlogPostExportAdapter) postToRow(post *models.BlogPost) []interface{} {
	// Get category name
	categoryName := ""
	if post.Category != nil {
		categoryName = post.Category.Name
	}

	// Get tags as comma-separated string
	var tagNames []string
	for _, tag := range post.Tags {
		tagNames = append(tagNames, tag.Name)
	}
	tags := ""
	if len(tagNames) > 0 {
		tags = fmt.Sprintf(`"%s"`, fmt.Sprintf("%v", tagNames))
	}

	// Format dates
	var scheduledAt, publishedAt interface{}
	if post.ScheduledAt != nil {
		scheduledAt = post.ScheduledAt.Format(time.RFC3339)
	}
	if post.PublishedAt != nil {
		publishedAt = post.PublishedAt.Format(time.RFC3339)
	}

	return []interface{}{
		post.ID,
		post.Title,
		post.Slug,
		post.Excerpt,
		post.Content,
		string(post.Type),
		string(post.Status),
		post.WorkflowState,
		post.CategoryID,
		categoryName,
		post.UserID,
		post.WebsiteID,
		post.TenantID,
		post.ViewCount,
		post.ReadingTime,
		post.IsFeatured,
		post.AllowComments,
		post.MetaTitle,
		post.MetaDescription,
		post.MetaKeywords,
		post.FeaturedImage,
		scheduledAt,
		publishedAt,
		post.CreatedAt.Format(time.RFC3339),
		post.UpdatedAt.Format(time.RFC3339),
		tags,
	}
}

// BlogPostImportAdapter implements the Importable interface for blog posts
type BlogPostImportAdapter struct {
	postRepo     repositories.BlogPostRepository
	categoryRepo repositories.BlogCategoryRepository
	tagRepo      repositories.BlogTagRepository
}

// NewBlogPostImportAdapter creates a new blog post import adapter
func NewBlogPostImportAdapter(
	postRepo repositories.BlogPostRepository,
	categoryRepo repositories.BlogCategoryRepository,
	tagRepo repositories.BlogTagRepository,
) *BlogPostImportAdapter {
	return &BlogPostImportAdapter{
		postRepo:     postRepo,
		categoryRepo: categoryRepo,
		tagRepo:      tagRepo,
	}
}

// GetImportHeaders returns the expected headers for blog post import
func (a *BlogPostImportAdapter) GetImportHeaders() []string {
	return []string{
		"title",
		"slug",
		"excerpt",
		"content",
		"type",
		"status",
		"category_name",
		"author_id",
		"website_id",
		"tenant_id",
		"is_featured",
		"allow_comments",
		"meta_title",
		"meta_description",
		"meta_keywords",
		"featured_image",
		"scheduled_at",
		"published_at",
		"tags",
	}
}

// ValidateImportData validates import data before processing
func (a *BlogPostImportAdapter) ValidateImportData(ctx context.Context, headers []string, data [][]interface{}) error {
	// Check required headers
	requiredHeaders := []string{"title", "content", "author_id", "website_id", "tenant_id"}
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[header] = i
	}

	for _, required := range requiredHeaders {
		if _, exists := headerMap[required]; !exists {
			return fmt.Errorf("required header missing: %s", required)
		}
	}

	// Validate each row
	for rowIndex, row := range data {
		if len(row) != len(headers) {
			return fmt.Errorf("row %d: column count mismatch", rowIndex+1)
		}

		// Validate required fields
		for _, required := range requiredHeaders {
			colIndex := headerMap[required]
			if row[colIndex] == nil || row[colIndex] == "" {
				return fmt.Errorf("row %d: required field '%s' is empty", rowIndex+1, required)
			}
		}

		// Validate data types
		if err := a.validateRowDataTypes(rowIndex+1, headers, row); err != nil {
			return err
		}
	}

	return nil
}

// ProcessImportData processes validated import data
func (a *BlogPostImportAdapter) ProcessImportData(ctx context.Context, headers []string, data [][]interface{}, options map[string]interface{}) (*export.ImportResult, error) {
	result := &export.ImportResult{
		TotalRows:    len(data),
		SuccessCount: 0,
		ErrorCount:   0,
		Errors:       []export.ImportError{},
	}

	// Create header map for easy access
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[header] = i
	}

	// Process each row
	for rowIndex, row := range data {
		if err := a.processImportRow(ctx, rowIndex+1, headerMap, row, options); err != nil {
			result.ErrorCount++
			result.Errors = append(result.Errors, export.ImportError{
				Row:     rowIndex + 1,
				Message: err.Error(),
			})
		} else {
			result.SuccessCount++
		}
	}

	return result, nil
}

// ValidateImportPermission validates if user can import posts
func (a *BlogPostImportAdapter) ValidateImportPermission(ctx context.Context, userID, tenantID uint) error {
	// TODO: Implement permission validation
	// For now, allow all authenticated users with tenant access
	if userID == 0 || tenantID == 0 {
		return fmt.Errorf("user and tenant required")
	}

	return nil
}

// validateRowDataTypes validates data types for a single row
func (a *BlogPostImportAdapter) validateRowDataTypes(rowNum int, headers []string, row []interface{}) error {
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[header] = i
	}

	// Validate numeric fields
	numericFields := []string{"author_id", "website_id", "tenant_id"}
	for _, field := range numericFields {
		if colIndex, exists := headerMap[field]; exists && row[colIndex] != nil {
			if _, err := strconv.ParseUint(fmt.Sprintf("%v", row[colIndex]), 10, 32); err != nil {
				return fmt.Errorf("row %d: invalid %s format", rowNum, field)
			}
		}
	}

	// Validate boolean fields
	boolFields := []string{"is_featured", "allow_comments"}
	for _, field := range boolFields {
		if colIndex, exists := headerMap[field]; exists && row[colIndex] != nil {
			if _, err := strconv.ParseBool(fmt.Sprintf("%v", row[colIndex])); err != nil {
				return fmt.Errorf("row %d: invalid %s format", rowNum, field)
			}
		}
	}

	// Validate date fields
	dateFields := []string{"scheduled_at", "published_at"}
	for _, field := range dateFields {
		if colIndex, exists := headerMap[field]; exists && row[colIndex] != nil && row[colIndex] != "" {
			if _, err := time.Parse(time.RFC3339, fmt.Sprintf("%v", row[colIndex])); err != nil {
				return fmt.Errorf("row %d: invalid %s format (expected RFC3339)", rowNum, field)
			}
		}
	}

	return nil
}

// processImportRow processes a single import row
func (a *BlogPostImportAdapter) processImportRow(ctx context.Context, rowNum int, headerMap map[string]int, row []interface{}, options map[string]interface{}) error {
	// Create blog post from row data
	post := &models.BlogPost{}

	// Set basic fields
	post.Title = fmt.Sprintf("%v", row[headerMap["title"]])

	if slugIndex, exists := headerMap["slug"]; exists && row[slugIndex] != nil && row[slugIndex] != "" {
		post.Slug = fmt.Sprintf("%v", row[slugIndex])
	}

	if excerptIndex, exists := headerMap["excerpt"]; exists && row[excerptIndex] != nil {
		post.Excerpt = fmt.Sprintf("%v", row[excerptIndex])
	}

	post.Content = fmt.Sprintf("%v", row[headerMap["content"]])

	// Set type and status
	if typeIndex, exists := headerMap["type"]; exists && row[typeIndex] != nil {
		postType := fmt.Sprintf("%v", row[typeIndex])
		post.Type = models.BlogPostType(postType)
	} else {
		post.Type = models.BlogPostTypePost
	}

	if statusIndex, exists := headerMap["status"]; exists && row[statusIndex] != nil {
		status := fmt.Sprintf("%v", row[statusIndex])
		post.Status = models.BlogPostStatus(status)
	} else {
		post.Status = models.BlogPostStatusDraft
	}

	// Set IDs
	authorID, _ := strconv.ParseUint(fmt.Sprintf("%v", row[headerMap["author_id"]]), 10, 32)
	post.UserID = uint(authorID)

	websiteID, _ := strconv.ParseUint(fmt.Sprintf("%v", row[headerMap["website_id"]]), 10, 32)
	post.WebsiteID = uint(websiteID)

	tenantID, _ := strconv.ParseUint(fmt.Sprintf("%v", row[headerMap["tenant_id"]]), 10, 32)
	post.TenantID = uint(tenantID)

	// Set boolean fields
	if featuredIndex, exists := headerMap["is_featured"]; exists && row[featuredIndex] != nil {
		featured, _ := strconv.ParseBool(fmt.Sprintf("%v", row[featuredIndex]))
		post.IsFeatured = featured
	}

	if commentsIndex, exists := headerMap["allow_comments"]; exists && row[commentsIndex] != nil {
		allowComments, _ := strconv.ParseBool(fmt.Sprintf("%v", row[commentsIndex]))
		post.AllowComments = allowComments
	}

	// Set meta fields
	if metaTitleIndex, exists := headerMap["meta_title"]; exists && row[metaTitleIndex] != nil {
		post.MetaTitle = fmt.Sprintf("%v", row[metaTitleIndex])
	}

	if metaDescIndex, exists := headerMap["meta_description"]; exists && row[metaDescIndex] != nil {
		post.MetaDescription = fmt.Sprintf("%v", row[metaDescIndex])
	}

	if metaKeywordsIndex, exists := headerMap["meta_keywords"]; exists && row[metaKeywordsIndex] != nil {
		post.MetaKeywords = fmt.Sprintf("%v", row[metaKeywordsIndex])
	}

	if featuredImageIndex, exists := headerMap["featured_image"]; exists && row[featuredImageIndex] != nil {
		post.FeaturedImage = fmt.Sprintf("%v", row[featuredImageIndex])
	}

	// Set date fields
	if scheduledIndex, exists := headerMap["scheduled_at"]; exists && row[scheduledIndex] != nil && row[scheduledIndex] != "" {
		if scheduledAt, err := time.Parse(time.RFC3339, fmt.Sprintf("%v", row[scheduledIndex])); err == nil {
			post.ScheduledAt = &scheduledAt
		}
	}

	if publishedIndex, exists := headerMap["published_at"]; exists && row[publishedIndex] != nil && row[publishedIndex] != "" {
		if publishedAt, err := time.Parse(time.RFC3339, fmt.Sprintf("%v", row[publishedIndex])); err == nil {
			post.PublishedAt = &publishedAt
		}
	}

	// Handle category
	if categoryIndex, exists := headerMap["category_name"]; exists && row[categoryIndex] != nil && row[categoryIndex] != "" {
		categoryName := fmt.Sprintf("%v", row[categoryIndex])
		if err := a.handleCategory(ctx, post, categoryName, options); err != nil {
			return fmt.Errorf("failed to handle category: %w", err)
		}
	}

	// Create the post
	if err := a.postRepo.Create(ctx, post); err != nil {
		return fmt.Errorf("failed to create post: %w", err)
	}

	// Handle tags
	if tagsIndex, exists := headerMap["tags"]; exists && row[tagsIndex] != nil && row[tagsIndex] != "" {
		tagsStr := fmt.Sprintf("%v", row[tagsIndex])
		if err := a.handleTags(ctx, post, tagsStr, options); err != nil {
			return fmt.Errorf("failed to handle tags: %w", err)
		}
	}

	return nil
}

// handleCategory handles category assignment during import
func (a *BlogPostImportAdapter) handleCategory(ctx context.Context, post *models.BlogPost, categoryName string, options map[string]interface{}) error {
	// Try to find existing category
	filter := &models.BlogCategoryFilter{
		TenantID:  post.TenantID,
		WebsiteID: post.WebsiteID,
		Search:    categoryName,
	}

	categories, _, err := a.categoryRepo.List(ctx, filter)
	if err != nil {
		return err
	}

	// Find exact match
	for _, category := range categories {
		if category.Name == categoryName {
			post.CategoryID = &category.ID
			return nil
		}
	}

	// Create new category if allowed
	createCategories, _ := options["create_categories"].(bool)
	if createCategories {
		newCategory := &models.BlogCategory{
			Name:      categoryName,
			Slug:      categoryName, // TODO: Generate proper slug
			TenantID:  post.TenantID,
			WebsiteID: post.WebsiteID,
		}

		if err := a.categoryRepo.Create(ctx, newCategory); err != nil {
			return err
		}

		post.CategoryID = &newCategory.ID
	}

	return nil
}

// handleTags handles tag assignment during import
func (a *BlogPostImportAdapter) handleTags(ctx context.Context, post *models.BlogPost, tagsStr string, options map[string]interface{}) error {
	// Parse tags (assuming comma-separated)
	// This is a simplified implementation
	tagNames := []string{tagsStr} // TODO: Implement proper parsing

	createTags, _ := options["create_tags"].(bool)

	for _, tagName := range tagNames {
		// Try to find existing tag
		filter := &models.BlogTagFilter{
			TenantID:  post.TenantID,
			WebsiteID: post.WebsiteID,
			Search:    tagName,
		}

		tags, _, err := a.tagRepo.List(ctx, filter)
		if err != nil {
			return err
		}

		// Find exact match
		var foundTag *models.BlogTag
		for _, tag := range tags {
			if tag.Name == tagName {
				foundTag = &tag
				break
			}
		}

		// Create new tag if not found and allowed
		if foundTag == nil && createTags {
			newTag := &models.BlogTag{
				Name:      tagName,
				Slug:      tagName, // TODO: Generate proper slug
				TenantID:  post.TenantID,
				WebsiteID: post.WebsiteID,
			}

			if err := a.tagRepo.Create(ctx, newTag); err != nil {
				return err
			}

			foundTag = newTag
		}

		// Associate tag with post
		if foundTag != nil {
			post.Tags = append(post.Tags, *foundTag)
		}
	}

	return nil
}
*/
