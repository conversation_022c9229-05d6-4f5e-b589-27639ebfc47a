package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogCategoryCreateRequest represents the request payload for creating a blog category
type BlogCategoryCreateRequest struct {
	TenantID    uint   `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID   uint   `json:"website_id" validate:"required,min=1" example:"1"`
	Name        string `json:"name" validate:"required,min=1,max=100" example:"Technology"`
	Slug        string `json:"slug" validate:"required,min=1,max=100" example:"technology"`
	Description string `json:"description,omitempty" validate:"max=500" example:"Posts about technology and programming"`
	ParentID    *uint  `json:"parent_id,omitempty" validate:"omitempty,min=1" example:"1"`
}

// BlogCategoryUpdateRequest represents the request payload for updating a blog category
type BlogCategoryUpdateRequest struct {
	Name        *string `json:"name,omitempty" validate:"omitempty,min=1,max=100" example:"Updated Technology"`
	Slug        *string `json:"slug,omitempty" validate:"omitempty,min=1,max=100" example:"updated-technology"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=500" example:"Updated description"`
	ParentID    *uint   `json:"parent_id,omitempty" validate:"omitempty,min=1" example:"2"`
}

// BlogCategoryResponse represents the response payload for a blog category
// KEEP_OMITEMPTY: Optional description, SEO meta fields, and nullable parent relationship
type BlogCategoryResponse struct {
	ID          uint      `json:"id"`
	TenantID    uint      `json:"tenant_id"`
	WebsiteID   uint      `json:"website_id"`
	Name        string    `json:"name"`
	Slug        string    `json:"slug"`
	Description string    `json:"description"`         // Always return, empty string if not set
	ParentID    *uint     `json:"parent_id,omitempty"` // KEEP_OMITEMPTY: Optional hierarchy relationship
	PostCount   int       `json:"post_count"`
	Status      string    `json:"status"` // active, inactive, deleted
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// BlogCategoryListResponse represents the response for listing blog categories
type BlogCategoryListResponse struct {
	Categories []BlogCategoryResponse `json:"categories"`
}

// BlogCategoryFilter represents filter parameters for listing blog categories
type BlogCategoryFilter struct {
	pagination.CursorRequest
	WebsiteID *uint  `json:"website_id,omitempty" form:"website_id" validate:"omitempty,min=1" example:"1"`
	Search    string `json:"search,omitempty" form:"search" validate:"omitempty,max=255" example:"technology"`
	IsActive  *bool  `json:"is_active,omitempty" form:"is_active" example:"true"`
	ParentID  *uint  `json:"parent_id,omitempty" form:"parent_id" validate:"omitempty,min=1" example:"1"`
	SortBy    string `json:"sort_by,omitempty" form:"sort_by" validate:"omitempty,oneof=id name created_at updated_at sort_order post_count" example:"sort_order"`
	SortOrder string `json:"sort_order,omitempty" form:"sort_order" validate:"omitempty,oneof=asc desc" example:"asc"`
}
