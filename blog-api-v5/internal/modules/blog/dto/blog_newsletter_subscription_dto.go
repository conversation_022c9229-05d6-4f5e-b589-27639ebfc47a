package dto

import (
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type CreateNewsletterSubscriptionRequest struct {
	Email      string  `json:"email" validate:"required,email,max=255"`
	Name       *string `json:"name,omitempty" validate:"omitempty,max=255"`
	Categories []uint  `json:"categories,omitempty" validate:"omitempty,dive,min=1"`
	Tags       []uint  `json:"tags,omitempty" validate:"omitempty,dive,min=1"`
	Frequency  string  `json:"frequency,omitempty" validate:"omitempty,oneof=instant daily weekly monthly"`
}

type UpdateNewsletterSubscriptionRequest struct {
	Name       *string `json:"name,omitempty" validate:"omitempty,max=255"`
	Categories []uint  `json:"categories,omitempty" validate:"omitempty,dive,min=1"`
	Tags       []uint  `json:"tags,omitempty" validate:"omitempty,dive,min=1"`
	Frequency  string  `json:"frequency,omitempty" validate:"omitempty,oneof=instant daily weekly monthly"`
}

type ConfirmNewsletterSubscriptionRequest struct {
	Token string `json:"token" validate:"required,len=64"`
}

type UnsubscribeNewsletterRequest struct {
	Token string `json:"token" validate:"required,len=64"`
}

type NewsletterSubscriptionResponse struct {
	ID          uint                                `json:"id"`
	Email       string                              `json:"email"`
	Name        *string                             `json:"name,omitempty"`
	Categories  []uint                              `json:"categories"`
	Tags        []uint                              `json:"tags"`
	Frequency   models.NewsletterFrequency          `json:"frequency"`
	ConfirmedAt *time.Time                          `json:"confirmed_at,omitempty"`
	CreatedAt   time.Time                           `json:"created_at"`
	UpdatedAt   time.Time                           `json:"updated_at"`
	Status      models.NewsletterSubscriptionStatus `json:"status"`
}

func ToNewsletterSubscriptionResponse(subscription *models.BlogNewsletterSubscription) *NewsletterSubscriptionResponse {
	if subscription == nil {
		return nil
	}

	categories := make([]uint, len(subscription.Categories))
	copy(categories, subscription.Categories)

	tags := make([]uint, len(subscription.Tags))
	copy(tags, subscription.Tags)

	return &NewsletterSubscriptionResponse{
		ID:          subscription.ID,
		Email:       subscription.Email,
		Name:        subscription.Name,
		Categories:  categories,
		Tags:        tags,
		Frequency:   subscription.Frequency,
		ConfirmedAt: subscription.ConfirmedAt,
		CreatedAt:   subscription.CreatedAt,
		UpdatedAt:   subscription.UpdatedAt,
		Status:      subscription.Status,
	}
}

type NewsletterSubscriptionListResponse struct {
	ID          uint                                `json:"id"`
	Email       string                              `json:"email"`
	Name        *string                             `json:"name,omitempty"`
	Frequency   models.NewsletterFrequency          `json:"frequency"`
	ConfirmedAt *time.Time                          `json:"confirmed_at,omitempty"`
	CreatedAt   time.Time                           `json:"created_at"`
	Status      models.NewsletterSubscriptionStatus `json:"status"`
}

func ToNewsletterSubscriptionListResponse(subscription *models.BlogNewsletterSubscription) *NewsletterSubscriptionListResponse {
	if subscription == nil {
		return nil
	}

	return &NewsletterSubscriptionListResponse{
		ID:          subscription.ID,
		Email:       subscription.Email,
		Name:        subscription.Name,
		Frequency:   subscription.Frequency,
		ConfirmedAt: subscription.ConfirmedAt,
		CreatedAt:   subscription.CreatedAt,
		Status:      subscription.Status,
	}
}

// BlogNewsletterSubscriptionListResponse represents the response for listing newsletter subscriptions
type BlogNewsletterSubscriptionListResponse struct {
	Subscriptions []NewsletterSubscriptionListResponse `json:"subscriptions"`
}

// BlogNewsletterSubscriptionFilter represents filter parameters for listing newsletter subscriptions
type BlogNewsletterSubscriptionFilter struct {
	pagination.CursorRequest
	WebsiteID   *uint                                `json:"website_id,omitempty" form:"website_id" validate:"omitempty,min=1" example:"1"`
	Email       string                               `json:"email,omitempty" form:"email" validate:"omitempty,email,max=255" example:"<EMAIL>"`
	Status      *models.NewsletterSubscriptionStatus `json:"status,omitempty" form:"status" validate:"omitempty,oneof=pending confirmed unsubscribed" example:"confirmed"`
	Frequency   *models.NewsletterFrequency          `json:"frequency,omitempty" form:"frequency" validate:"omitempty,oneof=instant daily weekly monthly" example:"weekly"`
	ConfirmedAt *time.Time                           `json:"confirmed_at,omitempty" form:"confirmed_at" example:"2024-01-01T00:00:00Z"`
	SortBy      string                               `json:"sort_by,omitempty" form:"sort_by" validate:"omitempty,oneof=id email created_at confirmed_at" example:"created_at"`
	SortOrder   string                               `json:"sort_order,omitempty" form:"sort_order" validate:"omitempty,oneof=asc desc" example:"desc"`
}
