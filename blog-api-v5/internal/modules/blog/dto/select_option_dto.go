package dto

// SelectOption represents a single option for select/dropdown UI components
// @Description Option item for select/dropdown components
type SelectOption struct {
	Value    uint   `json:"value" example:"1" description:"The ID value of the option"`
	Label    string `json:"label" example:"Technology" description:"Display text for the option"`
	Disabled bool   `json:"disabled,omitempty" example:"false" description:"Whether the option is disabled"`
	ParentID *uint  `json:"parent_id,omitempty" example:"null" description:"Parent ID for hierarchical selects"`
	Extra    map[string]interface{} `json:"extra,omitempty" description:"Additional data for the option"`
}

// CategorySelectOption represents a category option with hierarchy info
// @Description Category option for select components with hierarchy support
type CategorySelectOption struct {
	Value    uint   `json:"value" example:"1"`
	Label    string `json:"label" example:"Technology"`
	Disabled bool   `json:"disabled,omitempty" example:"false"`
	ParentID *uint  `json:"parent_id,omitempty" example:"null"`
	Level    uint   `json:"level" example:"0" description:"Depth level in hierarchy"`
	Path     string `json:"path,omitempty" example:"Parent > Technology" description:"Full path for display"`
}

// TagSelectOption represents a tag option
// @Description Tag option for select components
type TagSelectOption struct {
	Value     uint   `json:"value" example:"1"`
	Label     string `json:"label" example:"javascript"`
	Disabled  bool   `json:"disabled,omitempty" example:"false"`
	PostCount int    `json:"post_count,omitempty" example:"42" description:"Number of posts with this tag"`
}

// SelectResponse represents the response for select endpoints
// @Description Response wrapper for select endpoint data
type SelectResponse struct {
	Options []interface{} `json:"options" description:"Array of select options"`
	Total   int          `json:"total" example:"25" description:"Total number of options"`
}