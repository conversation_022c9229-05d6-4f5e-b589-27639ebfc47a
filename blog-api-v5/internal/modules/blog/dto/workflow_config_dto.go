package dto

import (
	"time"
)

// WorkflowConfigRequest represents a request to create or update workflow configuration
type WorkflowConfigRequest struct {
	WorkflowType     string                           `json:"workflow_type" validate:"required,oneof=standard simple advanced"`
	RoleMappings     map[string][]string              `json:"role_mappings" validate:"required"`
	StateTransitions map[string]interface{}           `json:"state_transitions" validate:"required"`
	AutoAssignments  map[string]interface{}           `json:"auto_assignments,omitempty"`
	Notifications    map[string]bool                  `json:"notifications,omitempty"`
	States           []WorkflowStateDefinition        `json:"states,omitempty"`      // New array format for React Flow
	Transitions      []WorkflowTransitionDefinition   `json:"transitions,omitempty"` // New array format for React Flow
}

// WorkflowConfigResponse represents workflow configuration in API responses
type WorkflowConfigResponse struct {
	ID               uint                             `json:"id"`
	TenantID         uint                             `json:"tenant_id"`
	WebsiteID        uint                             `json:"website_id"`
	WorkflowType     string                           `json:"workflow_type"`
	RoleMappings     map[string][]string              `json:"role_mappings"`
	StateTransitions map[string]interface{}           `json:"state_transitions"`
	AutoAssignments  map[string]interface{}           `json:"auto_assignments"`
	Notifications    map[string]bool                  `json:"notifications"`
	IsActive         bool                             `json:"is_active"`
	CreatedAt        time.Time                        `json:"created_at"`
	UpdatedAt        time.Time                        `json:"updated_at"`
	States           []WorkflowStateDefinition        `json:"states,omitempty"`      // New array format for React Flow
	Transitions      []WorkflowTransitionDefinition   `json:"transitions,omitempty"` // New array format for React Flow
}

// WorkflowStateDefinition represents a workflow state configuration
type WorkflowStateDefinition struct {
	Name         string                 `json:"name" validate:"required"`
	Label        string                 `json:"label" validate:"required"`
	Description  string                 `json:"description,omitempty"`
	AllowedRoles []string               `json:"allowed_roles" validate:"required,min=1"`
	Transitions  []string               `json:"transitions" validate:"required"`
	Color        string                 `json:"color,omitempty"`
	Icon         string                 `json:"icon,omitempty"`
	Position     map[string]interface{} `json:"position,omitempty"` // For React Flow positioning
}

// WorkflowTransitionDefinition represents a workflow transition configuration
type WorkflowTransitionDefinition struct {
	From           string   `json:"from" validate:"required"`
	To             string   `json:"to" validate:"required"`
	Action         string   `json:"action" validate:"required"`
	Label          string   `json:"label" validate:"required"`
	RequiredRoles  []string `json:"required_roles" validate:"required,min=1"`
	RequiresNotes  bool     `json:"requires_notes,omitempty"`
	RequiresAssign bool     `json:"requires_assign,omitempty"`
}

// StandardWorkflowConfigRequest represents a simplified way to configure standard workflow
type StandardWorkflowConfigRequest struct {
	EnableEditorReview   bool                              `json:"enable_editor_review"`
	EnableManagerReview  bool                              `json:"enable_manager_review"`
	AutoAssignEditor     uint                              `json:"auto_assign_editor,omitempty"`
	AutoAssignManager    uint                              `json:"auto_assign_manager,omitempty"`
	Notifications        map[string]bool                   `json:"notifications,omitempty"`
	CustomStates         []WorkflowStateDefinition         `json:"custom_states,omitempty"`
	CustomTransitions    []WorkflowTransitionDefinition    `json:"custom_transitions,omitempty"`
}

// WorkflowTemplateResponse represents a workflow template
type WorkflowTemplateResponse struct {
	ID           string                            `json:"id"`
	Name         string                            `json:"name"`
	Description  string                            `json:"description"`
	Type         string                            `json:"type"`
	States       []WorkflowStateDefinition         `json:"states"`
	Transitions  []WorkflowTransitionDefinition    `json:"transitions"`
	RoleMappings map[string][]string               `json:"role_mappings"`
	IsDefault    bool                              `json:"is_default"`
}