package handlers

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// Keep imports for swagger generation
var (
	_ = pagination.CursorResponse{}
)

// BlogAuthorHandler handles HTTP requests for blog author operations
type BlogAuthorHandler struct {
	service services.BlogAuthorService
	logger  utils.Logger
}

// NewBlogAuthorHandler creates a new instance of BlogAuthorHandler
func NewBlogAuthorHandler(service services.BlogAuthorService, logger utils.Logger) *BlogAuthorHandler {
	return &BlogAuthorHandler{
		service: service,
		logger:  logger,
	}
}

// Create creates a new blog author
// @Summary Create a new blog author
// @Description Create a new blog author for the specified tenant and website
// @Tags Blog Authors
// @Accept json
// @Produce json
// @Param X-Tenant-ID header uint true "Tenant ID"
// @Param X-Website-ID header uint true "Website ID"
// @Param body body models.BlogAuthorCreateRequest true "Blog author creation request"
// @Success 201 {object} response.Response{data=models.BlogAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /cms/v1/blog/authors [post]
// @Security BearerAuth
func (h *BlogAuthorHandler) Create(c *gin.Context) {
	h.logger.Info("BlogAuthorHandler.Create called")
	
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		h.logger.Error("Failed to get tenant ID", utils.Fields{"error": err.Error()})
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	var req models.BlogAuthorCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", utils.Fields{"error": err.Error(), "raw_body": c.Request.Body})
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body: " + err.Error()))
		return
	}
	
	h.logger.Info("Request bound successfully", utils.Fields{"request": req})

	author, err := h.service.Create(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		h.logger.Error("Failed to create blog author", utils.Fields{"error": err.Error()})
		middleware.AbortWithError(c, err)
		return
	}
	
	h.logger.Info("Service returned author", utils.Fields{"author": author})
	
	// Convert to response
	responseData := &models.BlogAuthorResponse{}
	if author != nil {
		responseData.FromBlogAuthor(author)
		h.logger.Info("Converted to response", utils.Fields{"response": responseData})
	} else {
		h.logger.Error("Author is nil after creation")
		middleware.AbortWithError(c, fmt.Errorf("failed to create author: author is nil"))
		return
	}
	
	response.CreatedWithContext(c, responseData)
}

// GetByID retrieves a blog author by ID
// @Summary Get blog author by ID
// @Description Get a blog author by ID for the specified tenant and website
// @Tags Blog Authors
// @Accept json
// @Produce json
// @Param X-Tenant-ID header uint true "Tenant ID"
// @Param X-Website-ID header uint true "Website ID"
// @Param id path uint true "Author ID"
// @Success 200 {object} response.Response{data=models.BlogAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /cms/v1/blog/authors/{id} [get]
// @Security BearerAuth
func (h *BlogAuthorHandler) GetByID(c *gin.Context) {
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid author ID"))
		return
	}

	author, err := h.service.GetByID(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Convert to response
	responseData := &models.BlogAuthorResponse{}
	responseData.FromBlogAuthor(author)

	response.SuccessWithContext(c, responseData)
}

// GetBySlug retrieves a blog author by slug
// @Summary Get blog author by slug
// @Description Get a blog author by slug for the specified tenant and website
// @Tags Blog Authors
// @Accept json
// @Produce json
// @Param X-Tenant-ID header uint true "Tenant ID"
// @Param X-Website-ID header uint true "Website ID"
// @Param slug path string true "Author slug"
// @Success 200 {object} response.Response{data=models.BlogAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /cms/v1/blog/authors/slug/{slug} [get]
// @Security BearerAuth
func (h *BlogAuthorHandler) GetBySlug(c *gin.Context) {
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	slug := c.Param("slug")
	if slug == "" {
		middleware.AbortWithError(c, utils.BadRequestError("Slug is required"))
		return
	}

	author, err := h.service.GetBySlug(c.Request.Context(), tenantID, websiteID, slug)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Convert to response
	responseData := &models.BlogAuthorResponse{}
	responseData.FromBlogAuthor(author)

	response.SuccessWithContext(c, responseData)
}

// Update updates a blog author
// @Summary Update blog author
// @Description Update a blog author for the specified tenant and website
// @Tags Blog Authors
// @Accept json
// @Produce json
// @Param X-Tenant-ID header uint true "Tenant ID"
// @Param X-Website-ID header uint true "Website ID"
// @Param id path uint true "Author ID"
// @Param body body models.BlogAuthorUpdateRequest true "Blog author update request"
// @Success 200 {object} response.Response{data=models.BlogAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /cms/v1/blog/authors/{id} [put]
// @Security BearerAuth
func (h *BlogAuthorHandler) Update(c *gin.Context) {
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid author ID"))
		return
	}

	var req models.BlogAuthorUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", utils.Fields{"error": err.Error()})
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	author, err := h.service.Update(c.Request.Context(), tenantID, websiteID, uint(id), req)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Convert to response
	responseData := &models.BlogAuthorResponse{}
	responseData.FromBlogAuthor(author)

	response.SuccessWithContext(c, responseData)
}

// Delete deletes a blog author
// @Summary Delete blog author
// @Description Soft delete a blog author for the specified tenant and website
// @Tags Blog Authors
// @Accept json
// @Produce json
// @Param X-Tenant-ID header uint true "Tenant ID"
// @Param X-Website-ID header uint true "Website ID"
// @Param id path uint true "Author ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /cms/v1/blog/authors/{id} [delete]
// @Security BearerAuth
func (h *BlogAuthorHandler) Delete(c *gin.Context) {
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid author ID"))
		return
	}

	if err := h.service.Delete(c.Request.Context(), tenantID, websiteID, uint(id)); err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, gin.H{"message": "Blog author deleted successfully"})
}

// List lists blog authors with pagination
// @Summary List blog authors
// @Description List blog authors with pagination for the specified tenant and website
// @Tags Blog Authors
// @Accept json
// @Produce json
// @Param X-Tenant-ID header uint true "Tenant ID"
// @Param X-Website-ID header uint true "Website ID"
// @Param search query string false "Search by name, email, or bio"
// @Param status query string false "Filter by status (active, inactive)"
// @Param is_verified query bool false "Filter by verification status"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Number of items per page (max 100)"
// @Param sort_by query string false "Sort by field (created_at, name, post_count)"
// @Param sort_order query string false "Sort order (asc, desc)"
// @Success 200 {object} response.Response{data=[]models.BlogAuthorResponse,meta=pagination.CursorResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /cms/v1/blog/authors [get]
// @Security BearerAuth
func (h *BlogAuthorHandler) List(c *gin.Context) {
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	filter := models.BlogAuthorFilter{
		Search:    c.Query("search"),
		Cursor:    c.Query("cursor"),
		SortBy:    c.Query("sort_by"),
		SortOrder: c.Query("sort_order"),
	}

	// Parse status
	if status := c.Query("status"); status != "" {
		filter.Status = models.BlogAuthorStatus(status)
	}

	// Parse is_verified
	if isVerifiedStr := c.Query("is_verified"); isVerifiedStr != "" {
		isVerified, err := strconv.ParseBool(isVerifiedStr)
		if err == nil {
			filter.IsVerified = &isVerified
		}
	}

	// Parse limit
	if limitStr := c.Query("limit"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err == nil && limit > 0 && limit <= 100 {
			filter.Limit = limit
		}
	}

	authors, cursorResp, err := h.service.List(c.Request.Context(), tenantID, websiteID, filter)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Convert to response
	responses := make([]models.BlogAuthorResponse, len(authors))
	for i, author := range authors {
		responses[i].FromBlogAuthor(&author)
	}

	response.CursorPaginatedWithContext(c, responses, *cursorResp)
}

// GetStats retrieves blog author statistics
// @Summary Get blog author statistics
// @Description Get statistics about blog authors for the specified tenant and website
// @Tags Blog Authors
// @Accept json
// @Produce json
// @Param X-Tenant-ID header uint true "Tenant ID"
// @Param X-Website-ID header uint true "Website ID"
// @Success 200 {object} response.Response{data=models.BlogAuthorStats}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /cms/v1/blog/authors/stats [get]
// @Security BearerAuth
func (h *BlogAuthorHandler) GetStats(c *gin.Context) {
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	stats, err := h.service.GetStats(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, stats)
}