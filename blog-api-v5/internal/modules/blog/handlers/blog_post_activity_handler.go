package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogPostActivityHandler handles HTTP requests for blog post activities
type BlogPostActivityHandler struct {
	activityService services.BlogPostActivityService
	logger          utils.Logger
}

// NewBlogPostActivityHandler creates a new blog post activity handler
func NewBlogPostActivityHandler(activityService services.BlogPostActivityService, logger utils.Logger) *BlogPostActivityHandler {
	return &BlogPostActivityHandler{
		activityService: activityService,
		logger:          logger,
	}
}

// GetPostActivities retrieves activities for a specific post
func (h *BlogPostActivityHandler) GetPostActivities(c *gin.Context) {
	// Get IDs from context and URL
	tenantID, _ := c.Get("tenantID")
	websiteID, _ := c.Get("websiteID")
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get limit from query
	limit := 50
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Get activities
	activities, err := h.activityService.GetPostActivities(c, tenantID.(uint), websiteID.(uint), uint(postID), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get post activities")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, activities)
}

// GetUserActivities retrieves activities for a specific user
func (h *BlogPostActivityHandler) GetUserActivities(c *gin.Context) {
	// Get IDs from context and URL
	tenantID, _ := c.Get("tenantID")
	websiteID, _ := c.Get("websiteID")
	userID, err := strconv.ParseUint(c.Param("userId"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Get limit from query
	limit := 50
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Get activities
	activities, err := h.activityService.GetUserActivities(c, tenantID.(uint), websiteID.(uint), uint(userID), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user activities")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, activities)
}

// ListActivities retrieves activities with filters
func (h *BlogPostActivityHandler) ListActivities(c *gin.Context) {
	// Get IDs from context
	tenantID, _ := c.Get("tenantID")
	websiteID, _ := c.Get("websiteID")

	// Parse cursor request
	limitStr := c.DefaultQuery("limit", "20")
	limit, _ := strconv.Atoi(limitStr)
	cursorReq := pagination.NewCursorRequest(c.Query("cursor"), limit)

	// Build filter from query parameters
	filter := &models.BlogPostActivityFilter{}

	// Post ID filter
	if postIDStr := c.Query("post_id"); postIDStr != "" {
		if postID, err := strconv.ParseUint(postIDStr, 10, 32); err == nil {
			id := uint(postID)
			filter.PostID = &id
		}
	}

	// User ID filter
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if userID, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			id := uint(userID)
			filter.UserID = &id
		}
	}

	// Action filter
	if action := c.Query("action"); action != "" {
		a := models.BlogPostActivityAction(action)
		filter.Action = &a
	}

	// Field name filter
	if fieldName := c.Query("field_name"); fieldName != "" {
		filter.FieldName = &fieldName
	}

	// Get activities with cursor pagination
	activities, cursorResp, err := h.activityService.ListActivitiesWithCursor(c, tenantID.(uint), websiteID.(uint), cursorReq, filter)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list activities")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.CursorPaginatedWithContext(c, activities, *cursorResp)
}

// GetActivityStats retrieves activity statistics
func (h *BlogPostActivityHandler) GetActivityStats(c *gin.Context) {
	// Get IDs from context
	tenantID, _ := c.Get("tenantID")
	websiteID, _ := c.Get("websiteID")

	// Get stats
	stats, err := h.activityService.GetActivityStats(c, tenantID.(uint), websiteID.(uint), nil)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get activity stats")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, stats)
}