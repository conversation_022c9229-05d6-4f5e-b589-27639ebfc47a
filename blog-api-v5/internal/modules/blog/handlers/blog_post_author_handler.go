package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type BlogPostAuthorHandler struct {
	authorService services.BlogPostAuthorService
	logger        utils.Logger
}

// NewBlogPostAuthorHandler creates a new blog post author handler
func NewBlogPostAuthorHandler(authorService services.BlogPostAuthorService, logger utils.Logger) *BlogPostAuthorHandler {
	return &BlogPostAuthorHandler{
		authorService: authorService,
		logger:        logger,
	}
}

// AddAuthor adds an author to a blog post
// @Summary Add author to blog post
// @Description Add a new author to a blog post with specified role
// @Tags Blog Post Authors
// @Accept json
// @Produce json
// @Param post_id path int true "Post ID"
// @Param request body models.BlogPostAuthorCreateRequest true "Author creation request"
// @Success 201 {object} response.Response{data=models.BlogPostAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors [post]
func (h *BlogPostAuthorHandler) AddAuthor(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	var req models.BlogPostAuthorCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Override post ID from URL
	req.PostID = uint(postID)

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	author, err := h.authorService.AddAuthor(c.Request.Context(), tenantID, websiteID, req.PostID, req.UserID, req.Role)
	if err != nil {
		h.logger.Error("Failed to add blog post author", "error", err, "post_id", postID, "user_id", req.UserID)

		if err.Error() == "user is already an author of this post" {
			middleware.AbortWithError(c, utils.ConflictError("User is already an author of this post"))
			return
		}
		if err.Error() == "post already has a primary author" {
			middleware.AbortWithError(c, utils.ConflictError("Post already has a primary author"))
			return
		}
		if err.Error() == "blog post not found" {
			middleware.AbortWithError(c, utils.NotFoundError("Blog post not found"))
			return
		}

		middleware.AbortWithError(c, err)
		return
	}

	response.CreatedWithContext(c, author)
}

// GetAuthor retrieves an author relationship by post ID and user ID
// @Summary Get author for blog post
// @Description Get author relationship between a user and a blog post
// @Tags Blog Post Authors
// @Produce json
// @Param post_id path int true "Post ID"
// @Param user_id path int true "User ID"
// @Success 200 {object} response.Response{data=models.BlogPostAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors/{user_id} [get]
func (h *BlogPostAuthorHandler) GetAuthor(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	userID, err := strconv.ParseUint(c.Param("user_id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	author, err := h.authorService.GetByPostAndUser(c.Request.Context(), tenantID, websiteID, uint(postID), uint(userID))
	if err != nil {
		h.logger.Error("Failed to get blog post author", "error", err, "post_id", postID, "user_id", userID)
		middleware.AbortWithError(c, err)
		return
	}

	if author == nil {
		middleware.AbortWithError(c, utils.NotFoundError("Author not found"))
		return
	}

	response.SuccessWithContext(c, author)
}

// UpdateAuthorRole updates the role of an author for a blog post
// @Summary Update author role
// @Description Update the role of an existing author for a blog post
// @Tags Blog Post Authors
// @Accept json
// @Produce json
// @Param post_id path int true "Post ID"
// @Param user_id path int true "User ID"
// @Param request body models.BlogPostAuthorUpdateRequest true "Author update request"
// @Success 200 {object} response.Response{data=models.BlogPostAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors/{user_id} [put]
func (h *BlogPostAuthorHandler) UpdateAuthorRole(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	userID, err := strconv.ParseUint(c.Param("user_id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	var req models.BlogPostAuthorUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	author, err := h.authorService.UpdateAuthorRole(c.Request.Context(), tenantID, websiteID, uint(postID), uint(userID), req.Role)
	if err != nil {
		h.logger.Error("Failed to update author role", "error", err, "post_id", postID, "user_id", userID, "role", req.Role)

		if err.Error() == "author not found" {
			middleware.AbortWithError(c, utils.NotFoundError("Author not found"))
			return
		}
		if err.Error() == "post already has a primary author" {
			middleware.AbortWithError(c, utils.ConflictError("Post already has a primary author"))
			return
		}

		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, author)
}

// RemoveAuthor removes an author from a blog post
// @Summary Remove author from blog post
// @Description Remove an author from a blog post
// @Tags Blog Post Authors
// @Produce json
// @Param post_id path int true "Post ID"
// @Param user_id path int true "User ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors/{user_id} [delete]
func (h *BlogPostAuthorHandler) RemoveAuthor(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	userID, err := strconv.ParseUint(c.Param("user_id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid user ID"))
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	err = h.authorService.RemoveAuthor(c.Request.Context(), tenantID, websiteID, uint(postID), uint(userID))
	if err != nil {
		h.logger.Error("Failed to remove author", "error", err, "post_id", postID, "user_id", userID)

		if err.Error() == "author not found" {
			middleware.AbortWithError(c, utils.NotFoundError("Author not found"))
			return
		}
		if err.Error() == "cannot remove the only author of the post" {
			middleware.AbortWithError(c, utils.ConflictError("Cannot remove the only author of the post"))
			return
		}

		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, nil)
}

// ListAuthorsByPost lists all authors for a specific blog post
// @Summary List authors for blog post
// @Description List all authors for a specific blog post with filtering and pagination
// @Tags Blog Post Authors
// @Produce json
// @Param post_id path int true "Post ID"
// @Param role query string false "Filter by role" Enums(primary,co-author,contributor,editor)
// @Param search query string false "Search by user name or email"
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Number of items per page (default: 20)"
// @Success 200 {object} response.Response{data=[]models.BlogPostAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors [get]
func (h *BlogPostAuthorHandler) ListAuthorsByPost(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Parse filter parameters
	filter := &models.BlogPostAuthorFilter{
		Role:      models.BlogPostAuthorRole(c.Query("role")),
		Search:    c.Query("search"),
		Page:      1,
		PageSize:  20,
		SortBy:    "created_at",
		SortOrder: "ASC",
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filter.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			filter.PageSize = ps
		}
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	authors, _, err := h.authorService.ListByPost(c.Request.Context(), tenantID, websiteID, uint(postID), filter)
	if err != nil {
		h.logger.Error("Failed to list authors by post", "error", err, "post_id", postID)
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, authors)
}

// GetPrimaryAuthor retrieves the primary author of a blog post
// @Summary Get primary author
// @Description Get the primary author of a blog post
// @Tags Blog Post Authors
// @Produce json
// @Param post_id path int true "Post ID"
// @Success 200 {object} response.Response{data=models.BlogPostAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors/primary [get]
func (h *BlogPostAuthorHandler) GetPrimaryAuthor(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	author, err := h.authorService.GetPrimaryAuthor(c.Request.Context(), tenantID, websiteID, uint(postID))
	if err != nil {
		h.logger.Error("Failed to get primary author", "error", err, "post_id", postID)
		response.InternalServerErrorWithContext(c, "Failed to get primary author")
		return
	}

	if author == nil {
		response.NotFoundWithContext(c, "Primary author not found")
		return
	}

	response.SuccessWithContext(c, author)
}

// BulkAddAuthors adds multiple authors to a blog post
// @Summary Bulk add authors
// @Description Add multiple authors to a blog post in a single operation
// @Tags Blog Post Authors
// @Accept json
// @Produce json
// @Param post_id path int true "Post ID"
// @Param request body []models.BlogPostAuthorCreateRequest true "Authors to add"
// @Success 201 {object} response.Response{data=[]models.BlogPostAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors/bulk [post]
func (h *BlogPostAuthorHandler) BulkAddAuthors(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	var req []models.BlogPostAuthorCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	if len(req) == 0 {
		response.BadRequestWithContext(c, "No authors provided")
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	// Override post ID for all authors
	for i := range req {
		req[i].PostID = uint(postID)
	}

	authors, err := h.authorService.BulkAddAuthors(c.Request.Context(), tenantID, websiteID, uint(postID), req)
	if err != nil {
		h.logger.Error("Failed to bulk add authors", "error", err, "post_id", postID, "count", len(req))

		if err.Error() == "blog post not found" {
			response.NotFoundWithContext(c, "Blog post not found")
			return
		}
		if err.Error() == "cannot add multiple primary authors" {
			response.ConflictWithContext(c, "Cannot add multiple primary authors")
			return
		}
		if err.Error() == "post already has a primary author" {
			response.ConflictWithContext(c, "Post already has a primary author")
			return
		}

		response.InternalServerErrorWithContext(c, "Failed to add authors")
		return
	}

	response.CreatedWithContext(c, authors)
}

// ReplaceAuthors replaces all authors for a blog post
// @Summary Replace all authors
// @Description Replace all authors for a blog post with a new set of authors
// @Tags Blog Post Authors
// @Accept json
// @Produce json
// @Param post_id path int true "Post ID"
// @Param request body []models.BlogPostAuthorCreateRequest true "New authors list"
// @Success 200 {object} response.Response{data=[]models.BlogPostAuthorResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors/replace [put]
func (h *BlogPostAuthorHandler) ReplaceAuthors(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	var req []models.BlogPostAuthorCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	if len(req) == 0 {
		response.BadRequestWithContext(c, "Cannot replace with empty author list")
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	// Override post ID for all authors
	for i := range req {
		req[i].PostID = uint(postID)
	}

	authors, err := h.authorService.ReplaceAuthors(c.Request.Context(), tenantID, websiteID, uint(postID), req)
	if err != nil {
		h.logger.Error("Failed to replace authors", "error", err, "post_id", postID, "count", len(req))

		if err.Error() == "blog post not found" {
			response.NotFoundWithContext(c, "Blog post not found")
			return
		}
		if err.Error() == "must have exactly one primary author" {
			response.BadRequestWithContext(c, "Must have exactly one primary author")
			return
		}

		response.InternalServerErrorWithContext(c, "Failed to replace authors")
		return
	}

	response.SuccessWithContext(c, authors)
}

// GetAuthorStats retrieves statistics about authors for a blog post
// @Summary Get author statistics
// @Description Get aggregated statistics about authors for a blog post
// @Tags Blog Post Authors
// @Produce json
// @Param post_id path int true "Post ID"
// @Success 200 {object} response.Response{data=models.BlogPostAuthorStats}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors/stats [get]
func (h *BlogPostAuthorHandler) GetAuthorStats(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	stats, err := h.authorService.GetAuthorStats(c.Request.Context(), tenantID, websiteID, uint(postID))
	if err != nil {
		h.logger.Error("Failed to get author stats", "error", err, "post_id", postID)
		response.InternalServerErrorWithContext(c, "Failed to get author statistics")
		return
	}

	response.SuccessWithContext(c, stats)
}

// TransferPrimaryAuthorship transfers primary authorship from one user to another
// @Summary Transfer primary authorship
// @Description Transfer primary authorship of a blog post from one user to another
// @Tags Blog Post Authors
// @Accept json
// @Produce json
// @Param post_id path int true "Post ID"
// @Param request body map[string]uint true "Transfer request with from_user_id and to_user_id"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{post_id}/authors/transfer-primary [post]
func (h *BlogPostAuthorHandler) TransferPrimaryAuthorship(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("post_id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID")
		return
	}

	var req struct {
		FromUserID uint `json:"from_user_id" validate:"required,min=1"`
		ToUserID   uint `json:"to_user_id" validate:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request format", err.Error())
		return
	}

	if req.FromUserID == req.ToUserID {
		response.BadRequestWithContext(c, "Cannot transfer to the same user")
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	err = h.authorService.TransferPrimaryAuthorship(c.Request.Context(), tenantID, websiteID, uint(postID), req.FromUserID, req.ToUserID)
	if err != nil {
		h.logger.Error("Failed to transfer primary authorship", "error", err, "post_id", postID, "from_user", req.FromUserID, "to_user", req.ToUserID)

		if err.Error() == "user is not the current primary author" {
			response.BadRequestWithContext(c, "User is not the current primary author")
			return
		}

		response.InternalServerErrorWithContext(c, "Failed to transfer primary authorship")
		return
	}

	response.SuccessWithContext(c, nil)
}

// ListForSelect lists authors for select components with cursor pagination
// @Summary List authors for select
// @Description Get a list of authors suitable for select components with cursor pagination
// @Tags Blog Post Authors
// @Produce json
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Number of items per page (default: 20, max: 100)"
// @Param search query string false "Search by author name or email"
// @Param role query string false "Filter by role" Enums(primary,co-author,contributor,editor)
// @Success 200 {object} response.ResponseMeta{data=[]models.BlogPostAuthorSelectResponse,meta=response.Meta}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/authors/select [get]
func (h *BlogPostAuthorHandler) ListForSelect(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantIDParsed, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid Tenant ID")
		return
	}
	tenantID := uint(tenantIDParsed)

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	// Parse cursor pagination parameters
	cursor := &pagination.CursorRequest{
		Cursor: c.Query("cursor"),
		Limit:  20, // default
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			if limit > 100 {
				limit = 100 // max limit
			}
			cursor.Limit = limit
		}
	}

	// Parse filters
	filters := make(map[string]interface{})
	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}
	if role := c.Query("role"); role != "" {
		filters["role"] = role
	}

	// Call service
	authors, cursorResponse, err := h.authorService.ListForSelect(c.Request.Context(), tenantID, websiteID, cursor, filters)
	if err != nil {
		h.logger.Error("Failed to list authors for select", "error", err, "tenant_id", tenantID)
		response.InternalServerErrorWithContext(c, "Failed to list authors")
		return
	}

	response.CursorPaginatedWithContext(c, authors, *cursorResponse)
}
