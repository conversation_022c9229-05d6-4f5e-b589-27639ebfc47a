package handlers

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type BlogPostHandler struct {
	postService services.BlogPostService
}

func NewBlogPostHandler(postService services.BlogPostService) *BlogPostHandler {
	return &BlogPostHandler{
		postService: postService,
	}
}

// convertToPostResponse converts models.BlogPostResponse to dto.BlogPostResponse
func (h *BlogPostHandler) convertToPostResponse(post *models.BlogPostResponse) *dto.BlogPostResponse {
	// Extract category IDs from categories
	categoryIDs := make([]uint, len(post.Categories))
	for i, category := range post.Categories {
		categoryIDs[i] = category.ID
	}

	// Get primary category ID
	var primaryCategoryID *uint
	if post.PrimaryCategory != nil {
		primaryCategoryID = &post.PrimaryCategory.ID
	}

	// For backward compatibility, use first category ID as categoryID
	categoryID := uint(0)
	if len(post.Categories) > 0 {
		categoryID = post.Categories[0].ID
	}

	// Extract tag IDs from tags
	tagIDs := make([]uint, 0)
	if len(post.Tags) > 0 {
		tagIDs = make([]uint, len(post.Tags))
		for i, tag := range post.Tags {
			tagIDs[i] = tag.ID
		}
	}

	// Featured image is now handled as URL string directly

	// Convert SEO metadata from models to DTO
	var seoMetadata *dto.SEOMetadata
	if post.SEOMetadata != nil {
		seoMetadata = &dto.SEOMetadata{
			MetaTitle:          post.SEOMetadata.MetaTitle,
			MetaDescription:    post.SEOMetadata.MetaDescription,
			MetaKeywords:       post.SEOMetadata.MetaKeywords,
			MetaRobots:         post.SEOMetadata.MetaRobots,
			CanonicalURL:       post.SEOMetadata.CanonicalURL,
			OGTitle:            post.SEOMetadata.OGTitle,
			OGDescription:      post.SEOMetadata.OGDescription,
			OGImage:            post.SEOMetadata.OGImage,
			OGType:             post.SEOMetadata.OGType,
			OGURL:              post.SEOMetadata.OGURL,
			OGSiteName:         post.SEOMetadata.OGSiteName,
			OGLocale:           post.SEOMetadata.OGLocale,
			TwitterCard:        post.SEOMetadata.TwitterCard,
			TwitterTitle:       post.SEOMetadata.TwitterTitle,
			TwitterDescription: post.SEOMetadata.TwitterDescription,
			TwitterImage:       post.SEOMetadata.TwitterImage,
			TwitterCreator:     post.SEOMetadata.TwitterCreator,
			TwitterSite:        post.SEOMetadata.TwitterSite,
			SchemaType:         post.SEOMetadata.SchemaType,
			SchemaData:         post.SEOMetadata.SchemaData,
			FocusKeyword:       post.SEOMetadata.FocusKeyword,
		}
	}

	response := &dto.BlogPostResponse{
		ID:                post.ID,
		Title:             post.Title,
		Slug:              post.Slug,
		Content:           post.Content,
		Excerpt:           post.Excerpt,
		Status:            post.Status,
		PublishedAt:       post.PublishedAt,
		CategoryID:        categoryID,
		CategoryIDs:       categoryIDs,
		PrimaryCategoryID: primaryCategoryID,
		TagIDs:            tagIDs,
		RelatedPostIDs:    post.RelatedPostIDs, // Use actual related post IDs from model
		FeaturedImage:     &post.FeaturedImage,
		UserID:            post.UserID,
		Type:              post.Type,
		IsFeatured:        post.IsFeatured,
		AllowComments:     post.AllowComments,
		IsSticky:          post.IsSticky, // Use actual IsSticky from model
		ViewCount:         post.ViewCount,
		CommentCount:      post.CommentCount, // Use actual comment count from model
		WebsiteID:         post.WebsiteID,
		TenantID:          post.TenantID,
		CreatedAt:         post.CreatedAt,
		UpdatedAt:         post.UpdatedAt,
		SEO:               seoMetadata, // Add SEO metadata
	}

	// Convert primary category if exists (for ?include=category)
	if post.PrimaryCategory != nil {
		response.Category = h.convertToCategoryResponse(post.PrimaryCategory)
	}

	// Convert tags if exists (for ?include=tags)
	if len(post.Tags) > 0 {
		response.Tags = make([]dto.BlogTagResponse, len(post.Tags))
		for i, tag := range post.Tags {
			response.Tags[i] = *h.convertToTagResponse(&tag)
		}
	}

	return response
}

// convertToCategoryResponse converts models.BlogCategoryResponse to dto.BlogCategoryResponse
func (h *BlogPostHandler) convertToCategoryResponse(category *models.BlogCategoryResponse) *dto.BlogCategoryResponse {
	return &dto.BlogCategoryResponse{
		ID:          category.ID,
		TenantID:    category.TenantID,
		WebsiteID:   category.WebsiteID,
		Name:        category.Name,
		Slug:        category.Slug,
		Description: category.Description,
		ParentID:    category.ParentID,
		PostCount:   0, // Not available in models.BlogCategoryResponse
		Status:      category.Status,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}

// convertToTagResponse converts models.BlogTagResponse to dto.BlogTagResponse
func (h *BlogPostHandler) convertToTagResponse(tag *models.BlogTagResponse) *dto.BlogTagResponse {
	return &dto.BlogTagResponse{
		ID:          tag.ID,
		TenantID:    tag.TenantID,
		WebsiteID:   tag.WebsiteID,
		Name:        tag.Name,
		Slug:        tag.Slug,
		Description: tag.Description,
		PostCount:   int(tag.PostCount),
		IsActive:    tag.IsActive,
		CreatedAt:   tag.CreatedAt,
		UpdatedAt:   tag.UpdatedAt,
	}
}

// CreatePost creates a new blog post
// @Summary      Create a new blog post
// @Description  Creates a new blog post with the provided data
// @Tags         Blog Posts
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.BlogPostCreateRequest true "Blog post data"
// @Success      201 {object} response.Response{data=dto.BlogPostResponse} "Blog post created successfully"
// @Failure      400 {object} response.Response "Invalid request body"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to create post"
// @Router       /api/cms/v1/blog/posts [post]
func (h *BlogPostHandler) CreatePost(c *gin.Context) {
	var req dto.BlogPostCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Get tenant ID from context
	tenantIDUint, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get user ID from context (author of the post)
	userIDUint, err := context.GetUserIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	// Use context user as creator
	creatorUserID := userIDUint

	// Auto-generate slug if not provided
	slug := req.Slug
	if slug == "" {
		// TODO: Generate slug from title
		slug = req.Title
	}

	// Featured image is now handled as URL string directly
	featuredImageURL := ""
	if req.FeaturedImage != nil {
		featuredImageURL = *req.FeaturedImage
	}

	// Set scheduled_at based on status
	var scheduledAt *time.Time
	if req.Status == models.BlogPostStatusScheduled && req.PublishedAt != nil {
		scheduledAt = req.PublishedAt
	}

	// Convert SEO metadata from DTO to models
	var seoMetadata *models.SEOMetadata
	if req.SEO != nil {
		seoMetadata = &models.SEOMetadata{
			MetaTitle:          req.SEO.MetaTitle,
			MetaDescription:    req.SEO.MetaDescription,
			MetaKeywords:       req.SEO.MetaKeywords,
			MetaRobots:         req.SEO.MetaRobots,
			CanonicalURL:       req.SEO.CanonicalURL,
			OGTitle:            req.SEO.OGTitle,
			OGDescription:      req.SEO.OGDescription,
			OGImage:            req.SEO.OGImage,
			OGType:             req.SEO.OGType,
			OGURL:              req.SEO.OGURL,
			OGSiteName:         req.SEO.OGSiteName,
			OGLocale:           req.SEO.OGLocale,
			TwitterCard:        req.SEO.TwitterCard,
			TwitterTitle:       req.SEO.TwitterTitle,
			TwitterDescription: req.SEO.TwitterDescription,
			TwitterImage:       req.SEO.TwitterImage,
			TwitterCreator:     req.SEO.TwitterCreator,
			TwitterSite:        req.SEO.TwitterSite,
			SchemaType:         req.SEO.SchemaType,
			SchemaData:         req.SEO.SchemaData,
			FocusKeyword:       req.SEO.FocusKeyword,
		}
	}

	// Convert DTO to models
	serviceReq := &models.BlogPostCreateRequest{
		TenantID:          tenantIDUint,
		WebsiteID:         websiteID,
		Slug:              slug,
		Title:             req.Title,
		Content:           req.Content,
		Excerpt:           req.Excerpt,
		UserID:            creatorUserID,
		CategoryIDs:       req.CategoryIDs,
		PrimaryCategoryID: req.PrimaryCategoryID,
		Type:              req.Type,
		IsFeatured:        req.IsFeatured,
		IsSticky:          req.IsSticky,
		AllowComments:     req.AllowComments,
		Password:          req.Password,
		FeaturedImage:     featuredImageURL,
		ScheduledAt:       scheduledAt,
		Status:            req.Status,
		TagIDs:            req.TagIDs,
		RelatedPostIDs:    req.RelatedPostIDs,
		SEOMetadata:       seoMetadata,
	}

	post, err := h.postService.Create(c.Request.Context(), serviceReq)
	if err != nil {
		// Use middleware error handling
		middleware.AbortWithError(c, err)
		return
	}

	// Convert models response to DTO response
	postResponse := h.convertToPostResponse(post)

	response.CreatedWithContext(c, postResponse)
}

// GetPost retrieves a blog post by ID
// @Summary      Get a blog post by ID
// @Description  Retrieves a single blog post by its ID
// @Tags         Blog Posts
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Blog post ID"
// @Success      200 {object} response.Response{data=dto.BlogPostResponse} "Blog post retrieved successfully"
// @Failure      400 {object} response.Response "Invalid post ID"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Post not found"
// @Router       /api/cms/v1/blog/posts/{id} [get]
func (h *BlogPostHandler) GetPost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	post, err := h.postService.GetByID(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Convert models response to DTO response
	postResponse := h.convertToPostResponse(post)

	response.SuccessWithContext(c, postResponse)
}

// GetPostBySlug retrieves a blog post by slug
func (h *BlogPostHandler) GetPostBySlug(c *gin.Context) {
	slug := c.Param("slug")

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	post, err := h.postService.GetBySlug(c.Request.Context(), tenantID, websiteID, slug)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Increment view count for published posts
	if post.Status == models.BlogPostStatusPublished {
		_ = h.postService.IncrementViewCount(c.Request.Context(), tenantID, websiteID, post.ID)
	}

	// Convert models response to DTO response
	postResponse := h.convertToPostResponse(post)

	response.SuccessWithContext(c, postResponse)
}

// UpdatePost updates a blog post
// @Summary      Update a blog post
// @Description  Updates an existing blog post with the provided data
// @Tags         Blog Posts
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Blog post ID"
// @Param        request body dto.BlogPostUpdateRequest true "Blog post update data"
// @Success      200 {object} response.Response{data=dto.BlogPostResponse} "Blog post updated successfully"
// @Failure      400 {object} response.Response "Invalid request body or post ID"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Post not found"
// @Failure      500 {object} response.Response "Failed to update post"
// @Router       /api/cms/v1/blog/posts/{id} [put]
func (h *BlogPostHandler) UpdatePost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	var req dto.BlogPostUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	// Featured image is now handled as URL string directly
	featuredImageURL := req.FeaturedImage

	// Set scheduled_at based on status
	var scheduledAt *time.Time
	if req.Status != nil && *req.Status == models.BlogPostStatusScheduled && req.PublishedAt != nil {
		scheduledAt = req.PublishedAt
	}

	// Handle tag updates - always use sync if tag_ids provided
	var tagIDs []uint
	var useTagSync bool

	if req.TagIDs != nil {
		// Use provided tag_ids (even if empty array to clear all tags)
		tagIDs = *req.TagIDs
		useTagSync = true
	}

	// Handle related posts updates - always use sync if related_post_ids provided
	var relatedPostIDs []uint
	var useRelatedPostSync bool

	if req.RelatedPostIDs != nil {
		// Use provided related_post_ids (even if empty array to clear all related posts)
		relatedPostIDs = *req.RelatedPostIDs
		useRelatedPostSync = true
	}

	// Convert SEO metadata from DTO to models
	var seoMetadata *models.SEOMetadata
	if req.SEO != nil {
		seoMetadata = &models.SEOMetadata{
			MetaTitle:          req.SEO.MetaTitle,
			MetaDescription:    req.SEO.MetaDescription,
			MetaKeywords:       req.SEO.MetaKeywords,
			MetaRobots:         req.SEO.MetaRobots,
			CanonicalURL:       req.SEO.CanonicalURL,
			OGTitle:            req.SEO.OGTitle,
			OGDescription:      req.SEO.OGDescription,
			OGImage:            req.SEO.OGImage,
			OGType:             req.SEO.OGType,
			OGURL:              req.SEO.OGURL,
			OGSiteName:         req.SEO.OGSiteName,
			OGLocale:           req.SEO.OGLocale,
			TwitterCard:        req.SEO.TwitterCard,
			TwitterTitle:       req.SEO.TwitterTitle,
			TwitterDescription: req.SEO.TwitterDescription,
			TwitterImage:       req.SEO.TwitterImage,
			TwitterCreator:     req.SEO.TwitterCreator,
			TwitterSite:        req.SEO.TwitterSite,
			SchemaType:         req.SEO.SchemaType,
			SchemaData:         req.SEO.SchemaData,
			FocusKeyword:       req.SEO.FocusKeyword,
		}
	}

	// Convert DTO to models
	serviceReq := &models.BlogPostUpdateRequest{
		CategoryIDs:       req.CategoryIDs,
		PrimaryCategoryID: req.PrimaryCategoryID,
		ScheduledAt:       scheduledAt,
		SEOMetadata:       seoMetadata,
	}

	// Only set TagIDs if we have tag updates
	if useTagSync {
		serviceReq.TagIDs = &tagIDs
	}

	// Only set RelatedPostIDs if we have related posts updates
	if useRelatedPostSync {
		serviceReq.RelatedPostIDs = &relatedPostIDs
	}

	// Handle pointer fields - assign pointer values directly
	if req.Slug != nil {
		serviceReq.Slug = req.Slug
	}
	if req.Title != nil {
		serviceReq.Title = req.Title
	}
	if req.Content != nil {
		serviceReq.Content = req.Content
	}
	if req.Excerpt != nil {
		serviceReq.Excerpt = req.Excerpt
	}
	if req.Type != nil {
		serviceReq.Type = req.Type
	}
	if req.IsFeatured != nil {
		serviceReq.IsFeatured = req.IsFeatured
	}
	if req.IsSticky != nil {
		serviceReq.IsSticky = req.IsSticky
	}
	if req.AllowComments != nil {
		serviceReq.AllowComments = req.AllowComments
	}
	if req.Password != nil {
		serviceReq.Password = req.Password
	}
	if featuredImageURL != nil {
		serviceReq.FeaturedImage = featuredImageURL
	}
	if req.Status != nil {
		serviceReq.Status = req.Status
	}

	post, err := h.postService.Update(c.Request.Context(), tenantID, websiteID, uint(id), serviceReq)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Convert models response to DTO response
	postResponse := h.convertToPostResponse(post)

	response.SuccessWithContext(c, postResponse)
}

// DeletePost deletes a blog post
func (h *BlogPostHandler) DeletePost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	err = h.postService.Delete(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, nil)
}

// ListPosts lists blog posts with filtering
func (h *BlogPostHandler) ListPosts(c *gin.Context) {
	// Use DTO filter for better cursor pagination support
	var filter dto.BlogPostFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid filter parameters"))
		return
	}

	// Get tenant ID from context (required for public routes with X-Tenant-ID header)
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Tenant ID is required. Please provide X-Tenant-ID header"))
		return
	}

	// Get website ID from middleware (required)
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		// Debug: Check what's in the context
		websiteIDFromHeader := c.GetHeader("X-Website-ID")
		websiteIDFromContext, exists := c.Get("website_id")
		middleware.AbortWithError(c, utils.BadRequestError(fmt.Sprintf("website_id is required in header - Header: %s, Context exists: %v, Context value: %v",
			websiteIDFromHeader, exists, websiteIDFromContext)))
		return
	}

	// Create cursor request for pagination
	cursorReq := &pagination.CursorRequest{
		Cursor: filter.Cursor,
		Limit:  pagination.ValidateLimit(filter.Limit),
	}

	// Use cursor-based pagination
	result, cursorResp, err := h.postService.ListWithCursor(c.Request.Context(), tenantID, uint(websiteID), cursorReq, map[string]interface{}{
		"title":       filter.Title,
		"status":      filter.Status,
		"type":        filter.Type,
		"category_id": filter.CategoryID,
		"author_id":   filter.UserID,
		"is_featured": filter.IsFeatured,
		"tag_id":      filter.TagID,
		"date_from":   filter.DateFrom,
		"date_to":     filter.DateTo,
		"exclude_ids": filter.ExcludeIDs,
		"sort_by":     filter.SortBy,
		"sort_order":  filter.SortOrder,
	})
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.CursorPaginatedWithContext(c, result, *cursorResp)
}

// ListPostsWithCursor lists blog posts with cursor-based pagination
// @Summary      List blog posts with cursor pagination
// @Description  Get paginated list of blog posts with filters using cursor pagination
// @Tags         Blog Posts
// @Produce      json
// @Security     Bearer
// @Param        website_id path uint true "Website ID"
// @Param        title query string false "Title filter"
// @Param        status query string false "Status filter" Enums(draft,review,published,scheduled,archived,rejected)
// @Param        type query string false "Type filter" Enums(post,page,announcement)
// @Param        category_id query uint false "Category ID filter"
// @Param        author_id query uint false "Author ID filter"
// @Param        is_featured query bool false "Featured filter"
// @Param        tag_id query uint false "Tag ID filter"
// @Param        date_from query string false "Date from filter (RFC3339)"
// @Param        date_to query string false "Date to filter (RFC3339)"
// @Param        cursor query string false "Pagination cursor"
// @Param        limit query int false "Items per page" default(20)
// @Param        sort_by query string false "Sort by field" Enums(id,title,created_at,updated_at,published_at,view_count) default(created_at)
// @Param        sort_order query string false "Sort order" Enums(asc,desc) default(desc)
// @Success      200 {object} dto.BlogPostListResponse
// @Failure      400 {object} response.Response "Invalid filter parameters"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to retrieve blog posts"
// @Router       /api/cms/v1/websites/{website_id}/blog/posts [get]
func (h *BlogPostHandler) ListPostsWithCursor(c *gin.Context) {
	var filter dto.BlogPostFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid filter parameters"))
		return
	}

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Create cursor request
	cursorReq := &pagination.CursorRequest{
		Cursor: filter.Cursor,
		Limit:  pagination.ValidateLimit(filter.Limit),
	}

	// Build filters map
	filters := make(map[string]interface{})
	if filter.Title != "" {
		filters["title"] = filter.Title
	}
	if filter.Status != nil {
		filters["status"] = *filter.Status
	}
	if filter.Type != nil {
		filters["type"] = *filter.Type
	}
	if filter.CategoryID != nil {
		filters["category_id"] = *filter.CategoryID
	}
	if filter.UserID != nil {
		filters["author_id"] = *filter.UserID
	}
	if filter.IsFeatured != nil {
		filters["is_featured"] = *filter.IsFeatured
	}
	if filter.TagID != nil {
		filters["tag_id"] = *filter.TagID
	}
	if filter.DateFrom != nil {
		filters["date_from"] = *filter.DateFrom
	}
	if filter.DateTo != nil {
		filters["date_to"] = *filter.DateTo
	}
	if len(filter.ExcludeIDs) > 0 {
		filters["exclude_ids"] = filter.ExcludeIDs
	}
	if filter.SortBy != "" {
		filters["sort_by"] = filter.SortBy
	}
	if filter.SortOrder != "" {
		filters["sort_order"] = filter.SortOrder
	}

	blogPostResponse, cursorResp, err := h.postService.ListWithCursor(c.Request.Context(), tenantID, websiteID, cursorReq, filters)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.CursorPaginatedWithContext(c, blogPostResponse, *cursorResp)
}

// GetPublishedPosts retrieves published blog posts
func (h *BlogPostHandler) GetPublishedPosts(c *gin.Context) {
	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	posts, total, err := h.postService.GetPublished(c.Request.Context(), tenantID, websiteID, limit, offset)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	data := map[string]interface{}{
		"posts": posts,
		"total": total,
	}

	response.SuccessWithContext(c, data)
}

// GetFeaturedPosts retrieves featured blog posts
func (h *BlogPostHandler) GetFeaturedPosts(c *gin.Context) {
	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	posts, err := h.postService.GetFeatured(c.Request.Context(), tenantID, websiteID, limit)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, posts)
}

// GetRelatedPosts retrieves related blog posts
func (h *BlogPostHandler) GetRelatedPosts(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "5"))

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	posts, err := h.postService.GetRelated(c.Request.Context(), tenantID, websiteID, uint(id), limit)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, posts)
}

// PublishPost publishes a blog post
func (h *BlogPostHandler) PublishPost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	post, err := h.postService.Publish(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, post)
}

// UnpublishPost unpublishes a blog post
func (h *BlogPostHandler) UnpublishPost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	post, err := h.postService.Unpublish(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, post)
}

// GetPostStats retrieves blog post statistics
func (h *BlogPostHandler) GetPostStats(c *gin.Context) {
	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	stats, err := h.postService.GetStats(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, stats)
}

// GetPopularPosts retrieves popular blog posts
func (h *BlogPostHandler) GetPopularPosts(c *gin.Context) {
	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	days, _ := strconv.Atoi(c.DefaultQuery("days", "7"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	posts, err := h.postService.GetPopular(c.Request.Context(), tenantID, websiteID, days, limit)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, posts)
}

// AttachTags attaches tags to a blog post
func (h *BlogPostHandler) AttachTags(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	var req struct {
		TagIDs []uint `json:"tag_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	err = h.postService.AttachTags(c.Request.Context(), tenantID, websiteID, uint(id), req.TagIDs)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, nil)
}

// DetachTags detaches tags from a blog post
func (h *BlogPostHandler) DetachTags(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	var req struct {
		TagIDs []uint `json:"tag_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	err = h.postService.DetachTags(c.Request.Context(), tenantID, websiteID, uint(id), req.TagIDs)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, nil)
}

// SyncTags syncs tags for a blog post
func (h *BlogPostHandler) DuplicatePost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Get tenant ID from context
	tenantID := c.GetUint("tenantID")
	if tenantID == 0 {
		middleware.AbortWithError(c, utils.UnauthorizedError("Tenant ID not found"))
		return
	}

	// Duplicate the post
	duplicatedPost, err := h.postService.Duplicate(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Convert to DTO response
	responseData := h.convertToPostResponse(duplicatedPost)

	response.SuccessWithContext(c, responseData)
}

func (h *BlogPostHandler) SyncTags(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	var req struct {
		TagIDs []uint `json:"tag_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	err = h.postService.SyncTags(c.Request.Context(), tenantID, websiteID, uint(id), req.TagIDs)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, nil)
}
