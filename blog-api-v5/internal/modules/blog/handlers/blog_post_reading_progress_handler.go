package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogPostReadingProgressHandler handles HTTP requests for reading progress tracking
type BlogPostReadingProgressHandler struct {
	progressService services.BlogPostReadingProgressService
}

// NewBlogPostReadingProgressHandler creates a new reading progress handler
func NewBlogPostReadingProgressHandler(
	progressService services.BlogPostReadingProgressService,
) *BlogPostReadingProgressHandler {
	return &BlogPostReadingProgressHandler{
		progressService: progressService,
	}
}

// UpdateReadingProgress godoc
// @Summary Update reading progress for a blog post
// @Description Update or create reading progress tracking for a specific blog post
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path uint true "Blog Post ID"
// @Param request body dto.UpdateReadingProgressRequest true "Reading progress data"
// @Success 200 {object} response.Response "Reading progress updated successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Blog post not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/posts/{id}/progress [post]
func (h *BlogPostReadingProgressHandler) UpdateReadingProgress(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Get blog post ID from path
	blogPostIDStr := c.Param("id")
	blogPostID, err := strconv.ParseUint(blogPostIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid blog post ID")
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User not authenticated")
		return
	}
	userIDUint, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user ID format")
		return
	}

	// Bind and validate request
	var req dto.UpdateReadingProgressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid request body: "+err.Error())
		return
	}

	// Basic validation
	if req.ReadingProgressPercentage < 0 || req.ReadingProgressPercentage > 100 {
		response.BadRequestWithContext(c, "Reading progress percentage must be between 0 and 100")
		return
	}
	if req.ScrollDepthPercentage < 0 || req.ScrollDepthPercentage > 100 {
		response.BadRequestWithContext(c, "Scroll depth percentage must be between 0 and 100")
		return
	}

	// Update reading progress
	progress, err := h.progressService.UpdateReadingProgress(
		c.Request.Context(),
		uint(tenantID),
		userIDUint,
		uint(blogPostID),
		&req,
		c,
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to update reading progress: "+err.Error())
		return
	}

	response.SuccessWithContext(c, progress)
}

// GetReadingProgress godoc
// @Summary Get reading progress for a blog post
// @Description Retrieve reading progress tracking for a specific blog post and user
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path uint true "Blog Post ID"
// @Success 200 {object} response.Response "Reading progress retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Reading progress not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/posts/{id}/progress [get]
func (h *BlogPostReadingProgressHandler) GetReadingProgress(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Get blog post ID from path
	blogPostIDStr := c.Param("id")
	blogPostID, err := strconv.ParseUint(blogPostIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid blog post ID")
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User not authenticated")
		return
	}
	userIDUint, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user ID format")
		return
	}

	// Get reading progress
	progress, err := h.progressService.GetReadingProgress(
		c.Request.Context(),
		uint(tenantID),
		userIDUint,
		uint(blogPostID),
	)
	if err != nil {
		response.NotFoundWithContext(c, "Reading progress not found: "+err.Error())
		return
	}

	response.SuccessWithContext(c, progress)
}

// GetPostReadingStats godoc
// @Summary Get reading statistics for a blog post
// @Description Retrieve comprehensive reading statistics for a specific blog post
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path uint true "Blog Post ID"
// @Success 200 {object} response.Response{data=dto.PostReadingStatsResponse} "Post reading statistics retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Blog post not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/posts/{id}/reading-stats [get]
func (h *BlogPostReadingProgressHandler) GetPostReadingStats(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Get blog post ID from path
	blogPostIDStr := c.Param("id")
	blogPostID, err := strconv.ParseUint(blogPostIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid blog post ID")
		return
	}

	// Get post reading statistics
	stats, err := h.progressService.GetPostReadingStats(
		c.Request.Context(),
		uint(tenantID),
		uint(blogPostID),
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get post reading statistics: "+err.Error())
		return
	}

	response.SuccessWithContext(c, stats)
}

// GetUserReadingStats godoc
// @Summary Get reading statistics for current user
// @Description Retrieve comprehensive reading statistics for the authenticated user
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Success 200 {object} response.Response{data=dto.UserReadingStatsResponse} "User reading statistics retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/reading-stats [get]
func (h *BlogPostReadingProgressHandler) GetUserReadingStats(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User not authenticated")
		return
	}
	userIDUint, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user ID format")
		return
	}

	// Get user reading statistics
	stats, err := h.progressService.GetUserReadingStats(
		c.Request.Context(),
		uint(tenantID),
		userIDUint,
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get user reading statistics: "+err.Error())
		return
	}

	response.SuccessWithContext(c, stats)
}

// GetReadingAnalytics godoc
// @Summary Get comprehensive reading analytics
// @Description Retrieve comprehensive reading analytics with trends and statistics
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param post_id query uint false "Blog Post ID (optional)"
// @Param user_id query uint false "User ID (optional, admin only)"
// @Param days query int false "Number of days for trends (default: 30)"
// @Success 200 {object} response.Response{data=dto.ReadingProgressAnalyticsResponse} "Reading analytics retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/reading-analytics [get]
func (h *BlogPostReadingProgressHandler) GetReadingAnalytics(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Parse optional query parameters
	var blogPostID *uint
	if postIDStr := c.Query("post_id"); postIDStr != "" {
		if postID, err := strconv.ParseUint(postIDStr, 10, 32); err == nil {
			blogPostIDUint := uint(postID)
			blogPostID = &blogPostIDUint
		}
	}

	var userID *uint
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if uID, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userIDUint := uint(uID)
			userID = &userIDUint
		}
	}

	days := 30 // Default to 30 days
	if daysStr := c.Query("days"); daysStr != "" {
		if d, err := strconv.Atoi(daysStr); err == nil && d > 0 && d <= 365 {
			days = d
		}
	}

	// Get reading analytics
	analytics, err := h.progressService.GetReadingAnalytics(
		c.Request.Context(),
		uint(tenantID),
		blogPostID,
		userID,
		days,
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get reading analytics: "+err.Error())
		return
	}

	response.SuccessWithContext(c, analytics)
}

// GetUserReadingHistory godoc
// @Summary Get reading history for current user
// @Description Retrieve paginated reading history for the authenticated user
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20, max: 100)"
// @Success 200 {object} response.Response{data=dto.ReadingProgressListResponse} "User reading history retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/reading-history [get]
func (h *BlogPostReadingProgressHandler) GetUserReadingHistory(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User not authenticated")
		return
	}
	userIDUint, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user ID format")
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSize := 20
	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// Get user reading history
	history, err := h.progressService.GetUserReadingHistory(
		c.Request.Context(),
		uint(tenantID),
		userIDUint,
		page,
		pageSize,
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get user reading history: "+err.Error())
		return
	}

	response.SuccessWithContext(c, history)
}

// GetPostReaders godoc
// @Summary Get readers for a blog post
// @Description Retrieve paginated list of readers for a specific blog post
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path uint true "Blog Post ID"
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20, max: 100)"
// @Success 200 {object} response.Response{data=dto.ReadingProgressListResponse} "Post readers retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Blog post not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/posts/{id}/readers [get]
func (h *BlogPostReadingProgressHandler) GetPostReaders(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Get blog post ID from path
	blogPostIDStr := c.Param("id")
	blogPostID, err := strconv.ParseUint(blogPostIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid blog post ID")
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSize := 20
	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// Get post readers
	readers, err := h.progressService.GetPostReaders(
		c.Request.Context(),
		uint(tenantID),
		uint(blogPostID),
		page,
		pageSize,
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get post readers: "+err.Error())
		return
	}

	response.SuccessWithContext(c, readers)
}

// GetUserReadingHistoryWithCursor godoc
// @Summary Get reading history for current user with cursor pagination
// @Description Retrieve reading history for the authenticated user using cursor-based pagination
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param cursor query string false "Pagination cursor"
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/reading-history/cursor [get]
func (h *BlogPostReadingProgressHandler) GetUserReadingHistoryWithCursor(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.UnauthorizedWithContext(c, "User not authenticated")
		return
	}
	userIDUint, ok := userID.(uint)
	if !ok {
		response.InternalServerErrorWithContext(c, "Invalid user ID format")
		return
	}

	// Parse cursor request
	var cursorReq pagination.CursorRequest
	if err := c.ShouldBindQuery(&cursorReq); err != nil {
		response.BadRequestWithContext(c, "Invalid cursor parameters", err.Error())
		return
	}

	// Set default limit
	if cursorReq.Limit == 0 {
		cursorReq.Limit = 20
	}

	// Get user reading history with cursor
	progress, cursorResp, err := h.progressService.ListByUserWithCursor(
		c.Request.Context(),
		uint(tenantID),
		userIDUint,
		&cursorReq,
		nil, // No additional filters for user history
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get user reading history: "+err.Error())
		return
	}

	response.SuccessWithContext(c, map[string]interface{}{
		"progress": progress,
		"cursor":   cursorResp,
	})
}

// GetPostReadersWithCursor godoc
// @Summary Get readers for a blog post with cursor pagination
// @Description Retrieve list of readers for a specific blog post using cursor-based pagination
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path uint true "Blog Post ID"
// @Param cursor query string false "Pagination cursor"
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 404 {object} response.Response "Blog post not found"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/posts/{id}/readers/cursor [get]
func (h *BlogPostReadingProgressHandler) GetPostReadersWithCursor(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Get blog post ID from path
	blogPostIDStr := c.Param("id")
	blogPostID, err := strconv.ParseUint(blogPostIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid blog post ID")
		return
	}

	// Parse cursor request
	var cursorReq pagination.CursorRequest
	if err := c.ShouldBindQuery(&cursorReq); err != nil {
		response.BadRequestWithContext(c, "Invalid cursor parameters", err.Error())
		return
	}

	// Set default limit
	if cursorReq.Limit == 0 {
		cursorReq.Limit = 20
	}

	// Get post readers with cursor
	progress, cursorResp, err := h.progressService.ListByPostWithCursor(
		c.Request.Context(),
		uint(tenantID),
		uint(blogPostID),
		&cursorReq,
		nil, // No additional filters for post readers
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to get post readers: "+err.Error())
		return
	}

	response.SuccessWithContext(c, map[string]interface{}{
		"progress": progress,
		"cursor":   cursorResp,
	})
}

// ListReadingProgressWithCursor godoc
// @Summary List all reading progress with cursor pagination
// @Description Retrieve all reading progress records using cursor-based pagination (admin only)
// @Tags Blog Reading Progress
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param cursor query string false "Pagination cursor"
// @Param limit query int false "Items per page" default(20)
// @Param user_id query uint false "Filter by user ID"
// @Param post_id query uint false "Filter by blog post ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/reading-progress/cursor [get]
func (h *BlogPostReadingProgressHandler) ListReadingProgressWithCursor(c *gin.Context) {
	// Get tenant ID from header
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	if tenantIDStr == "" {
		response.BadRequestWithContext(c, "Tenant ID is required")
		return
	}
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid tenant ID")
		return
	}

	// Parse cursor request
	var cursorReq pagination.CursorRequest
	if err := c.ShouldBindQuery(&cursorReq); err != nil {
		response.BadRequestWithContext(c, "Invalid cursor parameters", err.Error())
		return
	}

	// Set default limit
	if cursorReq.Limit == 0 {
		cursorReq.Limit = 20
	}

	// Build filters
	filters := make(map[string]interface{})

	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if uID, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			filters["user_id"] = uint(uID)
		}
	}

	if postIDStr := c.Query("post_id"); postIDStr != "" {
		if pID, err := strconv.ParseUint(postIDStr, 10, 32); err == nil {
			filters["post_id"] = uint(pID)
		}
	}

	// List all reading progress with cursor
	progress, cursorResp, err := h.progressService.ListWithCursor(
		c.Request.Context(),
		uint(tenantID),
		&cursorReq,
		filters,
	)
	if err != nil {
		response.InternalServerErrorWithContext(c, "Failed to list reading progress: "+err.Error())
		return
	}

	response.SuccessWithContext(c, map[string]interface{}{
		"progress": progress,
		"cursor":   cursorResp,
	})
}
