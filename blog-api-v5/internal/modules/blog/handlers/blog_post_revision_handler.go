package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// BlogPostRevisionHandler handles HTTP requests for blog post revisions
type BlogPostRevisionHandler struct {
	revisionService services.BlogPostRevisionService
	logger          utils.Logger
}

// NewBlogPostRevisionHandler creates a new blog post revision handler
func NewBlogPostRevisionHandler(revisionService services.BlogPostRevisionService, logger utils.Logger) *BlogPostRevisionHandler {
	return &BlogPostRevisionHandler{
		revisionService: revisionService,
		logger:          logger,
	}
}

// CreateRevision creates a new revision for a blog post
// @Summary      Create a new blog post revision
// @Description  Creates a new revision/version of a blog post
// @Tags         Blog Post Revisions
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Post ID"
// @Param        request body models.BlogPostRevisionCreateRequest true "Revision data"
// @Success      201 {object} response.Response{data=models.BlogPostRevisionResponse} "Revision created successfully"
// @Failure      400 {object} response.Response "Invalid request"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Post not found"
// @Failure      500 {object} response.Response "Failed to create revision"
// @Router       /api/cms/v1/blog/posts/{id}/revisions [post]
func (h *BlogPostRevisionHandler) CreateRevision(c *gin.Context) {
	// Get IDs from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	userID, err := context.GetUserIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get post ID from URL
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Parse request body
	var req models.BlogPostRevisionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Set IDs from context
	req.TenantID = tenantID
	req.WebsiteID = websiteID
	req.PostID = uint(postID)
	req.UserID = userID

	// Create revision
	revision, err := h.revisionService.Create(c, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create revision")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.CreatedWithContext(c, revision)
}

// GetRevisions retrieves revisions for a blog post
// @Summary      Get blog post revisions
// @Description  Retrieves all revisions for a specific blog post
// @Tags         Blog Post Revisions
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Post ID"
// @Param        limit query int false "Limit number of results" default(20)
// @Param        offset query int false "Offset for pagination" default(0)
// @Success      200 {object} response.Response{data=[]models.BlogPostRevisionResponse} "Revisions retrieved successfully"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Post not found"
// @Failure      500 {object} response.Response "Failed to get revisions"
// @Router       /api/cms/v1/blog/posts/{id}/revisions [get]
func (h *BlogPostRevisionHandler) GetRevisions(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get post ID from URL
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get pagination parameters
	limit := 20
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := 0
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	// Get revisions
	revisions, total, err := h.revisionService.GetByPostID(c, tenantID, uint(postID), limit, offset)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get revisions")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Return with pagination metadata in response body
	response.SuccessWithContext(c, map[string]interface{}{
		"items": revisions,
		"total": total,
		"limit": limit,
		"offset": offset,
	})
}

// GetRevision retrieves a specific revision
// @Summary      Get a specific revision
// @Description  Retrieves a specific revision by ID
// @Tags         Blog Post Revisions
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Post ID"
// @Param        revisionId path int true "Revision ID"
// @Success      200 {object} response.Response{data=models.BlogPostRevisionResponse} "Revision retrieved successfully"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Revision not found"
// @Failure      500 {object} response.Response "Failed to get revision"
// @Router       /api/cms/v1/blog/posts/{id}/revisions/{revisionId} [get]
func (h *BlogPostRevisionHandler) GetRevision(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get revision ID from URL
	revisionID, err := strconv.ParseUint(c.Param("revisionId"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid revision ID"))
		return
	}

	// Get revision
	revision, err := h.revisionService.GetByID(c, tenantID, uint(revisionID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get revision")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, revision)
}

// CompareRevisions compares two revisions
// @Summary      Compare two revisions
// @Description  Compares two revisions and shows the differences
// @Tags         Blog Post Revisions
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Post ID"
// @Param        from query int true "From revision ID"
// @Param        to query int true "To revision ID"
// @Success      200 {object} response.Response{data=models.BlogPostRevisionComparison} "Comparison retrieved successfully"
// @Failure      400 {object} response.Response "Invalid parameters"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Revision not found"
// @Failure      500 {object} response.Response "Failed to compare revisions"
// @Router       /api/cms/v1/blog/posts/{id}/revisions/compare [get]
func (h *BlogPostRevisionHandler) CompareRevisions(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get from and to revision IDs from query
	fromIDStr := c.Query("from")
	toIDStr := c.Query("to")

	if fromIDStr == "" || toIDStr == "" {
		middleware.AbortWithError(c, utils.BadRequestError("Both 'from' and 'to' revision IDs are required"))
		return
	}

	fromID, err := strconv.ParseUint(fromIDStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid 'from' revision ID"))
		return
	}

	toID, err := strconv.ParseUint(toIDStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid 'to' revision ID"))
		return
	}

	// Compare revisions
	comparison, err := h.revisionService.CompareRevisions(c, tenantID, uint(fromID), uint(toID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to compare revisions")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, comparison)
}

// RestoreRevision restores a blog post to a specific revision
// @Summary      Restore a revision
// @Description  Restores a blog post content to a specific revision
// @Tags         Blog Post Revisions
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Post ID"
// @Param        revisionId path int true "Revision ID"
// @Success      200 {object} response.Response{data=models.BlogPostResponse} "Post restored successfully"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Revision not found"
// @Failure      500 {object} response.Response "Failed to restore revision"
// @Router       /api/cms/v1/blog/posts/{id}/revisions/{revisionId}/restore [post]
func (h *BlogPostRevisionHandler) RestoreRevision(c *gin.Context) {
	// Get IDs from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get post ID and revision ID from URL
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	revisionID, err := strconv.ParseUint(c.Param("revisionId"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid revision ID"))
		return
	}

	// Get the revision
	revision, err := h.revisionService.GetByID(c, tenantID, uint(revisionID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get revision")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	// Ensure revision belongs to the correct post
	if revision.PostID != uint(postID) {
		middleware.AbortWithError(c, utils.BadRequestError("Revision does not belong to this post"))
		return
	}

	// Note: The actual restore functionality would need to be implemented in the post service
	// For now, we'll return a message indicating this feature needs implementation
	response.SuccessWithContext(c, map[string]interface{}{
		"message": "Restore functionality not yet implemented",
		"revision": revision,
		"tenant_id": tenantID,
		"website_id": websiteID,
	})
}

// DeleteRevision deletes a specific revision
// @Summary      Delete a revision
// @Description  Deletes a specific revision
// @Tags         Blog Post Revisions
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Post ID"
// @Param        revisionId path int true "Revision ID"
// @Success      200 {object} response.Response "Revision deleted successfully"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "Revision not found"
// @Failure      500 {object} response.Response "Failed to delete revision"
// @Router       /api/cms/v1/blog/posts/{id}/revisions/{revisionId} [delete]
func (h *BlogPostRevisionHandler) DeleteRevision(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get revision ID from URL
	revisionID, err := strconv.ParseUint(c.Param("revisionId"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid revision ID"))
		return
	}

	// Delete revision
	err = h.revisionService.Delete(c, tenantID, uint(revisionID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete revision")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, nil)
}

// GetLatestRevision gets the latest revision for a post
// @Summary      Get latest revision
// @Description  Gets the most recent revision for a blog post
// @Tags         Blog Post Revisions
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Post ID"
// @Success      200 {object} response.Response{data=models.BlogPostRevisionResponse} "Latest revision retrieved successfully"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      404 {object} response.Response "No revisions found"
// @Failure      500 {object} response.Response "Failed to get latest revision"
// @Router       /api/cms/v1/blog/posts/{id}/revisions/latest [get]
func (h *BlogPostRevisionHandler) GetLatestRevision(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get post ID from URL
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get latest revision
	revision, err := h.revisionService.GetLatest(c, tenantID, uint(postID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get latest revision")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, revision)
}

// CleanupOldRevisions deletes old revisions keeping only specified count
// @Summary      Cleanup old revisions
// @Description  Deletes old revisions, keeping only the specified number of recent revisions
// @Tags         Blog Post Revisions
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        id path int true "Post ID"
// @Param        keep query int true "Number of revisions to keep"
// @Success      200 {object} response.Response "Old revisions deleted successfully"
// @Failure      400 {object} response.Response "Invalid parameters"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to cleanup revisions"
// @Router       /api/cms/v1/blog/posts/{id}/revisions/cleanup [delete]
func (h *BlogPostRevisionHandler) CleanupOldRevisions(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get post ID from URL
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get keep count from query
	keepStr := c.Query("keep")
	if keepStr == "" {
		middleware.AbortWithError(c, utils.BadRequestError("'keep' parameter is required"))
		return
	}

	keep, err := strconv.Atoi(keepStr)
	if err != nil || keep < 1 {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid 'keep' value, must be a positive number"))
		return
	}

	// Cleanup old revisions
	err = h.revisionService.DeleteOldRevisions(c, tenantID, uint(postID), keep)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cleanup old revisions")
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.SuccessWithContext(c, map[string]interface{}{
		"message": "Old revisions cleaned up successfully",
		"kept_revisions": keep,
	})
}