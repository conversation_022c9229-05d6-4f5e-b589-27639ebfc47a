package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type BlogPostRoyaltyHandler struct {
	royaltyService services.BlogPostRoyaltyService
	logger         utils.Logger
}

// NewBlogPostRoyaltyHandler creates a new instance of blog post royalty handler
func NewBlogPostRoyaltyHandler(
	royaltyService services.BlogPostRoyaltyService,
	logger utils.Logger,
) *BlogPostRoyaltyHandler {
	return &BlogPostRoyaltyHandler{
		royaltyService: royaltyService,
		logger:         logger,
	}
}

// CreateRoyalty creates a new blog post royalty
// @Summary Create blog post royalty
// @Description Create a new royalty record for a blog post
// @Tags Blog Post Royalties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string true "Website ID"
// @Param request body models.BlogPostRoyaltyCreateRequest true "Create royalty request"
// @Success 201 {object} response.Response{data=models.BlogPostRoyaltyResponse}
// @Failure 400 {object} response.Response
// @Failure 409 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/royalties [post]
func (h *BlogPostRoyaltyHandler) CreateRoyalty(c *gin.Context) {
	var req models.BlogPostRoyaltyCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.BadRequestWithContext(c, "Invalid request body", err.Error())
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&req); err != nil {
		response.BadRequestWithContext(c, "Validation failed", err.Error())
		return
	}

	// Get tenant and website ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequestWithContext(c, err.Error())
		return
	}

	royalty, err := h.royaltyService.Create(c.Request.Context(), tenantID, websiteID, &req)
	if err != nil {
		h.logger.Error("Failed to create royalty", "error", err)
		if err.Error() == "royalty already exists for this post" {
			response.ConflictWithContext(c, "Royalty already exists")
			return
		}
		response.InternalServerErrorWithContext(c, "Failed to create royalty", err.Error())
		return
	}

	response.CreatedWithContext(c, royalty)
}

// GetRoyalty retrieves a blog post royalty by ID
// @Summary Get blog post royalty
// @Description Get a blog post royalty by ID
// @Tags Blog Post Royalties
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path string true "Royalty ID"
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyResponse}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/royalties/{id} [get]
func (h *BlogPostRoyaltyHandler) GetRoyalty(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid royalty ID", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	royalty, err := h.royaltyService.GetByID(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		h.logger.Error("Failed to get royalty", "error", err, "id", id)
		response.NotFoundWithContext(c, "Royalty not found")
		return
	}

	response.SuccessWithContext(c, royalty)
}

// GetRoyaltyByPost retrieves a blog post royalty by post ID
// @Summary Get blog post royalty by post ID
// @Description Get a blog post royalty by post ID
// @Tags Blog Post Royalties
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param postId path string true "Post ID"
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyResponse}
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{postId}/royalty [get]
func (h *BlogPostRoyaltyHandler) GetRoyaltyByPost(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("postId"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	royalty, err := h.royaltyService.GetByPostID(c.Request.Context(), tenantID, uint(postID))
	if err != nil {
		h.logger.Error("Failed to get royalty by post", "error", err, "postID", postID)
		response.NotFoundWithContext(c, "Royalty not found")
		return
	}

	response.SuccessWithContext(c, royalty)
}

// UpdateRoyalty updates a blog post royalty
// @Summary Update blog post royalty
// @Description Update a blog post royalty
// @Tags Blog Post Royalties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path string true "Royalty ID"
// @Param request body models.BlogPostRoyaltyUpdateRequest true "Update royalty request"
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/royalties/{id} [put]
func (h *BlogPostRoyaltyHandler) UpdateRoyalty(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid royalty ID", err.Error())
		return
	}

	var req models.BlogPostRoyaltyUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.BadRequestWithContext(c, "Invalid request body", err.Error())
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&req); err != nil {
		response.BadRequestWithContext(c, "Validation failed", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	royalty, err := h.royaltyService.Update(c.Request.Context(), tenantID, uint(id), &req)
	if err != nil {
		h.logger.Error("Failed to update royalty", "error", err, "id", id)
		response.InternalServerErrorWithContext(c, "Failed to update royalty", err.Error())
		return
	}

	response.SuccessWithContext(c, royalty)
}

// DeleteRoyalty deletes a blog post royalty
// @Summary Delete blog post royalty
// @Description Delete a blog post royalty
// @Tags Blog Post Royalties
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param id path string true "Royalty ID"
// @Success 200 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/royalties/{id} [delete]
func (h *BlogPostRoyaltyHandler) DeleteRoyalty(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid royalty ID", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	err = h.royaltyService.Delete(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		h.logger.Error("Failed to delete royalty", "error", err, "id", id)
		response.InternalServerErrorWithContext(c, "Failed to delete royalty", err.Error())
		return
	}

	response.SuccessWithContext(c, nil)
}

// ListRoyalties retrieves blog post royalties with filtering
// @Summary List blog post royalties
// @Description Get a list of blog post royalties with filtering and pagination
// @Tags Blog Post Royalties
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string false "Website ID"
// @Param post_id query int false "Filter by post ID"
// @Param website_id query int false "Filter by website ID"
// @Param page query int false "Page number" default(1)
// @Param page_size query int false "Page size" default(20)
// @Param sort_by query string false "Sort by field" Enums(created_at,updated_at,final_royalty)
// @Param sort_order query string false "Sort order" Enums(asc,desc)
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyListResponse}
// @Failure 500 {object} response.Response
// @Router /blog/royalties [get]
func (h *BlogPostRoyaltyHandler) ListRoyalties(c *gin.Context) {
	var filter models.BlogPostRoyaltyFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		response.BadRequestWithContext(c, "Invalid query parameters", err.Error())
		return
	}

	// Validate filter
	if err := utils.ValidateStruct(&filter); err != nil {
		response.BadRequestWithContext(c, "Validation failed", err.Error())
		return
	}

	royalties, err := h.royaltyService.List(c.Request.Context(), &filter)
	if err != nil {
		h.logger.Error("Failed to list royalties", "error", err)
		response.InternalServerErrorWithContext(c, "Failed to list royalties", err.Error())
		return
	}

	// Convert to cursor pagination format as per cursor-rule.md
	var nextCursor string
	hasMore := false
	
	// Use same defaults as service
	page := royalties.Page
	pageSize := royalties.Size
	
	// Calculate if there are more items
	if page > 0 && pageSize > 0 {
		totalPages := (int(royalties.Total) + pageSize - 1) / pageSize
		hasMore = page < totalPages
		
		// Create cursor for next page if there are more items
		if hasMore && len(royalties.Data) > 0 {
			lastItem := royalties.Data[len(royalties.Data)-1]
			cursor, err := pagination.EncodeCursor(lastItem.ID, lastItem.CreatedAt)
			if err != nil {
				h.logger.Error("Failed to encode cursor", "error", err)
				// Continue without cursor on error
			} else {
				nextCursor = cursor
			}
		}
	}
	
	meta := pagination.CursorResponse{
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}
	
	response.CursorPaginatedWithContext(c, royalties.Data, meta)
}

// ListRoyaltiesWithCursor retrieves blog post royalties with cursor pagination
// @Summary List blog post royalties with cursor
// @Description Get a list of blog post royalties with cursor-based pagination
// @Tags Blog Post Royalties
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string false "Website ID"
// @Param cursor query string false "Pagination cursor"
// @Param limit query int false "Number of items to return" default(20)
// @Success 200 {object} response.Response{data=[]models.BlogPostRoyaltyResponse,meta=pagination.CursorResponse}
// @Failure 500 {object} response.Response
// @Router /blog/royalties/cursor [get]
func (h *BlogPostRoyaltyHandler) ListRoyaltiesWithCursor(c *gin.Context) {
	var req pagination.CursorRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequestWithContext(c, "Invalid query parameters", err.Error())
		return
	}

	// Set default limit
	if req.Limit == 0 {
		req.Limit = 20
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequestWithContext(c, err.Error())
		return
	}

	// Additional filters can be added here
	filters := make(map[string]interface{})

	royalties, cursorResp, err := h.royaltyService.ListWithCursor(c.Request.Context(), tenantID, websiteID, &req, filters)
	if err != nil {
		h.logger.Error("Failed to list royalties with cursor", "error", err)
		response.InternalServerErrorWithContext(c, "Failed to list royalties", err.Error())
		return
	}

	response.CursorPaginatedWithContext(c, royalties, *cursorResp)
}

// CalculateRoyalty calculates the royalty amount for a post
// @Summary Calculate royalty
// @Description Calculate the royalty amount based on base amount and coefficients
// @Tags Blog Post Royalties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param postId path string true "Post ID"
// @Param request body models.BlogPostRoyaltyCalculateRequest true "Calculate request"
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyCalculateResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{postId}/royalty/calculate [post]
func (h *BlogPostRoyaltyHandler) CalculateRoyalty(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("postId"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID", err.Error())
		return
	}

	var req models.BlogPostRoyaltyCalculateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		response.BadRequestWithContext(c, "Invalid request body", err.Error())
		return
	}

	// Validate request
	if err := utils.ValidateStruct(&req); err != nil {
		response.BadRequestWithContext(c, "Validation failed", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	calculation, err := h.royaltyService.CalculateRoyalty(c.Request.Context(), tenantID, uint(postID), req.BaseAmount)
	if err != nil {
		h.logger.Error("Failed to calculate royalty", "error", err, "postID", postID)
		response.InternalServerErrorWithContext(c, "Failed to calculate royalty", err.Error())
		return
	}

	response.SuccessWithContext(c, calculation)
}

// UpdateCMSRoyalty updates the CMS calculated royalty amount
// @Summary Update CMS royalty
// @Description Update the CMS auto-calculated royalty amount
// @Tags Blog Post Royalties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param postId path string true "Post ID"
// @Param amount query float64 true "Royalty amount"
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{postId}/royalty/cms [put]
func (h *BlogPostRoyaltyHandler) UpdateCMSRoyalty(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("postId"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID", err.Error())
		return
	}

	amountStr := c.Query("amount")
	if amountStr == "" {
		response.BadRequestWithContext(c, "Amount is required", "")
		return
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid amount", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	royalty, err := h.royaltyService.UpdateCMSRoyalty(c.Request.Context(), tenantID, uint(postID), amount)
	if err != nil {
		h.logger.Error("Failed to update CMS royalty", "error", err, "postID", postID)
		response.InternalServerErrorWithContext(c, "Failed to update CMS royalty", err.Error())
		return
	}

	response.SuccessWithContext(c, royalty)
}

// UpdateEditorRoyalty updates the editor reviewed royalty amount
// @Summary Update editor royalty
// @Description Update the editor reviewed royalty amount
// @Tags Blog Post Royalties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param postId path string true "Post ID"
// @Param amount query float64 true "Royalty amount"
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{postId}/royalty/editor [put]
func (h *BlogPostRoyaltyHandler) UpdateEditorRoyalty(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("postId"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID", err.Error())
		return
	}

	amountStr := c.Query("amount")
	if amountStr == "" {
		response.BadRequestWithContext(c, "Amount is required", "")
		return
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid amount", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	royalty, err := h.royaltyService.UpdateEditorRoyalty(c.Request.Context(), tenantID, uint(postID), amount)
	if err != nil {
		h.logger.Error("Failed to update editor royalty", "error", err, "postID", postID)
		response.InternalServerErrorWithContext(c, "Failed to update editor royalty", err.Error())
		return
	}

	response.SuccessWithContext(c, royalty)
}

// UpdateFinalRoyalty updates the final approved royalty amount
// @Summary Update final royalty
// @Description Update the final approved royalty amount
// @Tags Blog Post Royalties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param postId path string true "Post ID"
// @Param amount query float64 true "Royalty amount"
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyResponse}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{postId}/royalty/final [put]
func (h *BlogPostRoyaltyHandler) UpdateFinalRoyalty(c *gin.Context) {
	postID, err := strconv.ParseUint(c.Param("postId"), 10, 32)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid post ID", err.Error())
		return
	}

	amountStr := c.Query("amount")
	if amountStr == "" {
		response.BadRequestWithContext(c, "Amount is required", "")
		return
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid amount", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	royalty, err := h.royaltyService.UpdateFinalRoyalty(c.Request.Context(), tenantID, uint(postID), amount)
	if err != nil {
		h.logger.Error("Failed to update final royalty", "error", err, "postID", postID)
		response.InternalServerErrorWithContext(c, "Failed to update final royalty", err.Error())
		return
	}

	response.SuccessWithContext(c, royalty)
}

// GetRoyaltyStats retrieves royalty statistics
// @Summary Get royalty statistics
// @Description Get statistical information about royalties
// @Tags Blog Post Royalties
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string false "Website ID"
// @Success 200 {object} response.Response{data=models.BlogPostRoyaltyStats}
// @Failure 500 {object} response.Response
// @Router /blog/royalties/stats [get]
func (h *BlogPostRoyaltyHandler) GetRoyaltyStats(c *gin.Context) {
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequestWithContext(c, err.Error())
		return
	}

	stats, err := h.royaltyService.GetRoyaltyStats(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		h.logger.Error("Failed to get royalty stats", "error", err)
		response.InternalServerErrorWithContext(c, "Failed to get royalty stats", err.Error())
		return
	}

	response.SuccessWithContext(c, stats)
}

// GetRoyaltyByPeriod retrieves total royalty for a specific period
// @Summary Get royalty by period
// @Description Get total royalty amount for a specific time period
// @Tags Blog Post Royalties
// @Produce json
// @Security BearerAuth
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string false "Website ID"
// @Param start_date query string true "Start date (YYYY-MM-DD)"
// @Param end_date query string true "End date (YYYY-MM-DD)"
// @Success 200 {object} response.Response{data=float64}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/royalties/period [get]
func (h *BlogPostRoyaltyHandler) GetRoyaltyByPeriod(c *gin.Context) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		response.BadRequestWithContext(c, "start_date and end_date are required", "")
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid start_date format", err.Error())
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		response.BadRequestWithContext(c, "Invalid end_date format", err.Error())
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.UnauthorizedWithContext(c, err.Error())
		return
	}

	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequestWithContext(c, err.Error())
		return
	}

	total, err := h.royaltyService.GetTotalRoyaltyByPeriod(c.Request.Context(), tenantID, websiteID, startDate, endDate)
	if err != nil {
		h.logger.Error("Failed to get royalty by period", "error", err)
		response.InternalServerErrorWithContext(c, "Failed to get royalty by period", err.Error())
		return
	}

	result := map[string]interface{}{
		"start_date":    startDateStr,
		"end_date":      endDateStr,
		"total_royalty": total,
	}

	response.SuccessWithContext(c, result)
}
