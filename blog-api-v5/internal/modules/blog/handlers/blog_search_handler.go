package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/search"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

type BlogSearchHandler struct {
	searchService *search.SearchService
	validator     validator.Validator
	logger        utils.Logger
}

func NewBlogSearchHandler(
	searchService *search.SearchService,
	validator validator.Validator,
	logger utils.Logger,
) *BlogSearchHandler {
	return &BlogSearchHandler{
		searchService: searchService,
		validator:     validator,
		logger:        logger,
	}
}

// SearchPosts handles GET /blog/search
// @Summary Search blog posts
// @Description Search published blog posts with full-text search and filters
// @Tags Blog Search
// @Accept json
// @Produce json
// @Param q query string true "Search query"
// @Param limit query int false "Number of results per page" default(20)
// @Param offset query int false "Number of results to skip" default(0)
// @Param categories query string false "Comma-separated category names"
// @Param tags query string false "Comma-separated tag names"
// @Param author_id query int false "Filter by author ID"
// @Param is_featured query bool false "Filter by featured status"
// @Param sort query string false "Sort order" Enums(published_at:desc,published_at:asc,title:asc,title:desc) default(published_at:desc)
// @Success 200 {object} response.Response{data=search.SearchResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/search [get]
func (h *BlogSearchHandler) SearchPosts(c *gin.Context) {
	var req search.SearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid query parameters"))
		return
	}

	// Get tenant context
	if tenantID, err := context.GetTenantIDUint(c); err == nil {
		req.TenantID = tenantID
	}

	// Get website context
	if websiteID, err := context.GetWebsiteIDUint(c); err == nil {
		req.WebsiteID = websiteID
	}

	// Parse categories from comma-separated string
	if categoriesStr := c.Query("categories"); categoriesStr != "" {
		req.Categories = parseCommaSeparated(categoriesStr)
	}

	// Parse tags from comma-separated string
	if tagsStr := c.Query("tags"); tagsStr != "" {
		req.Tags = parseCommaSeparated(tagsStr)
	}

	// Parse is_featured
	if featuredStr := c.Query("is_featured"); featuredStr != "" {
		if featured, err := strconv.ParseBool(featuredStr); err == nil {
			req.IsFeatured = &featured
		}
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError(err.Error()))
		return
	}

	// Perform search
	result, err := h.searchService.SearchPosts(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Blog post search failed", utils.Fields{
			"error": err.Error(),
			"query": req.Query,
		})
		middleware.AbortWithError(c, err)
		return
	}

	response.Success(c.Writer, result)
}

// Autocomplete handles GET /blog/search/autocomplete
// @Summary Get search autocomplete suggestions
// @Description Get autocomplete suggestions for blog post search
// @Tags Blog Search
// @Accept json
// @Produce json
// @Param q query string true "Search query"
// @Param limit query int false "Number of suggestions" default(10)
// @Success 200 {object} response.Response{data=search.AutocompleteResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/search/autocomplete [get]
func (h *BlogSearchHandler) Autocomplete(c *gin.Context) {
	var req search.AutocompleteRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid query parameters"))
		return
	}

	// Get tenant context
	if tenantID, err := context.GetTenantIDUint(c); err == nil {
		req.TenantID = tenantID
	}

	// Get website context
	if websiteID, err := context.GetWebsiteIDUint(c); err == nil {
		req.WebsiteID = websiteID
	}

	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError(err.Error()))
		return
	}

	// Get autocomplete suggestions
	result, err := h.searchService.Autocomplete(c.Request.Context(), &req)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.Success(c.Writer, result)
}

// GetSearchStats handles GET /blog/search/stats
// @Summary Get search statistics
// @Description Get search statistics for the current tenant
// @Tags Blog Search
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} response.Response{data=search.SearchStats}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/search/stats [get]
func (h *BlogSearchHandler) GetSearchStats(c *gin.Context) {
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError("Tenant context required"))
		return
	}

	stats, err := h.searchService.GetSearchStats(c.Request.Context(), tenantID)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.Success(c.Writer, stats)
}

// HealthCheck handles GET /blog/search/health
// @Summary Check search service health
// @Description Check the health status of the search service
// @Tags Blog Search
// @Accept json
// @Produce json
// @Success 200 {object} response.Response
// @Failure 503 {object} response.Response
// @Router /blog/search/health [get]
func (h *BlogSearchHandler) HealthCheck(c *gin.Context) {
	if err := h.searchService.HealthCheck(c.Request.Context()); err != nil {
		middleware.AbortWithError(c, utils.InternalError(err))
		return
	}

	response.Success(c.Writer, gin.H{
		"status":    "healthy",
		"timestamp": utils.GetCurrentTimestamp(),
	})
}

// parseCommaSeparated parses a comma-separated string into a slice of strings
func parseCommaSeparated(str string) []string {
	if str == "" {
		return nil
	}

	var result []string
	for _, item := range utils.SplitAndTrim(str, ",") {
		if item != "" {
			result = append(result, item)
		}
	}

	return result
}
