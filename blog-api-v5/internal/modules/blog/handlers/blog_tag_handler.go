package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type BlogTagHandler struct {
	tagService services.BlogTagService
}

func NewBlogTagHandler(tagService services.BlogTagService) *BlogTagHandler {
	return &BlogTagHandler{
		tagService: tagService,
	}
}

// CreateTag creates a new blog tag
func (h *BlogTagHandler) CreateTag(c *gin.Context) {
	var req models.BlogTagCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}
	req.TenantID = tenantID

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}
	req.WebsiteID = websiteID

	tag, err := h.tagService.Create(c.Request.Context(), &req)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.CreatedWithContext(c, tag)
}

// GetTag retrieves a blog tag by ID
func (h *BlogTagHandler) GetTag(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid tag ID"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	tag, err := h.tagService.GetByID(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, tag)
}

// GetTagBySlug retrieves a blog tag by slug
func (h *BlogTagHandler) GetTagBySlug(c *gin.Context) {
	slug := c.Param("slug")

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	tag, err := h.tagService.GetBySlug(c.Request.Context(), tenantID, websiteID, slug)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, tag)
}

// UpdateTag updates a blog tag
func (h *BlogTagHandler) UpdateTag(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid tag ID"))
		return
	}

	var req models.BlogTagUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	tag, err := h.tagService.Update(c.Request.Context(), tenantID, websiteID, uint(id), &req)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, tag)
}

// DeleteTag deletes a blog tag
func (h *BlogTagHandler) DeleteTag(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid tag ID"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	err = h.tagService.Delete(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, nil)
}

// ListTags lists blog tags with filtering and cursor pagination
// @Summary      List blog tags with cursor pagination
// @Description  Get paginated list of blog tags with filters using cursor pagination
// @Tags         Blog Tags
// @Produce      json
// @Security     Bearer
// @Param        website_id query uint true "Website ID"
// @Param        search query string false "Search filter"
// @Param        is_active query bool false "Active status filter"
// @Param        status query string false "Status filter" Enums(active,inactive,deleted)
// @Param        cursor query string false "Pagination cursor"
// @Param        limit query int false "Items per page" default(20)
// @Param        sort_by query string false "Sort by field" Enums(id,name,created_at,updated_at,usage_count) default(name)
// @Param        sort_order query string false "Sort order" Enums(asc,desc) default(asc)
// @Param        use_page query bool false "Use page-based pagination" default(false)
// @Param        page query int false "Page number (when use_page=true)" default(1)
// @Param        page_size query int false "Page size (when use_page=true)" default(20)
// @Success      200 {object} dto.BlogTagListResponse
// @Failure      400 {object} response.Response "Invalid filter parameters"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to retrieve blog tags"
// @Router       /api/cms/v1/blog/tags [get]
func (h *BlogTagHandler) ListTags(c *gin.Context) {
	var filter dto.BlogTagFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid filter parameters"))
		return
	}

	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from middleware (required)
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	// Create cursor request
	cursorReq := pagination.NewCursorRequest(filter.Cursor, filter.GetLimitWithDefault())

	// Check if page-based pagination is requested (for backward compatibility)
	if c.Query("use_page") == "true" {
		// Use traditional page-based pagination
		var modelFilter models.BlogTagFilter
		modelFilter.TenantID = tenantID
		modelFilter.WebsiteID = websiteID
		modelFilter.Search = filter.Search
		modelFilter.IsActive = filter.IsActive
		if filter.Status != nil {
			modelFilter.Status = string(*filter.Status)
		}

		// Parse page-based parameters
		modelFilter.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
		modelFilter.PageSize, _ = strconv.Atoi(c.DefaultQuery("page_size", "20"))
		modelFilter.SortBy = c.DefaultQuery("sort_by", "name")
		modelFilter.SortOrder = c.DefaultQuery("sort_order", "asc")

		tags, total, err := h.tagService.List(c.Request.Context(), &modelFilter)
		if err != nil {
			middleware.AbortWithError(c, err)
			return
		}

		// Build page-based response
		data := map[string]interface{}{
			"tags": tags,
			"meta": map[string]interface{}{
				"page":        modelFilter.Page,
				"page_size":   modelFilter.PageSize,
				"total":       int(total),
				"total_pages": (int(total) + modelFilter.PageSize - 1) / modelFilter.PageSize,
				"has_next":    modelFilter.Page < (int(total)+modelFilter.PageSize-1)/modelFilter.PageSize,
				"has_prev":    modelFilter.Page > 1,
			},
		}

		response.SuccessWithContext(c, data)
		return
	}

	// Use cursor-based pagination (default)
	result, cursorResp, err := h.tagService.ListWithCursor(c.Request.Context(), tenantID, websiteID, cursorReq, map[string]interface{}{
		"search":     filter.Search,
		"is_active":  filter.IsActive,
		"status":     filter.Status,
		"sort_by":    filter.SortBy,
		"sort_order": filter.SortOrder,
	})
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.CursorPaginatedWithContext(c, result, *cursorResp)
}

// GetMostUsedTags retrieves most used tags
func (h *BlogTagHandler) GetMostUsedTags(c *gin.Context) {
	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	tags, err := h.tagService.GetMostUsed(c.Request.Context(), tenantID, websiteID, limit)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, tags)
}

// GetTagSuggestions retrieves tag suggestions based on query
func (h *BlogTagHandler) GetTagSuggestions(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		middleware.AbortWithError(c, utils.BadRequestError("Query parameter 'q' is required"))
		return
	}

	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	tags, err := h.tagService.GetSuggestions(c.Request.Context(), tenantID, websiteID, query, limit)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, tags)
}

// GetTagStats retrieves tag statistics
func (h *BlogTagHandler) GetTagStats(c *gin.Context) {
	// Get website ID from middleware
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	stats, err := h.tagService.GetStats(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, stats)
}

// ListForSelect returns tags formatted for select/dropdown UI components
// @Summary Get tags for select dropdown
// @Description Get tags formatted for select/dropdown UI components with optional post count
// @Tags Blog Tags
// @Accept json
// @Produce json
// @Param X-Tenant-ID header string true "Tenant ID"
// @Param X-Website-ID header string true "Website ID"
// @Param include_count query boolean false "Include post count for each tag"
// @Param limit query integer false "Maximum number of tags to return"
// @Success 200 {object} response.Response{data=dto.SelectResponse} "Tags for select retrieved successfully"
// @Failure 400 {object} response.Response "Invalid request"
// @Failure 401 {object} response.Response "Unauthorized"
// @Failure 500 {object} response.Response "Internal server error"
// @Router /blog/tags/select [get]
// @Security BearerAuth
func (h *BlogTagHandler) ListForSelect(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		middleware.AbortWithError(c, utils.UnauthorizedError(err.Error()))
		return
	}

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}
	if websiteID == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("Website ID is required"))
		return
	}

	// Parse query parameters
	includeCount := c.Query("include_count") == "true"

	// Get tags for select
	options, err := h.tagService.GetSelectOptions(c.Request.Context(), tenantID, websiteID, includeCount)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	response.SuccessWithContext(c, dto.SelectResponse{
		Options: options,
		Total:   len(options),
	})
}
