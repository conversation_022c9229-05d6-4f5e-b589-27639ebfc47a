package handlers

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// BlogWorkflowConfigHandler handles HTTP requests for workflow configuration
type BlogWorkflowConfigHandler struct {
	configService services.BlogWorkflowConfigService
	validator     validator.Validator
	logger        utils.Logger
}

// NewBlogWorkflowConfigHandler creates a new instance of BlogWorkflowConfigHandler
func NewBlogWorkflowConfigHandler(
	configService services.BlogWorkflowConfigService,
	validator validator.Validator,
	logger utils.Logger,
) *BlogWorkflowConfigHandler {
	return &BlogWorkflowConfigHandler{
		configService: configService,
		validator:     validator,
		logger:        logger,
	}
}

// CreateOrUpdate handles POST /blog/workflow/config
func (h *BlogWorkflowConfigHandler) CreateOrUpdate(c *gin.Context) {
	// Get tenant and website from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	// Parse request body
	var req dto.WorkflowConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}
	
	// Validate request
	if err := h.validator.Validate(c.Request.Context(), &req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}
	
	// Create or update configuration
	result, err := h.configService.CreateOrUpdate(c.Request.Context(), tenantID, websiteID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create/update workflow configuration")
		response.InternalServerError(c.Writer, "Failed to save workflow configuration")
		return
	}
	
	response.Created(c.Writer, result)
}

// Get handles GET /blog/workflow/config
func (h *BlogWorkflowConfigHandler) Get(c *gin.Context) {
	// Get tenant and website from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	// Get configuration
	result, err := h.configService.Get(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get workflow configuration")
		response.InternalServerError(c.Writer, "Failed to retrieve workflow configuration")
		return
	}
	
	response.Success(c.Writer, result)
}

// Delete handles DELETE /blog/workflow/config
func (h *BlogWorkflowConfigHandler) Delete(c *gin.Context) {
	// Get tenant and website from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	// Delete configuration
	err = h.configService.Delete(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete workflow configuration")
		response.InternalServerError(c.Writer, "Failed to delete workflow configuration")
		return
	}
	
	response.NoContent(c.Writer)
}

// GetTemplates handles GET /blog/workflow/templates
func (h *BlogWorkflowConfigHandler) GetTemplates(c *gin.Context) {
	// Get templates
	templates, err := h.configService.GetTemplates(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get workflow templates")
		response.InternalServerError(c.Writer, "Failed to retrieve workflow templates")
		return
	}
	
	response.Success(c.Writer, templates)
}

// ApplyTemplate handles POST /blog/workflow/config/template/:templateId
func (h *BlogWorkflowConfigHandler) ApplyTemplate(c *gin.Context) {
	// Get tenant and website from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	// Get template ID from path
	templateID := c.Param("templateId")
	if templateID == "" {
		response.BadRequest(c.Writer, "Template ID is required")
		return
	}
	
	// Apply template
	result, err := h.configService.ApplyTemplate(c.Request.Context(), tenantID, websiteID, templateID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to apply workflow template")
		response.InternalServerError(c.Writer, "Failed to apply workflow template")
		return
	}
	
	response.Created(c.Writer, result)
}

// ApplyStandardWorkflow handles POST /blog/workflow/config/standard
func (h *BlogWorkflowConfigHandler) ApplyStandardWorkflow(c *gin.Context) {
	// Get tenant and website from context
	tenantID, err := context.GetTenantIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	websiteID, err := context.GetWebsiteIDUint(c)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}
	
	// Parse request body
	var req dto.StandardWorkflowConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body")
		return
	}
	
	// Apply standard workflow
	result, err := h.configService.ApplyStandardWorkflow(c.Request.Context(), tenantID, websiteID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to apply standard workflow")
		response.InternalServerError(c.Writer, "Failed to apply standard workflow configuration")
		return
	}
	
	response.Created(c.Writer, result)
}


