package handlers

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// WorkflowHandler handles blog workflow HTTP requests
type WorkflowHandler struct {
	workflowService services.WorkflowService
	postService     services.BlogPostService
}

// NewWorkflowHandler creates a new workflow handler
func NewWorkflowHandler(workflowService services.WorkflowService, postService services.BlogPostService) *WorkflowHandler {
	return &WorkflowHandler{
		workflowService: workflowService,
		postService:     postService,
	}
}

// TransitionWorkflow godoc
// @Summary Transition workflow state
// @Description Transition a blog post to a new workflow state
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Param request body models.WorkflowTransitionRequest true "Workflow transition request"
// @Success 200 {object} response.Response{data=models.BlogPostWithWorkflowResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/workflow/transition [post]
// @Security BearerAuth
func (h *WorkflowHandler) TransitionWorkflow(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get post ID from path
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get tenant ID and user info from context
	tenantID := middleware.GetTenantID(c)
	userID := middleware.GetUserID(c)
	userRole := middleware.GetUserRole(c)

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	// Parse request body
	var req models.WorkflowTransitionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Perform workflow transition
	err = h.workflowService.TransitionWorkflow(tenantID, uint(postID), userID, userRole, req.Action, &req)
	if err != nil {
		// Log the actual error for debugging
		c.Set("debug_error", err.Error())
		handleServiceError(c, err)
		return
	}

	// Get updated post with workflow info
	post, err := h.postService.GetByID(c.Request.Context(), tenantID, websiteID, uint(postID))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Get workflow info
	canEdit := h.workflowService.CanEditPost(post, userID, userRole)
	canPublish := h.workflowService.CanPublishPost(post, userRole)
	availableActions := h.workflowService.GetAvailableActions(post, userRole, userID)

	// Build response
	var response models.BlogPostWithWorkflowResponse
	response.FromBlogPost(post, canEdit, canPublish, availableActions)

	resp.Success(response)
}

// GetMyTasks godoc
// @Summary Get my workflow tasks
// @Description Get blog posts assigned to the current user
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Param cursor query string false "Pagination cursor"
// @Param limit query int false "Number of items per page" default(50) minimum(1) maximum(100)
// @Success 200 {object} response.ResponseMeta{data=[]models.WorkflowTaskResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/workflow/my-tasks [get]
// @Security BearerAuth
func (h *WorkflowHandler) GetMyTasks(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get tenant ID and user ID from context
	tenantID := middleware.GetTenantID(c)
	userID := middleware.GetUserID(c)

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limit := pagination.ParseLimitFromString(c.Query("limit"))

	// Get tasks
	posts, nextCursor, err := h.workflowService.GetMyTasks(tenantID, userID, cursor, limit)
	if err != nil {
		handleServiceError(c, err)
		return
	}

	// Convert to response format
	tasks := make([]models.WorkflowTaskResponse, len(posts))
	for i, post := range posts {
		tasks[i] = models.WorkflowTaskResponse{
			ID:            post.ID,
			Title:         post.Title,
			Slug:          post.Slug,
			UserID:      post.UserID,
			WorkflowState: post.WorkflowState,
			AssignedTo:    post.WorkflowAssignedTo,
			AssignedAt:    post.WorkflowAssignedAt,
			DueAt:         post.WorkflowDueAt,
			IsOverdue:     post.WorkflowDueAt != nil && post.WorkflowDueAt.Before(time.Now()),
			Notes:         post.WorkflowNotes,
			CreatedAt:     post.CreatedAt,
			UpdatedAt:     post.UpdatedAt,
		}
	}

	// Build cursor response
	cursorResp := pagination.CursorResponse{
		NextCursor: nextCursor,
		HasMore:    nextCursor != "",
		Count:      len(tasks),
		Limit:      limit,
	}

	resp.CursorPaginated(tasks, cursorResp)
}

// GetWorkflowQueue godoc
// @Summary Get workflow queue
// @Description Get blog posts in a specific workflow state
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Param state path string true "Workflow state" Enums(pending_review,in_review,pending_approval,pending_eic)
// @Param cursor query string false "Pagination cursor"
// @Param limit query int false "Number of items per page" default(50) minimum(1) maximum(100)
// @Success 200 {object} response.ResponseMeta{data=[]models.WorkflowTaskResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/workflow/queue/{state} [get]
// @Security BearerAuth
func (h *WorkflowHandler) GetWorkflowQueue(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get workflow state from path
	state := c.Param("state")
	workflowState := models.WorkflowState(state)

	// Validate workflow state
	validStates := []models.WorkflowState{
		models.WorkflowStatePendingReview,
		models.WorkflowStateInReview,
		models.WorkflowStatePendingApproval,
		models.WorkflowStatePendingEIC,
	}

	isValid := false
	for _, vs := range validStates {
		if workflowState == vs {
			isValid = true
			break
		}
	}

	if !isValid {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid workflow state"))
		return
	}

	// Get tenant ID from context
	tenantID := middleware.GetTenantID(c)

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limit := pagination.ParseLimitFromString(c.Query("limit"))

	// Get posts in queue
	posts, nextCursor, err := h.workflowService.GetWorkflowQueue(tenantID, workflowState, cursor, limit)
	if err != nil {
		handleServiceError(c, err)
		return
	}

	// Convert to response format
	tasks := make([]models.WorkflowTaskResponse, len(posts))
	for i, post := range posts {
		tasks[i] = models.WorkflowTaskResponse{
			ID:            post.ID,
			Title:         post.Title,
			Slug:          post.Slug,
			UserID:      post.UserID,
			WorkflowState: post.WorkflowState,
			AssignedTo:    post.WorkflowAssignedTo,
			AssignedAt:    post.WorkflowAssignedAt,
			DueAt:         post.WorkflowDueAt,
			IsOverdue:     post.WorkflowDueAt != nil && post.WorkflowDueAt.Before(time.Now()),
			Notes:         post.WorkflowNotes,
			CreatedAt:     post.CreatedAt,
			UpdatedAt:     post.UpdatedAt,
		}
	}

	// Build cursor response
	cursorResp := pagination.CursorResponse{
		NextCursor: nextCursor,
		HasMore:    nextCursor != "",
		Count:      len(tasks),
		Limit:      limit,
	}

	resp.CursorPaginated(tasks, cursorResp)
}

// GetWorkflowHistory godoc
// @Summary Get workflow history
// @Description Get workflow history for a blog post
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Param cursor query string false "Pagination cursor"
// @Param limit query int false "Number of items per page" default(50) minimum(1) maximum(100)
// @Success 200 {object} response.ResponseMeta{data=[]models.WorkflowHistoryResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/workflow/{id}/history [get]
// @Security BearerAuth
func (h *WorkflowHandler) GetWorkflowHistory(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get post ID from path
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get tenant ID from context
	tenantID := middleware.GetTenantID(c)

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limit := pagination.ParseLimitFromString(c.Query("limit"))

	// Get workflow history
	logs, nextCursor, err := h.workflowService.GetWorkflowHistory(tenantID, uint(postID), cursor, limit)
	if err != nil {
		handleServiceError(c, err)
		return
	}

	// Convert to response format
	history := make([]models.WorkflowHistoryResponse, len(logs))
	for i, log := range logs {
		history[i] = models.WorkflowHistoryResponse{
			ID:                log.ID,
			PostID:            log.PostID,
			Action:            log.Action,
			FromStatus:        log.FromStatus,
			ToStatus:          log.ToStatus,
			FromWorkflowState: log.FromWorkflowState,
			ToWorkflowState:   log.ToWorkflowState,
			UserID:            log.UserID,
			Reason:            log.Reason,
			Metadata:          log.Metadata,
			CreatedAt:         log.CreatedAt,
		}
	}

	// Build cursor response
	cursorResp := pagination.CursorResponse{
		NextCursor: nextCursor,
		HasMore:    nextCursor != "",
		Count:      len(history),
		Limit:      limit,
	}

	resp.CursorPaginated(history, cursorResp)
}

// GetAvailableActions godoc
// @Summary Get available workflow actions
// @Description Get available workflow actions for a blog post
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Success 200 {object} response.Response{data=[]models.WorkflowActionResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/workflow/{id}/actions [get]
// @Security BearerAuth
func (h *WorkflowHandler) GetAvailableActions(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get post ID from path
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get tenant ID and user info from context
	tenantID := middleware.GetTenantID(c)
	userID := middleware.GetUserID(c)
	userRole := middleware.GetUserRole(c)

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	// Get the post
	post, err := h.postService.GetByID(c.Request.Context(), tenantID, websiteID, uint(postID))
	if err != nil {
		handleServiceError(c, err)
		return
	}

	// Get available actions
	actions := h.workflowService.GetAvailableActions(post, userRole, userID)

	// Convert to response format
	actionResponses := make([]models.WorkflowActionResponse, 0, len(actions))
	for _, action := range actions {
		actionResp := models.WorkflowActionResponse{
			Action:             action,
			Label:              getActionLabel(action),
			Description:        getActionDescription(action),
			RequiresAssignment: action == "assign",
			RequiresNotes:      action == "return" || action == "reject",
		}
		actionResponses = append(actionResponses, actionResp)
	}

	resp.Success(actionResponses)
}

// AssignPost godoc
// @Summary Assign post to user
// @Description Assign a blog post to a specific user for review
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Param request body models.WorkflowAssignRequest true "Assignment request"
// @Success 200 {object} response.Response{data=models.BlogPostWithWorkflowResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/workflow/assign [post]
// @Security BearerAuth
func (h *WorkflowHandler) AssignPost(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get post ID from path
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get tenant ID from context
	tenantID := middleware.GetTenantID(c)

	// Get website ID from context
	websiteID, err := context.GetWebsiteID(c)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid website ID"))
		return
	}

	// Parse request body
	var req models.WorkflowAssignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Assign the post
	err = h.workflowService.AssignPost(tenantID, uint(postID), req.AssignToID, req.Notes, req.DueAt)
	if err != nil {
		handleServiceError(c, err)
		return
	}

	// Get updated post
	post, err := h.postService.GetByID(c.Request.Context(), tenantID, websiteID, uint(postID))
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	// Get workflow info
	userID := middleware.GetUserID(c)
	userRole := middleware.GetUserRole(c)
	canEdit := h.workflowService.CanEditPost(post, userID, userRole)
	canPublish := h.workflowService.CanPublishPost(post, userRole)
	availableActions := h.workflowService.GetAvailableActions(post, userRole, userID)

	// Build response
	var response models.BlogPostWithWorkflowResponse
	response.FromBlogPost(post, canEdit, canPublish, availableActions)

	resp.Success(response)
}

// UpdateWorkflowNotes godoc
// @Summary Update workflow notes
// @Description Update workflow notes for a blog post
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Param id path int true "Post ID"
// @Param request body models.WorkflowNotesUpdateRequest true "Notes update request"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/posts/{id}/workflow/notes [put]
// @Security BearerAuth
func (h *WorkflowHandler) UpdateWorkflowNotes(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get post ID from path
	postID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid post ID"))
		return
	}

	// Get tenant ID from context
	tenantID := middleware.GetTenantID(c)

	// Parse request body
	var req models.WorkflowNotesUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Update notes
	err = h.workflowService.UpdateWorkflowNotes(tenantID, uint(postID), req.Notes)
	if err != nil {
		handleServiceError(c, err)
		return
	}

	resp.Success(nil)
}

// GetWorkflowStats godoc
// @Summary Get workflow statistics
// @Description Get workflow statistics for the tenant
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=models.WorkflowStats}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/workflow/stats [get]
// @Security BearerAuth
func (h *WorkflowHandler) GetWorkflowStats(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get tenant ID from context
	tenantID := middleware.GetTenantID(c)

	// Get workflow statistics
	stats, err := h.workflowService.GetWorkflowStats(tenantID)
	if err != nil {
		handleServiceError(c, err)
		return
	}

	resp.Success(stats)
}

// BulkTransitionWorkflow godoc
// @Summary Bulk transition workflow states
// @Description Transition multiple blog posts to a new workflow state
// @Tags Blog Workflow
// @Accept json
// @Produce json
// @Param request body models.BulkWorkflowTransitionRequest true "Bulk workflow transition request"
// @Success 200 {object} response.Response{data=models.BulkWorkflowTransitionResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /blog/workflow/bulk-transition [post]
// @Security BearerAuth
func (h *WorkflowHandler) BulkTransitionWorkflow(c *gin.Context) {
	resp := response.NewResponse(c.Writer)

	// Get tenant ID and user info from context
	tenantID := middleware.GetTenantID(c)
	userID := middleware.GetUserID(c)
	userRole := middleware.GetUserRole(c)

	// Parse request body
	var req models.BulkWorkflowTransitionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithError(c, utils.BadRequestError("Invalid request body"))
		return
	}

	// Validate post IDs
	if len(req.PostIDs) == 0 {
		middleware.AbortWithError(c, utils.BadRequestError("No post IDs provided"))
		return
	}

	// Process each post
	var results models.BulkWorkflowTransitionResponse
	results.Results = make([]models.BulkTransitionResult, 0, len(req.PostIDs))
	results.TotalProcessed = len(req.PostIDs)

	for _, postID := range req.PostIDs {
		result := models.BulkTransitionResult{
			PostID:  postID,
			Success: false,
		}

		// Try to transition the workflow
		err := h.workflowService.TransitionWorkflow(tenantID, postID, userID, userRole, req.Action, &models.WorkflowTransitionRequest{
			Action: req.Action,
			Notes:  req.Notes,
		})

		if err != nil {
			result.Error = err.Error()
			results.TotalFailed++
		} else {
			result.Success = true
			results.TotalSucceeded++
		}

		results.Results = append(results.Results, result)
	}

	resp.Success(results)
}

// Helper functions

func getActionLabel(action string) string {
	labels := map[string]string{
		"submit":   "Submit for Review",
		"accept":   "Accept Assignment",
		"approve":  "Approve",
		"return":   "Return to Author",
		"reject":   "Reject",
		"escalate": "Escalate to EIC",
		"publish":  "Publish",
		"schedule": "Schedule",
		"edit":     "Edit",
		"assign":   "Assign to User",
	}

	if label, ok := labels[action]; ok {
		return label
	}
	return action
}

func getActionDescription(action string) string {
	descriptions := map[string]string{
		"submit":   "Submit the post for editorial review",
		"accept":   "Accept the post for editing",
		"approve":  "Approve the post for next stage",
		"return":   "Return the post to author with feedback",
		"reject":   "Reject the post permanently",
		"escalate": "Escalate to Editor-in-Chief for review",
		"publish":  "Publish the post immediately",
		"schedule": "Schedule the post for future publication",
		"edit":     "Edit the published post",
		"assign":   "Assign the post to a specific user",
	}

	if desc, ok := descriptions[action]; ok {
		return desc
	}
	return ""
}


func handleServiceError(c *gin.Context, err error) {
	// Log the actual error for debugging
	fmt.Printf("Workflow service error: %v\n", err)
	
	// Handle different types of errors
	switch err.Error() {
	case "post not found":
		middleware.AbortWithError(c, utils.NotFoundError("Post not found"))
	case "unauthorized":
		middleware.AbortWithError(c, utils.UnauthorizedError("Unauthorized"))
	case "forbidden":
		middleware.AbortWithError(c, utils.UnauthorizedError("Forbidden"))
	default:
		if contains(err.Error(), "not allowed") || contains(err.Error(), "cannot") {
			middleware.AbortWithError(c, utils.BadRequestError(err.Error()))
		} else {
			// Convert regular error to internal error for proper handling
			middleware.AbortWithError(c, utils.InternalError(err))
		}
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr
}
