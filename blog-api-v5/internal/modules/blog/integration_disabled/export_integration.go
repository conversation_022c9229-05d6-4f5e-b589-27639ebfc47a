package integration

// This file is disabled - contains export integration functionality that is not yet implemented
// All code is commented out to prevent build errors

/*
import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/export"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// BlogExportIntegration manages blog export/import integration
type BlogExportIntegration struct {
	exportHandler *handlers.BlogExportHandler
	exportService *services.BlogExportService
	db            *gorm.DB
	logger        utils.Logger
}

// NewBlogExportIntegration creates a new blog export integration
func NewBlogExportIntegration(
	db *gorm.DB,
	logger utils.Logger,
	mainExportService *export.Service,
	blogServices *services.BlogServices,
) *BlogExportIntegration {
	// Create blog export service
	blogExportService := services.NewBlogExportService(
		blogServices.PostService.GetRepository(),
		blogServices.CategoryService.GetRepository(),
		blogServices.TagService.GetRepository(),
		logger,
	)

	// Create blog export handler
	exportHandler := handlers.NewBlogExportHandler(
		mainExportService,
		blogServices.PostService,
		blogServices.CategoryService,
		blogServices.TagService,
		logger,
	)

	return &BlogExportIntegration{
		exportHandler: exportHandler,
		exportService: blogExportService,
		db:            db,
		logger:        logger,
	}
}

// RegisterAdapters registers blog export/import adapters with the main export system
func (i *BlogExportIntegration) RegisterAdapters() error {
	// Register blog post adapters
	postExportAdapter := services.NewBlogPostExportAdapter(i.exportService)
	postImportAdapter := services.NewBlogPostImportAdapter(i.exportService)

	// Register with main export system
	export.RegisterExportAdapter("blog", "posts", postExportAdapter)
	export.RegisterImportAdapter("blog", "posts", postImportAdapter)

	i.logger.Info("Blog export/import adapters registered successfully")
	return nil
}

// RegisterRoutes registers blog export/import routes
func (i *BlogExportIntegration) RegisterRoutes(router *gin.RouterGroup) {
	// routes.RegisterBlogExportRoutes(router, i.exportHandler)
	i.logger.Info("Blog export/import routes registered successfully")
}

// GetHandler returns the blog export handler
func (i *BlogExportIntegration) GetHandler() *handlers.BlogExportHandler {
	return i.exportHandler
}

// GetService returns the blog export service
func (i *BlogExportIntegration) GetService() *services.BlogExportService {
	return i.exportService
}

// BlogExportConfig contains blog-specific export configuration
type BlogExportConfig struct {
	MaxPostsPerExport       int    `json:"max_posts_per_export" default:"10000"`
	IncludeContentByDefault bool   `json:"include_content_by_default" default:"false"`
	IncludeTagsByDefault    bool   `json:"include_tags_by_default" default:"true"`
	StripHTMLByDefault      bool   `json:"strip_html_by_default" default:"false"`
	AutoGenerateSlug        bool   `json:"auto_generate_slug" default:"true"`
	AutoGenerateExcerpt     bool   `json:"auto_generate_excerpt" default:"true"`
	CreateCategoriesOnImport bool  `json:"create_categories_on_import" default:"false"`
	CreateTagsOnImport      bool   `json:"create_tags_on_import" default:"true"`
	DefaultPostStatus       string `json:"default_post_status" default:"draft"`
	RequireTitle            bool   `json:"require_title" default:"true"`
	RequireContent          bool   `json:"require_content" default:"false"`
	ValidateSlugUnique      bool   `json:"validate_slug_unique" default:"true"`
	MaxContentLength        int    `json:"max_content_length" default:"1000000"`
}

// DefaultBlogExportConfig returns default blog export configuration
func DefaultBlogExportConfig() *BlogExportConfig {
	return &BlogExportConfig{
		MaxPostsPerExport:       10000,
		IncludeContentByDefault: false,
		IncludeTagsByDefault:    true,
		StripHTMLByDefault:      false,
		AutoGenerateSlug:        true,
		AutoGenerateExcerpt:     true,
		CreateCategoriesOnImport: false,
		CreateTagsOnImport:      true,
		DefaultPostStatus:       "draft",
		RequireTitle:            true,
		RequireContent:          false,
		ValidateSlugUnique:      true,
		MaxContentLength:        1000000,
	}
}

// BlogExportStats contains blog export/import statistics
type BlogExportStats struct {
	TotalExports       int64 `json:"total_exports"`
	TotalImports       int64 `json:"total_imports"`
	PostsExported      int64 `json:"posts_exported"`
	PostsImported      int64 `json:"posts_imported"`
	CategoriesExported int64 `json:"categories_exported"`
	CategoriesImported int64 `json:"categories_imported"`
	TagsExported       int64 `json:"tags_exported"`
	TagsImported       int64 `json:"tags_imported"`
	ActiveExports      int64 `json:"active_exports"`
	ActiveImports      int64 `json:"active_imports"`
	FailedExports      int64 `json:"failed_exports"`
	FailedImports      int64 `json:"failed_imports"`
}
*/
