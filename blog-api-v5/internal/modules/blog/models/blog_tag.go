package models

import (
	"time"
)

// BlogTagStatus represents the status of a blog tag
// @Enum active,inactive,deleted
type BlogTagStatus string

const (
	BlogTagStatusActive   BlogTagStatus = "active"
	BlogTagStatusInactive BlogTagStatus = "inactive"
	BlogTagStatusDeleted  BlogTagStatus = "deleted"
)

// BlogTag represents a blog tag for content classification
type BlogTag struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint `gorm:"not null;index" json:"tenant_id"`
	WebsiteID uint `gorm:"not null;index" json:"website_id"`

	// Basic Information
	Slug        string `gorm:"type:varchar(255);not null" json:"slug" validate:"required,min=1,max=255"`
	Name        string `gorm:"type:varchar(255);not null" json:"name" validate:"required,min=1,max=255"`
	Description string `gorm:"type:text" json:"description,omitempty"`

	// Display Settings
	UsageCount uint `gorm:"default:0" json:"usage_count"`
	IsActive   bool `gorm:"default:true" json:"is_active"`

	// Status using enum for soft delete strategy
	Status BlogTagStatus `gorm:"type:varchar(20);not null;default:'active'" json:"status" validate:"oneof=active inactive deleted"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships - Blog posts will be handled through pivot table
}

// TableName returns the table name for the BlogTag model
func (BlogTag) TableName() string {
	return "blog_tags"
}

// BlogTagCreateRequest represents the request to create a blog tag
type BlogTagCreateRequest struct {
	TenantID    uint   `json:"tenant_id" validate:"required,min=1"`
	WebsiteID   uint   `json:"website_id" validate:"required,min=1"`
	Slug        string `json:"slug,omitempty" validate:"omitempty,min=1,max=255"`
	Name        string `json:"name" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty" validate:"max=1000"`
	IsActive    bool   `json:"is_active"`
}

// BlogTagUpdateRequest represents the request to update a blog tag
type BlogTagUpdateRequest struct {
	Slug        string `json:"slug" validate:"required,min=1,max=255"`
	Name        string `json:"name" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty" validate:"max=1000"`
	IsActive    bool   `json:"is_active"`
}

// BlogTagResponse represents the response when returning blog tag data
type BlogTagResponse struct {
	ID          uint      `json:"id"`
	TenantID    uint      `json:"tenant_id"`
	WebsiteID   uint      `json:"website_id"`
	Slug        string    `json:"slug"`
	Name        string    `json:"name"`
	Description string    `json:"description,omitempty"`
	UsageCount  uint      `json:"usage_count"`
	PostCount   uint      `json:"post_count"`
	IsActive    bool      `json:"is_active"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FromBlogTag converts a BlogTag model to BlogTagResponse
func (btr *BlogTagResponse) FromBlogTag(tag *BlogTag) {
	btr.ID = tag.ID
	btr.TenantID = tag.TenantID
	btr.WebsiteID = tag.WebsiteID
	btr.Slug = tag.Slug
	btr.Name = tag.Name
	btr.Description = tag.Description
	btr.UsageCount = tag.UsageCount
	btr.PostCount = tag.UsageCount // UsageCount represents the number of posts using this tag
	btr.IsActive = tag.IsActive
	btr.Status = string(tag.Status)
	btr.CreatedAt = tag.CreatedAt
	btr.UpdatedAt = tag.UpdatedAt
}

// BlogTagFilter represents filters for querying blog tags
type BlogTagFilter struct {
	TenantID  uint   `json:"tenant_id,omitempty"`
	WebsiteID uint   `json:"website_id,omitempty"`
	IsActive  *bool  `json:"is_active,omitempty"`
	Status    string `json:"status,omitempty"`
	Search    string `json:"search,omitempty"`
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
}

// BlogTagStats represents tag usage statistics
type BlogTagStats struct {
	TotalTags     int               `json:"total_tags"`
	ActiveTags    int               `json:"active_tags"`
	UnusedTags    int               `json:"unused_tags"`
	MostUsedTags  []BlogTagResponse `json:"most_used_tags"`
	RecentlyAdded []BlogTagResponse `json:"recently_added"`
}
