package models

import (
	"time"
)

// BlogPostWorkflowResponse represents workflow information in API responses
type BlogPostWorkflowResponse struct {
	State            WorkflowState `json:"state"`
	AssignedTo       *uint         `json:"assigned_to,omitempty"`
	AssignedAt       *time.Time    `json:"assigned_at,omitempty"`
	DueAt            *time.Time    `json:"due_at,omitempty"`
	CanEdit          bool          `json:"can_edit"`
	CanPublish       bool          `json:"can_publish"`
	AvailableActions []string      `json:"available_actions"`
	Notes            string        `json:"notes,omitempty"`
}

// WorkflowTransitionRequest represents a request to transition workflow state
type WorkflowTransitionRequest struct {
	Action     string     `json:"action" validate:"required,oneof=submit accept approve return reject assign escalate publish schedule edit request_changes"`
	AssignToID *uint      `json:"assign_to_id,omitempty"`
	Notes      string     `json:"notes,omitempty" validate:"max=1000"`
	Comment    string     `json:"comment,omitempty" validate:"max=1000"` // Alias for Notes
	DueAt      *time.Time `json:"due_at,omitempty"`
}

// WorkflowAssignRequest represents a request to assign a post to a user
type WorkflowAssignRequest struct {
	AssignToID uint       `json:"assign_to_id" validate:"required,min=1"`
	Notes      string     `json:"notes,omitempty" validate:"max=1000"`
	DueAt      *time.Time `json:"due_at,omitempty"`
}

// WorkflowNotesUpdateRequest represents a request to update workflow notes
type WorkflowNotesUpdateRequest struct {
	Notes string `json:"notes" validate:"max=1000"`
}

// WorkflowHistoryResponse represents a workflow history entry
type WorkflowHistoryResponse struct {
	ID                uint        `json:"id"`
	PostID            uint        `json:"post_id"`
	Action            string      `json:"action"`
	FromStatus        string      `json:"from_status,omitempty"`
	ToStatus          string      `json:"to_status,omitempty"`
	FromWorkflowState string      `json:"from_workflow_state,omitempty"`
	ToWorkflowState   string      `json:"to_workflow_state,omitempty"`
	UserID            uint        `json:"user_id"`
	UserName          string      `json:"user_name,omitempty"`
	AssignedTo        *uint       `json:"assigned_to,omitempty"`
	AssignedToName    string      `json:"assigned_to_name,omitempty"`
	Reason            string      `json:"reason,omitempty"`
	Metadata          interface{} `json:"metadata,omitempty"`
	CreatedAt         time.Time   `json:"created_at"`
}

// WorkflowQueueFilter represents filters for workflow queue queries
type WorkflowQueueFilter struct {
	TenantID       uint          `json:"tenant_id,omitempty"`
	WebsiteID      uint          `json:"website_id,omitempty"`
	WorkflowState  WorkflowState `json:"workflow_state,omitempty"`
	AssignedTo     *uint         `json:"assigned_to,omitempty"`
	OverdueOnly    bool          `json:"overdue_only,omitempty"`
	UnassignedOnly bool          `json:"unassigned_only,omitempty"`
	Cursor         string        `json:"cursor,omitempty"`
	Limit          int           `json:"limit,omitempty"`
}

// WorkflowTaskResponse represents a task in the workflow queue
type WorkflowTaskResponse struct {
	ID             uint          `json:"id"`
	Title          string        `json:"title"`
	Slug           string        `json:"slug"`
	UserID       uint          `json:"author_id"`
	AuthorName     string        `json:"author_name,omitempty"`
	WorkflowState  WorkflowState `json:"workflow_state"`
	AssignedTo     *uint         `json:"assigned_to,omitempty"`
	AssignedToName string        `json:"assigned_to_name,omitempty"`
	AssignedAt     *time.Time    `json:"assigned_at,omitempty"`
	DueAt          *time.Time    `json:"due_at,omitempty"`
	IsOverdue      bool          `json:"is_overdue"`
	Notes          string        `json:"notes,omitempty"`
	CreatedAt      time.Time     `json:"created_at"`
	UpdatedAt      time.Time     `json:"updated_at"`
}

// WorkflowActionResponse represents available workflow actions
type WorkflowActionResponse struct {
	Action             string `json:"action"`
	Label              string `json:"label"`
	Description        string `json:"description,omitempty"`
	RequiresAssignment bool   `json:"requires_assignment"`
	RequiresNotes      bool   `json:"requires_notes"`
}

// WorkflowConfig represents tenant-specific workflow configuration
type WorkflowConfig struct {
	ID               uint                   `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID         uint                   `gorm:"not null;uniqueIndex:idx_workflow_config_tenant_website" json:"tenant_id"`
	WebsiteID        uint                   `gorm:"not null;uniqueIndex:idx_workflow_config_tenant_website" json:"website_id"`
	WorkflowType     string                 `gorm:"type:varchar(50);default:'standard'" json:"workflow_type"`
	RoleMappings     map[string][]string    `gorm:"type:json;serializer:json" json:"role_mappings"`
	StateTransitions map[string]interface{} `gorm:"type:json;serializer:json" json:"state_transitions"`
	AutoAssignments  map[string]interface{} `gorm:"type:json;serializer:json" json:"auto_assignments"`
	Notifications    map[string]bool        `gorm:"type:json;serializer:json" json:"notifications"`
	States           interface{}            `gorm:"type:json;serializer:json" json:"states,omitempty"`      // Array format for React Flow
	Transitions      interface{}            `gorm:"type:json;serializer:json" json:"transitions,omitempty"` // Array format for React Flow
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
}

// TableName returns the table name for the WorkflowConfig model
func (WorkflowConfig) TableName() string {
	return "blog_workflow_configs"
}

// WorkflowLog represents a workflow audit log entry
type WorkflowLog struct {
	ID                uint                   `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID          uint                   `gorm:"not null;index" json:"tenant_id"`
	WebsiteID         uint                   `gorm:"not null;index" json:"website_id"`
	PostID            uint                   `gorm:"not null;index" json:"post_id"`
	UserID            uint                   `gorm:"not null;index" json:"user_id"`
	Action            string                 `gorm:"type:varchar(50);not null" json:"action"`
	FromStatus        string                 `gorm:"type:varchar(50)" json:"from_status,omitempty"`
	ToStatus          string                 `gorm:"type:varchar(50)" json:"to_status,omitempty"`
	FromWorkflowState string                 `gorm:"type:varchar(50)" json:"from_workflow_state,omitempty"`
	ToWorkflowState   string                 `gorm:"type:varchar(50)" json:"to_workflow_state,omitempty"`
	Reason            string                 `gorm:"type:text" json:"reason,omitempty"`
	Metadata          map[string]interface{} `gorm:"type:json;serializer:json" json:"metadata,omitempty"`
	CreatedAt         time.Time              `json:"created_at"`
}

// TableName returns the table name for the WorkflowLog model
func (WorkflowLog) TableName() string {
	return "blog_workflow_logs"
}

// WorkflowStats represents workflow statistics
type WorkflowStats struct {
	TotalInWorkflow    int                      `json:"total_in_workflow"`
	ByState            map[string]int           `json:"by_state"`
	OverdueTasks       int                      `json:"overdue_tasks"`
	UnassignedTasks    int                      `json:"unassigned_tasks"`
	AverageTimeByState map[string]int64 `json:"average_time_by_state"`
}

// BulkWorkflowTransitionRequest represents a request to transition multiple posts
type BulkWorkflowTransitionRequest struct {
	PostIDs []uint `json:"post_ids" binding:"required,min=1"`
	Action  string `json:"action" binding:"required"`
	Notes   string `json:"notes,omitempty"`
}

// BulkTransitionResult represents the result of a single bulk transition
type BulkTransitionResult struct {
	PostID  uint   `json:"post_id"`
	Success bool   `json:"success"`
	Error   string `json:"error,omitempty"`
}

// BulkWorkflowTransitionResponse represents the response for bulk workflow transitions
type BulkWorkflowTransitionResponse struct {
	TotalProcessed int                    `json:"total_processed"`
	TotalSucceeded int                    `json:"total_succeeded"`
	TotalFailed    int                    `json:"total_failed"`
	Results        []BulkTransitionResult `json:"results"`
}

// Enhanced BlogPostResponse with workflow information
type BlogPostWithWorkflowResponse struct {
	BlogPostResponse
	Workflow *BlogPostWorkflowResponse `json:"workflow,omitempty"`
}

// FromBlogPost converts a BlogPostResponse model to BlogPostWithWorkflowResponse
func (bwr *BlogPostWithWorkflowResponse) FromBlogPost(post *BlogPostResponse, canEdit bool, canPublish bool, availableActions []string) {
	// First populate the base response
	bwr.BlogPostResponse = *post

	// Add workflow information
	bwr.Workflow = &BlogPostWorkflowResponse{
		State:            post.WorkflowState,
		AssignedTo:       post.WorkflowAssignedTo,
		AssignedAt:       post.WorkflowAssignedAt,
		DueAt:            post.WorkflowDueAt,
		CanEdit:          canEdit,
		CanPublish:       canPublish,
		AvailableActions: availableActions,
		Notes:            post.WorkflowNotes,
	}
}
