package services

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// blogTagService implements BlogTagService interface
type blogTagService struct {
	repo repositories.BlogTagRepository
}

// NewBlogTagService creates a new blog tag service
func NewBlogTagService(repo repositories.BlogTagRepository) BlogTagService {
	return &blogTagService{
		repo: repo,
	}
}

// <PERSON><PERSON> creates a new blog tag
func (s *blogTagService) Create(ctx context.Context, req *models.BlogTagCreateRequest) (*models.BlogTagResponse, error) {
	// Auto-generate slug from name if not provided
	slug := req.Slug
	if slug == "" {
		slug = utils.Slugify(req.Name)
	}

	// Ensure slug is unique by appending numbers if needed
	originalSlug := slug
	counter := 1
	for {
		existing, err := s.repo.GetBySlug(ctx, req.TenantID, req.WebsiteID, slug)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Slug is unique, break out of loop
			break
		}
		if err != nil {
			// Some other error occurred
			return nil, fmt.Errorf("failed to check slug uniqueness: %w", err)
		}
		if existing != nil {
			// Slug exists, try with a number suffix
			slug = fmt.Sprintf("%s-%d", originalSlug, counter)
			counter++
		}
	}

	// Create the tag model
	tag := &models.BlogTag{
		TenantID:    req.TenantID,
		WebsiteID:   req.WebsiteID,
		Slug:        slug,
		Name:        req.Name,
		Description: req.Description,
		IsActive:    req.IsActive,
		UsageCount:  0,
		Status:      "active",
	}

	// Create the tag
	err := s.repo.Create(ctx, tag)
	if err != nil {
		return nil, fmt.Errorf("failed to create tag: %w", err)
	}

	// Convert to response
	response := &models.BlogTagResponse{}
	response.FromBlogTag(tag)
	return response, nil
}

// GetByID retrieves a blog tag by ID with tenant and website scope
func (s *blogTagService) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.BlogTagResponse, error) {
	tag, err := s.repo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("tag not found")
		}
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	response := &models.BlogTagResponse{}
	response.FromBlogTag(tag)
	return response, nil
}

// GetBySlug retrieves a blog tag by slug
func (s *blogTagService) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogTagResponse, error) {
	tag, err := s.repo.GetBySlug(ctx, tenantID, websiteID, slug)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("tag not found")
		}
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	response := &models.BlogTagResponse{}
	response.FromBlogTag(tag)
	return response, nil
}

// Update updates a blog tag with tenant and website scope
func (s *blogTagService) Update(ctx context.Context, tenantID, websiteID, id uint, req *models.BlogTagUpdateRequest) (*models.BlogTagResponse, error) {
	// Get existing tag
	existing, err := s.repo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("tag not found")
		}
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	// Check for duplicate slug if slug is being changed
	if req.Slug != existing.Slug {
		existingBySlug, err := s.repo.GetBySlug(ctx, tenantID, websiteID, req.Slug)
		if err == nil && existingBySlug != nil && existingBySlug.ID != existing.ID {
			return nil, fmt.Errorf("tag with slug '%s' already exists", req.Slug)
		}
	}

	// Update the tag
	existing.Slug = req.Slug
	existing.Name = req.Name
	existing.Description = req.Description
	existing.IsActive = req.IsActive

	err = s.repo.Update(ctx, tenantID, websiteID, id, existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update tag: %w", err)
	}

	response := &models.BlogTagResponse{}
	response.FromBlogTag(existing)
	return response, nil
}

// Delete soft deletes a blog tag with tenant and website scope
func (s *blogTagService) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	// Check if tag exists
	_, err := s.repo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("tag not found")
		}
		return fmt.Errorf("failed to get tag: %w", err)
	}

	// Soft delete using repository delete method
	err = s.repo.Delete(ctx, tenantID, websiteID, id)
	if err != nil {
		return fmt.Errorf("failed to delete tag: %w", err)
	}

	return nil
}

// List retrieves a list of blog tags with filtering
func (s *blogTagService) List(ctx context.Context, filter *models.BlogTagFilter) ([]models.BlogTagResponse, int64, error) {
	tags, total, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list tags: %w", err)
	}

	responses := make([]models.BlogTagResponse, len(tags))
	for i, tag := range tags {
		responses[i].FromBlogTag(&tag)
	}

	return responses, total, nil
}

// ListWithCursor retrieves tags with cursor-based pagination
func (s *blogTagService) ListWithCursor(ctx context.Context, tenantID, websiteID uint, cursor *pagination.CursorRequest, filters map[string]interface{}) ([]dto.BlogTagResponse, *pagination.CursorResponse, error) {
	tags, paginationResp, err := s.repo.ListWithCursor(ctx, tenantID, websiteID, cursor, filters)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list tags with cursor: %w", err)
	}

	// Convert tags to response DTOs
	tagResponses := make([]dto.BlogTagResponse, len(tags))
	for i, tag := range tags {
		tagResponse := &dto.BlogTagResponse{
			ID:          tag.ID,
			TenantID:    tag.TenantID,
			WebsiteID:   tag.WebsiteID,
			Name:        tag.Name,
			Slug:        tag.Slug,
			Description: tag.Description,
			PostCount:   int(tag.UsageCount),
			IsActive:    tag.IsActive,
			CreatedAt:   tag.CreatedAt,
			UpdatedAt:   tag.UpdatedAt,
		}
		tagResponses[i] = *tagResponse
	}

	return tagResponses, paginationResp, nil
}

// GetMostUsed retrieves the most used tags
func (s *blogTagService) GetMostUsed(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogTagResponse, error) {
	tags, err := s.repo.GetMostUsed(ctx, tenantID, websiteID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get most used tags: %w", err)
	}

	responses := make([]models.BlogTagResponse, len(tags))
	for i, tag := range tags {
		responses[i].FromBlogTag(&tag)
	}

	return responses, nil
}

// GetSuggestions retrieves tag suggestions based on a query
func (s *blogTagService) GetSuggestions(ctx context.Context, tenantID, websiteID uint, query string, limit int) ([]models.BlogTagResponse, error) {
	tags, err := s.repo.GetSuggestions(ctx, tenantID, websiteID, query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get tag suggestions: %w", err)
	}

	responses := make([]models.BlogTagResponse, len(tags))
	for i, tag := range tags {
		responses[i].FromBlogTag(&tag)
	}

	return responses, nil
}

// CreateOrFindTags creates new tags or finds existing ones by names
func (s *blogTagService) CreateOrFindTags(ctx context.Context, tenantID, websiteID uint, tagNames []string) ([]models.BlogTag, error) {
	if len(tagNames) == 0 {
		return []models.BlogTag{}, nil
	}

	// Remove duplicates and clean tag names
	uniqueNames := make(map[string]bool)
	cleanedNames := make([]string, 0, len(tagNames))

	for _, name := range tagNames {
		cleanName := strings.TrimSpace(name)
		if cleanName != "" && !uniqueNames[cleanName] {
			uniqueNames[cleanName] = true
			cleanedNames = append(cleanedNames, cleanName)
		}
	}

	if len(cleanedNames) == 0 {
		return []models.BlogTag{}, nil
	}

	// Get existing tags
	existingTags, err := s.repo.GetByNames(ctx, tenantID, websiteID, cleanedNames)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing tags: %w", err)
	}

	// Create a map of existing tag names for quick lookup
	existingNames := make(map[string]*models.BlogTag)
	for i := range existingTags {
		existingNames[strings.ToLower(existingTags[i].Name)] = &existingTags[i]
	}

	result := make([]models.BlogTag, 0, len(cleanedNames))

	// Check each name and create missing tags
	for _, name := range cleanedNames {
		if existing, found := existingNames[strings.ToLower(name)]; found {
			// Tag exists, add to result
			result = append(result, *existing)
		} else {
			// Tag doesn't exist, create it
			slug := s.generateSlug(name)

			// Ensure slug is unique
			originalSlug := slug
			counter := 1
			for {
				_, err := s.repo.GetBySlug(ctx, tenantID, websiteID, slug)
				if errors.Is(err, gorm.ErrRecordNotFound) {
					break // Slug is unique
				}
				if err != nil {
					return nil, fmt.Errorf("failed to check slug uniqueness: %w", err)
				}
				slug = fmt.Sprintf("%s-%d", originalSlug, counter)
				counter++
			}

			newTag := &models.BlogTag{
				TenantID:   tenantID,
				WebsiteID:  websiteID,
				Slug:       slug,
				Name:       name,
				IsActive:   true,
				UsageCount: 0,
				Status:     "active",
			}

			err = s.repo.Create(ctx, newTag)
			if err != nil {
				return nil, fmt.Errorf("failed to create tag '%s': %w", name, err)
			}

			result = append(result, *newTag)
		}
	}

	return result, nil
}

// GetStats retrieves tag statistics
func (s *blogTagService) GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogTagStats, error) {
	// Get total tags count
	filter := &models.BlogTagFilter{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Status:    "active",
	}

	_, totalCount, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get total tags count: %w", err)
	}

	// Get most used tags for additional stats
	mostUsed, err := s.repo.GetMostUsed(ctx, tenantID, websiteID, 5)
	if err != nil {
		return nil, fmt.Errorf("failed to get most used tags: %w", err)
	}

	// Calculate total usage
	var totalUsage uint
	for _, tag := range mostUsed {
		totalUsage += tag.UsageCount
	}

	// Get active tags count
	activeFilter := &models.BlogTagFilter{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Status:    "active",
		IsActive:  boolPtr(true),
	}

	_, activeCount, err := s.repo.List(ctx, activeFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get active tags count: %w", err)
	}

	// Calculate unused tags
	unusedCount := int(totalCount) - int(activeCount)
	if unusedCount < 0 {
		unusedCount = 0
	}

	stats := &models.BlogTagStats{
		TotalTags:     int(totalCount),
		ActiveTags:    int(activeCount),
		UnusedTags:    unusedCount,
		MostUsedTags:  make([]models.BlogTagResponse, len(mostUsed)),
		RecentlyAdded: make([]models.BlogTagResponse, 0),
	}

	// Convert most used tags to response format
	for i, tag := range mostUsed {
		stats.MostUsedTags[i].FromBlogTag(&tag)
	}

	// Get recently added tags (last 5)
	recentFilter := &models.BlogTagFilter{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Status:    "active",
		PageSize:  5,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	recentTags, _, err := s.repo.List(ctx, recentFilter)
	if err == nil {
		stats.RecentlyAdded = make([]models.BlogTagResponse, len(recentTags))
		for i, tag := range recentTags {
			stats.RecentlyAdded[i].FromBlogTag(&tag)
		}
	}

	return stats, nil
}

// generateSlug generates a URL-friendly slug from a tag name
func (s *blogTagService) generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Remove special characters (keep only alphanumeric, hyphens, and underscores)
	var result strings.Builder
	for _, char := range slug {
		if (char >= 'a' && char <= 'z') || (char >= '0' && char <= '9') || char == '-' || char == '_' {
			result.WriteRune(char)
		}
	}

	slug = result.String()

	// Remove multiple consecutive hyphens
	for strings.Contains(slug, "--") {
		slug = strings.ReplaceAll(slug, "--", "-")
	}

	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")

	// Ensure slug is not empty
	if slug == "" {
		slug = "tag"
	}

	return slug
}

// boolPtr returns a pointer to a boolean value
func boolPtr(b bool) *bool {
	return &b
}

// GetSelectOptions returns tags formatted for select/dropdown UI
func (s *blogTagService) GetSelectOptions(ctx context.Context, tenantID, websiteID uint, includeCount bool) ([]interface{}, error) {
	// Get all active tags
	filter := &models.BlogTagFilter{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Status:    "active",
	}

	tags, _, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get tags: %w", err)
	}

	// Convert to select options
	options := make([]interface{}, 0, len(tags))

	for _, tag := range tags {
		option := dto.TagSelectOption{
			Value:    tag.ID,
			Label:    tag.Name,
			Disabled: !tag.IsActive,
		}

		// Include post count if requested
		if includeCount {
			// Get post count for this tag
			postCount, err := s.repo.GetPostCount(ctx, tenantID, websiteID, tag.ID)
			if err == nil {
				option.PostCount = postCount
				option.Label = fmt.Sprintf("%s (%d)", tag.Name, postCount)
			}
		}

		options = append(options, option)
	}

	return options, nil
}
