package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
)

var (
	ErrRoleNotFound            = errors.New("role not found")
	ErrRoleAlreadyExists       = errors.New("role already exists")
	ErrCannotDeleteSystemRole  = errors.New("cannot delete system role")
	ErrCannotDeleteDefaultRole = errors.New("cannot delete default role")
	ErrRoleInUse               = errors.New("role is in use and cannot be deleted")
)

// RoleService handles role business logic
type RoleService interface {
	// Basic CRUD operations
	CreateRole(ctx context.Context, req *models.RoleCreateRequest) (*models.Role, error)
	GetRole(ctx context.Context, id uint) (*models.Role, error)
	GetRoleByName(ctx context.Context, tenantID uint, name string) (*models.Role, error)
	UpdateRole(ctx context.Context, id uint, req *models.RoleUpdateRequest) (*models.Role, error)
	DeleteRole(ctx context.Context, id uint) error

	// Role management
	GetRolesByTenant(ctx context.Context, tenantID uint, page, pageSize int) ([]*models.Role, int64, error)
	GetRolesByScope(ctx context.Context, tenantID uint, scope models.RoleScope, contextID *uint) ([]*models.Role, error)
	GetSystemRoles(ctx context.Context, tenantID uint) ([]*models.Role, error)
	GetDefaultRoles(ctx context.Context, tenantID uint) ([]*models.Role, error)
	GetRoleHierarchy(ctx context.Context, tenantID uint) ([]*models.Role, error)

	// Role permissions
	AssignPermissionsToRole(ctx context.Context, roleID uint, permissionIDs []uint, grantedBy *uint) error
	RevokePermissionsFromRole(ctx context.Context, roleID uint, permissionIDs []uint, revokedBy *uint) error
	GetRolePermissions(ctx context.Context, roleID uint) ([]*models.Permission, error)
	HasPermission(ctx context.Context, roleID uint, permissionID uint) (bool, error)

	// Role users
	GetRoleUsers(ctx context.Context, roleID uint) ([]*models.UserRole, error)
	GetRoleUserCount(ctx context.Context, roleID uint) (int64, error)

	// Role status management
	ActivateRole(ctx context.Context, roleID uint) error
	DeactivateRole(ctx context.Context, roleID uint) error

	// Search and filtering
	SearchRoles(ctx context.Context, tenantID uint, query string, filters *repositories.RoleFilters) ([]*models.Role, error)

	// Validation
	ValidateRole(ctx context.Context, role *models.Role) error
	CanDeleteRole(ctx context.Context, roleID uint) error
}

type roleService struct {
	roleRepo           repositories.RoleRepository
	rolePermissionRepo repositories.RolePermissionRepository
	userRoleRepo       repositories.UserRoleRepository
	permissionRepo     repositories.PermissionRepository
}

// NewRoleService creates a new role service
func NewRoleService(
	roleRepo repositories.RoleRepository,
	rolePermissionRepo repositories.RolePermissionRepository,
	userRoleRepo repositories.UserRoleRepository,
	permissionRepo repositories.PermissionRepository,
) RoleService {
	return &roleService{
		roleRepo:           roleRepo,
		rolePermissionRepo: rolePermissionRepo,
		userRoleRepo:       userRoleRepo,
		permissionRepo:     permissionRepo,
	}
}

// CreateRole creates a new role
func (s *roleService) CreateRole(ctx context.Context, req *models.RoleCreateRequest) (*models.Role, error) {
	// Check if role already exists
	exists, err := s.roleRepo.Exists(ctx, req.TenantID, req.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to check role existence: %w", err)
	}
	if exists {
		return nil, ErrRoleAlreadyExists
	}

	// Create role
	role := &models.Role{
		TenantID:      req.TenantID,
		Name:          req.Name,
		DisplayName:   req.DisplayName,
		Description:   req.Description,
		IsSystemRole:  req.IsSystemRole,
		IsDefaultRole: req.IsDefaultRole,
		Level:         req.Level,
		Scope:         req.Scope,
		ContextID:     req.ContextID,
		Color:         req.Color,
		Icon:          req.Icon,
		Capabilities:  req.Capabilities,
		Restrictions:  req.Restrictions,
		Status:        models.RoleStatusActive,
	}

	// Validate role
	if err := s.ValidateRole(ctx, role); err != nil {
		return nil, err
	}

	// Create role in database
	if err := s.roleRepo.Create(ctx, role); err != nil {
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	return role, nil
}

// GetRole retrieves a role by ID
func (s *roleService) GetRole(ctx context.Context, id uint) (*models.Role, error) {
	role, err := s.roleRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	if role == nil {
		return nil, ErrRoleNotFound
	}

	return role, nil
}

// GetRoleByName retrieves a role by name
func (s *roleService) GetRoleByName(ctx context.Context, tenantID uint, name string) (*models.Role, error) {
	role, err := s.roleRepo.GetByName(ctx, tenantID, name)
	if err != nil {
		return nil, fmt.Errorf("failed to get role by name: %w", err)
	}
	if role == nil {
		return nil, ErrRoleNotFound
	}

	return role, nil
}

// UpdateRole updates a role
func (s *roleService) UpdateRole(ctx context.Context, id uint, req *models.RoleUpdateRequest) (*models.Role, error) {
	// Get existing role
	role, err := s.GetRole(ctx, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != nil {
		role.Name = *req.Name
	}
	if req.DisplayName != nil {
		role.DisplayName = *req.DisplayName
	}
	if req.Description != nil {
		role.Description = req.Description
	}
	if req.IsSystemRole != nil {
		role.IsSystemRole = *req.IsSystemRole
	}
	if req.IsDefaultRole != nil {
		role.IsDefaultRole = *req.IsDefaultRole
	}
	if req.Level != nil {
		role.Level = *req.Level
	}
	if req.Scope != nil {
		role.Scope = *req.Scope
	}
	if req.ContextID != nil {
		role.ContextID = req.ContextID
	}
	if req.Color != nil {
		role.Color = req.Color
	}
	if req.Icon != nil {
		role.Icon = req.Icon
	}
	if req.Status != nil {
		role.Status = *req.Status
	}
	if req.Capabilities != nil {
		role.Capabilities = *req.Capabilities
	}
	if req.Restrictions != nil {
		role.Restrictions = *req.Restrictions
	}

	// Validate role
	if err := s.ValidateRole(ctx, role); err != nil {
		return nil, err
	}

	// Update role in database
	if err := s.roleRepo.Update(ctx, role); err != nil {
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	return role, nil
}

// DeleteRole deletes a role
func (s *roleService) DeleteRole(ctx context.Context, id uint) error {
	// Check if role can be deleted
	if err := s.CanDeleteRole(ctx, id); err != nil {
		return err
	}

	// Delete role
	if err := s.roleRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete role: %w", err)
	}

	return nil
}

// GetRolesByTenant retrieves roles by tenant with pagination
func (s *roleService) GetRolesByTenant(ctx context.Context, tenantID uint, page, pageSize int) ([]*models.Role, int64, error) {
	offset := (page - 1) * pageSize

	roles, err := s.roleRepo.GetByTenant(ctx, tenantID, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get roles by tenant: %w", err)
	}

	count, err := s.roleRepo.CountByTenant(ctx, tenantID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count roles by tenant: %w", err)
	}

	return roles, count, nil
}

// GetRolesByScope retrieves roles by scope
func (s *roleService) GetRolesByScope(ctx context.Context, tenantID uint, scope models.RoleScope, contextID *uint) ([]*models.Role, error) {
	roles, err := s.roleRepo.GetByScope(ctx, tenantID, scope, contextID)
	if err != nil {
		return nil, fmt.Errorf("failed to get roles by scope: %w", err)
	}

	return roles, nil
}

// GetSystemRoles retrieves system roles
func (s *roleService) GetSystemRoles(ctx context.Context, tenantID uint) ([]*models.Role, error) {
	roles, err := s.roleRepo.GetSystemRoles(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get system roles: %w", err)
	}

	return roles, nil
}

// GetDefaultRoles retrieves default roles
func (s *roleService) GetDefaultRoles(ctx context.Context, tenantID uint) ([]*models.Role, error) {
	roles, err := s.roleRepo.GetDefaultRoles(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get default roles: %w", err)
	}

	return roles, nil
}

// GetRoleHierarchy retrieves role hierarchy
func (s *roleService) GetRoleHierarchy(ctx context.Context, tenantID uint) ([]*models.Role, error) {
	roles, err := s.roleRepo.GetRoleHierarchy(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get role hierarchy: %w", err)
	}

	return roles, nil
}

// AssignPermissionsToRole assigns permissions to a role
func (s *roleService) AssignPermissionsToRole(ctx context.Context, roleID uint, permissionIDs []uint, grantedBy *uint) error {
	// Verify role exists
	role, err := s.GetRole(ctx, roleID)
	if err != nil {
		return err
	}
	if role == nil {
		return ErrRoleNotFound
	}

	// Verify permissions exist
	permissions, err := s.permissionRepo.GetByIDs(ctx, permissionIDs)
	if err != nil {
		return fmt.Errorf("failed to get permissions: %w", err)
	}
	if len(permissions) != len(permissionIDs) {
		return fmt.Errorf("some permissions not found")
	}

	// Assign permissions
	for _, permissionID := range permissionIDs {
		if err := s.roleRepo.AssignPermission(ctx, roleID, permissionID, grantedBy); err != nil {
			return fmt.Errorf("failed to assign permission %d to role: %w", permissionID, err)
		}
	}

	return nil
}

// RevokePermissionsFromRole revokes permissions from a role
func (s *roleService) RevokePermissionsFromRole(ctx context.Context, roleID uint, permissionIDs []uint, revokedBy *uint) error {
	// Verify role exists
	role, err := s.GetRole(ctx, roleID)
	if err != nil {
		return err
	}
	if role == nil {
		return ErrRoleNotFound
	}

	// Revoke permissions
	for _, permissionID := range permissionIDs {
		if err := s.roleRepo.RevokePermission(ctx, roleID, permissionID, revokedBy); err != nil {
			return fmt.Errorf("failed to revoke permission %d from role: %w", permissionID, err)
		}
	}

	return nil
}

// GetRolePermissions retrieves permissions for a role
func (s *roleService) GetRolePermissions(ctx context.Context, roleID uint) ([]*models.Permission, error) {
	permissions, err := s.roleRepo.GetRolePermissions(ctx, roleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}

	return permissions, nil
}

// HasPermission checks if a role has a specific permission
func (s *roleService) HasPermission(ctx context.Context, roleID uint, permissionID uint) (bool, error) {
	hasPermission, err := s.roleRepo.HasPermission(ctx, roleID, permissionID)
	if err != nil {
		return false, fmt.Errorf("failed to check role permission: %w", err)
	}

	return hasPermission, nil
}

// GetRoleUsers retrieves users for a role
func (s *roleService) GetRoleUsers(ctx context.Context, roleID uint) ([]*models.UserRole, error) {
	userRoles, err := s.roleRepo.GetRoleUsers(ctx, roleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get role users: %w", err)
	}

	return userRoles, nil
}

// GetRoleUserCount retrieves user count for a role
func (s *roleService) GetRoleUserCount(ctx context.Context, roleID uint) (int64, error) {
	userRoles, err := s.GetRoleUsers(ctx, roleID)
	if err != nil {
		return 0, err
	}

	return int64(len(userRoles)), nil
}

// ActivateRole activates a role
func (s *roleService) ActivateRole(ctx context.Context, roleID uint) error {
	if err := s.roleRepo.UpdateStatus(ctx, roleID, models.RoleStatusActive); err != nil {
		return fmt.Errorf("failed to activate role: %w", err)
	}

	return nil
}

// DeactivateRole deactivates a role
func (s *roleService) DeactivateRole(ctx context.Context, roleID uint) error {
	if err := s.roleRepo.UpdateStatus(ctx, roleID, models.RoleStatusInactive); err != nil {
		return fmt.Errorf("failed to deactivate role: %w", err)
	}

	return nil
}

// SearchRoles searches roles
func (s *roleService) SearchRoles(ctx context.Context, tenantID uint, query string, filters *repositories.RoleFilters) ([]*models.Role, error) {
	roles, err := s.roleRepo.Search(ctx, tenantID, query, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to search roles: %w", err)
	}

	return roles, nil
}

// ValidateRole validates a role
func (s *roleService) ValidateRole(ctx context.Context, role *models.Role) error {
	// Basic validation
	if role.Name == "" {
		return fmt.Errorf("role name is required")
	}
	if role.DisplayName == "" {
		return fmt.Errorf("role display name is required")
	}
	if role.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}

	// Level validation
	if role.Level > 100 {
		return fmt.Errorf("role level cannot exceed 100")
	}

	// Scope validation
	if role.Scope != models.RoleScopeTenant && role.Scope != models.RoleScopeWebsite && role.Scope != models.RoleScopeGlobal {
		return fmt.Errorf("invalid role scope")
	}

	// Context validation
	if role.Scope == models.RoleScopeWebsite && role.ContextID == nil {
		return fmt.Errorf("context ID is required for website scope")
	}

	return nil
}

// CanDeleteRole checks if a role can be deleted
func (s *roleService) CanDeleteRole(ctx context.Context, roleID uint) error {
	// Get role
	role, err := s.GetRole(ctx, roleID)
	if err != nil {
		return err
	}

	// Check if system role
	if role.IsSystemRole {
		return ErrCannotDeleteSystemRole
	}

	// Check if default role
	if role.IsDefaultRole {
		return ErrCannotDeleteDefaultRole
	}

	// Check if role is in use
	userCount, err := s.GetRoleUserCount(ctx, roleID)
	if err != nil {
		return err
	}
	if userCount > 0 {
		return ErrRoleInUse
	}

	return nil
}
