package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
)

// RoleTemplateService handles business logic for role templates
type RoleTemplateService struct {
	roleTemplateRepo repositories.RoleTemplateRepository
	permissionRepo   repositories.PermissionRepository
	auditRepo        repositories.AdminAuditLogRepository
}

// NewRoleTemplateService creates a new role template service
func NewRoleTemplateService(
	roleTemplateRepo repositories.RoleTemplateRepository,
	permissionRepo repositories.PermissionRepository,
	auditRepo repositories.AdminAuditLogRepository,
) *RoleTemplateService {
	return &RoleTemplateService{
		roleTemplateRepo: roleTemplateRepo,
		permissionRepo:   permissionRepo,
		auditRepo:        auditRepo,
	}
}

// CreateTemplate creates a new role template
func (s *RoleTemplateService) CreateTemplate(ctx context.Context, req *models.RoleTemplateCreateRequest, adminUserID uint) (*models.RoleTemplate, error) {
	// Validate permissions exist
	if len(req.DefaultPermissions) > 0 {
		invalidPerms, err := s.roleTemplateRepo.ValidatePermissions(ctx, req.DefaultPermissions)
		if err != nil {
			return nil, fmt.Errorf("failed to validate permissions: %w", err)
		}
		if len(invalidPerms) > 0 {
			return nil, fmt.Errorf("invalid permissions: %v", invalidPerms)
		}
	}

	// Check if name already exists
	exists, err := s.roleTemplateRepo.ExistsByName(ctx, req.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to check template name existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("role template with name '%s' already exists", req.Name)
	}

	// Create template
	template := &models.RoleTemplate{
		Name:                       req.Name,
		DisplayName:                req.DisplayName,
		Description:                req.Description,
		IsSystemTemplate:           req.IsSystemTemplate,
		IsDefaultTemplate:          req.IsDefaultTemplate,
		Level:                      req.Level,
		Scope:                      req.Scope,
		Color:                      req.Color,
		Icon:                       req.Icon,
		Capabilities:               req.Capabilities,
		Restrictions:               req.Restrictions,
		DefaultPermissions:         req.DefaultPermissions,
		AutoAssignOnTenantCreation: req.AutoAssignOnTenantCreation,
		AutoAssignToOwner:          req.AutoAssignToOwner,
		Category:                   req.Category,
		Tags:                       req.Tags,
		CreatedBy:                  &adminUserID,
	}

	err = s.roleTemplateRepo.Create(ctx, template)
	if err != nil {
		return nil, fmt.Errorf("failed to create role template: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, adminUserID, "create_role_template", models.AuditResourceTypeRoleTemplate, 
		&template.ID, nil, nil, nil, models.AuditSeverityInfo, models.PermissionRiskLevelMedium,
		"Created role template: "+template.Name, nil, template, true, nil, nil)

	return template, nil
}

// GetTemplate retrieves a role template by ID
func (s *RoleTemplateService) GetTemplate(ctx context.Context, id uint) (*models.RoleTemplate, error) {
	template, err := s.roleTemplateRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get role template: %w", err)
	}
	if template == nil {
		return nil, fmt.Errorf("role template not found")
	}
	return template, nil
}

// GetTemplateByName retrieves a role template by name
func (s *RoleTemplateService) GetTemplateByName(ctx context.Context, name string) (*models.RoleTemplate, error) {
	template, err := s.roleTemplateRepo.GetByName(ctx, name)
	if err != nil {
		return nil, fmt.Errorf("failed to get role template: %w", err)
	}
	if template == nil {
		return nil, fmt.Errorf("role template not found")
	}
	return template, nil
}

// UpdateTemplate updates a role template
func (s *RoleTemplateService) UpdateTemplate(ctx context.Context, id uint, req *models.RoleTemplateUpdateRequest, adminUserID uint) (*models.RoleTemplate, error) {
	// Get existing template for audit
	existingTemplate, err := s.roleTemplateRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing template: %w", err)
	}
	if existingTemplate == nil {
		return nil, fmt.Errorf("role template not found")
	}

	// Validate permissions if being updated
	if req.DefaultPermissions != nil && len(*req.DefaultPermissions) > 0 {
		invalidPerms, err := s.roleTemplateRepo.ValidatePermissions(ctx, *req.DefaultPermissions)
		if err != nil {
			return nil, fmt.Errorf("failed to validate permissions: %w", err)
		}
		if len(invalidPerms) > 0 {
			return nil, fmt.Errorf("invalid permissions: %v", invalidPerms)
		}
	}

	// Check name uniqueness if being updated
	if req.Name != nil && *req.Name != existingTemplate.Name {
		exists, err := s.roleTemplateRepo.ExistsByName(ctx, *req.Name)
		if err != nil {
			return nil, fmt.Errorf("failed to check template name existence: %w", err)
		}
		if exists {
			return nil, fmt.Errorf("role template with name '%s' already exists", *req.Name)
		}
	}

	// Update template
	err = s.roleTemplateRepo.Update(ctx, id, req)
	if err != nil {
		return nil, fmt.Errorf("failed to update role template: %w", err)
	}

	// Get updated template
	updatedTemplate, err := s.roleTemplateRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated template: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, adminUserID, "update_role_template", models.AuditResourceTypeRoleTemplate, 
		&id, nil, nil, nil, models.AuditSeverityInfo, models.PermissionRiskLevelMedium,
		"Updated role template: "+existingTemplate.Name, existingTemplate, updatedTemplate, true, nil, nil)

	return updatedTemplate, nil
}

// DeleteTemplate deletes a role template
func (s *RoleTemplateService) DeleteTemplate(ctx context.Context, id uint, adminUserID uint) error {
	// Get existing template for audit
	existingTemplate, err := s.roleTemplateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get existing template: %w", err)
	}
	if existingTemplate == nil {
		return fmt.Errorf("role template not found")
	}

	// Check if template is system template
	if existingTemplate.IsSystemTemplate {
		return fmt.Errorf("cannot delete system role template")
	}

	// Delete template
	err = s.roleTemplateRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete role template: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, adminUserID, "delete_role_template", models.AuditResourceTypeRoleTemplate, 
		&id, nil, nil, nil, models.AuditSeverityWarning, models.PermissionRiskLevelHigh,
		"Deleted role template: "+existingTemplate.Name, existingTemplate, nil, true, nil, nil)

	return nil
}

// ListTemplates lists role templates with filtering
func (s *RoleTemplateService) ListTemplates(ctx context.Context, filter *models.RoleTemplateFilter) ([]*models.RoleTemplate, error) {
	templates, err := s.roleTemplateRepo.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to list role templates: %w", err)
	}
	return templates, nil
}

// CountTemplates counts role templates with filtering
func (s *RoleTemplateService) CountTemplates(ctx context.Context, filter *models.RoleTemplateFilter) (int64, error) {
	count, err := s.roleTemplateRepo.Count(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count role templates: %w", err)
	}
	return count, nil
}

// SearchTemplates searches role templates
func (s *RoleTemplateService) SearchTemplates(ctx context.Context, query string, filter *models.RoleTemplateFilter) ([]*models.RoleTemplate, error) {
	templates, err := s.roleTemplateRepo.Search(ctx, query, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to search role templates: %w", err)
	}
	return templates, nil
}

// GetSystemTemplates retrieves system templates
func (s *RoleTemplateService) GetSystemTemplates(ctx context.Context) ([]*models.RoleTemplate, error) {
	templates, err := s.roleTemplateRepo.GetSystemTemplates(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get system templates: %w", err)
	}
	return templates, nil
}

// GetDefaultTemplates retrieves default templates
func (s *RoleTemplateService) GetDefaultTemplates(ctx context.Context) ([]*models.RoleTemplate, error) {
	templates, err := s.roleTemplateRepo.GetDefaultTemplates(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get default templates: %w", err)
	}
	return templates, nil
}

// ActivateTemplate activates a role template
func (s *RoleTemplateService) ActivateTemplate(ctx context.Context, id uint, adminUserID uint) error {
	template, err := s.roleTemplateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}
	if template == nil {
		return fmt.Errorf("role template not found")
	}

	err = s.roleTemplateRepo.Activate(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to activate role template: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, adminUserID, "activate_role_template", models.AuditResourceTypeRoleTemplate, 
		&id, nil, nil, nil, models.AuditSeverityInfo, models.PermissionRiskLevelLow,
		"Activated role template: "+template.Name, nil, nil, true, nil, nil)

	return nil
}

// DeactivateTemplate deactivates a role template
func (s *RoleTemplateService) DeactivateTemplate(ctx context.Context, id uint, adminUserID uint) error {
	template, err := s.roleTemplateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}
	if template == nil {
		return fmt.Errorf("role template not found")
	}

	err = s.roleTemplateRepo.Deactivate(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to deactivate role template: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, adminUserID, "deactivate_role_template", models.AuditResourceTypeRoleTemplate, 
		&id, nil, nil, nil, models.AuditSeverityWarning, models.PermissionRiskLevelMedium,
		"Deactivated role template: "+template.Name, nil, nil, true, nil, nil)

	return nil
}

// SetAsDefault sets a template as default
func (s *RoleTemplateService) SetAsDefault(ctx context.Context, id uint, adminUserID uint) error {
	template, err := s.roleTemplateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}
	if template == nil {
		return fmt.Errorf("role template not found")
	}

	err = s.roleTemplateRepo.SetAsDefault(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to set template as default: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, adminUserID, "set_default_role_template", models.AuditResourceTypeRoleTemplate, 
		&id, nil, nil, nil, models.AuditSeverityInfo, models.PermissionRiskLevelMedium,
		"Set role template as default: "+template.Name, nil, nil, true, nil, nil)

	return nil
}

// UnsetAsDefault unsets a template as default
func (s *RoleTemplateService) UnsetAsDefault(ctx context.Context, id uint, adminUserID uint) error {
	template, err := s.roleTemplateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}
	if template == nil {
		return fmt.Errorf("role template not found")
	}

	err = s.roleTemplateRepo.UnsetAsDefault(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to unset template as default: %w", err)
	}

	// Create audit log
	s.createAuditLog(ctx, adminUserID, "unset_default_role_template", models.AuditResourceTypeRoleTemplate, 
		&id, nil, nil, nil, models.AuditSeverityInfo, models.PermissionRiskLevelLow,
		"Unset role template as default: "+template.Name, nil, nil, true, nil, nil)

	return nil
}

// CreateBulkTemplates creates multiple role templates
func (s *RoleTemplateService) CreateBulkTemplates(ctx context.Context, req *models.RoleTemplateBulkCreateRequest, adminUserID uint) ([]*models.RoleTemplate, error) {
	var templates []*models.RoleTemplate

	// Validate each template
	for i, templateReq := range req.Templates {
		// Validate permissions
		if len(templateReq.DefaultPermissions) > 0 {
			invalidPerms, err := s.roleTemplateRepo.ValidatePermissions(ctx, templateReq.DefaultPermissions)
			if err != nil {
				return nil, fmt.Errorf("failed to validate permissions for template %d: %w", i, err)
			}
			if len(invalidPerms) > 0 {
				return nil, fmt.Errorf("invalid permissions in template %d: %v", i, invalidPerms)
			}
		}

		// Check name uniqueness
		exists, err := s.roleTemplateRepo.ExistsByName(ctx, templateReq.Name)
		if err != nil {
			return nil, fmt.Errorf("failed to check template name existence for template %d: %w", i, err)
		}
		if exists {
			return nil, fmt.Errorf("role template with name '%s' already exists (template %d)", templateReq.Name, i)
		}

		// Create template model
		template := &models.RoleTemplate{
			Name:                       templateReq.Name,
			DisplayName:                templateReq.DisplayName,
			Description:                templateReq.Description,
			IsSystemTemplate:           templateReq.IsSystemTemplate,
			IsDefaultTemplate:          templateReq.IsDefaultTemplate,
			Level:                      templateReq.Level,
			Scope:                      templateReq.Scope,
			Color:                      templateReq.Color,
			Icon:                       templateReq.Icon,
			Capabilities:               templateReq.Capabilities,
			Restrictions:               templateReq.Restrictions,
			DefaultPermissions:         templateReq.DefaultPermissions,
			AutoAssignOnTenantCreation: templateReq.AutoAssignOnTenantCreation,
			AutoAssignToOwner:          templateReq.AutoAssignToOwner,
			Category:                   templateReq.Category,
			Tags:                       templateReq.Tags,
			CreatedBy:                  &adminUserID,
		}

		templates = append(templates, template)
	}

	// Create all templates
	err := s.roleTemplateRepo.CreateBulk(ctx, templates)
	if err != nil {
		return nil, fmt.Errorf("failed to create role templates: %w", err)
	}

	// Create audit log
	templateNames := make([]string, len(templates))
	for i, template := range templates {
		templateNames[i] = template.Name
	}
	s.createAuditLog(ctx, adminUserID, "bulk_create_role_templates", models.AuditResourceTypeBulkOperation, 
		nil, nil, nil, nil, models.AuditSeverityInfo, models.PermissionRiskLevelMedium,
		fmt.Sprintf("Bulk created %d role templates: %v", len(templates), templateNames), 
		nil, templates, true, nil, nil)

	return templates, nil
}

// Helper method to create audit logs
func (s *RoleTemplateService) createAuditLog(ctx context.Context, adminUserID uint, action string, 
	resourceType models.AuditResourceType, resourceID, tenantID, websiteID, targetUserID *uint,
	severity models.AuditSeverity, riskLevel models.PermissionRiskLevel, description string,
	oldValues, newValues interface{}, success bool, errorMessage, errorCode *string) {
	
	// Convert values to AuditMetadata
	var oldValuesMap, newValuesMap models.AuditMetadata
	
	if oldValues != nil {
		oldValuesMap = make(models.AuditMetadata)
		oldValuesMap["data"] = oldValues
	}
	
	if newValues != nil {
		newValuesMap = make(models.AuditMetadata)
		newValuesMap["data"] = newValues
	}

	auditLog := &models.AdminAuditLog{
		AdminUserID:       adminUserID,
		Action:            action,
		ResourceType:      resourceType,
		ResourceID:        resourceID,
		TenantID:          tenantID,
		WebsiteID:         websiteID,
		TargetUserID:      targetUserID,
		ActionDescription: &description,
		OldValues:         oldValuesMap,
		NewValues:         newValuesMap,
		Severity:          severity,
		RiskLevel:         riskLevel,
		Success:           success,
		ErrorMessage:      errorMessage,
		ErrorCode:         errorCode,
		PerformedAt:       time.Now(),
	}

	// Best effort audit logging - don't fail the main operation if audit fails
	s.auditRepo.Create(ctx, auditLog)
}