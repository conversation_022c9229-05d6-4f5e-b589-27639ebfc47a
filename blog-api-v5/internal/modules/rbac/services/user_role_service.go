package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
)

var (
	ErrUserRoleNotFound      = errors.New("user role not found")
	ErrUserRoleAlreadyExists = errors.New("user role already exists")
	ErrCannotRevokeOwnRole   = errors.New("cannot revoke own role")
	ErrInvalidTimeRange      = errors.New("invalid time range")
)

// UserRoleService handles user role assignment business logic
type UserRoleService interface {
	// Configuration
	SetRBACEngine(engine RBACEngine)

	// Role assignment
	AssignRole(ctx context.Context, req *models.UserRoleCreateRequest) (*models.UserRole, error)
	AssignRoles(ctx context.Context, userID uint, roleIDs []uint, assignedBy uint) ([]*models.UserRole, error)
	AssignTemporaryRole(ctx context.Context, req *models.UserRoleCreateRequest, validUntil time.Time) (*models.UserRole, error)

	// Role revocation
	RevokeRole(ctx context.Context, userRoleID uint, revokedBy uint) error
	RevokeRoleByUserAndRole(ctx context.Context, userID uint, roleID uint, revokedBy uint) error
	RevokeAllUserRoles(ctx context.Context, userID uint, revokedBy uint) error

	// Role queries
	GetUserRole(ctx context.Context, id uint) (*models.UserRole, error)
	GetUserRoles(ctx context.Context, userID uint) ([]*models.UserRole, error)
	GetActiveUserRoles(ctx context.Context, userID uint) ([]*models.UserRole, error)
	GetUserRolesByContext(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error)

	// Role validation
	HasRole(ctx context.Context, userID uint, roleID uint) (bool, error)
	HasRoleInContext(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) (bool, error)
	HasAnyRole(ctx context.Context, userID uint, roleIDs []uint) (bool, error)
	HasAllRoles(ctx context.Context, userID uint, roleIDs []uint) (bool, error)

	// Primary role management
	GetPrimaryRole(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) (*models.UserRole, error)
	SetPrimaryRole(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) error
	ClearPrimaryRole(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) error

	// Role hierarchy
	GetEffectiveRoles(ctx context.Context, userID uint) ([]*models.UserRole, error)
	GetInheritedRoles(ctx context.Context, userID uint) ([]*models.UserRole, error)

	// Status management
	SuspendUserRole(ctx context.Context, userRoleID uint) error
	ReactivateUserRole(ctx context.Context, userRoleID uint) error
	UpdateUserRoleStatus(ctx context.Context, userRoleID uint, status models.UserRoleStatus) error

	// Temporal operations
	GetExpiredRoles(ctx context.Context, timestamp time.Time) ([]*models.UserRole, error)
	GetTemporaryRoles(ctx context.Context, userID uint) ([]*models.UserRole, error)
	ExtendRoleValidity(ctx context.Context, userRoleID uint, newValidUntil time.Time) error

	// Bulk operations
	AssignRolesBulk(ctx context.Context, assignments []models.UserRoleCreateRequest) ([]*models.UserRole, error)
	RevokeRolesBulk(ctx context.Context, userRoleIDs []uint, revokedBy uint) error

	// Search and filtering
	SearchUserRoles(ctx context.Context, filters *repositories.UserRoleFilters) ([]*models.UserRole, error)
	GetUsersByRole(ctx context.Context, roleID uint) ([]*models.UserRole, error)
	GetUsersInContext(ctx context.Context, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error)

	// Analytics
	GetRoleUsageStats(ctx context.Context, roleID uint) (map[string]interface{}, error)
	GetUserRoleHistory(ctx context.Context, userID uint) ([]*models.UserRole, error)

	// Current user endpoints
	GetCurrentUserRoles(ctx context.Context, userID uint, tenantID uint) ([]*models.UserRole, error)
	GetCurrentUserPermissions(ctx context.Context, userID uint, tenantID uint) ([]string, error)

	// Cleanup
	CleanupExpiredRoles(ctx context.Context) (int64, error)
	CleanupRevokedRoles(ctx context.Context, olderThan time.Time) (int64, error)
}

type userRoleService struct {
	userRoleRepo repositories.UserRoleRepository
	roleService  RoleService
	rbacEngine   RBACEngine
}

// NewUserRoleService creates a new user role service
func NewUserRoleService(
	userRoleRepo repositories.UserRoleRepository,
	roleService RoleService,
	rbacEngine RBACEngine,
) UserRoleService {
	return &userRoleService{
		userRoleRepo: userRoleRepo,
		roleService:  roleService,
		rbacEngine:   rbacEngine,
	}
}

// SetRBACEngine sets the RBAC engine for the service
func (s *userRoleService) SetRBACEngine(engine RBACEngine) {
	s.rbacEngine = engine
}

// AssignRole assigns a role to a user
func (s *userRoleService) AssignRole(ctx context.Context, req *models.UserRoleCreateRequest) (*models.UserRole, error) {
	// Validate role exists
	role, err := s.roleService.GetRole(ctx, req.RoleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	if role.Status != models.RoleStatusActive {
		return nil, fmt.Errorf("role is not active")
	}

	// Check if user already has this role
	existingRole, err := s.userRoleRepo.GetByUserAndRole(ctx, req.UserID, req.RoleID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing role: %w", err)
	}

	if existingRole != nil && existingRole.Status == models.UserRoleStatusActive {
		return nil, ErrUserRoleAlreadyExists
	}

	// Create user role
	userRole := &models.UserRole{
		UserID:      req.UserID,
		RoleID:      req.RoleID,
		ContextType: req.ContextType,
		ContextID:   req.ContextID,
		ValidFrom:   req.ValidFrom,
		ValidUntil:  req.ValidUntil,
		IsPrimary:   req.IsPrimary,
		IsInherited: req.IsInherited,
		IsTemporary: req.IsTemporary,
		Conditions:  req.Conditions,
		Limitations: req.Limitations,
		Status:      models.UserRoleStatusActive,
	}

	// Set defaults
	if userRole.ValidFrom == nil {
		now := time.Now()
		userRole.ValidFrom = &now
	}

	// Validate time range
	if userRole.ValidUntil != nil && userRole.ValidFrom != nil {
		if userRole.ValidUntil.Before(*userRole.ValidFrom) {
			return nil, ErrInvalidTimeRange
		}
	}

	// Create user role
	if err := s.userRoleRepo.Create(ctx, userRole); err != nil {
		return nil, fmt.Errorf("failed to create user role: %w", err)
	}

	// Clear user cache
	if s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, req.UserID, nil)
	}

	return userRole, nil
}

// AssignRoles assigns multiple roles to a user
func (s *userRoleService) AssignRoles(ctx context.Context, userID uint, roleIDs []uint, assignedBy uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole

	for _, roleID := range roleIDs {
		req := &models.UserRoleCreateRequest{
			UserID: userID,
			RoleID: roleID,
		}

		userRole, err := s.AssignRole(ctx, req)
		if err != nil {
			// Skip if role already exists
			if err == ErrUserRoleAlreadyExists {
				continue
			}
			return nil, fmt.Errorf("failed to assign role %d: %w", roleID, err)
		}

		userRoles = append(userRoles, userRole)
	}

	return userRoles, nil
}

// AssignTemporaryRole assigns a temporary role to a user
func (s *userRoleService) AssignTemporaryRole(ctx context.Context, req *models.UserRoleCreateRequest, validUntil time.Time) (*models.UserRole, error) {
	req.ValidUntil = &validUntil
	req.IsTemporary = true

	return s.AssignRole(ctx, req)
}

// RevokeRole revokes a user role
func (s *userRoleService) RevokeRole(ctx context.Context, userRoleID uint, revokedBy uint) error {
	// Get user role
	userRole, err := s.GetUserRole(ctx, userRoleID)
	if err != nil {
		return err
	}

	// Revoke role
	if err := s.userRoleRepo.Revoke(ctx, userRoleID, revokedBy); err != nil {
		return fmt.Errorf("failed to revoke user role: %w", err)
	}

	// Clear user cache
	if s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, userRole.UserID, nil)
	}

	return nil
}

// RevokeRoleByUserAndRole revokes a role from a user
func (s *userRoleService) RevokeRoleByUserAndRole(ctx context.Context, userID uint, roleID uint, revokedBy uint) error {
	// Get user role
	userRole, err := s.userRoleRepo.GetByUserAndRole(ctx, userID, roleID)
	if err != nil {
		return fmt.Errorf("failed to get user role: %w", err)
	}

	if userRole == nil {
		return ErrUserRoleNotFound
	}

	// Revoke role
	if err := s.userRoleRepo.RevokeByUserAndRole(ctx, userID, roleID, revokedBy); err != nil {
		return fmt.Errorf("failed to revoke user role: %w", err)
	}

	// Clear user cache
	if s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, userID, nil)
	}

	return nil
}

// RevokeAllUserRoles revokes all roles from a user
func (s *userRoleService) RevokeAllUserRoles(ctx context.Context, userID uint, revokedBy uint) error {
	// Get user roles
	userRoles, err := s.GetActiveUserRoles(ctx, userID)
	if err != nil {
		return err
	}

	// Revoke each role
	for _, userRole := range userRoles {
		if err := s.RevokeRole(ctx, userRole.ID, revokedBy); err != nil {
			return fmt.Errorf("failed to revoke role %d: %w", userRole.ID, err)
		}
	}

	return nil
}

// GetUserRole retrieves a user role by ID
func (s *userRoleService) GetUserRole(ctx context.Context, id uint) (*models.UserRole, error) {
	userRole, err := s.userRoleRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user role: %w", err)
	}

	if userRole == nil {
		return nil, ErrUserRoleNotFound
	}

	return userRole, nil
}

// GetUserRoles retrieves all roles for a user
func (s *userRoleService) GetUserRoles(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	userRoles, err := s.userRoleRepo.GetRolesByUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	return userRoles, nil
}

// GetActiveUserRoles retrieves active roles for a user
func (s *userRoleService) GetActiveUserRoles(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	userRoles, err := s.userRoleRepo.GetActiveRoles(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active user roles: %w", err)
	}

	return userRoles, nil
}

// GetUserRolesByContext retrieves user roles by context
func (s *userRoleService) GetUserRolesByContext(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error) {
	userRoles, err := s.userRoleRepo.GetByUserAndContext(ctx, userID, contextType, contextID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles by context: %w", err)
	}

	return userRoles, nil
}

// HasRole checks if a user has a specific role
func (s *userRoleService) HasRole(ctx context.Context, userID uint, roleID uint) (bool, error) {
	hasRole, err := s.userRoleRepo.HasRole(ctx, userID, roleID)
	if err != nil {
		return false, fmt.Errorf("failed to check if user has role: %w", err)
	}

	return hasRole, nil
}

// HasRoleInContext checks if a user has a role in a specific context
func (s *userRoleService) HasRoleInContext(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) (bool, error) {
	hasRole, err := s.userRoleRepo.HasRoleInContext(ctx, userID, roleID, contextType, contextID)
	if err != nil {
		return false, fmt.Errorf("failed to check if user has role in context: %w", err)
	}

	return hasRole, nil
}

// HasAnyRole checks if a user has any of the specified roles
func (s *userRoleService) HasAnyRole(ctx context.Context, userID uint, roleIDs []uint) (bool, error) {
	for _, roleID := range roleIDs {
		hasRole, err := s.HasRole(ctx, userID, roleID)
		if err != nil {
			return false, err
		}
		if hasRole {
			return true, nil
		}
	}

	return false, nil
}

// HasAllRoles checks if a user has all of the specified roles
func (s *userRoleService) HasAllRoles(ctx context.Context, userID uint, roleIDs []uint) (bool, error) {
	for _, roleID := range roleIDs {
		hasRole, err := s.HasRole(ctx, userID, roleID)
		if err != nil {
			return false, err
		}
		if !hasRole {
			return false, nil
		}
	}

	return true, nil
}

// GetPrimaryRole retrieves the primary role for a user in a context
func (s *userRoleService) GetPrimaryRole(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) (*models.UserRole, error) {
	userRole, err := s.userRoleRepo.GetPrimaryRole(ctx, userID, contextType, contextID)
	if err != nil {
		return nil, fmt.Errorf("failed to get primary role: %w", err)
	}

	return userRole, nil
}

// SetPrimaryRole sets the primary role for a user in a context
func (s *userRoleService) SetPrimaryRole(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) error {
	if err := s.userRoleRepo.SetPrimaryRole(ctx, userID, roleID, contextType, contextID); err != nil {
		return fmt.Errorf("failed to set primary role: %w", err)
	}

	// Clear user cache
	if s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, userID, nil)
	}

	return nil
}

// ClearPrimaryRole clears the primary role for a user in a context
func (s *userRoleService) ClearPrimaryRole(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) error {
	if err := s.userRoleRepo.ClearPrimaryRole(ctx, userID, contextType, contextID); err != nil {
		return fmt.Errorf("failed to clear primary role: %w", err)
	}

	// Clear user cache
	if s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, userID, nil)
	}

	return nil
}

// GetEffectiveRoles retrieves effective roles for a user (including inherited)
func (s *userRoleService) GetEffectiveRoles(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	// Get direct roles
	directRoles, err := s.GetActiveUserRoles(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get inherited roles
	inheritedRoles, err := s.GetInheritedRoles(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Combine and deduplicate
	roleMap := make(map[uint]*models.UserRole)

	for _, role := range directRoles {
		roleMap[role.RoleID] = role
	}

	for _, role := range inheritedRoles {
		if _, exists := roleMap[role.RoleID]; !exists {
			roleMap[role.RoleID] = role
		}
	}

	// Convert to slice
	effectiveRoles := make([]*models.UserRole, 0, len(roleMap))
	for _, role := range roleMap {
		effectiveRoles = append(effectiveRoles, role)
	}

	return effectiveRoles, nil
}

// GetInheritedRoles retrieves inherited roles for a user
func (s *userRoleService) GetInheritedRoles(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	inheritedRoles, err := s.userRoleRepo.GetInheritedRoles(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get inherited roles: %w", err)
	}

	return inheritedRoles, nil
}

// SuspendUserRole suspends a user role
func (s *userRoleService) SuspendUserRole(ctx context.Context, userRoleID uint) error {
	if err := s.userRoleRepo.Suspend(ctx, userRoleID); err != nil {
		return fmt.Errorf("failed to suspend user role: %w", err)
	}

	// Clear user cache
	userRole, err := s.GetUserRole(ctx, userRoleID)
	if err == nil && s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, userRole.UserID, nil)
	}

	return nil
}

// ReactivateUserRole reactivates a user role
func (s *userRoleService) ReactivateUserRole(ctx context.Context, userRoleID uint) error {
	if err := s.userRoleRepo.Reactivate(ctx, userRoleID); err != nil {
		return fmt.Errorf("failed to reactivate user role: %w", err)
	}

	// Clear user cache
	userRole, err := s.GetUserRole(ctx, userRoleID)
	if err == nil && s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, userRole.UserID, nil)
	}

	return nil
}

// UpdateUserRoleStatus updates the status of a user role
func (s *userRoleService) UpdateUserRoleStatus(ctx context.Context, userRoleID uint, status models.UserRoleStatus) error {
	if err := s.userRoleRepo.UpdateStatus(ctx, userRoleID, status); err != nil {
		return fmt.Errorf("failed to update user role status: %w", err)
	}

	// Clear user cache
	userRole, err := s.GetUserRole(ctx, userRoleID)
	if err == nil && s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, userRole.UserID, nil)
	}

	return nil
}

// GetExpiredRoles retrieves expired roles
func (s *userRoleService) GetExpiredRoles(ctx context.Context, timestamp time.Time) ([]*models.UserRole, error) {
	expiredRoles, err := s.userRoleRepo.GetExpiredRoles(ctx, timestamp)
	if err != nil {
		return nil, fmt.Errorf("failed to get expired roles: %w", err)
	}

	return expiredRoles, nil
}

// GetTemporaryRoles retrieves temporary roles for a user
func (s *userRoleService) GetTemporaryRoles(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	temporaryRoles, err := s.userRoleRepo.GetTemporaryRoles(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get temporary roles: %w", err)
	}

	return temporaryRoles, nil
}

// ExtendRoleValidity extends the validity of a user role
func (s *userRoleService) ExtendRoleValidity(ctx context.Context, userRoleID uint, newValidUntil time.Time) error {
	userRole, err := s.GetUserRole(ctx, userRoleID)
	if err != nil {
		return err
	}

	// Validate new time
	if userRole.ValidFrom != nil && newValidUntil.Before(*userRole.ValidFrom) {
		return ErrInvalidTimeRange
	}

	// Update role
	userRole.ValidUntil = &newValidUntil
	if err := s.userRoleRepo.Update(ctx, userRole); err != nil {
		return fmt.Errorf("failed to update user role validity: %w", err)
	}

	// Clear user cache
	if s.rbacEngine != nil {
		s.rbacEngine.ClearUserCache(ctx, userRole.UserID, nil)
	}

	return nil
}

// AssignRolesBulk assigns roles in bulk
func (s *userRoleService) AssignRolesBulk(ctx context.Context, assignments []models.UserRoleCreateRequest) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole

	for _, req := range assignments {
		userRole, err := s.AssignRole(ctx, &req)
		if err != nil {
			return nil, fmt.Errorf("failed to assign role in bulk: %w", err)
		}
		userRoles = append(userRoles, userRole)
	}

	return userRoles, nil
}

// RevokeRolesBulk revokes roles in bulk
func (s *userRoleService) RevokeRolesBulk(ctx context.Context, userRoleIDs []uint, revokedBy uint) error {
	if err := s.userRoleRepo.RevokeBulk(ctx, userRoleIDs, revokedBy); err != nil {
		return fmt.Errorf("failed to revoke roles in bulk: %w", err)
	}

	// Clear cache for affected users
	if s.rbacEngine != nil {
		s.rbacEngine.ClearAllCache(ctx)
	}

	return nil
}

// SearchUserRoles searches user roles
func (s *userRoleService) SearchUserRoles(ctx context.Context, filters *repositories.UserRoleFilters) ([]*models.UserRole, error) {
	userRoles, err := s.userRoleRepo.Search(ctx, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to search user roles: %w", err)
	}

	return userRoles, nil
}

// GetUsersByRole retrieves users by role
func (s *userRoleService) GetUsersByRole(ctx context.Context, roleID uint) ([]*models.UserRole, error) {
	userRoles, err := s.userRoleRepo.GetUsersByRole(ctx, roleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get users by role: %w", err)
	}

	return userRoles, nil
}

// GetUsersInContext retrieves users in a specific context
func (s *userRoleService) GetUsersInContext(ctx context.Context, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error) {
	userRoles, err := s.userRoleRepo.GetUsersInContext(ctx, contextType, contextID)
	if err != nil {
		return nil, fmt.Errorf("failed to get users in context: %w", err)
	}

	return userRoles, nil
}

// GetRoleUsageStats retrieves role usage statistics
func (s *userRoleService) GetRoleUsageStats(ctx context.Context, roleID uint) (map[string]interface{}, error) {
	userRoles, err := s.GetUsersByRole(ctx, roleID)
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"total_users":     len(userRoles),
		"active_users":    0,
		"suspended_users": 0,
		"expired_users":   0,
	}

	now := time.Now()
	for _, userRole := range userRoles {
		switch userRole.Status {
		case models.UserRoleStatusActive:
			if userRole.ValidUntil == nil || userRole.ValidUntil.After(now) {
				stats["active_users"] = stats["active_users"].(int) + 1
			} else {
				stats["expired_users"] = stats["expired_users"].(int) + 1
			}
		case models.UserRoleStatusSuspended:
			stats["suspended_users"] = stats["suspended_users"].(int) + 1
		}
	}

	return stats, nil
}

// GetUserRoleHistory retrieves role history for a user
func (s *userRoleService) GetUserRoleHistory(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	userRoles, err := s.GetUserRoles(ctx, userID)
	if err != nil {
		return nil, err
	}

	return userRoles, nil
}

// CleanupExpiredRoles removes expired roles
func (s *userRoleService) CleanupExpiredRoles(ctx context.Context) (int64, error) {
	count, err := s.userRoleRepo.CleanupExpiredRoles(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup expired roles: %w", err)
	}

	// Clear all cache after cleanup
	if s.rbacEngine != nil {
		s.rbacEngine.ClearAllCache(ctx)
	}

	return count, nil
}

// CleanupRevokedRoles removes old revoked roles
func (s *userRoleService) CleanupRevokedRoles(ctx context.Context, olderThan time.Time) (int64, error) {
	count, err := s.userRoleRepo.CleanupRevokedRoles(ctx, olderThan)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup revoked roles: %w", err)
	}

	// Clear all cache after cleanup
	if s.rbacEngine != nil {
		s.rbacEngine.ClearAllCache(ctx)
	}

	return count, nil
}

// GetCurrentUserRoles retrieves roles for current user in current tenant
func (s *userRoleService) GetCurrentUserRoles(ctx context.Context, userID uint, tenantID uint) ([]*models.UserRole, error) {
	userRoles, err := s.userRoleRepo.GetActiveRoles(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current user roles: %w", err)
	}

	// Filter by tenant ID directly from userRole
	var filteredRoles []*models.UserRole
	for _, userRole := range userRoles {
		if userRole.TenantID == tenantID {
			filteredRoles = append(filteredRoles, userRole)
		}
	}

	return filteredRoles, nil
}

// GetCurrentUserPermissions retrieves all permission names for current user in current tenant
func (s *userRoleService) GetCurrentUserPermissions(ctx context.Context, userID uint, tenantID uint) ([]string, error) {
	// Get user roles in this tenant
	userRoles, err := s.GetCurrentUserRoles(ctx, userID, tenantID)
	if err != nil {
		return nil, err
	}

	// Collect all role IDs
	roleIDs := make([]uint, len(userRoles))
	for i, userRole := range userRoles {
		roleIDs[i] = userRole.RoleID
	}

	if len(roleIDs) == 0 {
		return []string{}, nil
	}

	// Get permissions for these roles using RBAC engine if available
	if s.rbacEngine != nil {
		permissionMap, err := s.rbacEngine.GetUserPermissions(ctx, userID, &tenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to get user permissions from RBAC engine: %w", err)
		}

		// Extract permission names where value is true
		permissions := make([]string, 0, len(permissionMap))
		for permissionName, hasPermission := range permissionMap {
			if hasPermission {
				permissions = append(permissions, permissionName)
			}
		}

		return permissions, nil
	}

	// Fallback: get permissions for each role individually
	var allPermissions []*models.Permission
	seen := make(map[string]bool)
	
	for _, roleID := range roleIDs {
		permissions, err := s.roleService.GetRolePermissions(ctx, roleID)
		if err != nil {
			return nil, fmt.Errorf("failed to get role permissions for role %d: %w", roleID, err)
		}
		allPermissions = append(allPermissions, permissions...)
	}

	// Extract unique permission names
	permissionNames := make([]string, 0, len(allPermissions))
	for _, permission := range allPermissions {
		if !seen[permission.Name] {
			permissionNames = append(permissionNames, permission.Name)
			seen[permission.Name] = true
		}
	}

	return permissionNames, nil
}
