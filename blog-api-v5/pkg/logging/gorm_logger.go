package logging

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
)

// BeautifulGormLogger is a custom GORM logger that formats SQL queries beautifully
type BeautifulGormLogger struct {
	logger.Config
}

// NewBeautifulGormLogger creates a new beautiful GORM logger
func NewBeautifulGormLogger(config logger.Config) logger.Interface {
	return &BeautifulGormLogger{
		Config: config,
	}
}

// LogMode sets the log level
func (l *BeautifulGormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newLogger := *l
	newLogger.LogLevel = level
	return &newLogger
}

// Info logs info messages
func (l BeautifulGormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Info {
		fmt.Printf("🔵 [GORM INFO] %s\n", fmt.Sprintf(msg, data...))
	}
}

// Warn logs warning messages
func (l BeautifulGormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		fmt.Printf("🟡 [GORM WARN] %s\n", fmt.Sprintf(msg, data...))
	}
}

// Error logs error messages
func (l BeautifulGormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		fmt.Printf("🔴 [GORM ERROR] %s\n", fmt.Sprintf(msg, data...))
	}
}

// Trace logs SQL queries with beautiful formatting
func (l BeautifulGormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	// Format SQL query beautifully
	formattedSQL := l.formatSQL(sql)

	// Choose color based on execution time
	var timeColor string
	switch {
	case elapsed < 100*time.Millisecond:
		timeColor = "🟢" // Green for fast queries
	case elapsed < 500*time.Millisecond:
		timeColor = "🟡" // Yellow for medium queries
	default:
		timeColor = "🔴" // Red for slow queries
	}

	// Format the log message
	if err != nil {
		// Check if it's a "record not found" error - treat as warning
		if err.Error() == "record not found" {
			fmt.Printf("🟡 [SQL WARNING] %s [%.3fms] [rows:%d]\n%s\nWarning: %v\n\n",
				utils.FileWithLineNum(),
				float64(elapsed.Nanoseconds())/1e6,
				rows,
				formattedSQL,
				err,
			)
		} else {
			fmt.Printf("🔴 [SQL ERROR] %s [%.3fms] [rows:%d]\n%s\nError: %v\n\n",
				utils.FileWithLineNum(),
				float64(elapsed.Nanoseconds())/1e6,
				rows,
				formattedSQL,
				err,
			)
		}
	} else {
		fmt.Printf("%s [SQL] %s [%.3fms] [rows:%d]\n%s\n\n",
			timeColor,
			utils.FileWithLineNum(),
			float64(elapsed.Nanoseconds())/1e6,
			rows,
			formattedSQL,
		)
	}
}

// formatSQL formats SQL query for better readability
func (l BeautifulGormLogger) formatSQL(sql string) string {
	// Remove extra spaces and normalize
	sql = strings.TrimSpace(sql)
	sql = strings.ReplaceAll(sql, "\n", " ")
	sql = strings.ReplaceAll(sql, "\t", " ")

	// Replace multiple spaces with single space
	for strings.Contains(sql, "  ") {
		sql = strings.ReplaceAll(sql, "  ", " ")
	}

	// Add line breaks for better readability
	keywords := []string{
		"SELECT", "FROM", "WHERE", "JOIN", "LEFT JOIN", "RIGHT JOIN", "INNER JOIN",
		"GROUP BY", "ORDER BY", "HAVING", "LIMIT", "OFFSET", "INSERT", "UPDATE", "DELETE",
		"VALUES", "SET", "AND", "OR",
	}

	for _, keyword := range keywords {
		// Add line break before keywords (except at the beginning)
		sql = strings.ReplaceAll(sql, " "+keyword+" ", "\n"+keyword+" ")
		sql = strings.ReplaceAll(sql, " "+strings.ToLower(keyword)+" ", "\n"+keyword+" ")
	}

	// Clean up the beginning
	if strings.HasPrefix(sql, "\n") {
		sql = strings.TrimPrefix(sql, "\n")
	}

	// Add proper indentation with more padding
	lines := strings.Split(sql, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if i == 0 {
			// First line gets emoji and base padding
			lines[i] = "📝   " + line
		} else if line != "" {
			// Subsequent lines get more indentation
			lines[i] = "     " + line
		}
	}

	return strings.Join(lines, "\n")
}
