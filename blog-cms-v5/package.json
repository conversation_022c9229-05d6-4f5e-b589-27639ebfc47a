{"name": "blog-ui-v1", "private": true, "version": "1.0.0", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev --open", "format": "prettier --write .", "lint": "eslint .", "preview": "rsbuild preview", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:register": "playwright test tests/register-happy-case.spec.ts --headed --project chromium", "test:register-quick": "playwright test tests/register-happy-case.spec.ts -g 'quick form fill' --headed --project chromium", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "importSorter": {"autoFormat": "onSave", "quoteMark": "single", "wrappingStyle": {"maxBindingNamesPerLine": 2}}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/plots": "^2.3.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@measured/puck": "^0.19.3", "@nosferatu500/react-sortable-tree": "^4.4.0", "@types/lodash": "^4.17.12", "@types/react": "^18.3.12", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.1", "@types/styled-components": "^5.1.34", "@udecode/plate": "^48.0.5", "@udecode/plate-alignment": "^49.0.0", "@udecode/plate-autoformat": "^48.0.0", "@udecode/plate-basic-elements": "^48.0.0", "@udecode/plate-basic-marks": "^48.0.0", "@udecode/plate-block-quote": "^48.0.0", "@udecode/plate-break": "^48.0.0", "@udecode/plate-code-block": "^48.0.0", "@udecode/plate-comments": "^48.0.0", "@udecode/plate-common": "^41.0.13", "@udecode/plate-core": "^48.0.5", "@udecode/plate-dnd": "^48.0.0", "@udecode/plate-find-replace": "^48.0.0", "@udecode/plate-floating": "^48.0.0", "@udecode/plate-font": "^48.0.0", "@udecode/plate-heading": "^48.0.0", "@udecode/plate-highlight": "^48.0.0", "@udecode/plate-indent": "^48.0.0", "@udecode/plate-kbd": "^48.0.0", "@udecode/plate-layout": "^48.0.0", "@udecode/plate-line-height": "^48.0.0", "@udecode/plate-link": "^48.0.0", "@udecode/plate-list": "^48.0.0", "@udecode/plate-media": "^48.0.0", "@udecode/plate-node-id": "^48.0.6", "@udecode/plate-normalizers": "^48.0.0", "@udecode/plate-paragraph": "^36.0.0", "@udecode/plate-reset-node": "^48.0.0", "@udecode/plate-select": "^48.0.0", "@udecode/plate-serializer-html": "^21.5.1", "@udecode/plate-serializer-md": "^36.4.0", "@udecode/plate-suggestion": "^48.0.0", "@udecode/plate-table": "^48.0.6", "@udecode/plate-toolbar": "^8.3.0", "@udecode/plate-trailing-block": "^48.0.0", "antd": "^5.21.5", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "dayjs": "^1.11.13", "debug": "^4.3.7", "dompurify": "^3.1.7", "dotenv-webpack": "^8.1.0", "eslint-plugin-jsx-a11y": "^6.10.1", "eslint-plugin-prettier": "^5.2.1", "i18next": "^23.16.3", "i18next-http-backend": "^2.6.2", "immer": "^9.0.12", "konva": "^9.3.20", "lodash": "^4.17.21", "mobx": "^6.13.7", "moment": "^2.30.1", "postcss": "^8.4.47", "query-string": "^9.1.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.0", "react-icons": "^5.5.0", "react-json-editor-ajrm": "^2.5.14", "react-json-pretty": "^2.2.0", "react-konva": "^18.2.12", "react-number-format": "^5.4.2", "react-photo-editor": "^3.0.0", "react-responsive": "^10.0.0", "react-router-dom": "^6.27.0", "react-slick": "^0.30.2", "react-use": "^17.5.1", "reactflow": "^11.11.4", "reactjs-tiptap-editor": "^0.3.5", "slate": "^0.118.0", "slate-dom": "^0.117.4", "slate-history": "^0.113.1", "slate-hyperscript": "^0.100.0", "slate-react": "^0.117.4", "slick-carousel": "^1.8.1", "slugify": "^1.6.6", "socket.io-client": "^4.8.1", "styled-components": "^6.1.17", "tailwindcss": "^3.4.14", "use-image": "^1.1.4", "web-vitals": "^4.2.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/compat": "^1.2.1", "@eslint/js": "^9.13.0", "@playwright/test": "^1.54.1", "@rsbuild/core": "^1.0.17", "@rsbuild/plugin-react": "^1.0.5", "@rsbuild/plugin-sass": "^1.0.4", "@storybook/addon-docs": "^9.1.1", "@storybook/addon-webpack5-compiler-swc": "^3.0.0", "@storybook/react-webpack5": "^9.1.1", "@storybook/test": "^8.6.14", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/debug": "^4.1.12", "@types/dompurify": "^3.0.5", "@types/jest": "^30.0.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-slick": "^0.23.13", "@types/slick-carousel": "^1.6.40", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "css-loader": "^7.1.2", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-storybook": "^9.1.1", "globals": "^15.11.0", "husky": "^9.1.6", "i18next-parser": "^9.1.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "sass": "^1.89.2", "sass-loader": "^16.0.5", "storybook": "^9.1.1", "style-loader": "^4.0.0", "ts-jest": "^29.4.0", "typescript": "^5.6.3", "typescript-eslint": "^8.11.0"}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}": ["prettier --write", "eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "npx --no-install lint-staged"}}}