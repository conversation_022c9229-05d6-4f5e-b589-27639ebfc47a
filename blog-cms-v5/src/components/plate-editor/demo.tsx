import React, { useState } from 'react';
import { Card, Typography, Space } from 'antd';
import { PlateEditor } from './plate-editor';
import { Descendant } from 'slate';

const { Title, Paragraph } = Typography;

const initialValue: Descendant[] = [
  {
    type: 'heading-one',
    children: [{ text: 'Welcome to Plate Editor' }],
  },
  {
    type: 'paragraph',
    children: [
      { text: 'This is a ' },
      { text: 'rich text editor', bold: true },
      { text: ' built with ' },
      { text: 'Slate.js', italic: true },
      { text: '. You can format text, create headings, lists, and more!' },
    ],
  },
  {
    type: 'heading-two',
    children: [{ text: 'Features' }],
  },
  {
    type: 'bulleted-list',
    children: [
      {
        type: 'list-item',
        children: [{ text: 'Bold, italic, underline formatting' }],
      },
      {
        type: 'list-item',
        children: [{ text: 'Multiple heading levels' }],
      },
      {
        type: 'list-item',
        children: [{ text: 'Bulleted and numbered lists' }],
      },
      {
        type: 'list-item',
        children: [{ text: 'Block quotes and code blocks' }],
      },
    ],
  },
  {
    type: 'block-quote',
    children: [{ text: 'This is a block quote. Perfect for highlighting important information!' }],
  },
  {
    type: 'code-block',
    children: [{ text: 'console.log("Hello, Plate Editor!");' }],
  },
];

export const PlateEditorDemo: React.FC = () => {
  const [value, setValue] = useState<Descendant[]>(initialValue);

  const handleChange = (newValue: Descendant[]) => {
    setValue(newValue);
    console.log('Editor content changed:', newValue);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Title level={2}>Plate Editor Demo</Title>
          <Paragraph>
            This is a demonstration of the Plate Editor component built with Slate.js.
            Try editing the content below using the toolbar or keyboard shortcuts:
          </Paragraph>
          <ul>
            <li><strong>Ctrl/Cmd + B</strong> - Bold</li>
            <li><strong>Ctrl/Cmd + I</strong> - Italic</li>
            <li><strong>Ctrl/Cmd + U</strong> - Underline</li>
            <li><strong>Ctrl/Cmd + `</strong> - Code</li>
          </ul>
        </div>

        <Card title="Rich Text Editor" style={{ width: '100%' }}>
          <PlateEditor
            value={value}
            onChange={handleChange}
            placeholder="Start typing your content here..."
            theme="light"
          />
        </Card>

        <Card title="Editor Output (JSON)" style={{ width: '100%' }}>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '16px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '300px',
            fontSize: '12px'
          }}>
            {JSON.stringify(value, null, 2)}
          </pre>
        </Card>
      </Space>
    </div>
  );
};

export default PlateEditorDemo;
