/* Plate Editor Styles */
.plate-editor {
  position: relative;
  width: 100%;
}

.plate-editor.dark {
  background-color: #1f1f1f;
  color: #ffffff;
}

.plate-editor.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Editor Content Styles */
.plate-editor [data-slate-editor] {
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.plate-editor [data-slate-editor]:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Heading Styles */
.plate-editor h1 {
  font-size: 2em;
  font-weight: 600;
  margin: 0.67em 0;
  line-height: 1.2;
}

.plate-editor h2 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 0.75em 0;
  line-height: 1.3;
}

.plate-editor h3 {
  font-size: 1.17em;
  font-weight: 600;
  margin: 0.83em 0;
  line-height: 1.4;
}

.plate-editor h4 {
  font-size: 1em;
  font-weight: 600;
  margin: 1.12em 0;
  line-height: 1.4;
}

.plate-editor h5 {
  font-size: 0.83em;
  font-weight: 600;
  margin: 1.5em 0;
  line-height: 1.4;
}

.plate-editor h6 {
  font-size: 0.75em;
  font-weight: 600;
  margin: 1.67em 0;
  line-height: 1.4;
}

/* Paragraph Styles */
.plate-editor p {
  margin: 1em 0;
  line-height: 1.6;
}

/* List Styles */
.plate-editor ul,
.plate-editor ol {
  margin: 1em 0;
  padding-left: 2em;
}

.plate-editor li {
  margin: 0.5em 0;
  line-height: 1.6;
}

/* Blockquote Styles */
.plate-editor blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #d9d9d9;
  background-color: #fafafa;
  font-style: italic;
}

.plate-editor.dark blockquote {
  border-left-color: #434343;
  background-color: #2a2a2a;
}

/* Code Block Styles */
.plate-editor pre {
  margin: 1em 0;
  padding: 1em;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.plate-editor.dark pre {
  background-color: #2a2a2a;
  border-color: #434343;
}

.plate-editor code {
  padding: 0.2em 0.4em;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.plate-editor.dark code {
  background-color: #2a2a2a;
  border-color: #434343;
}

/* Mark Styles */
.plate-editor strong {
  font-weight: 600;
}

.plate-editor em {
  font-style: italic;
}

.plate-editor u {
  text-decoration: underline;
}

.plate-editor s {
  text-decoration: line-through;
}

/* Link Styles */
.plate-editor a {
  color: #1890ff;
  text-decoration: none;
}

.plate-editor a:hover {
  text-decoration: underline;
}

.plate-editor.dark a {
  color: #40a9ff;
}

/* Table Styles */
.plate-editor table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.plate-editor th,
.plate-editor td {
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  text-align: left;
}

.plate-editor th {
  background-color: #fafafa;
  font-weight: 600;
}

.plate-editor.dark th {
  background-color: #2a2a2a;
}

.plate-editor.dark th,
.plate-editor.dark td {
  border-color: #434343;
}

/* Highlight Styles */
.plate-editor mark {
  background-color: #fff566;
  padding: 0.1em 0.2em;
  border-radius: 2px;
}

.plate-editor.dark mark {
  background-color: #d4b106;
  color: #000;
}

/* Keyboard Styles */
.plate-editor kbd {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  padding: 0.1em 0.3em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.plate-editor.dark kbd {
  background-color: #2a2a2a;
  border-color: #434343;
}

/* Placeholder Styles */
.plate-editor [data-slate-placeholder] {
  color: #bfbfbf;
  pointer-events: none;
  user-select: none;
  position: absolute;
  top: 16px;
  left: 16px;
}

.plate-editor.dark [data-slate-placeholder] {
  color: #8c8c8c;
}

/* Selection Styles */
.plate-editor [data-slate-selected] {
  background-color: rgba(24, 144, 255, 0.1);
}

.plate-editor.dark [data-slate-selected] {
  background-color: rgba(64, 169, 255, 0.2);
}

/* Media Styles */
.plate-editor img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.plate-editor video {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plate-editor [data-slate-editor] {
    padding: 12px;
  }
  
  .plate-editor [data-slate-placeholder] {
    top: 12px;
    left: 12px;
  }
}
