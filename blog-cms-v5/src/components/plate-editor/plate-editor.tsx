import React, { useCallback, useMemo } from 'react';
import { createE<PERSON><PERSON>, Editor, BaseEditor, Descendant } from 'slate';
import { Slate, Editable, withReact, ReactEditor, RenderElementProps, RenderLeafProps } from 'slate-react';
import { withHistory } from 'slate-history';
import { PlateToolbar } from './toolbar';

// Import CSS
import './plate-editor.css';

// Types
type CustomElement = {
  type: 'paragraph' | 'heading-one' | 'heading-two' | 'heading-three' | 'block-quote' | 'bulleted-list' | 'numbered-list' | 'list-item' | 'code-block';
  children: CustomText[];
};

type CustomText = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  code?: boolean;
};

declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

// Constants
const DEFAULT_EDITOR_CONTENT: CustomElement[] = [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
];

const DEBOUNCE_DELAY = 300;

// Editor Props Interface
export interface PlateEditorProps {
  value?: Descendant[];
  onChange?: (value: Descendant[]) => void;
  placeholder?: string;
  disabled?: boolean;
  theme?: 'light' | 'dark';
  debounceDelay?: number;
  readOnly?: boolean;
}

// Utility function for debouncing
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

// Element renderer
const Element = ({ attributes, children, element }: RenderElementProps) => {
  switch (element.type) {
    case 'heading-one':
      return <h1 {...attributes}>{children}</h1>;
    case 'heading-two':
      return <h2 {...attributes}>{children}</h2>;
    case 'heading-three':
      return <h3 {...attributes}>{children}</h3>;
    case 'block-quote':
      return <blockquote {...attributes}>{children}</blockquote>;
    case 'bulleted-list':
      return <ul {...attributes}>{children}</ul>;
    case 'numbered-list':
      return <ol {...attributes}>{children}</ol>;
    case 'list-item':
      return <li {...attributes}>{children}</li>;
    case 'code-block':
      return <pre {...attributes}><code>{children}</code></pre>;
    default:
      return <p {...attributes}>{children}</p>;
  }
};

// Leaf renderer
const Leaf = ({ attributes, children, leaf }: RenderLeafProps) => {
  if (leaf.bold) {
    children = <strong>{children}</strong>;
  }

  if (leaf.italic) {
    children = <em>{children}</em>;
  }

  if (leaf.underline) {
    children = <u>{children}</u>;
  }

  if (leaf.code) {
    children = <code>{children}</code>;
  }

  return <span {...attributes}>{children}</span>;
};

export function PlateEditor({
  value,
  onChange,
  placeholder = 'Nhập nội dung...',
  disabled = false,
  theme = 'light',
  debounceDelay = DEBOUNCE_DELAY,
  readOnly = false,
}: PlateEditorProps) {
  // Create editor instance
  const editor = useMemo(
    () => withHistory(withReact(createEditor())),
    []
  );

  // Debounced change handler
  const debouncedOnChange = useMemo(
    () => debounce((newValue: Descendant[]) => {
      onChange?.(newValue);
    }, debounceDelay),
    [onChange, debounceDelay]
  );

  // Handle editor value change
  const handleChange = useCallback(
    (newValue: Descendant[]) => {
      debouncedOnChange(newValue);
    },
    [debouncedOnChange]
  );

  // Memoized editor content
  const editorValue = useMemo(() => value || DEFAULT_EDITOR_CONTENT, [value]);

  // Handle key commands
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!event.ctrlKey && !event.metaKey) {
      return;
    }

    switch (event.key) {
      case 'b': {
        event.preventDefault();
        Editor.addMark(editor, 'bold', true);
        break;
      }
      case 'i': {
        event.preventDefault();
        Editor.addMark(editor, 'italic', true);
        break;
      }
      case 'u': {
        event.preventDefault();
        Editor.addMark(editor, 'underline', true);
        break;
      }
      case '`': {
        event.preventDefault();
        Editor.addMark(editor, 'code', true);
        break;
      }
    }
  }, [editor]);

  return (
    <div className={`plate-editor ${theme === 'dark' ? 'dark' : ''} ${disabled ? 'disabled' : ''}`}>
      <Slate
        editor={editor}
        initialValue={editorValue}
        onChange={handleChange}
      >
        <div
          style={{
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            overflow: 'hidden',
          }}
        >
          <PlateToolbar />
          <Editable
            placeholder={placeholder}
            readOnly={readOnly || disabled}
            renderElement={Element}
            renderLeaf={Leaf}
            onKeyDown={handleKeyDown}
            style={{
              minHeight: '200px',
              padding: '16px',
              fontSize: '14px',
              lineHeight: '1.5',
              outline: 'none',
            }}
          />
        </div>
      </Slate>
    </div>
  );
}

export default PlateEditor;
