import React from 'react';
import { Editor, Transforms, Element as SlateElement } from 'slate';
import { useSlate } from 'slate-react';
import { Button, Space, Divider } from 'antd';
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  CodeOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';

// Types
type BlockFormat = 'paragraph' | 'heading-one' | 'heading-two' | 'heading-three' | 'block-quote' | 'bulleted-list' | 'numbered-list' | 'code-block';
type MarkFormat = 'bold' | 'italic' | 'underline' | 'code';

// Helper functions
const isBlockActive = (editor: Editor, format: BlockFormat) => {
  const { selection } = editor;
  if (!selection) return false;

  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: n =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        n.type === format,
    })
  );

  return !!match;
};

const isMarkActive = (editor: Editor, format: MarkFormat) => {
  const marks = Editor.marks(editor);
  return marks ? marks[format] === true : false;
};

const toggleBlock = (editor: Editor, format: BlockFormat) => {
  const isActive = isBlockActive(editor, format);
  const isList = ['numbered-list', 'bulleted-list'].includes(format);

  Transforms.unwrapNodes(editor, {
    match: n =>
      !Editor.isEditor(n) &&
      SlateElement.isElement(n) &&
      ['numbered-list', 'bulleted-list'].includes(n.type),
    split: true,
  });

  let newProperties: Partial<SlateElement>;
  if (isActive) {
    newProperties = { type: 'paragraph' };
  } else if (isList) {
    newProperties = { type: 'list-item' };
  } else {
    newProperties = { type: format };
  }

  Transforms.setNodes<SlateElement>(editor, newProperties);

  if (!isActive && isList) {
    const block = { type: format, children: [] };
    Transforms.wrapNodes(editor, block);
  }
};

const toggleMark = (editor: Editor, format: MarkFormat) => {
  const isActive = isMarkActive(editor, format);

  if (isActive) {
    Editor.removeMark(editor, format);
  } else {
    Editor.addMark(editor, format, true);
  }
};

// Toolbar button component
interface ToolbarButtonProps {
  format: BlockFormat | MarkFormat;
  icon: React.ReactNode;
  isBlock?: boolean;
  children?: React.ReactNode;
}

const ToolbarButton: React.FC<ToolbarButtonProps> = ({ format, icon, isBlock = false, children }) => {
  const editor = useSlate();
  const isActive = isBlock ? isBlockActive(editor, format as BlockFormat) : isMarkActive(editor, format as MarkFormat);

  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault();
    if (isBlock) {
      toggleBlock(editor, format as BlockFormat);
    } else {
      toggleMark(editor, format as MarkFormat);
    }
  };

  return (
    <Button
      type={isActive ? 'primary' : 'default'}
      size="small"
      icon={icon}
      onMouseDown={handleMouseDown}
      style={{
        border: isActive ? '1px solid #1890ff' : '1px solid #d9d9d9',
        backgroundColor: isActive ? '#1890ff' : 'transparent',
        color: isActive ? '#fff' : '#000',
      }}
    >
      {children}
    </Button>
  );
};

// Main toolbar component
export const PlateToolbar: React.FC = () => {
  return (
    <div
      style={{
        padding: '8px 16px',
        borderBottom: '1px solid #d9d9d9',
        backgroundColor: '#fafafa',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        flexWrap: 'wrap',
      }}
    >
      <Space size="small">
        {/* Text formatting */}
        <ToolbarButton format="bold" icon={<BoldOutlined />} />
        <ToolbarButton format="italic" icon={<ItalicOutlined />} />
        <ToolbarButton format="underline" icon={<UnderlineOutlined />} />
        <ToolbarButton format="code" icon={<CodeOutlined />} />
      </Space>

      <Divider type="vertical" style={{ height: '20px' }} />

      <Space size="small">
        {/* Headings */}
        <ToolbarButton format="heading-one" icon={<span>H1</span>} isBlock>
          H1
        </ToolbarButton>
        <ToolbarButton format="heading-two" icon={<span>H2</span>} isBlock>
          H2
        </ToolbarButton>
        <ToolbarButton format="heading-three" icon={<span>H3</span>} isBlock>
          H3
        </ToolbarButton>
      </Space>

      <Divider type="vertical" style={{ height: '20px' }} />

      <Space size="small">
        {/* Lists */}
        <ToolbarButton format="bulleted-list" icon={<UnorderedListOutlined />} isBlock />
        <ToolbarButton format="numbered-list" icon={<OrderedListOutlined />} isBlock />
      </Space>

      <Divider type="vertical" style={{ height: '20px' }} />

      <Space size="small">
        {/* Block elements */}
        <ToolbarButton format="block-quote" icon={<span>"</span>} isBlock>
          Quote
        </ToolbarButton>
        <ToolbarButton format="code-block" icon={<CodeOutlined />} isBlock>
          Code Block
        </ToolbarButton>
      </Space>
    </div>
  );
};

export default PlateToolbar;
