// BlogTagResponse DTO - API v3 response format
export interface BlogTagResponse {
  id: number;
  tenant_id: number;
  website_id: number;
  name: string;
  slug: string;
  description?: string;
  post_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// BlogTagCreateRequest DTO - for creating new tags
export interface BlogTagCreateRequest {
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
}

// BlogTagUpdateRequest DTO - for updating existing tags
export interface BlogTagUpdateRequest {
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
}

// BlogTagListResponse DTO - for paginated list responses
export interface BlogTagListResponse {
  data: BlogTagResponse[];
  meta?: {
    next_cursor?: string;
    has_more: boolean;
  };
}

// BlogTagFilter - for query parameters
export interface BlogTagFilter {
  cursor?: string;
  limit?: number;
  search?: string;
  is_active?: boolean;
  sort_by?: 'name' | 'created_at' | 'post_count';
  sort_order?: 'asc' | 'desc';
}

// BlogTagSelectOption - for select components
export interface BlogTagSelectOption {
  id: number;
  name: string;
  slug: string;
  post_count?: number;
  description?: string;
}

// Legacy type for backward compatibility (deprecated)
export type BlogTag = BlogTagResponse;
