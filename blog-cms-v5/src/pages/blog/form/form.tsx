import { Card, Col, Form, Input, message, Row, Spin } from 'antd';
import { Dayjs } from 'dayjs';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { PlateEditor } from '../../../components/plate-editor';
import { InputSlug } from '../../../components/input';
import { useNavigateTenant, useTenantId } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import useSEOMetaStore from '../../seo-meta/store';
import {
  createItem,
  getItem,
  updateItem,
  updatePostRelatedPosts,
} from '../api';
import {
  BlogMenu,
  BlogSchedule,
  BlogTopBar,
  BlogRevisionsModal,
} from '../components';
import { BlogHistoryModal } from '../components/blog-history';
import { MODULE } from '../config';
import useBlogStore, { useBlogUI } from '../store';
import {
  Blog,
  BlogPostStatus,
  BlogPostCreateRequest,
  BlogPostUpdateRequest,
  transformBlog,
} from '../type';
import { WorkflowWidget } from '../../blog-workflow/components/WorkflowWidget';
const FormItem = Form.Item;

interface IndexFormProps {
  id?: string;
}

// Updated FormValues interface to match Blog type structure
interface FormValues {
  title?: string;
  slug?: string;
  excerpt?: string;
  content?: string;
  featured_image?: string;
  status?: BlogPostStatus | string;
  password?: string;
  allow_comments?: boolean;
  is_featured?: boolean;
  is_sticky?: boolean;
  published_at?: string;
  scheduled_at?: string;
  author_id?: number;

  // Related data
  category_id?: number;
  tag_ids?: number[] | string[];

  // SEO data
  seo?: {
    meta_title?: string;
    meta_description?: string;
    meta_keywords?: string[];
    og_title?: string;
    og_description?: string;
    twitter_title?: string;
    focus_keyword?: string;
  };
}

const IndexForm: React.FC<IndexFormProps> = ({ id }) => {
  // Bố cục 2 cột: menu bên trái, form bên phải
  // Sử dụng Row/Col của antd

  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);

  // Use optimized store hooks
  const { loading } = useBlogUI();

  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const initForm: FormValues = {};
  const [formValues, setFormValues] = useState<Blog>();
  const getSEOData = useSEOMetaStore((state) => state.getSEOData);
  const [scheduleVisible, setScheduleVisible] = useState<boolean>(false);
  const [scheduledDateTime, setScheduledDateTime] = useState<Dayjs | null>(
    null,
  );
  const navigate = useNavigateTenant();
  const tenantId = useTenantId();
  const [historyModalVisible, setHistoryModalVisible] = useState(false);
  const [revisionsModalVisible, setRevisionsModalVisible] = useState(false);

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
      // Set loading to false for new items
      useBlogStore.getState().setUIState({ loading: false });
    } else if (id) {
      setIsNew(false);
      // Define getItemData inside useEffect to avoid dependency issues
      const getItemData = async (_id: string) => {
        // Use the new store method to set loading
        useBlogStore.getState().setUIState({ loading: true });

        const res = await getItem(_id);
        if (res.status.success) {
          const item = transformBlog(res.data);

          // Set form fields
          form.setFieldsValue(item);
          console.log('✅ Form fields set with item data');

          // Explicitly set content field to ensure it's properly registered
          if (item.content) {
            form.setFieldValue('content', item.content);
            console.log(
              '✅ Content field explicitly set:',
              item.content?.length ? `${item.content.length} chars` : 'empty',
            );
          }

          // Update store with batch updates
          useBlogStore.getState().setDataState({ currentBlog: item });
          useBlogStore.getState().setFormState({ formValues: item });

          // Update local state
          setFormValues(item);
          console.log('✅ Store and local state updated');

          // Set loading to false
          useBlogStore.getState().setUIState({ loading: false });
        } else {
          message.error(res.status.message);
          useBlogStore.getState().setUIState({ loading: false });
        }
      };
      getItemData(id);
    }
  }, [id, form]);

  // useEffect(() => {
  //   if (item && !isNew) {
  //     logger('Updating form with item data:', item);
  //     form.setFieldsValue(item);
  //     // Đặc biệt đảm bảo description field được cập nhật
  //     if (item.description !== undefined) {
  //       form.setFieldValue('description', item.description);
  //       logger('Set description field value:', item.description);
  //     }
  //     // Đặc biệt đảm bảo content field được cập nhật
  //     if (item.content !== undefined) {
  //       form.setFieldValue('content', item.content);
  //       logger(
  //         'Set content field value:',
  //         item.content?.length ? `${item.content.length} chars` : 'empty',
  //       );
  //     }
  //     // Cập nhật scheduled date nếu có
  //     if (item.schedule_at || item.publish_date) {
  //       const publishDate = dayjs(item.schedule_at || item.publish_date);
  //       setScheduledDateTime(publishDate);
  //       logger('Set scheduled date:', publishDate.format('DD/MM/YYYY HH:mm'));
  //     }
  //   }
  // }, [item, form, isNew, logger]);

  const onFinish = async (values: FormValues) => {
    try {
      let res;
      const { form: formState } = useBlogStore.getState();
      const drawerData = formState.drawerData;

      // Debug logging for related posts
      console.group('🚀 RELATED POSTS DEBUG');
      console.log('📋 Drawer Data:', drawerData);
      console.log('🔗 Related Post IDs:', drawerData?.related_post_ids);
      console.log(
        '📊 Type of related_post_ids:',
        typeof drawerData?.related_post_ids,
      );
      console.log('🔢 Is Array?', Array.isArray(drawerData?.related_post_ids));
      console.log(
        '📏 Array Length:',
        drawerData?.related_post_ids?.length || 0,
      );
      console.groupEnd();

      // Debug logging for content tracking
      console.group('🚀 FORM SUBMISSION DEBUG');
      console.log('📝 Form values from onFinish:', values);
      console.log('📦 Current formValues state:', formValues);

      // Create payload with proper v2.0 API field structure
      const status = form.getFieldValue('status');

      // Create payload with proper v2.0 API field structure
      const payload: Blog = {
        ...formValues,
        ...values,
      };

      if (status) {
        payload.status = status;
      }

      // Ensure content is properly included - prioritize formValues content if it exists
      if (formValues?.content && formValues.content.trim() !== '') {
        payload.content = formValues.content;
        console.log(
          '✅ Using content from formValues state:',
          formValues.content?.length
            ? `${formValues.content.length} chars`
            : 'empty',
        );
      } else if (values.content && values.content.trim() !== '') {
        payload.content = values.content;
        console.log(
          '✅ Using content from form values:',
          values.content?.length ? `${values.content.length} chars` : 'empty',
        );
      }

      // Map fields to API format
      if (values.title && !payload.title) {
        payload.title = values.title;
      }

      // Ensure category_ids and tag_ids are properly set
      if (values.category_ids) {
        payload.category_ids = values.category_ids;
      }
      if (values.tag_ids) {
        payload.tag_ids = values.tag_ids;
      }

      console.log('🎯 Final payload content:', payload.content);
      console.log(
        '📏 Final payload content length:',
        payload.content?.length || 0,
      );
      console.groupEnd();

      // Merge drawer data using type-safe approach
      if (drawerData) {
        logger('Adding drawer data to payload - raw:', drawerData);

        // Use the extractDrawerFields function for type safety
        const safeDrawerData = drawerData;
        logger('Adding drawer data to payload - filtered:', safeDrawerData);

        // Specifically handle related_post_ids
        if (
          safeDrawerData.related_post_ids &&
          Array.isArray(safeDrawerData.related_post_ids)
        ) {
          logger(
            'Found related_post_ids in drawer data:',
            safeDrawerData.related_post_ids,
          );
          payload.related_post_ids = safeDrawerData.related_post_ids
            .map((id) => (typeof id === 'string' ? parseInt(id, 10) : id))
            .filter((id) => !isNaN(id));
          logger('Normalized related_post_ids:', payload.related_post_ids);
        }

        // Merge drawer data with proper field mapping
        Object.assign(payload, safeDrawerData);

        // Handle specific field mappings for drawer data
        if (safeDrawerData.publish_date && !payload.schedule_at) {
          payload.schedule_at = safeDrawerData.publish_date;
        }

        console.group('🔄 DRAWER DATA MERGE');
        console.log('📥 Raw drawer data:', drawerData);
        console.log('✅ Filtered drawer data:', safeDrawerData);
        console.log('🎯 Payload after merge:', payload);
        console.groupEnd();
      }

      // Handle schedule date conversion
      if (payload.scheduleAt) {
        payload.schedule_at = payload.scheduleAt.toISOString();
        payload.published_at = payload.scheduleAt.toISOString();
        // Keep legacy field for compatibility
        payload.publish_date = payload.scheduleAt.toISOString();
      }

      // Ensure tag_ids and category_ids are properly formatted arrays
      if (payload.tag_ids) {
        if (!Array.isArray(payload.tag_ids)) {
          payload.tag_ids = [payload.tag_ids];
        }
        // Convert all tag_ids to numbers for v2.0 API
        payload.tag_ids = payload.tag_ids
          .map((id) => (typeof id === 'string' ? parseInt(id, 10) : id))
          .filter((id) => !isNaN(id)); // Remove invalid IDs
      }

      // Handle categories array conversion to category_ids
      if (
        payload.categories &&
        Array.isArray(payload.categories) &&
        payload.categories.length > 0
      ) {
        // If categories is an array of objects, extract IDs
        if (
          typeof payload.categories[0] === 'object' &&
          payload.categories[0].id
        ) {
          payload.category_ids = payload.categories
            .map((category: any) =>
              typeof category.id === 'string'
                ? parseInt(category.id, 10)
                : category.id,
            )
            .filter((id) => !isNaN(id));
        }
      }

      // Ensure category_ids is an array and convert to numbers
      if (payload.category_ids) {
        if (!Array.isArray(payload.category_ids)) {
          payload.category_ids = [payload.category_ids];
        }
        payload.category_ids = payload.category_ids
          .map((id) => (typeof id === 'string' ? parseInt(id, 10) : id))
          .filter((id) => !isNaN(id)); // Remove invalid IDs
      }

      // Handle legacy categoryId field
      if (payload.categoryId) {
        const categoryIdNumber =
          typeof payload.categoryId === 'string'
            ? parseInt(payload.categoryId, 10)
            : payload.categoryId;

        if (!isNaN(categoryIdNumber)) {
          if (!payload.category_ids) {
            payload.category_ids = [categoryIdNumber];
          } else if (!payload.category_ids.includes(categoryIdNumber)) {
            payload.category_ids.push(categoryIdNumber);
          }
        }
      }

      const seoData = getSEOData();
      // Convert SeoMeta to SEOMetaData format

      payload.seo = seoData;

      // Log payload trước khi chuyển đổi
      logger('Original payload', payload);

      // Chuẩn bị payload cho API với kiểu any để tránh lỗi TypeScript
      const apiPayload: any = { ...payload };

      // Ensure title is properly set
      if (!apiPayload.title && formValues?.title) {
        apiPayload.title = formValues.title;
      }

      // Remove tenant_id and website_id from payload as they're sent in headers
      delete apiPayload.tenant_id;
      delete apiPayload.website_id;

      // Log payload sau khi chuyển đổi
      logger('Final API payload', apiPayload);

      if (isNew) {
        res = await createItem(apiPayload);
        if (res.status.success) {
          message.success(t('addSuccess'));
          // Chuyển hướng đến trang chi tiết bài viết sau khi tạo thành công
          if (res.data?.id) {
            const postId = res.data.id;
            // Navigate to blog detail view after creating blog post
            navigate(`/blog/${postId}`);
          }
        }
      } else {
        res = await updateItem(id!, apiPayload);
        if (res.status.success) {
          message.success(t('updateSuccess'));

          // Update related posts if they exist in payload or drawer data
          if (
            (payload.related_post_ids && payload.related_post_ids.length > 0) ||
            (drawerData?.related_post_ids &&
              drawerData.related_post_ids.length > 0)
          ) {
            const finalRelatedPostIds =
              payload.related_post_ids || drawerData?.related_post_ids || [];

            logger('Updating related posts with:', finalRelatedPostIds);
            try {
              await updatePostRelatedPosts(id!, {
                related_post_ids: finalRelatedPostIds,
                is_bidirectional: true,
              });
              logger('Related posts updated successfully');
            } catch (relatedError) {
              logger('Error updating related posts:', relatedError);
              // Don't show error to user as main update was successful
            }
          } else {
            logger('No related posts to update - empty array or undefined');
          }
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setFormValues(res.data);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') ?? t('submitError'),
      );
    }
  };

  const formValuesChange = useCallback(
    (changedValues: any, allValues: any) => {
      console.log('formValuesChange', changedValues, allValues);

      if ('content' in changedValues) {
        // Chỉ cập nhật content trong store, không cập nhật toàn bộ form
        useBlogStore.getState().setFormState({
          formValues: {
            ...useBlogStore.getState().form.formValues,
            content: changedValues.content,
          },
        });
        return; // Thoát sớm, không xử lý các trường hợp khác khi content thay đổi
      } else {
        useBlogStore.getState().setFormState({
          formValues: {
            ...formValues,
            ...allValues,
          },
        });
      }

      // If the title changes and slug is empty, trigger slug generation
      if (
        'title' in changedValues &&
        (!allValues.slug || allValues.slug === '')
      ) {
        // Let the InputSlug component handle this via its sourceField prop
        logger('Title changed, slug will be auto-generated');
      }
    },
    [formValues, logger],
  );

  // Hàm xử lý mở schedule modal
  const handleOpenSchedule = () => {
    setScheduleVisible(true);
  };

  // Hàm xử lý đóng schedule modal
  const handleCloseSchedule = () => {
    setScheduleVisible(false);
  };

  const handleOpenHistory = () => {
    if (!isNew && id) {
      setHistoryModalVisible(true);
    } else {
      message.warning('Chỉ có thể xem lịch sử của bài viết đã được lưu');
    }
  };

  const handleOpenRevisions = () => {
    if (!isNew && id) {
      setRevisionsModalVisible(true);
    } else {
      message.warning('Chỉ có thể xem revisions của bài viết đã được lưu');
    }
  };

  const handleRevisionRestore = () => {
    // Reload the blog post after a revision is restored
    if (id) {
      const getItemData = async (_id: string) => {
        useBlogStore.getState().setUIState({ loading: true });
        const res = await getItem(_id);
        if (res.status.success) {
          const item = transformBlog(res.data);
          form.setFieldsValue(item);
          setFormValues(item);
          useBlogStore.getState().setDataState({ currentBlog: item });
          useBlogStore.getState().setFormState({ formValues: item });
          message.success('Bài viết đã được cập nhật từ revision');
        }
        useBlogStore.getState().setUIState({ loading: false });
      };
      getItemData(id);
    }
  };

  // Hàm xử lý khi user chọn thời gian schedule
  const handleSchedule = async (dateTime: Dayjs) => {
    try {
      setScheduledDateTime(dateTime);
      setFormValues((prev) => ({
        ...prev,
        scheduleAt: dateTime,
        publish_date: dateTime.toISOString(),
        schedule_at: dateTime.toISOString(),
        status: BlogPostStatus.SCHEDULED, // Set status thành schedule
      }));
      // Cập nhật status trong form
      form.setFieldValue('status', BlogPostStatus.SCHEDULED);

      // Submit form và chờ kết quả
      await form.validateFields();
      const formValues = form.getFieldsValue();

      // Gọi onFinish để lưu dữ liệu
      await onFinish(formValues);

      message.success(
        `Đã lên lịch xuất bản vào: ${dateTime.format('DD/MM/YYYY HH:mm')}`,
      );

      // Điều hướng về trang danh sách
      navigate('/blog');
    } catch (error) {
      logger('Error scheduling post:', error);
      message.error('Có lỗi xảy ra khi lên lịch bài viết');
    }
  };

  // Hàm xử lý lưu bài viết
  const onSave = () => {
    form.submit();
  };

  // Hàm xử lý xuất bản bài viết
  const onUpdateStatus = async (status: BlogPostStatus) => {
    console.log('[publish]', status);

    // Cập nhật form
    form.setFieldValue('status', status);
    form.submit();
  };

  return (
    <Row gutter={24}>
      <Col
        xs={24}
        sm={6}
        md={5}
        lg={4}
        xl={2}
        style={{
          borderRight: '1px solid #f0f0f0',
          minHeight: 600,
          maxWidth: '90px',
          width: '90px',
        }}
      >
        <BlogMenu />
      </Col>
      <Col xs={24} sm={18} md={19} lg={20} xl={22}>
        <div className="blog-form-container">
          <BlogTopBar
            isNew={isNew}
            onSave={onSave}
            onSchedule={handleOpenSchedule}
            onUpdateStatus={onUpdateStatus}
            loading={loading}
            onHistory={handleOpenHistory}
            onRevisions={handleOpenRevisions}
          />
          <Spin spinning={loading}>
            <Form
              style={{ marginTop: 8 }}
              form={form}
              name="form"
              layout="vertical"
              onFinish={onFinish}
              autoComplete="off"
              initialValues={initForm}
              onValuesChange={formValuesChange}
              className="form-blog-detail min-h-screen"
            >
              <Row gutter={16}>
                <Col xs={24} lg={18}>
                  <Card size="small" title="Thông tin chung">
                    <FormItem
                      label={t('Tiêu đề bài viết')}
                      name="title"
                      rules={[
                        { required: true, message: t('pleaseEnterData') },
                      ]}
                    >
                      <Input />
                    </FormItem>

                    <FormItem
                      label={t('Đường dẫn URL (tự động)')}
                      name="slug"
                      className="form-item-slug"
                      rules={[
                        { required: false, message: t('pleaseEnterData') },
                      ]}
                    >
                      <InputSlug
                        form={form}
                        name="slug"
                        sourceField="title"
                        isEditMode={!isNew}
                        value={formValues?.slug || ''}
                      />
                    </FormItem>
                  </Card>

                  <Card size="small" title="Nội dung">
                    <FormItem
                      rules={[
                        { required: false, message: t('pleaseEnterData') },
                      ]}
                      name="content"
                    >
                      {!loading && (
                        <PlateEditor
                          placeholder="Nhập nội dung bài viết..."
                          theme="light"
                        />
                      )}
                    </FormItem>
                  </Card>
                </Col>

                <Col xs={24} lg={6}>
                  {!isNew && formValues && formValues.id && (
                    <Card size="small" title="Workflow">
                      <WorkflowWidget
                        post={formValues}
                        onUpdate={() => {
                          // Reload blog post after workflow update
                          if (id) {
                            const getItemData = async (_id: string) => {
                              const res = await getItem(_id);
                              if (res.status.success) {
                                const blogData = transformBlog(res.data);
                                form.setFieldsValue(blogData);
                                setFormValues(blogData);
                              }
                            };
                            getItemData(id);
                          }
                        }}
                        showHistory={true}
                      />
                    </Card>
                  )}
                </Col>
              </Row>
            </Form>
          </Spin>
        </div>
      </Col>

      {/* <BlogSetupDrawer visible={drawerVisible} onClose={handleDrawerClose} /> */}

      <BlogSchedule
        visible={scheduleVisible}
        onClose={handleCloseSchedule}
        onSchedule={handleSchedule}
        initialDateTime={scheduledDateTime || undefined}
      />

      {!isNew && id && formValues && (
        <BlogHistoryModal
          visible={historyModalVisible}
          onClose={() => setHistoryModalVisible(false)}
          blogPostId={parseInt(id)}
          blogTitle={formValues.title}
        />
      )}

      {!isNew && id && formValues && (
        <BlogRevisionsModal
          visible={revisionsModalVisible}
          onClose={() => setRevisionsModalVisible(false)}
          blogPostId={parseInt(id)}
          blogTitle={formValues.title}
          onRevisionRestore={handleRevisionRestore}
        />
      )}
    </Row>
  );
};

export default IndexForm;
